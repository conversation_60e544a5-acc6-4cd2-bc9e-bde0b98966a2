# 优化的Dockerfile - 针对中国网络环境加速
# Build stage
FROM golang:1.23-alpine AS builder

# 使用中国镜像源加速
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# Install build dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# 设置Go代理加速
ENV GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=sum.golang.google.cn \
    GO111MODULE=on

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY backend/go.mod backend/go.sum ./

# Download dependencies (this layer will be cached if go.mod/go.sum don't change)
RUN go mod download && go mod verify

# Copy source code
COPY backend/ .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main cmd/server/main.go

# Verify the binary
RUN ls -la ./main && echo "Binary built successfully"

# Runtime stage - 使用更轻量的基础镜像
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh

# 使用中科大镜像源加速
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list

# 合并RUN命令减少层数，加速构建
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 核心依赖
    libreoffice-core \
    libreoffice-impress \
    libreoffice-writer \
    libreoffice-calc \
    # 中文支持
    libreoffice-l10n-zh-cn \
    # 媒体处理
    ffmpeg \
    imagemagick \
    poppler-utils \
    # 系统工具
    curl \
    ca-certificates \
    # 字体支持
    fonts-liberation \
    fonts-dejavu-core \
    fonts-noto-cjk \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    fontconfig \
    # 语言环境
    locales \
    # Java支持（LibreOffice需要）
    default-jre-headless \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 配置中文环境
RUN locale-gen zh_CN.UTF-8 && \
    update-locale LANG=zh_CN.UTF-8

# 简化字体配置
RUN fc-cache -fv

# 配置ImageMagick允许PDF操作
RUN sed -i 's/rights="none" pattern="PDF"/rights="read|write" pattern="PDF"/' /etc/ImageMagick-6/policy.xml

# 创建应用用户
RUN groupadd -r -g 1001 appgroup && \
    useradd -r -u 1001 -g appgroup -s /bin/false -c "App User" appuser

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/main .

# Create necessary directories with proper permissions
RUN mkdir -p uploads screenshots videos temp logs && \
    chown -R appuser:appgroup /app && \
    chmod -R 755 /app && \
    chmod +x /app/main

# Create user home directory
RUN mkdir -p /home/<USER>
    chown appuser:appgroup /home/<USER>

# Switch to app user
USER appuser

# Set environment variables for the application
ENV HOME=/home/<USER>
    PATH=/app:$PATH

# Expose port
EXPOSE 8080

# Add labels
LABEL maintainer="PPT Narrator Team" \
      version="1.0.0" \
      description="PPT Narrator - Fast build version"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["./main"]
