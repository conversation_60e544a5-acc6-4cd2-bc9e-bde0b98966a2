version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=ppt_narrator
      - POSTGRES_USER=ppt_narrator
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-ppt_narrator123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - LC_ALL=C.UTF-8
      - LANG=C.UTF-8
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ppt_narrator -d ppt_narrator"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - ppt-network

  mcp-pipeline-server:
    build: ./mcp
    environment:
      - PORT=48080
      - PPT_NARRATOR_URL=http://ppt-narrator:8080
      - MCP_ACCESS_KEY=${MCP_ACCESS_KEY:-}
      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-false}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-false}
      - MCP_DEBUG=${MCP_DEBUG:-false}
    restart: unless-stopped
    depends_on:
      - ppt-narrator
    networks:
      - ppt-network

  ppt-narrator:
    build: .
    environment:
      - PORT=8080
      - DATABASE_URL=postgres://ppt_narrator:${POSTGRES_PASSWORD:-ppt_narrator123}@postgres:5432/ppt_narrator?sslmode=disable&client_encoding=UTF8
      - UPLOAD_DIR=/app/uploads
      - SCREENSHOT_DIR=/app/screenshots
      - VIDEO_DIR=/app/videos
      - TEMP_DIR=/app/temp
      - AI_PROVIDER=${AI_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - MINIMAX_GROUP_ID=${MINIMAX_GROUP_ID}
      - MINIMAX_MODEL=${MINIMAX_MODEL:-abab6.5s-chat}
      - TTS_PROVIDER=${TTS_PROVIDER:-openai}
      - TTS_VOICE=${TTS_VOICE:-alloy}
      - TTS_SPEED=${TTS_SPEED:-1.0}
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID}
      - MINIMAX_TTS_MODEL=${MINIMAX_TTS_MODEL:-speech-02-hd}
      - MINIMAX_TTS_VOICE_ID=${MINIMAX_TTS_VOICE_ID:-male-qn-qingse}
      - LIBREOFFICE_PATH=libreoffice
      - FFMPEG_PATH=ffmpeg
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-}
      - NARRATOR_ROLE=${NARRATOR_ROLE:-资深教授}
      - NARRATOR_STYLE=${NARRATOR_STYLE:-亲切自然}
      - TARGET_AUDIENCE=${TARGET_AUDIENCE:-大学生}
      - SPEAKING_TONE=${SPEAKING_TONE:-轻松友好}
      - SPEECH_NATURALNESS=${SPEECH_NATURALNESS:-高度口语化}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - TEMPERATURE=${TEMPERATURE:-0.7}
    volumes:
      - ppt_uploads_prod:/app/uploads
      - ppt_screenshots_prod:/app/screenshots
      - ppt_videos_prod:/app/videos
      - ppt_temp_prod:/app/temp
      - ppt_audio_prod:/app/audio
      - ppt_work_prod:/app/work
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ppt-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    restart: unless-stopped
    depends_on:
      - ppt-narrator
      - mcp-pipeline-server
    networks:
      - ppt-network

volumes:
  postgres_data_prod:
  ppt_uploads_prod:
  ppt_screenshots_prod:
  ppt_videos_prod:
  ppt_temp_prod:
  ppt_audio_prod:
  ppt_work_prod:
  nginx_logs:

networks:
  ppt-network:
    driver: bridge
