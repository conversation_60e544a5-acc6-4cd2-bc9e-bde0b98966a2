# PPT Narrator Makefile

.PHONY: help build run clean test docker-build docker-run docker-dev docker-prod deps check-deps

# Default target
help:
	@echo "PPT Narrator - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  make run          - Run the server locally"
	@echo "  make dev          - Run development server with auto-reload"
	@echo "  make build        - Build the binary"
	@echo "  make test         - Run tests"
	@echo "  make clean        - Clean build artifacts and temp files"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run with Docker Compose"
	@echo "  make docker-dev   - Run development environment with Docker"
	@echo "  make docker-prod  - Run production environment with Docker"
	@echo "  make docker-alpine- Build Alpine-based Docker image"
	@echo ""
	@echo "Setup:"
	@echo "  make setup        - Initial setup (create directories, copy env)"
	@echo "  make deps         - Download dependencies"
	@echo "  make check-deps   - Check system dependencies"
	@echo ""

# Setup project
setup:
	@echo "Setting up PPT Narrator..."
	@mkdir -p uploads screenshots videos temp
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from example"; fi
	@echo "Setup complete! Please edit .env file with your configuration."

# Download dependencies
deps:
	@echo "Downloading Go dependencies..."
	cd backend && go mod download
	cd backend && go mod tidy

# Check system dependencies
check-deps:
	@echo "Checking system dependencies..."
	@command -v go >/dev/null 2>&1 || { echo "Go is not installed"; exit 1; }
	@command -v libreoffice >/dev/null 2>&1 || { echo "LibreOffice is not installed"; exit 1; }
	@command -v ffmpeg >/dev/null 2>&1 || { echo "FFmpeg is not installed"; exit 1; }
	@echo "All dependencies are available!"

# Build the application
build: deps
	@echo "Building PPT Narrator..."
	@mkdir -p bin
	cd backend && CGO_ENABLED=0 go build -o ../bin/ppt-narrator cmd/server/main.go
	@echo "Build complete: bin/ppt-narrator"

# Run the server
run: deps
	@echo "Starting PPT Narrator server..."
	@mkdir -p uploads screenshots videos temp
	cd backend && go run cmd/server/main.go

# Clean build artifacts
clean:
	@echo "Cleaning up..."
	@rm -rf bin/
	@rm -rf temp/*
	@rm -f *.log
	@echo "Clean complete"

# Run tests
test:
	@echo "Running tests..."
	cd backend && go test -v ./...

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t ppt-narrator .

# Build Alpine Docker image
docker-alpine:
	@echo "Building Alpine Docker image..."
	docker build -f Dockerfile.alpine -t ppt-narrator:alpine .

# Build development Docker image
docker-build-dev:
	@echo "Building development Docker image..."
	docker build -f Dockerfile.dev -t ppt-narrator:dev .

# Run with Docker Compose (default)
docker-run:
	@echo "Starting with Docker Compose..."
	@if [ ! -f .env ]; then echo "Please create .env file first"; exit 1; fi
	docker-compose up --build

# Run development environment with Docker
docker-dev:
	@echo "Starting development environment with Docker Compose..."
	@if [ ! -f .env ]; then echo "Please create .env file first"; exit 1; fi
	docker-compose -f docker-compose.dev.yml up --build

# Run production environment with Docker
docker-prod:
	@echo "Starting production environment with Docker Compose..."
	@if [ ! -f .env ]; then echo "Please create .env file first"; exit 1; fi
	docker-compose -f docker-compose.prod.yml up --build -d

# Stop Docker containers
docker-stop:
	@echo "Stopping Docker containers..."
	docker-compose down

# Stop and remove Docker containers with volumes
docker-clean:
	@echo "Cleaning up Docker containers and volumes..."
	docker-compose down -v --remove-orphans
	docker system prune -f

# View Docker logs
docker-logs:
	@echo "Viewing Docker logs..."
	docker-compose logs -f

# Execute shell in running container
docker-shell:
	@echo "Opening shell in running container..."
	docker-compose exec ppt-narrator /bin/bash

# Development server with auto-reload (requires air)
dev:
	@if command -v air >/dev/null 2>&1; then \
		echo "Starting development server with auto-reload..."; \
		air; \
	else \
		echo "Air not installed. Install with: go install github.com/cosmtrek/air@latest"; \
		echo "Falling back to normal run..."; \
		make run; \
	fi

# Install development tools
install-dev-tools:
	@echo "Installing development tools..."
	go install github.com/cosmtrek/air@latest
	@echo "Development tools installed"

# Format code
fmt:
	@echo "Formatting code..."
	cd backend && go fmt ./...
	@echo "Code formatted"

# Lint code (requires golangci-lint)
lint:
	@if command -v golangci-lint >/dev/null 2>&1; then \
		echo "Running linter..."; \
		cd backend && golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install from: https://golangci-lint.run/usage/install/"; \
	fi

# Security check (requires gosec)
security:
	@if command -v gosec >/dev/null 2>&1; then \
		echo "Running security check..."; \
		cd backend && gosec ./...; \
	else \
		echo "gosec not installed. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Generate API documentation (if using swag)
docs:
	@if command -v swag >/dev/null 2>&1; then \
		echo "Generating API documentation..."; \
		cd backend && swag init -g cmd/server/main.go; \
	else \
		echo "swag not installed. Install with: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# Full check (format, lint, test)
check: fmt lint test
	@echo "All checks passed!"

# Release build
release: clean
	@echo "Building release version..."
	@mkdir -p bin
	cd backend && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o ../bin/ppt-narrator-linux-amd64 cmd/server/main.go
	cd backend && CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags="-w -s" -o ../bin/ppt-narrator-windows-amd64.exe cmd/server/main.go
	cd backend && CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags="-w -s" -o ../bin/ppt-narrator-darwin-amd64 cmd/server/main.go
	@echo "Release builds complete in bin/"

# Show project status
status:
	@echo "PPT Narrator Project Status:"
	@echo "============================"
	@echo "Go version: $(shell go version)"
	@echo "Project structure:"
	@find . -name "*.go" | head -10
	@echo "Dependencies:"
	@cd backend && go list -m all | head -5
	@echo "Build status:"
	@if [ -f bin/ppt-narrator ]; then echo "✓ Binary exists"; else echo "✗ Binary not built"; fi
	@echo "Configuration:"
	@if [ -f .env ]; then echo "✓ .env file exists"; else echo "✗ .env file missing"; fi
