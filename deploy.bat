@echo off
setlocal enabledelayedexpansion

REM PPT Narrator 部署脚本 (Windows版本)
REM 用于快速部署整个PPT Narrator商业服务平台

echo ==========================================
echo        PPT Narrator 部署脚本
echo ==========================================
echo.

REM 检查Docker是否安装
echo [INFO] 检查系统依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 系统依赖检查通过

REM 创建必要的目录
echo [INFO] 创建必要的目录...
if not exist "uploads" mkdir uploads
if not exist "videos" mkdir videos
if not exist "temp" mkdir temp
if not exist "screenshots" mkdir screenshots
if not exist "audio" mkdir audio
if not exist "work" mkdir work
if not exist "logs" mkdir logs

echo [SUCCESS] 目录创建完成

REM 停止现有服务
echo [INFO] 停止现有服务...
docker-compose down >nul 2>&1

REM 构建和启动服务
echo [INFO] 构建Docker镜像...
docker-compose build --no-cache

if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败
    pause
    exit /b 1
)

echo [INFO] 启动服务...
docker-compose up -d

if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 服务启动完成

REM 等待服务就绪
echo [INFO] 等待服务就绪...
timeout /t 30 /nobreak >nul

REM 显示部署信息
echo [SUCCESS] PPT Narrator 部署完成！
echo.
echo ==========================================
echo            服务访问地址
echo ==========================================
echo 用户前端:     http://localhost:3000
echo 管理员后台:   http://localhost:3001
echo 业务API:      http://localhost:8081
echo PPT生成器:    http://localhost:8080
echo ==========================================
echo.
echo 默认账号信息:
echo 管理员账号:   admin / password
echo 测试用户:     testuser / password
echo.
echo 常用命令:
echo 查看服务状态: docker-compose ps
echo 查看日志:     docker-compose logs -f [service]
echo 停止服务:     docker-compose down
echo 重启服务:     docker-compose restart
echo.

pause
