version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ppt-narrator-postgres
    environment:
      POSTGRES_DB: ppt_narrator_business
      POSTGRES_USER: ppt_user
      POSTGRES_PASSWORD: ppt_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - ppt-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ppt-narrator-redis
    ports:
      - "6379:6379"
    networks:
      - ppt-network

  # 商业API后端
  business-api:
    build:
      context: ./business-api
      dockerfile: Dockerfile
    container_name: ppt-narrator-business-api
    environment:
      - DATABASE_URL=**********************************************/ppt_narrator_business?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - PPT_GENERATOR_URL=http://host.docker.internal:8080
      - UPLOAD_DIR=/app/uploads
      - PORT=8080
    volumes:
      - ./uploads:/app/uploads
      - ./videos:/app/videos
    ports:
      - "8081:8080"
    depends_on:
      - postgres
      - redis
    networks:
      - ppt-network

  # 用户前端
  user-frontend:
    build:
      context: ./user-frontend
      dockerfile: Dockerfile
    container_name: ppt-narrator-user-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8081
    ports:
      - "3000:80"
    depends_on:
      - business-api
    networks:
      - ppt-network

  # 管理员后台
  admin-dashboard:
    build:
      context: ./admin-dashboard
      dockerfile: Dockerfile
    container_name: ppt-narrator-admin-dashboard
    environment:
      - REACT_APP_API_URL=http://localhost:8081
    ports:
      - "3001:80"
    depends_on:
      - business-api
    networks:
      - ppt-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ppt-narrator-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - user-frontend
      - admin-dashboard
      - business-api
    networks:
      - ppt-network

volumes:
  postgres_data:

networks:
  ppt-network:
    driver: bridge
