# Git
.git
.gitignore
.gitattributes

# Development files
.env
.env.local
.env.example
.air.toml

# Build artifacts
bin/
tmp/
build/
dist/
target/
out/

# Runtime directories (will be created in container)
uploads/
videos/
temp/
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Generated files and archives
*.tar
*.tar.gz
*.zip
*.rar
*.7z
*.exe

# Test files
*_test.go
testdata/
test_files/
coverage.out
coverage.html

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Documentation (keep only essential)
*.md
!README.md

# Other services (not needed for business-api)
../../backend/
../../mcp/
../../web/user-frontend/
../../web/admin-dashboard/
../../docs/
../../tests/
../../tools/
../../scripts/
../../uploads/
../../videos/
../../screenshots/
