package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"ppt-narrator-business/internal/config"
	"ppt-narrator-business/internal/handlers"
	"ppt-narrator-business/internal/middleware"
	"ppt-narrator-business/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Set Gin mode
	gin.SetMode(cfg.Mode)

	// Initialize database
	dbService, err := services.NewDatabaseService(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer dbService.Close()

	// Create default admin and configs
	if err := dbService.CreateDefaultAdmin(); err != nil {
		log.Printf("Warning: Failed to create default admin: %v", err)
	}
	if err := dbService.CreateDefaultConfigs(); err != nil {
		log.Printf("Warning: Failed to create default configs: %v", err)
	}

	// Initialize Redis client
	redisOpt, err := redis.ParseURL(cfg.RedisURL)
	if err != nil {
		log.Fatalf("Failed to parse Redis URL: %v", err)
	}
	redisClient := redis.NewClient(redisOpt)

	// Test Redis connection
	if err := redisClient.Ping(context.Background()).Err(); err != nil {
		log.Printf("Warning: Redis connection failed: %v", err)
		redisClient = nil // Disable Redis-dependent features
	}

	// Initialize services
	authService := services.NewAuthService(dbService.GetDB(), cfg.JWTSecret, cfg.JWTExpiration)
	creditService := services.NewCreditService(dbService.GetDB())
	redeemCodeService := services.NewRedeemCodeService(dbService.GetDB(), creditService)
	pptGeneratorService := services.NewPPTGeneratorService(
		dbService.GetDB(),
		cfg.PPTGeneratorURL,
		cfg.PPTGeneratorTimeout,
		cfg.UploadDir,
		cfg.VideoDir,
	)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, creditService, cfg.DefaultCredits)
	creditHandler := handlers.NewCreditHandler(creditService, redeemCodeService)
	redeemCodeHandler := handlers.NewRedeemCodeHandler(redeemCodeService)
	projectHandler := handlers.NewProjectHandler(pptGeneratorService, creditService, cfg.PPTGenerationCost)
	adminHandler := handlers.NewAdminHandler(authService, creditService, redeemCodeService, pptGeneratorService)

	// Initialize Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware(cfg.CORSOrigins))

	// Add rate limiting if Redis is available
	if redisClient != nil {
		router.Use(middleware.RateLimitMiddleware(redisClient, cfg.RateLimitRequests, cfg.RateLimitWindow))
	}

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		status := gin.H{
			"status":   "ok",
			"database": "connected",
			"redis":    "disconnected",
		}

		// Check database health
		if err := dbService.Health(); err != nil {
			status["database"] = "error: " + err.Error()
			c.JSON(http.StatusServiceUnavailable, status)
			return
		}

		// Check Redis health
		if redisClient != nil {
			if err := redisClient.Ping(context.Background()).Err(); err != nil {
				status["redis"] = "error: " + err.Error()
			} else {
				status["redis"] = "connected"
			}
		}

		c.JSON(http.StatusOK, status)
	})

	// API routes
	api := router.Group("/api/v1")

	// Public routes (no authentication required)
	public := api.Group("")
	{
		public.POST("/auth/register", authHandler.Register)
		public.POST("/auth/login", authHandler.Login)
	}

	// Protected routes (authentication required)
	protected := api.Group("")
	protected.Use(middleware.AuthMiddleware(authService))
	{
		// Auth routes
		auth := protected.Group("/auth")
		{
			auth.GET("/profile", authHandler.GetProfile)
			auth.PUT("/profile", authHandler.UpdateProfile)
			auth.POST("/change-password", authHandler.ChangePassword)
			auth.POST("/refresh-token", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
		}

		// Credit routes
		credits := protected.Group("/credits")
		{
			credits.GET("", creditHandler.GetCredits)
			credits.GET("/history", creditHandler.GetCreditHistory)
			credits.GET("/stats", creditHandler.GetCreditStats)
			credits.POST("/redeem", creditHandler.RedeemCode)
			credits.GET("/redeem-history", creditHandler.GetRedeemHistory)
		}

		// PPT project routes
		projects := protected.Group("/projects")
		{
			projects.POST("/upload-and-process", projectHandler.UploadAndProcess)
			projects.GET("", projectHandler.GetProjects)
			projects.GET("/:id", projectHandler.GetProject)
			projects.DELETE("/:id", projectHandler.DeleteProject)
			projects.GET("/:id/download", projectHandler.DownloadVideo)
		}
	}

	// Admin routes (admin authentication required)
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware(authService))
	admin.Use(middleware.AdminMiddleware())
	{
		// Credit management
		adminCredits := admin.Group("/credits")
		{
			adminCredits.POST("/add", creditHandler.AdminAddCredits)
			adminCredits.POST("/transfer", creditHandler.AdminTransferCredits)
			adminCredits.POST("/bulk-add", creditHandler.AdminBulkAddCredits)
		}

		// Redeem code management
		adminCodes := admin.Group("/redeem-codes")
		{
			adminCodes.POST("", redeemCodeHandler.CreateRedeemCodes)
			adminCodes.GET("", redeemCodeHandler.GetRedeemCodes)
			adminCodes.PUT("/:id", redeemCodeHandler.UpdateRedeemCode)
			adminCodes.DELETE("/:id", redeemCodeHandler.DeleteRedeemCode)
			adminCodes.GET("/stats", redeemCodeHandler.GetRedeemCodeStats)
			adminCodes.POST("/batch", redeemCodeHandler.BatchCreateRedeemCodes)
		}

		// Dashboard
		admin.GET("/dashboard/stats", adminHandler.GetDashboardStats)

		// User management
		adminUsers := admin.Group("/users")
		{
			adminUsers.GET("", adminHandler.GetUsers)
			adminUsers.GET("/stats", adminHandler.GetUserStats)
			adminUsers.PUT("/:id/status", adminHandler.UpdateUserStatus)
		}

		// Project management
		adminProjects := admin.Group("/projects")
		{
			adminProjects.GET("", projectHandler.AdminGetProjects)
			adminProjects.GET("/stats", projectHandler.AdminGetProjectStats)
		}

		// Additional credits management
		adminCreditsExtra := admin.Group("/credits-extra")
		{
			adminCreditsExtra.POST("/add", adminHandler.AddCreditsToUser)
		}

		// System management
		adminSystem := admin.Group("/system")
		{
			adminSystem.GET("/info", adminHandler.GetSystemInfo)
		}
	}

	// Start server
	addr := fmt.Sprintf("%s:%s", cfg.Host, cfg.Port)
	log.Printf("Starting server on %s", addr)
	log.Printf("Environment: %s", cfg.Mode)
	log.Printf("Database: %s", cfg.DatabaseURL)
	if redisClient != nil {
		log.Printf("Redis: %s", cfg.RedisURL)
	} else {
		log.Printf("Redis: disabled")
	}

	if err := router.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
