package handlers

import (
	"net/http"
	"ppt-narrator-business/internal/models"
	"ppt-narrator-business/internal/services"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// ProjectHandler handles project-related endpoints
type ProjectHandler struct {
	pptGeneratorService *services.PPTGeneratorService
	creditService       *services.CreditService
	pptGenerationCost   int64
}

// NewProjectHandler creates a new project handler
func NewProjectHandler(pptGeneratorService *services.PPTGeneratorService, creditService *services.CreditService, pptGenerationCost int64) *ProjectHandler {
	return &ProjectHandler{
		pptGeneratorService: pptGeneratorService,
		creditService:       creditService,
		pptGenerationCost:   pptGenerationCost,
	}
}

// UploadAndProcess handles PPT file upload and processing
func (h *ProjectHandler) UploadAndProcess(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Check if user has enough credits
	hasEnough, currentCredits, err := h.creditService.CheckSufficientCredits(userID.(string), h.pptGenerationCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to check credits: " + err.Error(),
		})
		return
	}

	if !hasEnough {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Insufficient credits",
			"data": gin.H{
				"required": h.pptGenerationCost,
				"current":  currentCredits,
			},
		})
		return
	}

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "No file uploaded: " + err.Error(),
		})
		return
	}

	// Validate file type
	if !h.isValidPPTFile(file.Filename) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid file type. Only .ppt and .pptx files are allowed",
		})
		return
	}

	// Get form data
	projectName := c.PostForm("project_name")
	if projectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project name is required",
		})
		return
	}

	// Parse configuration
	config := h.parseProjectConfig(c)

	// Create project request
	req := &services.CreateProjectRequest{
		UserID:           userID.(string),
		ProjectName:      projectName,
		UserRequirements: c.PostForm("user_requirements"),
		Config:           config,
		File:             file,
	}

	// Create project
	project, err := h.pptGeneratorService.CreateProject(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create project: " + err.Error(),
		})
		return
	}

	// Consume credits
	err = h.creditService.ConsumeCredits(
		userID.(string),
		h.pptGenerationCost,
		"PPT generation: "+projectName,
		project.ID,
		"project",
	)
	if err != nil {
		// If credit consumption fails, we should clean up the project
		// For now, just log the error
		// TODO: Implement proper cleanup
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to consume credits: " + err.Error(),
		})
		return
	}

	// Update project with credits cost
	project.CreditsCost = h.pptGenerationCost

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Project created successfully",
		"data":    project,
	})
}

// GetProjects returns user's projects
func (h *ProjectHandler) GetProjects(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	projects, total, err := h.pptGeneratorService.GetUserProjects(userID.(string), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"projects":  projects,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetProject returns a specific project
func (h *ProjectHandler) GetProject(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	project, err := h.pptGeneratorService.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user owns the project
	if project.UserID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "Access denied",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    project,
	})
}

// DeleteProject deletes a project
func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	err := h.pptGeneratorService.DeleteProject(projectID, userID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Project deleted successfully",
	})
}

// DownloadVideo handles video file download
func (h *ProjectHandler) DownloadVideo(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	project, err := h.pptGeneratorService.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user owns the project
	if project.UserID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "Access denied",
		})
		return
	}

	// Check if project is completed
	if project.Status != "completed" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project is not completed yet",
		})
		return
	}

	// Check if video file exists
	if project.VideoPath == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Video file not found",
		})
		return
	}

	// Serve the file
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+project.Name+".mp4")
	c.Header("Content-Type", "application/octet-stream")
	c.File(project.VideoPath)
}

// isValidPPTFile checks if the file has a valid PPT extension
func (h *ProjectHandler) isValidPPTFile(filename string) bool {
	ext := strings.ToLower(filename)
	return strings.HasSuffix(ext, ".ppt") || strings.HasSuffix(ext, ".pptx")
}

// parseProjectConfig parses project configuration from form data
func (h *ProjectHandler) parseProjectConfig(c *gin.Context) models.ProjectConfig {
	config := models.ProjectConfig{
		NarrationStyle:     c.DefaultPostForm("narration_style", "professional"),
		Voice:              c.DefaultPostForm("voice", "alloy"),
		Language:           c.DefaultPostForm("language", "zh-CN"),
		DetailLevel:        c.DefaultPostForm("detail_level", "medium"),
		IncludeTransitions: c.DefaultPostForm("include_transitions", "true") == "true",
		CustomPrompt:       c.PostForm("custom_prompt"),
	}

	// Parse speech speed
	if speedStr := c.PostForm("speech_speed"); speedStr != "" {
		if speed, err := strconv.ParseFloat(speedStr, 64); err == nil {
			config.SpeechSpeed = speed
		} else {
			config.SpeechSpeed = 1.0
		}
	} else {
		config.SpeechSpeed = 1.0
	}

	return config
}

// Admin endpoints

// AdminGetProjects returns all projects (admin only)
func (h *ProjectHandler) AdminGetProjects(c *gin.Context) {
	// Parse pagination and filters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	projects, total, err := h.pptGeneratorService.GetAllProjects(page, pageSize, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"projects":  projects,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
			"status":    status,
		},
	})
}

// AdminGetProjectStats returns project statistics (admin only)
func (h *ProjectHandler) AdminGetProjectStats(c *gin.Context) {
	stats, err := h.pptGeneratorService.GetProjectStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
