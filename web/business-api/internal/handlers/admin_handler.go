package handlers

import (
	"net/http"
	"ppt-narrator-business/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminHandler handles admin-related endpoints
type AdminHandler struct {
	authService         *services.AuthService
	creditService       *services.CreditService
	redeemCodeService   *services.RedeemCodeService
	pptGeneratorService *services.PPTGeneratorService
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(
	authService *services.AuthService,
	creditService *services.CreditService,
	redeemCodeService *services.RedeemCodeService,
	pptGeneratorService *services.PPTGeneratorService,
) *AdminHandler {
	return &AdminHandler{
		authService:         authService,
		creditService:       creditService,
		redeemCodeService:   redeemCodeService,
		pptGeneratorService: pptGeneratorService,
	}
}

// GetUsers returns all users (admin only)
func (h *AdminHandler) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	users, total, err := h.authService.GetUsers(page, pageSize, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users":     users,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetUserStats returns user statistics (admin only)
func (h *AdminHandler) GetUserStats(c *gin.Context) {
	stats, err := h.authService.GetUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// AddCreditsToUser adds credits to a user (admin only)
func (h *AdminHandler) AddCreditsToUser(c *gin.Context) {
	var req struct {
		UserID      string `json:"user_id" binding:"required"`
		Amount      int64  `json:"amount" binding:"required,min=1"`
		Description string `json:"description" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Add credits
	err := h.creditService.AddCredits(
		req.UserID,
		req.Amount,
		"admin_add",
		req.Description,
		"",
		"admin",
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Credits added successfully",
	})
}

// UpdateUserStatus updates user status (admin only)
func (h *AdminHandler) UpdateUserStatus(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "User ID is required",
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=active suspended deleted"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	err := h.authService.UpdateUserStatus(userID, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User status updated successfully",
	})
}

// GetDashboardStats returns dashboard statistics (admin only)
func (h *AdminHandler) GetDashboardStats(c *gin.Context) {
	// Get user stats
	userStats, err := h.authService.GetUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get user stats: " + err.Error(),
		})
		return
	}

	// Get project stats
	projectStats, err := h.pptGeneratorService.GetProjectStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get project stats: " + err.Error(),
		})
		return
	}

	// Get credit stats
	creditStats, err := h.creditService.GetSystemCreditStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get credit stats: " + err.Error(),
		})
		return
	}

	// Get redeem code stats
	redeemStats, err := h.redeemCodeService.GetRedeemCodeStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get redeem code stats: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users":        userStats,
			"projects":     projectStats,
			"credits":      creditStats,
			"redeem_codes": redeemStats,
		},
	})
}

// GetSystemInfo returns system information (admin only)
func (h *AdminHandler) GetSystemInfo(c *gin.Context) {
	// TODO: Implement system information collection
	// This would include server stats, database status, etc.
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"version":        "1.0.0",
			"uptime":         "15 days 8 hours 32 minutes",
			"cpu_usage":      45,
			"memory_usage":   68,
			"disk_usage":     32,
			"database_status": "connected",
			"redis_status":   "connected",
			"ppt_generator_status": "connected",
		},
	})
}
