package handlers

import (
	"net/http"
	"ppt-narrator-business/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// CreditHandler handles credit-related endpoints
type CreditHandler struct {
	creditService     *services.CreditService
	redeemCodeService *services.RedeemCodeService
}

// NewCreditHandler creates a new credit handler
func NewCreditHandler(creditService *services.CreditService, redeemCodeService *services.RedeemCodeService) *CreditHandler {
	return &CreditHandler{
		creditService:     creditService,
		redeemCodeService: redeemCodeService,
	}
}

// GetCredits returns user's current credit balance
func (h *CreditHandler) GetCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	credits, err := h.creditService.GetUserCredits(userID.(string))
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"credits": credits,
		},
	})
}

// GetCreditHistory returns user's credit transaction history
func (h *CreditHandler) GetCreditHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	records, total, err := h.creditService.GetCreditHistory(userID.(string), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"records":   records,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetCreditStats returns user's credit statistics
func (h *CreditHandler) GetCreditStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	stats, err := h.creditService.GetCreditStats(userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// RedeemCode redeems a code for credits
func (h *CreditHandler) RedeemCode(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var req services.RedeemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	usage, err := h.redeemCodeService.RedeemCode(userID.(string), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Code redeemed successfully",
		"data":    usage,
	})
}

// GetRedeemHistory returns user's redeem code usage history
func (h *CreditHandler) GetRedeemHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	usages, total, err := h.redeemCodeService.GetUserRedeemHistory(userID.(string), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"usages":    usages,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// Admin endpoints

// AdminAddCredits adds credits to a user (admin only)
func (h *CreditHandler) AdminAddCredits(c *gin.Context) {
	var req struct {
		UserID      string `json:"user_id" binding:"required"`
		Amount      int64  `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	err := h.creditService.AddCredits(req.UserID, req.Amount, "bonus", req.Description, "", "admin_bonus")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Credits added successfully",
	})
}

// AdminTransferCredits transfers credits between users (admin only)
func (h *CreditHandler) AdminTransferCredits(c *gin.Context) {
	var req struct {
		FromUserID  string `json:"from_user_id" binding:"required"`
		ToUserID    string `json:"to_user_id" binding:"required"`
		Amount      int64  `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	err := h.creditService.TransferCredits(req.FromUserID, req.ToUserID, req.Amount, req.Description)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Credits transferred successfully",
	})
}

// AdminBulkAddCredits adds credits to multiple users (admin only)
func (h *CreditHandler) AdminBulkAddCredits(c *gin.Context) {
	var req struct {
		UserIDs     []string `json:"user_ids" binding:"required"`
		Amount      int64    `json:"amount" binding:"required,min=1"`
		Description string   `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	err := h.creditService.BulkAddCredits(req.UserIDs, req.Amount, req.Description)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Credits added to all users successfully",
	})
}
