package handlers

import (
	"net/http"
	"ppt-narrator-business/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RedeemCodeHandler handles redeem code endpoints (admin only)
type RedeemCodeHandler struct {
	redeemCodeService *services.RedeemCodeService
}

// NewRedeemCodeHandler creates a new redeem code handler
func NewRedeemCodeHandler(redeemCodeService *services.RedeemCodeService) *RedeemCodeHandler {
	return &RedeemCodeHandler{
		redeemCodeService: redeemCodeService,
	}
}

// CreateRedeemCodes generates new redeem codes (admin only)
func (h *RedeemCodeHandler) CreateRedeemCodes(c *gin.Context) {
	var req services.CreateRedeemCodeRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	// Get admin user ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	codes, err := h.redeemCodeService.GenerateRedeemCodes(&req, userID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Redeem codes created successfully",
		"data": gin.H{
			"codes": codes,
			"count": len(codes),
		},
	})
}

// GetRedeemCodes returns paginated list of redeem codes (admin only)
func (h *RedeemCodeHandler) GetRedeemCodes(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	codes, total, err := h.redeemCodeService.GetRedeemCodes(page, pageSize, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"codes":     codes,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
			"status":    status,
		},
	})
}

// UpdateRedeemCode updates a redeem code (admin only)
func (h *RedeemCodeHandler) UpdateRedeemCode(c *gin.Context) {
	codeID := c.Param("id")
	if codeID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Code ID is required",
		})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	err := h.redeemCodeService.UpdateRedeemCode(codeID, updates)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Redeem code updated successfully",
	})
}

// DeleteRedeemCode soft deletes a redeem code (admin only)
func (h *RedeemCodeHandler) DeleteRedeemCode(c *gin.Context) {
	codeID := c.Param("id")
	if codeID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Code ID is required",
		})
		return
	}

	err := h.redeemCodeService.DeleteRedeemCode(codeID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Redeem code deleted successfully",
	})
}

// GetRedeemCodeStats returns statistics about redeem codes (admin only)
func (h *RedeemCodeHandler) GetRedeemCodeStats(c *gin.Context) {
	// This would require additional methods in the service
	// For now, return basic stats
	
	activeCount, _, err := h.redeemCodeService.GetRedeemCodes(1, 1, "active")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get active codes count: " + err.Error(),
		})
		return
	}

	usedCount, _, err := h.redeemCodeService.GetRedeemCodes(1, 1, "used")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get used codes count: " + err.Error(),
		})
		return
	}

	expiredCount, _, err := h.redeemCodeService.GetRedeemCodes(1, 1, "expired")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get expired codes count: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"active_codes":  activeCount,
			"used_codes":    usedCount,
			"expired_codes": expiredCount,
		},
	})
}

// BatchCreateRedeemCodes creates multiple batches of redeem codes (admin only)
func (h *RedeemCodeHandler) BatchCreateRedeemCodes(c *gin.Context) {
	var req struct {
		Batches []services.CreateRedeemCodeRequest `json:"batches" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data: " + err.Error(),
		})
		return
	}

	// Get admin user ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	var allCodes []interface{}
	totalCount := 0

	for i, batch := range req.Batches {
		codes, err := h.redeemCodeService.GenerateRedeemCodes(&batch, userID.(string))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to create batch " + strconv.Itoa(i+1) + ": " + err.Error(),
			})
			return
		}

		allCodes = append(allCodes, gin.H{
			"batch":       i + 1,
			"codes":       codes,
			"count":       len(codes),
			"credits":     batch.Credits,
			"description": batch.Description,
		})
		totalCount += len(codes)
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "All redeem code batches created successfully",
		"data": gin.H{
			"batches":     allCodes,
			"total_count": totalCount,
		},
	})
}
