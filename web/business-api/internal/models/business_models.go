package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	Username  string    `json:"username" gorm:"uniqueIndex;not null"`
	Email     string    `json:"email" gorm:"uniqueIndex;not null"`
	Password  string    `json:"-" gorm:"not null"` // 不在JSON中返回密码
	Nickname  string    `json:"nickname"`
	Avatar    string    `json:"avatar"`
	Status    string    `json:"status" gorm:"default:'active'"` // active, suspended, deleted
	Role      string    `json:"role" gorm:"default:'user'"`     // user, admin
	Credits   int64     `json:"credits" gorm:"default:0"`       // 用户点数
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联关系
	Projects      []PPTProject      `json:"projects" gorm:"foreignKey:UserID"`
	Orders        []Order           `json:"orders" gorm:"foreignKey:UserID"`
	CreditRecords []CreditRecord    `json:"credit_records" gorm:"foreignKey:UserID"`
	RedeemCodes   []RedeemCodeUsage `json:"redeem_codes" gorm:"foreignKey:UserID"`
}

// RedeemCode represents a redeemable code for credits
type RedeemCode struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Code        string    `json:"code" gorm:"uniqueIndex;not null"`
	Credits     int64     `json:"credits" gorm:"not null"`
	Description string    `json:"description"`
	Status      string    `json:"status" gorm:"default:'active'"` // active, used, expired, disabled
	MaxUses     int       `json:"max_uses" gorm:"default:1"`      // 最大使用次数
	UsedCount   int       `json:"used_count" gorm:"default:0"`    // 已使用次数
	ExpiresAt   *time.Time `json:"expires_at"`                    // 过期时间
	CreatedBy   string    `json:"created_by"`                     // 创建者ID
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联关系
	Usages []RedeemCodeUsage `json:"usages" gorm:"foreignKey:RedeemCodeID"`
}

// RedeemCodeUsage represents the usage record of a redeem code
type RedeemCodeUsage struct {
	ID           string     `json:"id" gorm:"primaryKey"`
	RedeemCodeID string     `json:"redeem_code_id" gorm:"not null"`
	RedeemCode   RedeemCode `json:"redeem_code" gorm:"foreignKey:RedeemCodeID"`
	UserID       string     `json:"user_id" gorm:"not null"`
	User         User       `json:"user" gorm:"foreignKey:UserID"`
	Credits      int64      `json:"credits" gorm:"not null"`
	UsedAt       time.Time  `json:"used_at"`
}

// CreditRecord represents credit transaction history
type CreditRecord struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	UserID      string    `json:"user_id" gorm:"not null"`
	User        User      `json:"user" gorm:"foreignKey:UserID"`
	Type        string    `json:"type" gorm:"not null"`        // recharge, consume, refund, bonus
	Amount      int64     `json:"amount" gorm:"not null"`      // 正数为增加，负数为消费
	Balance     int64     `json:"balance" gorm:"not null"`     // 操作后余额
	Description string    `json:"description"`
	RelatedID   string    `json:"related_id"`                  // 关联的订单ID、项目ID等
	RelatedType string    `json:"related_type"`                // order, project, redeem_code
	CreatedAt   time.Time `json:"created_at"`
}

// Order represents a user order
type Order struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	UserID      string    `json:"user_id" gorm:"not null"`
	User        User      `json:"user" gorm:"foreignKey:UserID"`
	Type        string    `json:"type" gorm:"not null"`     // ppt_generation, credit_purchase
	Status      string    `json:"status" gorm:"not null"`   // pending, processing, completed, failed, cancelled
	Amount      int64     `json:"amount" gorm:"not null"`   // 订单金额（点数）
	Description string    `json:"description"`
	Metadata    string    `json:"metadata" gorm:"type:text"` // JSON格式的额外数据
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联关系
	Projects []PPTProject `json:"projects" gorm:"foreignKey:OrderID"`
}

// PPTProject 扩展原有的PPTProject模型
type PPTProject struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	UserID      string    `json:"user_id" gorm:"not null"`      // 新增用户ID
	User        User      `json:"user" gorm:"foreignKey:UserID"` // 新增用户关联
	OrderID     string    `json:"order_id"`                     // 关联订单ID
	Order       *Order    `json:"order" gorm:"foreignKey:OrderID"`
	Name        string    `json:"name" gorm:"not null"`
	Description string    `json:"description"`
	Status      string    `json:"status" gorm:"default:'created'"` // created, processing, completed, failed
	
	// 配置参数
	Config      ProjectConfig `json:"config" gorm:"embedded"` // 项目配置
	
	// 消费点数
	CreditsCost int64     `json:"credits_cost" gorm:"default:0"` // 消费的点数
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// File information
	OriginalFileName string `json:"original_file_name"`
	FilePath         string `json:"file_path"`
	FileSize         int64  `json:"file_size"`

	// Processing results
	SlideCount      int    `json:"slide_count"`
	ScreenshotsPath string `json:"screenshots_path"`
	NarrationScript string `json:"narration_script" gorm:"type:text"`
	VideoPath       string `json:"video_path"`
	AudioPath       string `json:"audio_path"`

	// Processing metadata
	ProcessingLog    string `json:"processing_log" gorm:"type:text"`
	ErrorMessage     string `json:"error_message" gorm:"type:text"`
	ProcessingTimeMs int64  `json:"processing_time_ms"`

	// Relations
	Slides   []PPTSlide `json:"slides" gorm:"foreignKey:ProjectID"`
	Memories []AIMemory `json:"memories" gorm:"foreignKey:ProjectID"`
}

// PPTSlide represents a single slide in the PPT
type PPTSlide struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	SlideNumber int    `json:"slide_number" gorm:"not null"`
	Title       string `json:"title"`
	Content     string `json:"content" gorm:"type:text"`

	// Screenshot information
	ScreenshotPath   string `json:"screenshot_path"`
	ScreenshotWidth  int    `json:"screenshot_width"`
	ScreenshotHeight int    `json:"screenshot_height"`

	// Narration for this slide
	NarrationText  string  `json:"narration_text" gorm:"type:text"`
	NarrationAudio string  `json:"narration_audio"`
	Duration       float64 `json:"duration"` // in seconds

	// Timestamps in final video
	StartTime float64 `json:"start_time"`
	EndTime   float64 `json:"end_time"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AIMemory represents AI memory for context and continuity
type AIMemory struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	MemoryKey   string `json:"memory_key" gorm:"not null"`           // e.g., "presentation_theme", "target_audience"
	MemoryValue string `json:"memory_value" gorm:"type:text"`        // JSON or plain text
	MemoryType  string `json:"memory_type" gorm:"default:'context'"` // context, preference, fact

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ProcessingJob represents a background processing job
type ProcessingJob struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	JobType  string `json:"job_type" gorm:"not null"`        // screenshot, narration, tts, video
	Status   string `json:"status" gorm:"default:'pending'"` // pending, running, completed, failed
	Progress int    `json:"progress" gorm:"default:0"`       // 0-100

	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	ErrorMsg    string     `json:"error_msg" gorm:"type:text"`

	// Job-specific data (JSON)
	JobData string `json:"job_data" gorm:"type:text"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserPreference represents user preferences for narration
type UserPreference struct {
	ID     string `json:"id" gorm:"primaryKey"`
	UserID string `json:"user_id"` // For future multi-user support

	// Narration preferences
	NarrationStyle string  `json:"narration_style" gorm:"default:'professional'"` // professional, casual, educational
	SpeechSpeed    float64 `json:"speech_speed" gorm:"default:1.0"`
	Voice          string  `json:"voice" gorm:"default:'alloy'"`

	// Content preferences
	IncludeTransitions bool   `json:"include_transitions" gorm:"default:true"`
	DetailLevel        string `json:"detail_level" gorm:"default:'medium'"` // brief, medium, detailed
	Language           string `json:"language" gorm:"default:'zh-CN'"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ProjectConfig represents project configuration
type ProjectConfig struct {
	UserRequirements string  `json:"user_requirements"`                    // 用户需求描述
	NarrationStyle   string  `json:"narration_style" gorm:"default:'professional'"` // professional, casual, educational
	Voice            string  `json:"voice" gorm:"default:'alloy'"`         // 音色选择
	SpeechSpeed      float64 `json:"speech_speed" gorm:"default:1.0"`      // 语速
	Language         string  `json:"language" gorm:"default:'zh-CN'"`      // 语言
	IncludeTransitions bool  `json:"include_transitions" gorm:"default:true"` // 是否包含过渡
	DetailLevel      string  `json:"detail_level" gorm:"default:'medium'"` // brief, medium, detailed
	CustomPrompt     string  `json:"custom_prompt"`                        // 自定义提示词
}

// SystemConfig represents system configuration
type SystemConfig struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Key         string    `json:"key" gorm:"uniqueIndex;not null"`
	Value       string    `json:"value" gorm:"type:text"`
	Description string    `json:"description"`
	Type        string    `json:"type" gorm:"default:'string'"` // string, number, boolean, json
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TaskQueue represents background task queue
type TaskQueue struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Type        string    `json:"type" gorm:"not null"`        // ppt_generation, cleanup, notification
	Status      string    `json:"status" gorm:"default:'pending'"` // pending, running, completed, failed
	Priority    int       `json:"priority" gorm:"default:0"`   // 优先级，数字越大优先级越高
	Payload     string    `json:"payload" gorm:"type:text"`    // JSON格式的任务数据
	Result      string    `json:"result" gorm:"type:text"`     // 任务结果
	ErrorMsg    string    `json:"error_msg" gorm:"type:text"`  // 错误信息
	Attempts    int       `json:"attempts" gorm:"default:0"`   // 尝试次数
	MaxAttempts int       `json:"max_attempts" gorm:"default:3"` // 最大尝试次数
	ScheduledAt time.Time `json:"scheduled_at"`                // 计划执行时间
	StartedAt   *time.Time `json:"started_at"`                 // 开始执行时间
	CompletedAt *time.Time `json:"completed_at"`               // 完成时间
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
