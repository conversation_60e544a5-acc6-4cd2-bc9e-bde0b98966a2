package services

import (
	"fmt"
	"ppt-narrator-business/internal/models"
	"strings"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DatabaseService handles database operations
type DatabaseService struct {
	db *gorm.DB
}

// NewDatabaseService creates a new database service
func NewDatabaseService(databaseURL string) (*DatabaseService, error) {
	var db *gorm.DB
	var err error

	// Check if it's a PostgreSQL URL or SQLite path
	if strings.HasPrefix(databaseURL, "postgres://") || strings.HasPrefix(databaseURL, "postgresql://") {
		// PostgreSQL connection
		db, err = gorm.Open(postgres.Open(databaseURL), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})
	} else {
		// SQLite connection (backward compatibility)
		db, err = gorm.Open(sqlite.Open(databaseURL), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		// Business models
		&models.User{},
		&models.RedeemCode{},
		&models.RedeemCodeUsage{},
		&models.CreditRecord{},
		&models.Order{},
		&models.PPTProject{},
		&models.SystemConfig{},
		&models.TaskQueue{},
		
		// Original models (if needed)
		&models.PPTSlide{},
		&models.AIMemory{},
		&models.ProcessingJob{},
		&models.UserPreference{},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return &DatabaseService{db: db}, nil
}

// GetDB returns the database instance
func (s *DatabaseService) GetDB() *gorm.DB {
	return s.db
}

// Close closes the database connection
func (s *DatabaseService) Close() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Health checks database health
func (s *DatabaseService) Health() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// Transaction executes a function within a database transaction
func (s *DatabaseService) Transaction(fn func(*gorm.DB) error) error {
	return s.db.Transaction(fn)
}

// CreateDefaultAdmin creates a default admin user if none exists
func (s *DatabaseService) CreateDefaultAdmin() error {
	var count int64
	if err := s.db.Model(&models.User{}).Where("role = ?", "admin").Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		// Create default admin user
		admin := &models.User{
			ID:       "admin-default",
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Nickname: "Administrator",
			Role:     "admin",
			Status:   "active",
			Credits:  1000000, // Give admin lots of credits
		}

		if err := s.db.Create(admin).Error; err != nil {
			return fmt.Errorf("failed to create default admin: %w", err)
		}
	}

	return nil
}

// CreateDefaultConfigs creates default system configurations
func (s *DatabaseService) CreateDefaultConfigs() error {
	defaultConfigs := []models.SystemConfig{
		{
			ID:          "default-credits",
			Key:         "default_credits",
			Value:       "100",
			Description: "Default credits for new users",
			Type:        "number",
		},
		{
			ID:          "ppt-generation-cost",
			Key:         "ppt_generation_cost",
			Value:       "10",
			Description: "Credits cost for PPT generation",
			Type:        "number",
		},
		{
			ID:          "max-file-size",
			Key:         "max_file_size",
			Value:       "104857600", // 100MB
			Description: "Maximum file size for uploads (bytes)",
			Type:        "number",
		},
		{
			ID:          "allowed-file-types",
			Key:         "allowed_file_types",
			Value:       `[".pptx", ".ppt"]`,
			Description: "Allowed file types for upload",
			Type:        "json",
		},
		{
			ID:          "site-title",
			Key:         "site_title",
			Value:       "PPT Narrator",
			Description: "Site title",
			Type:        "string",
		},
		{
			ID:          "site-description",
			Key:         "site_description",
			Value:       "AI-powered PPT narration service",
			Description: "Site description",
			Type:        "string",
		},
	}

	for _, config := range defaultConfigs {
		var existing models.SystemConfig
		if err := s.db.Where("key = ?", config.Key).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// Create new config
				if err := s.db.Create(&config).Error; err != nil {
					return fmt.Errorf("failed to create config %s: %w", config.Key, err)
				}
			} else {
				return fmt.Errorf("failed to check config %s: %w", config.Key, err)
			}
		}
	}

	return nil
}
