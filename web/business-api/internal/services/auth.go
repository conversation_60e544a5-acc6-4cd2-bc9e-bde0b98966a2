package services

import (
	"errors"
	"fmt"
	"ppt-narrator-business/internal/models"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService handles authentication operations
type AuthService struct {
	db        *gorm.DB
	jwtSecret []byte
	jwtExpiry time.Duration
}

// NewAuthService creates a new auth service
func NewAuthService(db *gorm.DB, jwtSecret string, jwtExpiry time.Duration) *AuthService {
	return &AuthService{
		db:        db,
		jwtSecret: []byte(jwtSecret),
		jwtExpiry: jwtExpiry,
	}
}

// Claims represents JWT claims
type Claims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// RegisterRequest represents user registration request
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Nickname string `json:"nickname"`
}

// LoginRequest represents user login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// AuthResponse represents authentication response
type AuthResponse struct {
	Token     string      `json:"token"`
	User      *models.User `json:"user"`
	ExpiresAt time.Time   `json:"expires_at"`
}

// Register creates a new user account
func (s *AuthService) Register(req *RegisterRequest, defaultCredits int64) (*AuthResponse, error) {
	// Check if username already exists
	var existingUser models.User
	if err := s.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("username or email already exists")
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &models.User{
		ID:       uuid.New().String(),
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Role:     "user",
		Status:   "active",
		Credits:  defaultCredits,
	}

	if user.Nickname == "" {
		user.Nickname = user.Username
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create user
	if err := tx.Create(user).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create initial credit record
	if defaultCredits > 0 {
		creditRecord := &models.CreditRecord{
			ID:          uuid.New().String(),
			UserID:      user.ID,
			Type:        "bonus",
			Amount:      defaultCredits,
			Balance:     defaultCredits,
			Description: "Registration bonus",
			RelatedType: "registration",
		}

		if err := tx.Create(creditRecord).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to create credit record: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Generate token
	token, expiresAt, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Remove password from response
	user.Password = ""

	return &AuthResponse{
		Token:     token,
		User:      user,
		ExpiresAt: expiresAt,
	}, nil
}

// Login authenticates a user and returns a token
func (s *AuthService) Login(req *LoginRequest) (*AuthResponse, error) {
	// Find user
	var user models.User
	if err := s.db.Where("username = ? OR email = ?", req.Username, req.Username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("invalid username or password")
		}
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	// Check if user is active
	if user.Status != "active" {
		return nil, errors.New("account is suspended or inactive")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid username or password")
	}

	// Generate token
	token, expiresAt, err := s.GenerateToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Remove password from response
	user.Password = ""

	return &AuthResponse{
		Token:     token,
		User:      &user,
		ExpiresAt: expiresAt,
	}, nil
}

// GenerateToken generates a JWT token for a user
func (s *AuthService) GenerateToken(user *models.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(s.jwtExpiry)

	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "ppt-narrator-business",
			Subject:   user.ID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(s.jwtSecret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateToken validates a JWT token and returns claims
func (s *AuthService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GetUserByID retrieves a user by ID
func (s *AuthService) GetUserByID(userID string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ? AND status = ?", userID, "active").First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Remove password from response
	user.Password = ""
	return &user, nil
}

// GetUsers returns paginated users (admin only)
func (s *AuthService) GetUsers(page, pageSize int, status string) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{})

	// Apply status filter
	if status != "" && status != "all" {
		query = query.Where("status = ?", status)
	}

	// Count total users
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Get paginated users
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// GetUserStats returns user statistics
func (s *AuthService) GetUserStats() (map[string]interface{}, error) {
	var stats struct {
		Total     int64 `json:"total"`
		Active    int64 `json:"active"`
		Suspended int64 `json:"suspended"`
		Deleted   int64 `json:"deleted"`
	}

	// Get total users
	if err := s.db.Model(&models.User{}).Count(&stats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to get total users: %w", err)
	}

	// Get active users
	if err := s.db.Model(&models.User{}).Where("status = ?", "active").Count(&stats.Active).Error; err != nil {
		return nil, fmt.Errorf("failed to get active users: %w", err)
	}

	// Get suspended users
	if err := s.db.Model(&models.User{}).Where("status = ?", "suspended").Count(&stats.Suspended).Error; err != nil {
		return nil, fmt.Errorf("failed to get suspended users: %w", err)
	}

	// Get deleted users
	if err := s.db.Model(&models.User{}).Where("status = ?", "deleted").Count(&stats.Deleted).Error; err != nil {
		return nil, fmt.Errorf("failed to get deleted users: %w", err)
	}

	return map[string]interface{}{
		"total":     stats.Total,
		"active":    stats.Active,
		"suspended": stats.Suspended,
		"deleted":   stats.Deleted,
	}, nil
}

// UpdateUserStatus updates user status
func (s *AuthService) UpdateUserStatus(userID, status string) error {
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Update("status", status)
	if result.Error != nil {
		return fmt.Errorf("failed to update user status: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	return nil
}

// UpdateUserProfile updates user profile information
func (s *AuthService) UpdateUserProfile(userID string, updates map[string]interface{}) (*models.User, error) {
	// Remove sensitive fields that shouldn't be updated via this method
	delete(updates, "password")
	delete(updates, "role")
	delete(updates, "status")
	delete(updates, "credits")

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update user profile: %w", err)
	}

	return s.GetUserByID(userID)
}

// ChangePassword changes user password
func (s *AuthService) ChangePassword(userID, oldPassword, newPassword string) error {
	// Get user with password
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Verify old password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return errors.New("invalid old password")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}
