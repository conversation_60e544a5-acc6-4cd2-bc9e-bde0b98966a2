package services

import (
	"errors"
	"fmt"
	"ppt-narrator-business/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CreditService handles credit operations
type CreditService struct {
	db *gorm.DB
}

// NewCreditService creates a new credit service
func NewCreditService(db *gorm.DB) *CreditService {
	return &CreditService{db: db}
}

// GetUserCredits returns user's current credit balance
func (s *CreditService) GetUserCredits(userID string) (int64, error) {
	var user models.User
	if err := s.db.Select("credits").Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, errors.New("user not found")
		}
		return 0, fmt.Errorf("failed to get user credits: %w", err)
	}
	return user.Credits, nil
}

// AddCredits adds credits to user account
func (s *CreditService) AddCredits(userID string, amount int64, creditType, description, relatedID, relatedType string) error {
	if amount <= 0 {
		return errors.New("amount must be positive")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get current user credits with lock
		var user models.User
		if err := tx.Set("gorm:query_option", "FOR UPDATE").Where("id = ?", userID).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return errors.New("user not found")
			}
			return fmt.Errorf("failed to get user: %w", err)
		}

		// Update user credits
		newBalance := user.Credits + amount
		if err := tx.Model(&user).Update("credits", newBalance).Error; err != nil {
			return fmt.Errorf("failed to update user credits: %w", err)
		}

		// Create credit record
		record := &models.CreditRecord{
			ID:          uuid.New().String(),
			UserID:      userID,
			Type:        creditType,
			Amount:      amount,
			Balance:     newBalance,
			Description: description,
			RelatedID:   relatedID,
			RelatedType: relatedType,
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("failed to create credit record: %w", err)
		}

		return nil
	})
}

// ConsumeCredits consumes credits from user account
func (s *CreditService) ConsumeCredits(userID string, amount int64, description, relatedID, relatedType string) error {
	if amount <= 0 {
		return errors.New("amount must be positive")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get current user credits with lock
		var user models.User
		if err := tx.Set("gorm:query_option", "FOR UPDATE").Where("id = ?", userID).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return errors.New("user not found")
			}
			return fmt.Errorf("failed to get user: %w", err)
		}

		// Check if user has enough credits
		if user.Credits < amount {
			return errors.New("insufficient credits")
		}

		// Update user credits
		newBalance := user.Credits - amount
		if err := tx.Model(&user).Update("credits", newBalance).Error; err != nil {
			return fmt.Errorf("failed to update user credits: %w", err)
		}

		// Create credit record (negative amount for consumption)
		record := &models.CreditRecord{
			ID:          uuid.New().String(),
			UserID:      userID,
			Type:        "consume",
			Amount:      -amount, // Negative for consumption
			Balance:     newBalance,
			Description: description,
			RelatedID:   relatedID,
			RelatedType: relatedType,
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("failed to create credit record: %w", err)
		}

		return nil
	})
}

// RefundCredits refunds credits to user account
func (s *CreditService) RefundCredits(userID string, amount int64, description, relatedID, relatedType string) error {
	if amount <= 0 {
		return errors.New("amount must be positive")
	}

	return s.AddCredits(userID, amount, "refund", description, relatedID, relatedType)
}

// GetCreditHistory returns user's credit transaction history
func (s *CreditService) GetCreditHistory(userID string, page, pageSize int) ([]models.CreditRecord, int64, error) {
	var records []models.CreditRecord
	var total int64

	// Count total records
	if err := s.db.Model(&models.CreditRecord{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count credit records: %w", err)
	}

	// Get paginated records
	offset := (page - 1) * pageSize
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get credit records: %w", err)
	}

	return records, total, nil
}

// CheckSufficientCredits checks if user has enough credits
func (s *CreditService) CheckSufficientCredits(userID string, amount int64) (bool, int64, error) {
	credits, err := s.GetUserCredits(userID)
	if err != nil {
		return false, 0, err
	}
	return credits >= amount, credits, nil
}

// GetCreditStats returns credit statistics for a user
func (s *CreditService) GetCreditStats(userID string) (map[string]interface{}, error) {
	var stats struct {
		TotalEarned   int64 `json:"total_earned"`
		TotalConsumed int64 `json:"total_consumed"`
		TotalRefunded int64 `json:"total_refunded"`
	}

	// Get total earned (positive amounts)
	if err := s.db.Model(&models.CreditRecord{}).
		Where("user_id = ? AND amount > 0", userID).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalEarned).Error; err != nil {
		return nil, fmt.Errorf("failed to get total earned: %w", err)
	}

	// Get total consumed (negative amounts, but we want positive value)
	if err := s.db.Model(&models.CreditRecord{}).
		Where("user_id = ? AND amount < 0", userID).
		Select("COALESCE(ABS(SUM(amount)), 0)").
		Scan(&stats.TotalConsumed).Error; err != nil {
		return nil, fmt.Errorf("failed to get total consumed: %w", err)
	}

	// Get total refunded
	if err := s.db.Model(&models.CreditRecord{}).
		Where("user_id = ? AND type = ?", userID, "refund").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalRefunded).Error; err != nil {
		return nil, fmt.Errorf("failed to get total refunded: %w", err)
	}

	// Get current balance
	currentBalance, err := s.GetUserCredits(userID)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"current_balance":  currentBalance,
		"total_earned":     stats.TotalEarned,
		"total_consumed":   stats.TotalConsumed,
		"total_refunded":   stats.TotalRefunded,
	}, nil
}

// TransferCredits transfers credits between users (admin only)
func (s *CreditService) TransferCredits(fromUserID, toUserID string, amount int64, description string) error {
	if amount <= 0 {
		return errors.New("amount must be positive")
	}

	if fromUserID == toUserID {
		return errors.New("cannot transfer credits to the same user")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Consume credits from source user
		if err := s.ConsumeCredits(fromUserID, amount, fmt.Sprintf("Transfer to user: %s", description), toUserID, "transfer"); err != nil {
			return fmt.Errorf("failed to consume credits from source user: %w", err)
		}

		// Add credits to destination user
		if err := s.AddCredits(toUserID, amount, "transfer", fmt.Sprintf("Transfer from user: %s", description), fromUserID, "transfer"); err != nil {
			return fmt.Errorf("failed to add credits to destination user: %w", err)
		}

		return nil
	})
}

// BulkAddCredits adds credits to multiple users (admin only)
func (s *CreditService) BulkAddCredits(userIDs []string, amount int64, description string) error {
	if amount <= 0 {
		return errors.New("amount must be positive")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		for _, userID := range userIDs {
			if err := s.AddCredits(userID, amount, "bonus", description, "", "bulk_bonus"); err != nil {
				return fmt.Errorf("failed to add credits to user %s: %w", userID, err)
			}
		}
		return nil
	})
}

// GetSystemCreditStats returns system-wide credit statistics
func (s *CreditService) GetSystemCreditStats() (map[string]interface{}, error) {
	var stats struct {
		TotalIssued   int64 `json:"total_issued"`
		TotalConsumed int64 `json:"total_consumed"`
		TotalBalance  int64 `json:"total_balance"`
	}

	// Get total credits issued (all positive transactions)
	if err := s.db.Model(&models.CreditRecord{}).
		Where("amount > 0").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalIssued).Error; err != nil {
		return nil, fmt.Errorf("failed to get total issued credits: %w", err)
	}

	// Get total credits consumed (all negative transactions)
	var totalConsumedNegative int64
	if err := s.db.Model(&models.CreditRecord{}).
		Where("amount < 0").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalConsumedNegative).Error; err != nil {
		return nil, fmt.Errorf("failed to get total consumed credits: %w", err)
	}
	stats.TotalConsumed = -totalConsumedNegative // Convert to positive

	// Get total current balance (sum of all user credits)
	if err := s.db.Model(&models.User{}).
		Select("COALESCE(SUM(credits), 0)").
		Scan(&stats.TotalBalance).Error; err != nil {
		return nil, fmt.Errorf("failed to get total balance: %w", err)
	}

	return map[string]interface{}{
		"total_issued":   stats.TotalIssued,
		"total_consumed": stats.TotalConsumed,
		"total_balance":  stats.TotalBalance,
	}, nil
}
