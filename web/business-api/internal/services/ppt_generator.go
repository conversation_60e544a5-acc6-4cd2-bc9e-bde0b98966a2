package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"ppt-narrator-business/internal/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PPTGeneratorService handles integration with the PPT generator
type PPTGeneratorService struct {
	db          *gorm.DB
	generatorURL string
	timeout     time.Duration
	uploadDir   string
	videoDir    string
}

// NewPPTGeneratorService creates a new PPT generator service
func NewPPTGeneratorService(db *gorm.DB, generatorURL string, timeout time.Duration, uploadDir, videoDir string) *PPTGeneratorService {
	return &PPTGeneratorService{
		db:          db,
		generatorURL: generatorURL,
		timeout:     timeout,
		uploadDir:   uploadDir,
		videoDir:    videoDir,
	}
}

// CreateProjectRequest represents a request to create a new project
type CreateProjectRequest struct {
	UserID           string                `json:"user_id"`
	ProjectName      string                `json:"project_name"`
	UserRequirements string                `json:"user_requirements"`
	Config           models.ProjectConfig  `json:"config"`
	File             *multipart.FileHeader `json:"-"`
}

// CreateProject creates a new PPT project and starts processing
func (s *PPTGeneratorService) CreateProject(req *CreateProjectRequest) (*models.PPTProject, error) {
	// Create project record
	project := &models.PPTProject{
		ID:               uuid.New().String(),
		UserID:           req.UserID,
		Name:             req.ProjectName,
		Status:           "created",
		Config:           req.Config,
		OriginalFileName: req.File.Filename,
		FileSize:         req.File.Size,
	}

	// Save uploaded file
	uploadPath := filepath.Join(s.uploadDir, project.ID+"_"+req.File.Filename)
	if err := s.saveUploadedFile(req.File, uploadPath); err != nil {
		return nil, fmt.Errorf("failed to save uploaded file: %w", err)
	}
	project.FilePath = uploadPath

	// Create project in database
	if err := s.db.Create(project).Error; err != nil {
		// Clean up uploaded file if database creation fails
		os.Remove(uploadPath)
		return nil, fmt.Errorf("failed to create project: %w", err)
	}

	// Start processing asynchronously
	go s.processProject(project)

	return project, nil
}

// processProject processes the PPT project using the generator service
func (s *PPTGeneratorService) processProject(project *models.PPTProject) {
	// Update status to processing
	s.updateProjectStatus(project.ID, "processing", "screenshot", 0)

	// Call the PPT generator API
	if err := s.callPPTGenerator(project); err != nil {
		s.updateProjectStatus(project.ID, "failed", "", 0)
		s.updateProjectError(project.ID, err.Error())
		return
	}

	// Update status to completed
	s.updateProjectStatus(project.ID, "completed", "completed", 100)
}

// callPPTGenerator calls the original PPT generator service
func (s *PPTGeneratorService) callPPTGenerator(project *models.PPTProject) error {
	// Prepare request to original PPT generator
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add file
	file, err := os.Open(project.FilePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	part, err := writer.CreateFormFile("file", project.OriginalFileName)
	if err != nil {
		return fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := io.Copy(part, file); err != nil {
		return fmt.Errorf("failed to copy file: %w", err)
	}

	// Add other form fields
	writer.WriteField("user_requirements", project.Config.UserRequirements)
	writer.WriteField("narration_style", project.Config.NarrationStyle)
	writer.WriteField("voice", project.Config.Voice)
	writer.WriteField("speech_speed", fmt.Sprintf("%.1f", project.Config.SpeechSpeed))
	writer.WriteField("language", project.Config.Language)
	writer.WriteField("include_transitions", fmt.Sprintf("%t", project.Config.IncludeTransitions))
	writer.WriteField("detail_level", project.Config.DetailLevel)
	writer.WriteField("custom_prompt", project.Config.CustomPrompt)

	writer.Close()

	// Create HTTP request
	req, err := http.NewRequest("POST", s.generatorURL+"/generate", &buf)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Make request with timeout
	client := &http.Client{Timeout: s.timeout}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to call PPT generator: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("PPT generator returned error: %s", string(body))
	}

	// Parse response
	var result struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Data    struct {
			ProjectID       string `json:"project_id"`
			SlideCount      int    `json:"slide_count"`
			ScreenshotsPath string `json:"screenshots_path"`
			NarrationScript string `json:"narration_script"`
			VideoPath       string `json:"video_path"`
			AudioPath       string `json:"audio_path"`
			ProcessingTime  int64  `json:"processing_time_ms"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("PPT generator failed: %s", result.Message)
	}

	// Update project with results
	updates := map[string]interface{}{
		"slide_count":        result.Data.SlideCount,
		"screenshots_path":   result.Data.ScreenshotsPath,
		"narration_script":   result.Data.NarrationScript,
		"video_path":         result.Data.VideoPath,
		"audio_path":         result.Data.AudioPath,
		"processing_time_ms": result.Data.ProcessingTime,
	}

	if err := s.db.Model(&models.PPTProject{}).Where("id = ?", project.ID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update project results: %w", err)
	}

	return nil
}

// saveUploadedFile saves the uploaded file to disk
func (s *PPTGeneratorService) saveUploadedFile(fileHeader *multipart.FileHeader, dst string) error {
	src, err := fileHeader.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	// Create directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// updateProjectStatus updates the project status and progress
func (s *PPTGeneratorService) updateProjectStatus(projectID, status, currentStage string, progress int) {
	updates := map[string]interface{}{
		"status":   status,
		"progress": progress,
	}

	if currentStage != "" {
		updates["current_stage"] = currentStage
	}

	s.db.Model(&models.PPTProject{}).Where("id = ?", projectID).Updates(updates)
}

// updateProjectError updates the project error message
func (s *PPTGeneratorService) updateProjectError(projectID, errorMsg string) {
	s.db.Model(&models.PPTProject{}).Where("id = ?", projectID).Update("error_message", errorMsg)
}

// GetProject retrieves a project by ID
func (s *PPTGeneratorService) GetProject(projectID string) (*models.PPTProject, error) {
	var project models.PPTProject
	if err := s.db.Where("id = ?", projectID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("project not found")
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}
	return &project, nil
}

// GetUserProjects retrieves projects for a specific user
func (s *PPTGeneratorService) GetUserProjects(userID string, page, pageSize int) ([]models.PPTProject, int64, error) {
	var projects []models.PPTProject
	var total int64

	// Count total projects
	if err := s.db.Model(&models.PPTProject{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count projects: %w", err)
	}

	// Get paginated projects
	offset := (page - 1) * pageSize
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&projects).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get projects: %w", err)
	}

	return projects, total, nil
}

// DeleteProject deletes a project and its associated files
func (s *PPTGeneratorService) DeleteProject(projectID, userID string) error {
	// Get project first to check ownership and get file paths
	var project models.PPTProject
	if err := s.db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("project not found or access denied")
		}
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Delete project from database
	if err := s.db.Delete(&project).Error; err != nil {
		return fmt.Errorf("failed to delete project: %w", err)
	}

	// Clean up files asynchronously
	go s.cleanupProjectFiles(&project)

	return nil
}

// cleanupProjectFiles removes project files from disk
func (s *PPTGeneratorService) cleanupProjectFiles(project *models.PPTProject) {
	// Remove uploaded file
	if project.FilePath != "" {
		os.Remove(project.FilePath)
	}

	// Remove generated files
	if project.VideoPath != "" {
		os.Remove(project.VideoPath)
	}
	if project.AudioPath != "" {
		os.Remove(project.AudioPath)
	}
	if project.ScreenshotsPath != "" {
		os.RemoveAll(project.ScreenshotsPath)
	}
}

// GetProjectStats returns project statistics
func (s *PPTGeneratorService) GetProjectStats() (map[string]interface{}, error) {
	var stats struct {
		Total      int64 `json:"total"`
		Processing int64 `json:"processing"`
		Completed  int64 `json:"completed"`
		Failed     int64 `json:"failed"`
	}

	// Get total projects
	if err := s.db.Model(&models.PPTProject{}).Count(&stats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to get total projects: %w", err)
	}

	// Get processing projects
	if err := s.db.Model(&models.PPTProject{}).Where("status = ?", "processing").Count(&stats.Processing).Error; err != nil {
		return nil, fmt.Errorf("failed to get processing projects: %w", err)
	}

	// Get completed projects
	if err := s.db.Model(&models.PPTProject{}).Where("status = ?", "completed").Count(&stats.Completed).Error; err != nil {
		return nil, fmt.Errorf("failed to get completed projects: %w", err)
	}

	// Get failed projects
	if err := s.db.Model(&models.PPTProject{}).Where("status = ?", "failed").Count(&stats.Failed).Error; err != nil {
		return nil, fmt.Errorf("failed to get failed projects: %w", err)
	}

	return map[string]interface{}{
		"total":      stats.Total,
		"processing": stats.Processing,
		"completed":  stats.Completed,
		"failed":     stats.Failed,
	}, nil
}

// GetAllProjects retrieves all projects with pagination and filters (admin only)
func (s *PPTGeneratorService) GetAllProjects(page, pageSize int, status string) ([]models.PPTProject, int64, error) {
	var projects []models.PPTProject
	var total int64

	query := s.db.Model(&models.PPTProject{})

	// Apply status filter
	if status != "" && status != "all" {
		query = query.Where("status = ?", status)
	}

	// Count total projects
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count projects: %w", err)
	}

	// Get paginated projects with user information
	offset := (page - 1) * pageSize
	if err := query.Preload("User").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&projects).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get projects: %w", err)
	}

	return projects, total, nil
}
