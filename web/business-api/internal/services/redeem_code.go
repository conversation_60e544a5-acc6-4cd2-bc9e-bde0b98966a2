package services

import (
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"ppt-narrator-business/internal/models"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RedeemCodeService handles redeem code operations
type RedeemCodeService struct {
	db            *gorm.DB
	creditService *CreditService
}

// NewRedeemCodeService creates a new redeem code service
func NewRedeemCodeService(db *gorm.DB, creditService *CreditService) *RedeemCodeService {
	return &RedeemCodeService{
		db:            db,
		creditService: creditService,
	}
}

// CreateRedeemCodeRequest represents a request to create redeem codes
type CreateRedeemCodeRequest struct {
	Credits     int64      `json:"credits" binding:"required,min=1"`
	Description string     `json:"description"`
	MaxUses     int        `json:"max_uses" binding:"min=1"`
	ExpiresAt   *time.Time `json:"expires_at"`
	Count       int        `json:"count" binding:"min=1,max=1000"` // Number of codes to generate
	Prefix      string     `json:"prefix"`                         // Optional prefix for codes
}

// RedeemRequest represents a request to redeem a code
type RedeemRequest struct {
	Code string `json:"code" binding:"required"`
}

// GenerateRedeemCodes generates multiple redeem codes
func (s *RedeemCodeService) GenerateRedeemCodes(req *CreateRedeemCodeRequest, createdBy string) ([]models.RedeemCode, error) {
	if req.Count <= 0 || req.Count > 1000 {
		return nil, errors.New("count must be between 1 and 1000")
	}

	codes := make([]models.RedeemCode, 0, req.Count)

	return codes, s.db.Transaction(func(tx *gorm.DB) error {
		for i := 0; i < req.Count; i++ {
			// Generate unique code
			code, err := s.generateUniqueCode(tx, req.Prefix)
			if err != nil {
				return fmt.Errorf("failed to generate unique code: %w", err)
			}

			redeemCode := models.RedeemCode{
				ID:          uuid.New().String(),
				Code:        code,
				Credits:     req.Credits,
				Description: req.Description,
				Status:      "active",
				MaxUses:     req.MaxUses,
				UsedCount:   0,
				ExpiresAt:   req.ExpiresAt,
				CreatedBy:   createdBy,
			}

			if err := tx.Create(&redeemCode).Error; err != nil {
				return fmt.Errorf("failed to create redeem code: %w", err)
			}

			codes = append(codes, redeemCode)
		}

		return nil
	})
}

// RedeemCode redeems a code for a user
func (s *RedeemCodeService) RedeemCode(userID string, req *RedeemRequest) (*models.RedeemCodeUsage, error) {
	var usage *models.RedeemCodeUsage

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Find and lock the redeem code
		var redeemCode models.RedeemCode
		if err := tx.Set("gorm:query_option", "FOR UPDATE").Where("code = ?", strings.ToUpper(req.Code)).First(&redeemCode).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return errors.New("invalid redeem code")
			}
			return fmt.Errorf("failed to find redeem code: %w", err)
		}

		// Check if code is active
		if redeemCode.Status != "active" {
			return errors.New("redeem code is not active")
		}

		// Check if code has expired
		if redeemCode.ExpiresAt != nil && time.Now().After(*redeemCode.ExpiresAt) {
			// Mark as expired
			tx.Model(&redeemCode).Update("status", "expired")
			return errors.New("redeem code has expired")
		}

		// Check if code has reached max uses
		if redeemCode.UsedCount >= redeemCode.MaxUses {
			// Mark as used
			tx.Model(&redeemCode).Update("status", "used")
			return errors.New("redeem code has reached maximum uses")
		}

		// Check if user has already used this code (if max_uses is 1)
		if redeemCode.MaxUses == 1 {
			var existingUsage models.RedeemCodeUsage
			if err := tx.Where("redeem_code_id = ? AND user_id = ?", redeemCode.ID, userID).First(&existingUsage).Error; err == nil {
				return errors.New("you have already used this redeem code")
			} else if err != gorm.ErrRecordNotFound {
				return fmt.Errorf("failed to check existing usage: %w", err)
			}
		}

		// Create usage record
		usage = &models.RedeemCodeUsage{
			ID:           uuid.New().String(),
			RedeemCodeID: redeemCode.ID,
			UserID:       userID,
			Credits:      redeemCode.Credits,
			UsedAt:       time.Now(),
		}

		if err := tx.Create(usage).Error; err != nil {
			return fmt.Errorf("failed to create usage record: %w", err)
		}

		// Update redeem code usage count
		newUsedCount := redeemCode.UsedCount + 1
		updates := map[string]interface{}{
			"used_count": newUsedCount,
		}

		// Mark as used if reached max uses
		if newUsedCount >= redeemCode.MaxUses {
			updates["status"] = "used"
		}

		if err := tx.Model(&redeemCode).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update redeem code: %w", err)
		}

		// Add credits to user account
		if err := s.creditService.AddCredits(
			userID,
			redeemCode.Credits,
			"recharge",
			fmt.Sprintf("Redeemed code: %s", redeemCode.Code),
			redeemCode.ID,
			"redeem_code",
		); err != nil {
			return fmt.Errorf("failed to add credits: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Load the redeem code for the response
	if err := s.db.Preload("RedeemCode").First(usage, "id = ?", usage.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to load usage with redeem code: %w", err)
	}

	return usage, nil
}

// GetRedeemCodes returns paginated list of redeem codes (admin only)
func (s *RedeemCodeService) GetRedeemCodes(page, pageSize int, status string) ([]models.RedeemCode, int64, error) {
	var codes []models.RedeemCode
	var total int64

	query := s.db.Model(&models.RedeemCode{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count redeem codes: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := query.Preload("Usages").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&codes).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get redeem codes: %w", err)
	}

	return codes, total, nil
}

// GetRedeemCodeStats returns redeem code statistics
func (s *RedeemCodeService) GetRedeemCodeStats() (map[string]interface{}, error) {
	var stats struct {
		Total   int64 `json:"total"`
		Active  int64 `json:"active"`
		Used    int64 `json:"used"`
		Expired int64 `json:"expired"`
	}

	// Get total codes
	if err := s.db.Model(&models.RedeemCode{}).Count(&stats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to get total codes: %w", err)
	}

	// Get active codes
	if err := s.db.Model(&models.RedeemCode{}).Where("status = ?", "active").Count(&stats.Active).Error; err != nil {
		return nil, fmt.Errorf("failed to get active codes: %w", err)
	}

	// Get used codes
	if err := s.db.Model(&models.RedeemCode{}).Where("status = ?", "used").Count(&stats.Used).Error; err != nil {
		return nil, fmt.Errorf("failed to get used codes: %w", err)
	}

	// Get expired codes
	if err := s.db.Model(&models.RedeemCode{}).Where("status = ?", "expired").Count(&stats.Expired).Error; err != nil {
		return nil, fmt.Errorf("failed to get expired codes: %w", err)
	}

	return map[string]interface{}{
		"total":   stats.Total,
		"active":  stats.Active,
		"used":    stats.Used,
		"expired": stats.Expired,
	}, nil
}

// GetUserRedeemHistory returns user's redeem history
func (s *RedeemCodeService) GetUserRedeemHistory(userID string, page, pageSize int) ([]models.RedeemCodeUsage, int64, error) {
	var usages []models.RedeemCodeUsage
	var total int64

	// Count total
	if err := s.db.Model(&models.RedeemCodeUsage{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count redeem history: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * pageSize
	if err := s.db.Where("user_id = ?", userID).
		Preload("RedeemCode").
		Order("used_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&usages).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get redeem history: %w", err)
	}

	return usages, total, nil
}

// UpdateRedeemCode updates a redeem code (admin only)
func (s *RedeemCodeService) UpdateRedeemCode(codeID string, updates map[string]interface{}) error {
	// Don't allow updating the code itself or usage count
	delete(updates, "code")
	delete(updates, "used_count")
	delete(updates, "id")

	if err := s.db.Model(&models.RedeemCode{}).Where("id = ?", codeID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update redeem code: %w", err)
	}

	return nil
}

// DeleteRedeemCode soft deletes a redeem code (admin only)
func (s *RedeemCodeService) DeleteRedeemCode(codeID string) error {
	if err := s.db.Model(&models.RedeemCode{}).Where("id = ?", codeID).Update("status", "disabled").Error; err != nil {
		return fmt.Errorf("failed to delete redeem code: %w", err)
	}

	return nil
}

// generateUniqueCode generates a unique redeem code
func (s *RedeemCodeService) generateUniqueCode(tx *gorm.DB, prefix string) (string, error) {
	const maxAttempts = 10
	const codeLength = 12

	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Generate random code
		code := s.generateRandomCode(codeLength)
		if prefix != "" {
			code = strings.ToUpper(prefix) + "-" + code
		}

		// Check if code already exists
		var existing models.RedeemCode
		if err := tx.Where("code = ?", code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return code, nil
			}
			return "", fmt.Errorf("failed to check existing code: %w", err)
		}
	}

	return "", errors.New("failed to generate unique code after maximum attempts")
}

// generateRandomCode generates a random alphanumeric code
func (s *RedeemCodeService) generateRandomCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)

	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[num.Int64()]
	}

	return string(result)
}
