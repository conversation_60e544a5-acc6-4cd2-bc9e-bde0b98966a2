package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RateLimitMiddleware creates rate limiting middleware using Redis
func RateLimitMiddleware(redisClient *redis.Client, requests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client identifier (IP address or user ID if authenticated)
		clientID := getClientID(c)
		
		// Create Redis key
		key := fmt.Sprintf("rate_limit:%s", clientID)
		
		ctx := context.Background()
		
		// Get current count
		current, err := redisClient.Get(ctx, key).Int()
		if err != nil && err != redis.Nil {
			// If Redis is unavailable, allow the request
			c.Next()
			return
		}
		
		// Check if limit exceeded
		if current >= requests {
			c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
			c<PERSON><PERSON>("X-RateLimit-Remaining", "0")
			c<PERSON><PERSON>("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(window).Unix(), 10))
			
			c.J<PERSON>(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "Rate limit exceeded",
				"retry_after": int(window.Seconds()),
			})
			c.Abort()
			return
		}
		
		// Increment counter
		pipe := redisClient.Pipeline()
		pipe.Incr(ctx, key)
		pipe.Expire(ctx, key, window)
		_, err = pipe.Exec(ctx)
		
		if err != nil {
			// If Redis operation fails, allow the request but log the error
			fmt.Printf("Rate limit Redis error: %v\n", err)
		}
		
		// Set rate limit headers
		remaining := requests - current - 1
		if remaining < 0 {
			remaining = 0
		}
		
		c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(window).Unix(), 10))
		
		c.Next()
	}
}

// getClientID returns a unique identifier for the client
func getClientID(c *gin.Context) string {
	// If user is authenticated, use user ID
	if userID, exists := c.Get("user_id"); exists {
		return fmt.Sprintf("user:%s", userID)
	}
	
	// Otherwise use IP address
	return fmt.Sprintf("ip:%s", c.ClientIP())
}

// FileUploadRateLimitMiddleware creates special rate limiting for file uploads
func FileUploadRateLimitMiddleware(redisClient *redis.Client, uploads int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client identifier
		clientID := getClientID(c)
		
		// Create Redis key for file uploads
		key := fmt.Sprintf("upload_rate_limit:%s", clientID)
		
		ctx := context.Background()
		
		// Get current count
		current, err := redisClient.Get(ctx, key).Int()
		if err != nil && err != redis.Nil {
			// If Redis is unavailable, allow the request
			c.Next()
			return
		}
		
		// Check if limit exceeded
		if current >= uploads {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "File upload rate limit exceeded",
				"retry_after": int(window.Seconds()),
			})
			c.Abort()
			return
		}
		
		// Increment counter
		pipe := redisClient.Pipeline()
		pipe.Incr(ctx, key)
		pipe.Expire(ctx, key, window)
		_, err = pipe.Exec(ctx)
		
		if err != nil {
			fmt.Printf("Upload rate limit Redis error: %v\n", err)
		}
		
		c.Next()
	}
}
