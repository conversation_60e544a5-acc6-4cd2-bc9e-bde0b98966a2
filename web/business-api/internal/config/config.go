package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port string
	Host string
	Mode string // debug, release, test

	// Database configuration
	DatabaseURL string

	// Redis configuration
	RedisURL string

	// JWT configuration
	JWTSecret     string
	JWTExpiration time.Duration

	// File storage configuration
	UploadDir    string
	VideoDir     string
	MaxFileSize  int64
	AllowedTypes []string

	// PPT Generator integration
	PPTGeneratorURL string
	PPTGeneratorTimeout time.Duration

	// Credit system configuration
	DefaultCredits        int64
	PPTGenerationCost     int64
	RedeemCodeExpiration  time.Duration

	// Task queue configuration
	TaskWorkers     int
	TaskRetryDelay  time.Duration
	TaskMaxAttempts int

	// Rate limiting
	RateLimitRequests int
	RateLimitWindow   time.Duration

	// CORS configuration
	CORSOrigins []string
}

// Load loads configuration from environment variables
func Load() *Config {
	config := &Config{
		// Server defaults
		Port: getEnv("PORT", "8080"),
		Host: getEnv("HOST", "0.0.0.0"),
		Mode: getEnv("GIN_MODE", "debug"),

		// Database
		DatabaseURL: getEnv("DATABASE_URL", "ppt_narrator_business.db"),

		// Redis
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),

		// JWT
		JWTSecret:     getEnv("JWT_SECRET", "your-secret-key"),
		JWTExpiration: getDurationEnv("JWT_EXPIRATION", 24*time.Hour),

		// File storage
		UploadDir:    getEnv("UPLOAD_DIR", "./uploads"),
		VideoDir:     getEnv("VIDEO_DIR", "./videos"),
		MaxFileSize:  getInt64Env("MAX_FILE_SIZE", 100*1024*1024), // 100MB
		AllowedTypes: []string{".pptx", ".ppt"},

		// PPT Generator
		PPTGeneratorURL:     getEnv("PPT_GENERATOR_URL", "http://localhost:8080"),
		PPTGeneratorTimeout: getDurationEnv("PPT_GENERATOR_TIMEOUT", 30*time.Minute),

		// Credit system
		DefaultCredits:       getInt64Env("DEFAULT_CREDITS", 100),
		PPTGenerationCost:    getInt64Env("PPT_GENERATION_COST", 10),
		RedeemCodeExpiration: getDurationEnv("REDEEM_CODE_EXPIRATION", 30*24*time.Hour), // 30 days

		// Task queue
		TaskWorkers:     getIntEnv("TASK_WORKERS", 5),
		TaskRetryDelay:  getDurationEnv("TASK_RETRY_DELAY", 5*time.Minute),
		TaskMaxAttempts: getIntEnv("TASK_MAX_ATTEMPTS", 3),

		// Rate limiting
		RateLimitRequests: getIntEnv("RATE_LIMIT_REQUESTS", 100),
		RateLimitWindow:   getDurationEnv("RATE_LIMIT_WINDOW", time.Hour),

		// CORS
		CORSOrigins: []string{
			getEnv("FRONTEND_URL", "http://localhost:3000"),
			getEnv("ADMIN_URL", "http://localhost:3001"),
		},
	}

	return config
}

// Helper functions to get environment variables with defaults
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getInt64Env(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
