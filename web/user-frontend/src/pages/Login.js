import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Form, Input, Button, Typography, Space, Divider } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const result = await login(values);
      if (!result.success) {
        // Error is already handled in AuthContext
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-form">
        <Title level={2} className="login-title">
          PPT Narrator
        </Title>
        <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginBottom: 30 }}>
          AI驱动的PPT讲解视频生成服务
        </Text>

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名或邮箱' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名或邮箱"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>或</Divider>

        <div className="register-link">
          <Space>
            <Text>还没有账号？</Text>
            <Link to="/register">立即注册</Link>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default Login;
