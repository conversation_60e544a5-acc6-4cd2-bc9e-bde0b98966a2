import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Typography,
  Space,
  Row,
  Col,
  Progress,
  Tag,
  Descriptions,
  Alert,
  Steps,
  Timeline,
  Divider,
  message,
} from 'antd';
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation } from 'react-query';
import { projectService } from '../services/projectService';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch project data
  const { data: projectData, isLoading, refetch } = useQuery(
    ['project', id],
    () => projectService.getProject(id),
    {
      refetchInterval: (data) => {
        // Refetch every 10 seconds if project is processing
        return data?.data?.status === 'processing' ? 10000 : false;
      },
      onError: (error) => {
        message.error('加载项目详情失败：' + (error.response?.data?.error || error.message));
      },
    }
  );

  const project = projectData?.data;

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      case 'created':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      case 'created':
        return '已创建';
      default:
        return status;
    }
  };

  const getCurrentStep = (status, currentStage) => {
    if (status === 'failed') return -1;
    if (status === 'completed') return 4;
    
    switch (currentStage) {
      case 'screenshot':
        return 0;
      case 'narration':
        return 1;
      case 'tts':
        return 2;
      case 'video':
        return 3;
      default:
        return 0;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Download mutation
  const downloadMutation = useMutation(projectService.downloadVideo, {
    onSuccess: () => {
      message.success('视频下载完成');
    },
    onError: (error) => {
      message.error('下载失败：' + (error.response?.data?.error || error.message));
    },
  });

  const handleDownload = () => {
    if (project.status !== 'completed') {
      message.warning('项目尚未完成，无法下载');
      return;
    }
    downloadMutation.mutate(project.id);
  };

  const handleRetry = () => {
    // TODO: Implement retry functionality
    message.info('重新开始处理...');
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!project) {
    return <div>Project not found</div>;
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回项目列表
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            刷新状态
          </Button>
        </Space>
      </div>

      <div style={{ marginBottom: 24 }}>
        <Space align="center">
          <Title level={2} style={{ margin: 0 }}>
            {project.name}
          </Title>
          <Tag color={getStatusColor(project.status)} style={{ fontSize: 14 }}>
            {getStatusText(project.status)}
          </Tag>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* Progress and Status */}
        <Col xs={24} lg={16}>
          <Card title="处理进度">
            {project.status === 'processing' && (
              <div style={{ marginBottom: 24 }}>
                <Progress
                  percent={project.progress}
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
                <Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
                  当前阶段: {project.current_stage}
                </Text>
              </div>
            )}

            <Steps
              current={getCurrentStep(project.status, project.current_stage)}
              status={project.status === 'failed' ? 'error' : 'process'}
            >
              <Step title="幻灯片截图" description="提取PPT中的图片内容" />
              <Step title="生成讲解文本" description="AI分析并生成讲解内容" />
              <Step title="语音合成" description="将文本转换为语音" />
              <Step title="视频合成" description="合成最终的讲解视频" />
            </Steps>

            {project.status === 'failed' && (
              <Alert
                message="处理失败"
                description={project.error_message || '处理过程中发生错误，请重试'}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
                action={
                  <Button size="small" onClick={handleRetry}>
                    重试
                  </Button>
                }
              />
            )}

            {project.status === 'completed' && (
              <Alert
                message="处理完成"
                description="您的PPT讲解视频已生成完成，可以下载观看了！"
                type="success"
                showIcon
                style={{ marginTop: 16 }}
                action={
                  <Button
                    type="primary"
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  >
                    下载视频
                  </Button>
                }
              />
            )}
          </Card>

          {/* Processing Log */}
          <Card title="处理日志" style={{ marginTop: 16 }}>
            <Timeline>
              {project.processing_log.map((log, index) => (
                <Timeline.Item
                  key={index}
                  color={log.status === 'completed' ? 'green' : log.status === 'failed' ? 'red' : 'blue'}
                >
                  <div>
                    <Text strong>{log.message}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {new Date(log.timestamp).toLocaleString()}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </Col>

        {/* Project Information */}
        <Col xs={24} lg={8}>
          <Card title="项目信息">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="项目ID">
                <Text code>{project.id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="原始文件">
                {project.original_file_name}
              </Descriptions.Item>
              <Descriptions.Item label="文件大小">
                {formatFileSize(project.file_size)}
              </Descriptions.Item>
              <Descriptions.Item label="幻灯片数">
                {project.slide_count || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="消费点数">
                {project.credits_cost > 0 ? `${project.credits_cost} 点数` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(project.created_at).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(project.updated_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Configuration */}
          <Card title="生成配置" style={{ marginTop: 16 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="讲解风格">
                {project.config.narration_style === 'professional' ? '专业商务' :
                 project.config.narration_style === 'casual' ? '轻松随意' : '教育培训'}
              </Descriptions.Item>
              <Descriptions.Item label="音色">
                {project.config.voice}
              </Descriptions.Item>
              <Descriptions.Item label="语速">
                {project.config.speech_speed}x
              </Descriptions.Item>
              <Descriptions.Item label="详细程度">
                {project.config.detail_level === 'brief' ? '简洁' :
                 project.config.detail_level === 'medium' ? '适中' : '详细'}
              </Descriptions.Item>
              <Descriptions.Item label="包含过渡">
                {project.config.include_transitions ? '是' : '否'}
              </Descriptions.Item>
            </Descriptions>

            {project.config.user_requirements && (
              <>
                <Divider />
                <div>
                  <Text strong>需求描述:</Text>
                  <Paragraph style={{ marginTop: 8 }}>
                    {project.config.user_requirements}
                  </Paragraph>
                </div>
              </>
            )}
          </Card>

          {/* Actions */}
          <Card title="操作" style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {project.status === 'completed' && (
                <Button
                  type="primary"
                  block
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载视频
                </Button>
              )}
              {project.status === 'failed' && (
                <Button
                  type="primary"
                  block
                  onClick={handleRetry}
                >
                  重新处理
                </Button>
              )}
              <Button
                block
                onClick={() => navigate('/projects')}
              >
                返回项目列表
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ProjectDetail;
