import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Typography,
  Space,
  Row,
  Col,
  Select,
  Slider,
  Switch,
  message,
  Alert,
  Divider,
} from 'antd';
import {
  InboxOutlined,
  ArrowLeftOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useMutation } from 'react-query';
import { projectService } from '../services/projectService';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Dragger } = Upload;

const CreateProject = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [estimatedCost, setEstimatedCost] = useState(10);

  // Create project mutation
  const createProjectMutation = useMutation(projectService.uploadAndProcess, {
    onSuccess: (data) => {
      message.success('项目创建成功，开始处理中...');
      navigate('/projects');
    },
    onError: (error) => {
      message.error('项目创建失败：' + (error.response?.data?.error || error.message));
    },
  });

  const voiceOptions = [
    { value: 'alloy', label: 'Alloy - 中性，平衡' },
    { value: 'echo', label: 'Echo - 男性，深沉' },
    { value: 'fable', label: 'Fable - 英式，优雅' },
    { value: 'onyx', label: 'Onyx - 男性，深沉' },
    { value: 'nova', label: 'Nova - 女性，活泼' },
    { value: 'shimmer', label: 'Shimmer - 女性，温和' },
  ];

  const narrationStyles = [
    { value: 'professional', label: '专业商务' },
    { value: 'casual', label: '轻松随意' },
    { value: 'educational', label: '教育培训' },
  ];

  const detailLevels = [
    { value: 'brief', label: '简洁' },
    { value: 'medium', label: '适中' },
    { value: 'detailed', label: '详细' },
  ];

  const uploadProps = {
    name: 'file',
    multiple: false,
    fileList,
    accept: '.pptx,.ppt',
    beforeUpload: (file) => {
      const isPPT = file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                    file.type === 'application/vnd.ms-powerpoint' ||
                    file.name.toLowerCase().endsWith('.pptx') ||
                    file.name.toLowerCase().endsWith('.ppt');
      
      if (!isPPT) {
        message.error('只能上传 PPT 或 PPTX 文件！');
        return false;
      }

      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件大小不能超过 100MB！');
        return false;
      }

      // Estimate cost based on file size (rough estimation)
      const estimatedSlides = Math.ceil(file.size / (1024 * 1024) * 5); // Rough estimate
      const cost = Math.max(10, Math.min(estimatedSlides, 50)); // Between 10-50 credits
      setEstimatedCost(cost);

      return false; // Prevent auto upload
    },
    onChange: (info) => {
      setFileList(info.fileList.slice(-1)); // Keep only the latest file
    },
    onDrop: (e) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  const handleSubmit = async (values) => {
    if (fileList.length === 0) {
      message.error('请选择要上传的PPT文件');
      return;
    }

    if (user.credits < estimatedCost) {
      message.error('点数不足，请先充值');
      return;
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', fileList[0].originFileObj);
    formData.append('project_name', values.project_name);
    formData.append('user_requirements', values.user_requirements || '');

    // Add configuration
    formData.append('narration_style', values.narration_style);
    formData.append('voice', values.voice);
    formData.append('speech_speed', values.speech_speed.toString());
    formData.append('language', values.language);
    formData.append('include_transitions', values.include_transitions.toString());
    formData.append('detail_level', values.detail_level);
    formData.append('custom_prompt', values.custom_prompt || '');

    createProjectMutation.mutate(formData);
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回项目列表
          </Button>
        </Space>
      </div>

      <div style={{ marginBottom: 24 }}>
        <Title level={2}>创建新项目</Title>
        <Text type="secondary">上传您的PPT文件并配置生成参数</Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          narration_style: 'professional',
          voice: 'alloy',
          speech_speed: 1.0,
          language: 'zh-CN',
          include_transitions: true,
          detail_level: 'medium',
        }}
      >
        <Row gutter={[24, 24]}>
          {/* File Upload */}
          <Col xs={24} lg={12}>
            <Card title="文件上传">
              <Form.Item
                name="project_name"
                label="项目名称"
                rules={[
                  { required: true, message: '请输入项目名称' },
                  { max: 100, message: '项目名称不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入项目名称" />
              </Form.Item>

              <Form.Item label="PPT文件">
                <Dragger {...uploadProps}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持 .ppt 和 .pptx 格式，文件大小不超过 100MB
                  </p>
                </Dragger>
              </Form.Item>

              <Form.Item
                name="user_requirements"
                label="需求描述"
              >
                <TextArea
                  rows={4}
                  placeholder="请描述您对讲解视频的特殊要求，例如：语调、重点内容、目标受众等（可选）"
                />
              </Form.Item>
            </Card>
          </Col>

          {/* Configuration */}
          <Col xs={24} lg={12}>
            <Card title="生成配置">
              <Form.Item
                name="narration_style"
                label="讲解风格"
              >
                <Select>
                  {narrationStyles.map(style => (
                    <Option key={style.value} value={style.value}>
                      {style.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="voice"
                label="音色选择"
              >
                <Select>
                  {voiceOptions.map(voice => (
                    <Option key={voice.value} value={voice.value}>
                      {voice.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="speech_speed"
                label="语速"
              >
                <Slider
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  marks={{
                    0.5: '慢',
                    1.0: '正常',
                    1.5: '快',
                    2.0: '很快',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="detail_level"
                label="详细程度"
              >
                <Select>
                  {detailLevels.map(level => (
                    <Option key={level.value} value={level.value}>
                      {level.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="include_transitions"
                label="包含过渡语句"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="custom_prompt"
                label="自定义提示词"
              >
                <TextArea
                  rows={3}
                  placeholder="输入自定义的AI提示词来控制生成效果（高级选项，可选）"
                />
              </Form.Item>
            </Card>

            {/* Cost Estimation */}
            <Card title="费用预估" style={{ marginTop: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>预估消费:</Text>
                  <Text strong style={{ color: '#1890ff' }}>
                    {estimatedCost} 点数
                  </Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>当前余额:</Text>
                  <Text strong style={{ color: user?.credits >= estimatedCost ? '#52c41a' : '#ff4d4f' }}>
                    {user?.credits || 0} 点数
                  </Text>
                </div>
                {user?.credits < estimatedCost && (
                  <Alert
                    message="点数不足"
                    description="您的点数余额不足以完成此项目，请先充值。"
                    type="warning"
                    showIcon
                    action={
                      <Button size="small" onClick={() => navigate('/credits')}>
                        去充值
                      </Button>
                    }
                  />
                )}
              </Space>
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button
              size="large"
              onClick={() => navigate('/projects')}
            >
              取消
            </Button>
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={createProjectMutation.isLoading}
              icon={<RocketOutlined />}
              disabled={user?.credits < estimatedCost}
            >
              创建项目并开始处理
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default CreateProject;
