import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Table,
  Tag,
  Typography,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Modal,
} from 'antd';
import {
  CreditCardOutlined,
  GiftOutlined,
  HistoryOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '../contexts/AuthContext';
import { creditService } from '../services/creditService';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Credits = () => {
  const [redeemForm] = Form.useForm();
  const [redeemModalVisible, setRedeemModalVisible] = useState(false);
  const { user, refreshUserData } = useAuth();
  const queryClient = useQueryClient();

  // Fetch credit stats
  const { data: creditStats, isLoading: statsLoading } = useQuery(
    'creditStats',
    creditService.getCreditStats
  );

  // Fetch credit history
  const { data: creditHistory, isLoading: historyLoading } = useQuery(
    'creditHistory',
    () => creditService.getCreditHistory(1, 50)
  );

  // Fetch redeem history
  const { data: redeemHistory, isLoading: redeemLoading } = useQuery(
    'redeemHistory',
    () => creditService.getRedeemHistory(1, 50)
  );

  // Redeem code mutation
  const redeemMutation = useMutation(creditService.redeemCode, {
    onSuccess: (data) => {
      message.success(`成功兑换 ${data.data.credits} 点数！`);
      redeemForm.resetFields();
      setRedeemModalVisible(false);
      // Refresh data
      queryClient.invalidateQueries('creditStats');
      queryClient.invalidateQueries('creditHistory');
      queryClient.invalidateQueries('redeemHistory');
      refreshUserData();
    },
    onError: (error) => {
      message.error(error.response?.data?.error || '兑换失败');
    },
  });

  const handleRedeemSubmit = (values) => {
    redeemMutation.mutate(values.code);
  };

  const creditHistoryColumns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          recharge: { text: '充值', color: 'green' },
          consume: { text: '消费', color: 'red' },
          refund: { text: '退款', color: 'orange' },
          bonus: { text: '奖励', color: 'blue' },
          transfer: { text: '转账', color: 'purple' },
        };
        const config = typeMap[type] || { text: type, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <Text className={amount > 0 ? 'credit-amount positive' : 'credit-amount negative'}>
          {amount > 0 ? '+' : ''}{amount}
        </Text>
      ),
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance) => <Text strong>{balance}</Text>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  const redeemHistoryColumns = [
    {
      title: '兑换时间',
      dataIndex: 'used_at',
      key: 'used_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '兑换码',
      dataIndex: ['redeem_code', 'code'],
      key: 'code',
      render: (code) => <Text code>{code}</Text>,
    },
    {
      title: '获得点数',
      dataIndex: 'credits',
      key: 'credits',
      render: (credits) => (
        <Text className="credit-amount positive">+{credits}</Text>
      ),
    },
    {
      title: '描述',
      dataIndex: ['redeem_code', 'description'],
      key: 'description',
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>点数管理</Title>
        <Text type="secondary">管理您的点数余额和充值记录</Text>
      </div>

      {/* Credit Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="当前余额"
              value={user?.credits || 0}
              suffix="点数"
              valueStyle={{ color: '#1890ff' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="累计获得"
              value={creditStats?.data?.total_earned || 0}
              suffix="点数"
              valueStyle={{ color: '#52c41a' }}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="累计消费"
              value={creditStats?.data?.total_consumed || 0}
              suffix="点数"
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<HistoryOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="累计退款"
              value={creditStats?.data?.total_refunded || 0}
              suffix="点数"
              valueStyle={{ color: '#faad14' }}
              prefix={<ReloadOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Tabs */}
      <Card>
        <Tabs defaultActiveKey="recharge">
          <TabPane tab="充值点数" key="recharge">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Space direction="vertical" size="large">
                <GiftOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                <Title level={3}>使用兑换码充值</Title>
                <Text type="secondary">
                  输入您的兑换码来获取点数
                </Text>
                <Button
                  type="primary"
                  size="large"
                  onClick={() => setRedeemModalVisible(true)}
                >
                  兑换点数
                </Button>
              </Space>
            </div>
          </TabPane>

          <TabPane tab="消费记录" key="history">
            <Table
              columns={creditHistoryColumns}
              dataSource={creditHistory?.data?.records || []}
              loading={historyLoading}
              rowKey="id"
              pagination={{
                total: creditHistory?.data?.total || 0,
                pageSize: 50,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>

          <TabPane tab="兑换记录" key="redeem">
            <Table
              columns={redeemHistoryColumns}
              dataSource={redeemHistory?.data?.usages || []}
              loading={redeemLoading}
              rowKey="id"
              pagination={{
                total: redeemHistory?.data?.total || 0,
                pageSize: 50,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Redeem Modal */}
      <Modal
        title="兑换点数"
        open={redeemModalVisible}
        onCancel={() => setRedeemModalVisible(false)}
        footer={null}
      >
        <Form
          form={redeemForm}
          onFinish={handleRedeemSubmit}
          layout="vertical"
        >
          <Form.Item
            name="code"
            label="兑换码"
            rules={[
              { required: true, message: '请输入兑换码' },
            ]}
          >
            <Input
              placeholder="请输入兑换码"
              className="redeem-code-input"
              style={{ textAlign: 'center' }}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={redeemMutation.isLoading}
              block
            >
              立即兑换
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Credits;
