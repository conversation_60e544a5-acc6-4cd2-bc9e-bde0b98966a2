import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Statistic,
  Button,
  List,
  Tag,
  Typography,
  Space,
  Progress,
  Empty,
} from 'antd';
import {
  FileTextOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useQuery } from 'react-query';
import { creditService } from '../services/creditService';
import { projectService } from '../services/projectService';

const { Title, Text } = Typography;

const Dashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch credit stats
  const { data: creditStats } = useQuery(
    'creditStats',
    creditService.getCreditStats,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch recent projects
  const { data: projectsData } = useQuery(
    'recentProjects',
    () => projectService.getProjects(1, 5),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const recentProjects = projectsData?.data?.projects || [];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      case 'created':
        return '已创建';
      default:
        return status;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'failed':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Text type="secondary">欢迎回来，{user?.nickname || user?.username}！</Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="dashboard-stats">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="当前余额"
              value={user?.credits || 0}
              suffix="点数"
              valueStyle={{ color: '#1890ff' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总消费"
              value={creditStats?.data?.total_consumed || 0}
              suffix="点数"
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="项目总数"
              value={recentProjects.length}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="完成项目"
              value={recentProjects.filter(p => p.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        {/* Recent Projects */}
        <Col xs={24} lg={16}>
          <Card
            title="最近项目"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/projects/new')}
              >
                新建项目
              </Button>
            }
          >
            {recentProjects.length > 0 ? (
              <List
                dataSource={recentProjects}
                renderItem={(project) => (
                  <List.Item
                    actions={[
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => navigate(`/projects/${project.id}`)}
                      >
                        查看
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          {project.name}
                          <Tag
                            color={getStatusColor(project.status)}
                            icon={getStatusIcon(project.status)}
                          >
                            {getStatusText(project.status)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text type="secondary">
                            创建时间: {new Date(project.created_at).toLocaleString()}
                          </Text>
                          {project.status === 'processing' && (
                            <Progress
                              percent={project.progress}
                              size="small"
                              status="active"
                            />
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty
                description="暂无项目"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/projects/new')}
                >
                  创建第一个项目
                </Button>
              </Empty>
            )}
          </Card>
        </Col>

        {/* Quick Actions */}
        <Col xs={24} lg={8}>
          <Card title="快速操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                block
                icon={<PlusOutlined />}
                onClick={() => navigate('/projects/new')}
              >
                新建PPT项目
              </Button>
              <Button
                block
                icon={<FileTextOutlined />}
                onClick={() => navigate('/projects')}
              >
                查看所有项目
              </Button>
              <Button
                block
                icon={<CreditCardOutlined />}
                onClick={() => navigate('/credits')}
              >
                充值点数
              </Button>
            </Space>
          </Card>

          {/* Credit Stats */}
          {creditStats?.data && (
            <Card title="点数统计" style={{ marginTop: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>当前余额:</Text>
                  <Text strong style={{ color: '#1890ff' }}>
                    {creditStats.data.current_balance}
                  </Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>累计获得:</Text>
                  <Text strong style={{ color: '#52c41a' }}>
                    {creditStats.data.total_earned}
                  </Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text>累计消费:</Text>
                  <Text strong style={{ color: '#ff4d4f' }}>
                    {creditStats.data.total_consumed}
                  </Text>
                </div>
                {creditStats.data.total_refunded > 0 && (
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>累计退款:</Text>
                    <Text strong style={{ color: '#faad14' }}>
                      {creditStats.data.total_refunded}
                    </Text>
                  </div>
                )}
              </Space>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
