import api from './api';

export const creditService = {
  // Get user credits
  getCredits: async () => {
    const response = await api.get('/credits');
    return response.data;
  },

  // Get credit history
  getCreditHistory: async (page = 1, pageSize = 20) => {
    const response = await api.get('/credits/history', {
      params: { page, page_size: pageSize }
    });
    return response.data;
  },

  // Get credit statistics
  getCreditStats: async () => {
    const response = await api.get('/credits/stats');
    return response.data;
  },

  // Redeem code
  redeemCode: async (code) => {
    const response = await api.post('/credits/redeem', { code });
    return response.data;
  },

  // Get redeem history
  getRedeemHistory: async (page = 1, pageSize = 20) => {
    const response = await api.get('/credits/redeem-history', {
      params: { page, page_size: pageSize }
    });
    return response.data;
  },
};
