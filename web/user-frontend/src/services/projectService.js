import api from './api';

export const projectService = {
  // Upload PPT and start processing
  uploadAndProcess: async (formData) => {
    const response = await api.post('/projects/upload-and-process', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 1800000, // 30 minutes
    });
    return response.data;
  },

  // Get user projects
  getProjects: async (page = 1, pageSize = 20) => {
    const response = await api.get('/projects', {
      params: { page, page_size: pageSize }
    });
    return response.data;
  },

  // Get project details
  getProject: async (projectId) => {
    const response = await api.get(`/projects/${projectId}`);
    return response.data;
  },

  // Delete project
  deleteProject: async (projectId) => {
    const response = await api.delete(`/projects/${projectId}`);
    return response.data;
  },

  // Download video
  downloadVideo: async (projectId) => {
    const response = await api.get(`/projects/${projectId}/download`, {
      responseType: 'blob',
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from response headers or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'video.mp4';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    
    return { success: true };
  },
};
