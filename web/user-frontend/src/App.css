.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #1890ff;
  font-size: 24px;
  font-weight: bold;
}

.register-link {
  text-align: center;
  margin-top: 16px;
}

.dashboard-stats {
  margin-bottom: 24px;
}

.project-card {
  margin-bottom: 16px;
}

.project-status {
  text-transform: capitalize;
}

.credit-balance {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-area.dragover {
  border-color: #1890ff;
  background: #e6f7ff;
}

.progress-container {
  margin: 20px 0;
}

.stage-progress {
  margin-bottom: 16px;
}

.stage-progress .ant-progress-text {
  font-size: 12px;
}

.video-preview {
  width: 100%;
  max-width: 800px;
  margin: 20px auto;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.config-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
}

.voice-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.voice-option {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.voice-option:hover {
  border-color: #1890ff;
}

.voice-option.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.credit-history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.credit-history-item:last-child {
  border-bottom: none;
}

.credit-amount {
  font-weight: 600;
}

.credit-amount.positive {
  color: #52c41a;
}

.credit-amount.negative {
  color: #ff4d4f;
}

.redeem-code-input {
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: monospace;
}

/* Responsive design */
@media (max-width: 768px) {
  .login-form {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .voice-selector {
    grid-template-columns: 1fr;
  }
  
  .dashboard-stats .ant-col {
    margin-bottom: 16px;
  }
}
