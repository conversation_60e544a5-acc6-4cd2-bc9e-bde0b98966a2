# PPT Narrator 商业服务

基于PPT Narrator生成器的完整商业服务平台，包含用户前端、管理员后台和商业API后端。

## 项目结构

```
web/
├── README.md                    # 项目说明
├── docker-compose.yml          # Docker编排文件
├── nginx.conf                  # Nginx配置
├── business-api/               # 商业服务后端 (Gin)
│   ├── cmd/
│   │   └── main.go
│   ├── internal/
│   │   ├── models/             # 数据模型
│   │   ├── handlers/           # API处理器
│   │   ├── services/           # 业务逻辑
│   │   ├── middleware/         # 中间件
│   │   └── config/             # 配置
│   ├── go.mod
│   ├── go.sum
│   └── Dockerfile
├── user-frontend/              # 用户前端 (React + Ant Design)
│   ├── public/
│   ├── src/
│   │   ├── components/         # 通用组件
│   │   ├── pages/              # 页面组件
│   │   ├── services/           # API服务
│   │   ├── utils/              # 工具函数
│   │   ├── hooks/              # 自定义Hooks
│   │   └── App.js
│   ├── package.json
│   └── Dockerfile
└── admin-dashboard/            # 管理员后台 (React + Ant Design)
    ├── public/
    ├── src/
    │   ├── components/         # 通用组件
    │   ├── pages/              # 页面组件
    │   ├── services/           # API服务
    │   ├── utils/              # 工具函数
    │   └── App.js
    ├── package.json
    └── Dockerfile
```

## 功能特性

### 用户前端
- 用户注册登录
- 充值码充值点数
- 上传PPT文件
- 配置生成参数（提示词、音色等）
- 任务进度监控
- 历史记录管理
- 视频下载

### 管理员后台
- 用户管理
- 充值码生成和管理
- 任务监控和管理
- 系统统计和报表
- 配置管理

### 商业API后端
- JWT用户认证
- 点数系统
- 充值码系统
- 任务队列管理
- 文件上传处理
- 与PPT生成器集成

## 技术栈

- **后端**: Go + Gin + GORM + PostgreSQL/SQLite
- **前端**: React + Ant Design + Axios
- **部署**: Docker + Docker Compose + Nginx
- **认证**: JWT Token
- **文件存储**: 本地存储/云存储

## 快速开始

1. 克隆项目
2. 配置环境变量
3. 启动服务：`docker-compose up -d`
4. 访问用户前端：http://localhost:3000
5. 访问管理员后台：http://localhost:3001

## 环境变量

### Business API
```env
PORT=8081
DATABASE_URL=postgres://user:pass@host:port/db
REDIS_URL=redis://host:port
JWT_SECRET=your-secret-key
PPT_GENERATOR_URL=http://ppt-generator:8080
DEFAULT_CREDITS=100
PPT_GENERATION_COST=10
```

### Frontend
```env
REACT_APP_API_URL=http://localhost:8081/api/v1
```

## API文档

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/profile` - 获取用户信息
- `PUT /api/v1/auth/profile` - 更新用户信息

### 项目管理
- `POST /api/v1/projects/upload-and-process` - 上传PPT并开始处理
- `GET /api/v1/projects` - 获取用户项目列表
- `GET /api/v1/projects/:id` - 获取项目详情
- `DELETE /api/v1/projects/:id` - 删除项目
- `GET /api/v1/projects/:id/download` - 下载生成的视频

### 点数管理
- `GET /api/v1/credits` - 获取点数余额
- `GET /api/v1/credits/history` - 获取点数历史
- `POST /api/v1/credits/redeem` - 兑换充值码

### 管理员功能
- `GET /api/v1/admin/redeem-codes` - 获取充值码列表
- `POST /api/v1/admin/redeem-codes` - 创建充值码
- `POST /api/v1/admin/credits/add` - 给用户充值点数

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认Docker和Docker Compose版本
   - 查看服务日志定位问题

2. **数据库连接失败**
   - 确认PostgreSQL服务正常运行
   - 检查数据库连接字符串
   - 验证用户权限

3. **文件上传失败**
   - 检查文件大小限制
   - 确认上传目录权限
   - 查看Nginx配置

4. **PPT处理失败**
   - 确认PPT生成器服务正常
   - 检查文件格式是否支持
   - 查看处理日志
