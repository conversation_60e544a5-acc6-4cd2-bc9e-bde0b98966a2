import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Space,
  Typography,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  GiftOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const RedeemCodes = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [selectedCode, setSelectedCode] = useState(null);
  const [loading, setLoading] = useState(false);
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [batchForm] = Form.useForm();

  // Mock data - replace with actual API calls
  const stats = {
    totalCodes: 156,
    activeCodes: 45,
    usedCodes: 89,
    expiredCodes: 22,
  };

  const redeemCodes = [
    {
      id: '1',
      code: 'WELCOME2024',
      credits: 100,
      description: '新用户欢迎礼包',
      status: 'active',
      max_uses: 1,
      used_count: 0,
      expires_at: '2024-12-31T23:59:59Z',
      created_at: '2024-01-01T00:00:00Z',
      created_by: 'admin',
    },
    {
      id: '2',
      code: 'SPRING50',
      credits: 50,
      description: '春季促销活动',
      status: 'used',
      max_uses: 100,
      used_count: 100,
      expires_at: '2024-03-31T23:59:59Z',
      created_at: '2024-03-01T00:00:00Z',
      created_by: 'admin',
    },
    {
      id: '3',
      code: 'EXPIRED10',
      credits: 10,
      description: '已过期测试码',
      status: 'expired',
      max_uses: 1,
      used_count: 0,
      expires_at: '2024-01-31T23:59:59Z',
      created_at: '2024-01-01T00:00:00Z',
      created_by: 'admin',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'used':
        return 'default';
      case 'expired':
        return 'warning';
      case 'disabled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'used':
        return '已用完';
      case 'expired':
        return '已过期';
      case 'disabled':
        return '已禁用';
      default:
        return status;
    }
  };

  const handleCreateSubmit = async (values) => {
    setLoading(true);
    try {
      // API call to create redeem codes
      console.log('Creating redeem codes:', values);
      message.success('兑换码创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
    } catch (error) {
      message.error('创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleEditSubmit = async (values) => {
    setLoading(true);
    try {
      // API call to update redeem code
      console.log('Updating redeem code:', selectedCode.id, values);
      message.success('兑换码更新成功');
      setEditModalVisible(false);
      editForm.resetFields();
      setSelectedCode(null);
    } catch (error) {
      message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchCreate = async (values) => {
    setLoading(true);
    try {
      // API call to create batch redeem codes
      console.log('Creating batch redeem codes:', values);
      message.success('批量兑换码创建成功');
      setBatchModalVisible(false);
      batchForm.resetFields();
    } catch (error) {
      message.error('批量创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (codeId) => {
    try {
      // API call to delete redeem code
      console.log('Deleting redeem code:', codeId);
      message.success('兑换码删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleCopyCode = (code) => {
    navigator.clipboard.writeText(code);
    message.success('兑换码已复制到剪贴板');
  };

  const handleEdit = (record) => {
    setSelectedCode(record);
    editForm.setFieldsValue({
      description: record.description,
      status: record.status,
      expires_at: record.expires_at ? new Date(record.expires_at) : null,
    });
    setEditModalVisible(true);
  };

  const columns = [
    {
      title: '兑换码',
      dataIndex: 'code',
      key: 'code',
      render: (code) => (
        <Space>
          <Text code>{code}</Text>
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopyCode(code)}
          />
        </Space>
      ),
    },
    {
      title: '点数',
      dataIndex: 'credits',
      key: 'credits',
      render: (credits) => <Text strong>{credits}</Text>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record) => (
        <Text>
          {record.used_count} / {record.max_uses}
        </Text>
      ),
    },
    {
      title: '过期时间',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (text) => text ? new Date(text).toLocaleString() : '永不过期',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个兑换码吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>充值码管理</Title>
        <Text type="secondary">管理系统中的充值兑换码</Text>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总兑换码"
              value={stats.totalCodes}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃兑换码"
              value={stats.activeCodes}
              valueStyle={{ color: '#52c41a' }}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已用完"
              value={stats.usedCodes}
              valueStyle={{ color: '#faad14' }}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已过期"
              value={stats.expiredCodes}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Actions */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建兑换码
          </Button>
          <Button
            icon={<PlusOutlined />}
            onClick={() => setBatchModalVisible(true)}
          >
            批量创建
          </Button>
        </Space>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={redeemCodes}
          rowKey="id"
          loading={loading}
          pagination={{
            total: redeemCodes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* Create Modal */}
      <Modal
        title="创建兑换码"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item
            name="credits"
            label="点数"
            rules={[{ required: true, message: '请输入点数' }]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input placeholder="兑换码描述" />
          </Form.Item>

          <Form.Item
            name="max_uses"
            label="最大使用次数"
            rules={[{ required: true, message: '请输入最大使用次数' }]}
            initialValue={1}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="count"
            label="生成数量"
            rules={[{ required: true, message: '请输入生成数量' }]}
            initialValue={1}
          >
            <InputNumber min={1} max={1000} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="prefix"
            label="前缀"
          >
            <Input placeholder="兑换码前缀（可选）" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="编辑兑换码"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="description"
            label="描述"
          >
            <Input placeholder="兑换码描述" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Option value="active">活跃</Option>
              <Option value="disabled">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Batch Create Modal */}
      <Modal
        title="批量创建兑换码"
        open={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchCreate}
        >
          <Form.Item
            name="batches"
            label="批次配置"
          >
            <TextArea
              rows={10}
              placeholder={`请输入批次配置，每行一个批次，格式：点数,数量,描述,前缀
例如：
100,10,新用户礼包,WELCOME
50,20,春季活动,SPRING
20,50,普通充值,NORMAL`}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => setBatchModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                批量创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RedeemCodes;
