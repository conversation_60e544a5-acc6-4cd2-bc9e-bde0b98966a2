import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Typography,
  Tag,
  Avatar,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  CreditCardOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { adminService } from '../services/adminService';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const Users = () => {
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [creditForm] = Form.useForm();
  const queryClient = useQueryClient();

  // Fetch user stats
  const { data: userStatsData } = useQuery(
    'userStats',
    adminService.getUserStats
  );

  // Fetch users
  const { data: usersData, isLoading, refetch } = useQuery(
    ['users', page, pageSize, statusFilter],
    () => adminService.getUsers(page, pageSize, { status: statusFilter !== 'all' ? statusFilter : undefined }),
    {
      keepPreviousData: true,
    }
  );

  const stats = userStatsData?.data || {
    total: 0,
    active: 0,
    suspended: 0,
    deleted: 0,
  };

  const users = usersData?.data?.users || [];
  const total = usersData?.data?.total || 0;

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'suspended':
        return 'error';
      case 'deleted':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return '正常';
      case 'suspended':
        return '已暂停';
      case 'deleted':
        return '已删除';
      default:
        return status;
    }
  };

  const handleAddCredits = (user) => {
    setSelectedUser(user);
    creditForm.setFieldsValue({
      amount: 100,
      description: '管理员充值',
    });
    setCreditModalVisible(true);
  };

  // Add credits mutation
  const addCreditsMutation = useMutation(
    ({ userId, amount, description }) => adminService.addCreditsToUser(userId, amount, description),
    {
      onSuccess: () => {
        message.success(`成功为用户 ${selectedUser.username} 充值点数`);
        setCreditModalVisible(false);
        creditForm.resetFields();
        setSelectedUser(null);
        queryClient.invalidateQueries('users');
      },
      onError: (error) => {
        message.error('充值失败：' + (error.response?.data?.error || error.message));
      },
    }
  );

  // Update status mutation
  const updateStatusMutation = useMutation(
    ({ userId, status }) => adminService.updateUserStatus(userId, status),
    {
      onSuccess: () => {
        message.success('用户状态更新成功');
        queryClient.invalidateQueries('users');
        queryClient.invalidateQueries('userStats');
      },
      onError: (error) => {
        message.error('状态更新失败：' + (error.response?.data?.error || error.message));
      },
    }
  );

  const handleCreditSubmit = async (values) => {
    addCreditsMutation.mutate({
      userId: selectedUser.id,
      amount: values.amount,
      description: values.description,
    });
  };

  const handleStatusChange = async (userId, newStatus) => {
    updateStatusMutation.mutate({ userId, status: newStatus });
  };

  const columns = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} src={record.avatar} />
          <div>
            <div><Text strong>{record.nickname || record.username}</Text></div>
            <div><Text type="secondary" style={{ fontSize: 12 }}>{record.email}</Text></div>
          </div>
        </Space>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '点数余额',
      dataIndex: 'credits',
      key: 'credits',
      render: (credits) => (
        <Text strong style={{ color: credits > 0 ? '#52c41a' : '#ff4d4f' }}>
          {credits}
        </Text>
      ),
    },
    {
      title: '项目数',
      dataIndex: 'project_count',
      key: 'project_count',
    },
    {
      title: '总消费',
      dataIndex: 'total_consumed',
      key: 'total_consumed',
      render: (consumed) => <Text>{consumed} 点数</Text>,
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleDateString(),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (text) => text ? new Date(text).toLocaleDateString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<CreditCardOutlined />}
            onClick={() => handleAddCredits(record)}
          >
            充值
          </Button>
          {record.status === 'active' ? (
            <Popconfirm
              title="确定要暂停这个用户吗？"
              onConfirm={() => handleStatusChange(record.id, 'suspended')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                暂停
              </Button>
            </Popconfirm>
          ) : (
            <Button
              type="link"
              onClick={() => handleStatusChange(record.id, 'active')}
            >
              激活
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // Filter users based on search (status filter is handled by API)
  const filteredUsers = users.filter(user => {
    if (!searchText) return true;
    return user.username.toLowerCase().includes(searchText.toLowerCase()) ||
           user.email.toLowerCase().includes(searchText.toLowerCase()) ||
           (user.nickname && user.nickname.toLowerCase().includes(searchText.toLowerCase()));
  });

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>用户管理</Title>
        <Text type="secondary">管理系统中的所有用户</Text>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats.active}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="暂停用户"
              value={stats.suspended}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已删除"
              value={stats.deleted}
              valueStyle={{ color: '#1890ff' }}
              prefix={<PlusOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索用户名、邮箱或昵称"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              style={{ width: '100%' }}
              value={statusFilter}
              onChange={(value) => {
                setStatusFilter(value);
                setPage(1); // Reset to first page when filter changes
              }}
            >
              <Option value="all">全部状态</Option>
              <Option value="active">正常</Option>
              <Option value="suspended">已暂停</Option>
              <Option value="deleted">已删除</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          loading={isLoading || addCreditsMutation.isLoading || updateStatusMutation.isLoading}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (newPage, newPageSize) => {
              setPage(newPage);
              setPageSize(newPageSize);
            },
          }}
        />
      </Card>

      {/* Credit Modal */}
      <Modal
        title={`为用户 ${selectedUser?.username} 充值点数`}
        open={creditModalVisible}
        onCancel={() => setCreditModalVisible(false)}
        footer={null}
      >
        <Form
          form={creditForm}
          layout="vertical"
          onFinish={handleCreditSubmit}
        >
          <Form.Item
            name="amount"
            label="充值点数"
            rules={[
              { required: true, message: '请输入充值点数' },
              { type: 'number', min: 1, message: '点数必须大于0' },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: '100%' }}
              placeholder="请输入充值点数"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="充值说明"
            rules={[{ required: true, message: '请输入充值说明' }]}
          >
            <Input placeholder="请输入充值说明" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => setCreditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={addCreditsMutation.isLoading}>
                确认充值
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Users;
