import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  Typography,
  Space,
  Divider,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  message,
  Switch,
  Select,
} from 'antd';
import {
  SettingOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  SafetyOutlined,
  SaveOutlined,
  ReloadOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const System = () => {
  const [loading, setLoading] = useState(false);
  const [configForm] = Form.useForm();

  // Mock system data - replace with actual API calls
  const systemInfo = {
    version: '1.0.0',
    uptime: '15 天 8 小时 32 分钟',
    cpu_usage: 45,
    memory_usage: 68,
    disk_usage: 32,
    database_status: 'connected',
    redis_status: 'connected',
    ppt_generator_status: 'connected',
  };

  const systemConfig = {
    site_title: 'PPT Narrator',
    site_description: 'AI驱动的PPT讲解视频生成服务',
    default_credits: 100,
    ppt_generation_cost: 10,
    max_file_size: 104857600, // 100MB
    allowed_file_types: ['.pptx', '.ppt'],
    maintenance_mode: false,
    registration_enabled: true,
    max_concurrent_jobs: 5,
    job_timeout: 1800, // 30 minutes
  };

  const handleConfigSubmit = async (values) => {
    setLoading(true);
    try {
      // API call to update system config
      console.log('Updating system config:', values);
      message.success('系统配置更新成功');
    } catch (error) {
      message.error('配置更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSystemRestart = async () => {
    try {
      setLoading(true);
      // API call to restart system
      console.log('Restarting system...');
      message.success('系统重启命令已发送');
    } catch (error) {
      message.error('系统重启失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    return status === 'connected' ? '#52c41a' : '#ff4d4f';
  };

  const getStatusText = (status) => {
    return status === 'connected' ? '正常' : '异常';
  };

  const getProgressColor = (value) => {
    if (value < 50) return '#52c41a';
    if (value < 80) return '#faad14';
    return '#ff4d4f';
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统设置</Title>
        <Text type="secondary">管理系统配置和监控系统状态</Text>
      </div>

      <Row gutter={[16, 16]}>
        {/* System Status */}
        <Col xs={24} lg={12}>
          <Card title="系统状态" icon={<CloudServerOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="system-info">
                <div className="system-info-item">
                  <Text>系统版本:</Text>
                  <Text strong>{systemInfo.version}</Text>
                </div>
                <div className="system-info-item">
                  <Text>运行时间:</Text>
                  <Text>{systemInfo.uptime}</Text>
                </div>
                <div className="system-info-item">
                  <Text>数据库:</Text>
                  <Text style={{ color: getStatusColor(systemInfo.database_status) }}>
                    {getStatusText(systemInfo.database_status)}
                  </Text>
                </div>
                <div className="system-info-item">
                  <Text>Redis:</Text>
                  <Text style={{ color: getStatusColor(systemInfo.redis_status) }}>
                    {getStatusText(systemInfo.redis_status)}
                  </Text>
                </div>
                <div className="system-info-item">
                  <Text>PPT生成器:</Text>
                  <Text style={{ color: getStatusColor(systemInfo.ppt_generator_status) }}>
                    {getStatusText(systemInfo.ppt_generator_status)}
                  </Text>
                </div>
              </div>

              <Divider />

              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>CPU 使用率</Text>
                  <Progress
                    percent={systemInfo.cpu_usage}
                    strokeColor={getProgressColor(systemInfo.cpu_usage)}
                    size="small"
                  />
                </div>
                <div>
                  <Text>内存使用率</Text>
                  <Progress
                    percent={systemInfo.memory_usage}
                    strokeColor={getProgressColor(systemInfo.memory_usage)}
                    size="small"
                  />
                </div>
                <div>
                  <Text>磁盘使用率</Text>
                  <Progress
                    percent={systemInfo.disk_usage}
                    strokeColor={getProgressColor(systemInfo.disk_usage)}
                    size="small"
                  />
                </div>
              </Space>

              <Divider />

              <Button
                type="primary"
                danger
                icon={<ReloadOutlined />}
                onClick={handleSystemRestart}
                loading={loading}
              >
                重启系统
              </Button>
            </Space>
          </Card>
        </Col>

        {/* System Configuration */}
        <Col xs={24} lg={12}>
          <Card title="系统配置" icon={<SettingOutlined />}>
            <Form
              form={configForm}
              layout="vertical"
              onFinish={handleConfigSubmit}
              initialValues={systemConfig}
            >
              <Form.Item
                name="site_title"
                label="网站标题"
                rules={[{ required: true, message: '请输入网站标题' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="site_description"
                label="网站描述"
              >
                <TextArea rows={2} />
              </Form.Item>

              <Form.Item
                name="default_credits"
                label="新用户默认点数"
                rules={[{ required: true, message: '请输入默认点数' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="ppt_generation_cost"
                label="PPT生成消费点数"
                rules={[{ required: true, message: '请输入消费点数' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="max_file_size"
                label="最大文件大小 (字节)"
                rules={[{ required: true, message: '请输入最大文件大小' }]}
              >
                <InputNumber min={1024} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="max_concurrent_jobs"
                label="最大并发任务数"
                rules={[{ required: true, message: '请输入最大并发任务数' }]}
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="job_timeout"
                label="任务超时时间 (秒)"
                rules={[{ required: true, message: '请输入任务超时时间' }]}
              >
                <InputNumber min={60} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="maintenance_mode"
                label="维护模式"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="registration_enabled"
                label="允许用户注册"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                  block
                >
                  保存配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* System Alerts */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="系统警告" icon={<SafetyOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {systemInfo.memory_usage > 80 && (
                <Alert
                  message="内存使用率过高"
                  description="当前内存使用率超过80%，建议检查系统负载或增加内存容量。"
                  type="warning"
                  showIcon
                />
              )}
              
              {systemInfo.cpu_usage > 90 && (
                <Alert
                  message="CPU使用率过高"
                  description="当前CPU使用率超过90%，系统可能响应缓慢。"
                  type="error"
                  showIcon
                />
              )}
              
              {systemInfo.disk_usage > 85 && (
                <Alert
                  message="磁盘空间不足"
                  description="磁盘使用率超过85%，请及时清理磁盘空间或扩容。"
                  type="warning"
                  showIcon
                />
              )}

              {systemInfo.database_status !== 'connected' && (
                <Alert
                  message="数据库连接异常"
                  description="数据库连接状态异常，请检查数据库服务。"
                  type="error"
                  showIcon
                />
              )}

              {systemInfo.redis_status !== 'connected' && (
                <Alert
                  message="Redis连接异常"
                  description="Redis连接状态异常，缓存功能可能受影响。"
                  type="warning"
                  showIcon
                />
              )}

              {systemConfig.maintenance_mode && (
                <Alert
                  message="维护模式已启用"
                  description="系统当前处于维护模式，用户无法正常使用服务。"
                  type="info"
                  showIcon
                />
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default System;
