import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Table,
  Tag,
  Progress,
  Space,
  Button,
} from 'antd';
import {
  UserOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  GiftOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { useQuery } from 'react-query';
import { adminService } from '../services/adminService';

const { Title, Text } = Typography;

const Dashboard = () => {
  // Fetch dashboard stats
  const { data: dashboardData, isLoading } = useQuery(
    'dashboardStats',
    adminService.getDashboardStats,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const stats = dashboardData?.data || {
    users: { total: 0, active: 0 },
    projects: { total: 0, completed: 0 },
    credits: { total_issued: 0, total_consumed: 0 },
    redeem_codes: { active: 0, used: 0 },
  };

  const recentProjects = [
    {
      id: '1',
      name: '季度业务汇报',
      user: 'user123',
      status: 'completed',
      created_at: '2024-01-15T10:30:00Z',
      progress: 100,
    },
    {
      id: '2',
      name: '产品介绍演示',
      user: 'user456',
      status: 'processing',
      created_at: '2024-01-14T15:20:00Z',
      progress: 65,
    },
    {
      id: '3',
      name: '团队培训材料',
      user: 'user789',
      status: 'failed',
      created_at: '2024-01-13T09:15:00Z',
      progress: 0,
    },
  ];

  const chartData = [
    { name: '1月', users: 400, projects: 240 },
    { name: '2月', users: 300, projects: 139 },
    { name: '3月', users: 200, projects: 980 },
    { name: '4月', users: 278, projects: 390 },
    { name: '5月', users: 189, projects: 480 },
    { name: '6月', users: 239, projects: 380 },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      default:
        return status;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'failed':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Space direction="vertical" size={0}>
          <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
            {getStatusText(status)}
          </Tag>
          {status === 'processing' && (
            <Progress percent={record.progress} size="small" showInfo={false} />
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统概览</Title>
        <Text type="secondary">PPT Narrator 管理后台数据统计</Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="dashboard-stats">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.users?.total || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats.users?.active || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={stats.projects?.total || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="完成项目"
              value={stats.projects?.completed || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已发放点数"
              value={stats.credits?.total_issued || 0}
              prefix={<CreditCardOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已消费点数"
              value={stats.credits?.total_consumed || 0}
              prefix={<CreditCardOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃兑换码"
              value={stats.redeem_codes?.active || 0}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已用兑换码"
              value={stats.redeem_codes?.used || 0}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        {/* Charts */}
        <Col xs={24} lg={12}>
          <Card title="用户增长趋势">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="users" stroke="#1890ff" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="项目创建统计">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="projects" fill="#52c41a" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Recent Projects */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24}>
          <Card
            title="最近项目"
            extra={
              <Button type="link" onClick={() => window.location.href = '/projects'}>
                查看全部
              </Button>
            }
          >
            <Table
              columns={projectColumns}
              dataSource={recentProjects}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
