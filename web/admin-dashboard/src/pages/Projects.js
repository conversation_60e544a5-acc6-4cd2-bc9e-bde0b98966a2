import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Tooltip,
  Popconfirm,
  message,
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const Projects = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - replace with actual API calls
  const stats = {
    totalProjects: 5678,
    processingProjects: 45,
    completedProjects: 4321,
    failedProjects: 234,
  };

  const projects = [
    {
      id: '1',
      name: '季度业务汇报',
      user_id: 'user123',
      username: 'user123',
      status: 'completed',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T11:45:00Z',
      credits_cost: 15,
      slide_count: 20,
      progress: 100,
      current_stage: 'completed',
      original_file_name: '季度业务汇报.pptx',
      file_size: 3145728,
      processing_time_ms: 180000,
    },
    {
      id: '2',
      name: '产品介绍演示',
      user_id: 'user456',
      username: 'user456',
      status: 'processing',
      created_at: '2024-01-14T15:20:00Z',
      updated_at: '2024-01-14T15:25:00Z',
      credits_cost: 12,
      slide_count: 18,
      progress: 65,
      current_stage: 'narration',
      original_file_name: '产品介绍演示.pptx',
      file_size: 2621440,
      processing_time_ms: null,
    },
    {
      id: '3',
      name: '团队培训材料',
      user_id: 'user789',
      username: 'user789',
      status: 'failed',
      created_at: '2024-01-13T09:15:00Z',
      updated_at: '2024-01-13T09:20:00Z',
      credits_cost: 0,
      slide_count: 0,
      progress: 0,
      current_stage: 'screenshot',
      original_file_name: '团队培训材料.pptx',
      file_size: 1572864,
      error_message: '文件格式不支持',
      processing_time_ms: null,
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      case 'created':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      case 'created':
        return '已创建';
      default:
        return status;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'failed':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getStageText = (stage) => {
    switch (stage) {
      case 'screenshot':
        return '截图处理';
      case 'narration':
        return '生成讲解';
      case 'tts':
        return '语音合成';
      case 'video':
        return '视频合成';
      case 'completed':
        return '已完成';
      default:
        return stage;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatProcessingTime = (ms) => {
    if (!ms) return '-';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const handleDelete = async (projectId) => {
    try {
      setLoading(true);
      // API call to delete project
      console.log('Deleting project:', projectId);
      message.success('项目删除成功');
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = async (projectId) => {
    try {
      setLoading(true);
      // API call to retry project
      console.log('Retrying project:', projectId);
      message.success('项目重新处理中...');
    } catch (error) {
      message.error('重试失败');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '项目信息',
      key: 'project',
      render: (_, record) => (
        <div>
          <div><Text strong>{record.name}</Text></div>
          <div><Text type="secondary" style={{ fontSize: 12 }}>{record.original_file_name}</Text></div>
          <div><Text type="secondary" style={{ fontSize: 12 }}>用户: {record.username}</Text></div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Space direction="vertical" size={0}>
          <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
            {getStatusText(status)}
          </Tag>
          {status === 'processing' && (
            <div>
              <Progress percent={record.progress} size="small" showInfo={false} />
              <Text type="secondary" style={{ fontSize: 11 }}>
                {getStageText(record.current_stage)}
              </Text>
            </div>
          )}
          {status === 'failed' && record.error_message && (
            <Tooltip title={record.error_message}>
              <Text type="danger" style={{ fontSize: 11 }}>
                {record.error_message.length > 15 
                  ? record.error_message.substring(0, 15) + '...'
                  : record.error_message
                }
              </Text>
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '文件信息',
      key: 'file_info',
      render: (_, record) => (
        <div>
          <div><Text>幻灯片: {record.slide_count || '-'}</Text></div>
          <div><Text>大小: {formatFileSize(record.file_size)}</Text></div>
          <div><Text>耗时: {formatProcessingTime(record.processing_time_ms)}</Text></div>
        </div>
      ),
    },
    {
      title: '消费点数',
      dataIndex: 'credits_cost',
      key: 'credits_cost',
      render: (cost) => (
        <Text strong style={{ color: cost > 0 ? '#ff4d4f' : '#666' }}>
          {cost > 0 ? `${cost} 点数` : '-'}
        </Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => console.log('View project:', record.id)}
          >
            查看
          </Button>
          {record.status === 'failed' && (
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={() => handleRetry(record.id)}
            >
              重试
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个项目吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Filter projects based on search and status
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         project.username.toLowerCase().includes(searchText.toLowerCase()) ||
                         project.original_file_name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>项目监控</Title>
        <Text type="secondary">监控和管理所有用户的PPT项目</Text>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={stats.totalProjects}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.processingProjects}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completedProjects}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats.failedProjects}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索项目名称、用户或文件名"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              style={{ width: '100%' }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="all">全部状态</Option>
              <Option value="created">已创建</Option>
              <Option value="processing">处理中</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Col>
          <Col xs={24} sm={24} md={10} style={{ textAlign: 'right' }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredProjects}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredProjects.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );
};

export default Projects;
