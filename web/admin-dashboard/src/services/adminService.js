import api from './api';

export const adminService = {
  // User management
  getUsers: async (page = 1, pageSize = 20, filters = {}) => {
    const response = await api.get('/admin/users', {
      params: { page, page_size: pageSize, ...filters }
    });
    return response.data;
  },

  getUserStats: async () => {
    const response = await api.get('/admin/users/stats');
    return response.data;
  },

  addCreditsToUser: async (userId, amount, description) => {
    const response = await api.post('/admin/credits/add', {
      user_id: userId,
      amount,
      description,
    });
    return response.data;
  },

  updateUserStatus: async (userId, status) => {
    const response = await api.put(`/admin/users/${userId}/status`, {
      status,
    });
    return response.data;
  },

  // Redeem code management
  getRedeemCodes: async (page = 1, pageSize = 20, filters = {}) => {
    const response = await api.get('/admin/redeem-codes', {
      params: { page, page_size: pageSize, ...filters }
    });
    return response.data;
  },

  createRedeemCode: async (data) => {
    const response = await api.post('/admin/redeem-codes', data);
    return response.data;
  },

  createBatchRedeemCodes: async (batches) => {
    const response = await api.post('/admin/redeem-codes/batch', {
      batches,
    });
    return response.data;
  },

  updateRedeemCode: async (codeId, data) => {
    const response = await api.put(`/admin/redeem-codes/${codeId}`, data);
    return response.data;
  },

  deleteRedeemCode: async (codeId) => {
    const response = await api.delete(`/admin/redeem-codes/${codeId}`);
    return response.data;
  },

  getRedeemCodeStats: async () => {
    const response = await api.get('/admin/redeem-codes/stats');
    return response.data;
  },

  // Project management
  getProjects: async (page = 1, pageSize = 20, filters = {}) => {
    const response = await api.get('/admin/projects', {
      params: { page, page_size: pageSize, ...filters }
    });
    return response.data;
  },

  getProjectStats: async () => {
    const response = await api.get('/admin/projects/stats');
    return response.data;
  },

  deleteProject: async (projectId) => {
    const response = await api.delete(`/admin/projects/${projectId}`);
    return response.data;
  },

  retryProject: async (projectId) => {
    const response = await api.post(`/admin/projects/${projectId}/retry`);
    return response.data;
  },

  // System management
  getSystemStats: async () => {
    const response = await api.get('/admin/system/stats');
    return response.data;
  },

  getSystemConfig: async () => {
    const response = await api.get('/admin/system/config');
    return response.data;
  },

  updateSystemConfig: async (config) => {
    const response = await api.put('/admin/system/config', config);
    return response.data;
  },

  restartSystem: async () => {
    const response = await api.post('/admin/system/restart');
    return response.data;
  },

  // Dashboard data
  getDashboardStats: async () => {
    const response = await api.get('/admin/dashboard/stats');
    return response.data;
  },

  getDashboardChartData: async (type, period = '7d') => {
    const response = await api.get('/admin/dashboard/charts', {
      params: { type, period }
    });
    return response.data;
  },
};
