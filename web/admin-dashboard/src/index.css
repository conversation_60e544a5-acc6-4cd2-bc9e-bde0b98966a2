body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Admin specific styles */
.admin-header {
  background: #001529 !important;
  color: white;
}

.admin-sider {
  background: #001529 !important;
}

.admin-menu {
  background: #001529 !important;
  border-right: none !important;
}

.admin-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.admin-menu .ant-menu-item:hover {
  background-color: #1890ff !important;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Ant Design customizations for admin */
.ant-layout-sider-collapsed .ant-menu-item-icon {
  font-size: 16px;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-btn {
  border-radius: 6px;
}

.ant-input {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
}

/* Statistics cards */
.ant-statistic-content {
  font-size: 20px;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

/* Table customizations */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* Progress bars */
.ant-progress-line {
  margin-bottom: 8px;
}

.ant-progress-text {
  font-size: 12px;
}
