import React, { createContext, useContext, useState, useEffect } from 'react';
import { message } from 'antd';
import api from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(localStorage.getItem('admin_token'));

  // Set token in API headers
  useEffect(() => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // Load user profile
      loadUserProfile();
    } else {
      delete api.defaults.headers.common['Authorization'];
      setLoading(false);
    }
  }, [token]);

  const loadUserProfile = async () => {
    try {
      const response = await api.get('/auth/profile');
      if (response.data.success) {
        const userData = response.data.data.user;
        // Only allow admin users
        if (userData.role === 'admin') {
          setUser(userData);
        } else {
          throw new Error('Access denied: Admin privileges required');
        }
      } else {
        throw new Error(response.data.error);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
      // If token is invalid or user is not admin, clear it
      if (error.response?.status === 401 || error.message.includes('Admin privileges')) {
        logout();
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);
      if (response.data.success) {
        const { token: newToken, user: userData } = response.data.data;
        
        // Check if user is admin
        if (userData.role !== 'admin') {
          message.error('权限不足：需要管理员权限');
          return { success: false, error: 'Admin privileges required' };
        }
        
        setToken(newToken);
        setUser(userData);
        localStorage.setItem('admin_token', newToken);
        api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
        message.success('登录成功');
        return { success: true };
      } else {
        throw new Error(response.data.error);
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || '登录失败';
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('admin_token');
    delete api.defaults.headers.common['Authorization'];
    message.success('已退出登录');
  };

  const refreshUserData = async () => {
    if (token) {
      await loadUserProfile();
    }
  };

  const value = {
    user,
    loading,
    token,
    login,
    logout,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
