.App {
  text-align: center;
}

/* Login page styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #1890ff;
  font-size: 24px;
  font-weight: bold;
}

/* Dashboard styles */
.dashboard-stats {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* Chart container */
.chart-container {
  height: 300px;
  width: 100%;
}

/* Table styles */
.table-actions {
  display: flex;
  gap: 8px;
}

.status-tag {
  text-transform: capitalize;
}

/* Form styles */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
}

/* Code display */
.code-display {
  font-family: 'Courier New', monospace;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

/* Batch operations */
.batch-operations {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

/* Statistics cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  padding: 20px;
  text-align: center;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-card-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stats-card-label {
  font-size: 14px;
  color: #666;
}

/* System info */
.system-info {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.system-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.system-info-item:last-child {
  border-bottom: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .login-form {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .dashboard-stats .ant-col {
    margin-bottom: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .table-actions {
    flex-direction: column;
    gap: 4px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .system-info {
    background: #1f1f1f;
    color: #fff;
  }
  
  .system-info-item {
    border-bottom-color: #333;
  }
}
