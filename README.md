# PPT Narrator

一个智能PPT讲解服务，能够自动为PowerPoint演示文稿生成讲述稿并合成讲解视频。

## 功能特性

### 🎯 核心功能
1. **PPT上传与截图生成** - 上传pptx文件，自动生成每页截图
2. **AI智能讲述稿生成** - 基于PPT内容和用户需求生成专业讲述稿
3. **文本转语音** - 将讲述稿转换为自然语音
4. **视频合成** - 使用FFmpeg将截图和语音合成为完整视频

### 🧠 AI特性
- **多AI提供商支持** - 支持OpenAI和MiniMax等多种AI服务
- **环境变量配置系统提示** - 可通过环境变量自定义AI行为
- **智能记忆服务** - AI可记录和使用上下文信息
- **连贯性保证** - 确保多页面讲述的逻辑连贯性
- **个性化定制** - 支持不同讲述风格和语言

### 💾 数据存储
- **内存存储** - 所有数据存储在内存中，重启后清空
- **无数据库依赖** - 简化部署，适合快速原型和测试

## 快速开始

### 环境要求
- Go 1.21+
- LibreOffice (用于PPT转换)
- FFmpeg (用于视频合成和音频处理)
- AI服务API Key：
  - OpenAI API Key (如果使用OpenAI)
  - MiniMax API Key (如果使用MiniMax)
  - MiniMax TTS API Key (如果使用MiniMax TTS)
  - EasyVoice 用户名和密码 (如果使用EasyVoice TTS)

### 🚀 EasyVoice TTS 快速启动（推荐中文用户）

如果你想使用 EasyVoice TTS 功能，可以使用我们提供的快速启动脚本：

#### Windows 用户
```cmd
# 1. 复制配置文件
copy .env.easyvoice.example .env

# 2. 编辑 .env 文件，设置你的凭据
# EASYVOICE_USERNAME=your_username
# EASYVOICE_PASSWORD=your_password
# OPENAI_API_KEY=your_openai_key

# 3. 运行快速启动脚本
tools\start-with-easyvoice.bat
```

#### Linux/macOS 用户
```bash
# 1. 复制配置文件
cp .env.easyvoice.example .env

# 2. 编辑 .env 文件，设置你的凭据
# EASYVOICE_USERNAME=your_username
# EASYVOICE_PASSWORD=your_password
# OPENAI_API_KEY=your_openai_key

# 3. 运行快速启动脚本
./tools/start-with-easyvoice.sh
```

### 🚀 MiniMax TTS 快速启动

如果你想使用 MiniMax TTS 功能，可以使用我们提供的快速启动脚本：

#### Windows 用户
```bash
# 1. 设置 MiniMax API 凭据
set MINIMAX_TTS_API_KEY=your_api_key_here
set MINIMAX_TTS_GROUP_ID=your_group_id_here

# 2. 运行快速启动脚本
start_with_minimax.bat
```

#### Linux/macOS 用户
```bash
# 1. 设置 MiniMax API 凭据
export MINIMAX_TTS_API_KEY=your_api_key_here
export MINIMAX_TTS_GROUP_ID=your_group_id_here

# 2. 设置 TTS 提供商
export TTS_PROVIDER=minimax

# 3. 启动服务
go run cmd/server/main.go
```

### 🧪 测试 MiniMax TTS 功能

```bash
# Windows
test_minimax_tts.bat

# Linux/macOS
chmod +x test_minimax_tts.sh
./test_minimax_tts.sh
```

### 安装依赖

#### Ubuntu/Debian
```bash
# 安装LibreOffice
sudo apt update
sudo apt install libreoffice

# 安装FFmpeg
sudo apt install ffmpeg

# 验证安装
libreoffice --version
ffmpeg -version
```

#### macOS
```bash
# 使用Homebrew安装
brew install --cask libreoffice
brew install ffmpeg

# 验证安装
libreoffice --version
ffmpeg -version
```

#### Windows
1. 下载并安装 [LibreOffice](https://www.libreoffice.org/download/download/)
2. 下载并安装 [FFmpeg](https://ffmpeg.org/download.html)
3. 确保两个程序都在系统PATH中

### 配置环境变量

创建 `.env` 文件或设置环境变量：

```bash
# 服务器配置
PORT=8080

# 目录配置
UPLOAD_DIR=./uploads
SCREENSHOT_DIR=./screenshots
VIDEO_DIR=./videos
TEMP_DIR=./temp

# AI提供商配置
AI_PROVIDER=openai  # 或 minimax

# OpenAI配置（如果使用OpenAI）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_BASE_URL=https://api.openai.com/v1

# MiniMax配置（如果使用MiniMax）
MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_group_id_here
MINIMAX_MODEL=abab6.5s-chat
MINIMAX_BASE_URL=https://api.minimaxi.com/v1/text/chatcompletion_v2

# TTS配置
TTS_PROVIDER=minimax  # 支持: openai, minimax, easyvoice (默认: minimax)
TTS_VOICE=alloy
TTS_SPEED=1.0

# MiniMax TTS配置（如果使用MiniMax TTS）
MINIMAX_TTS_API_KEY=your_minimax_tts_api_key_here
MINIMAX_TTS_GROUP_ID=your_minimax_tts_group_id_here
MINIMAX_TTS_MODEL=speech-02-hd
MINIMAX_TTS_VOICE_ID=male-qn-qingse
MINIMAX_TTS_EMOTION=happy

# EasyVoice TTS配置（如果使用EasyVoice TTS）
EASYVOICE_API_URL=https://easyvoice.wetolink.com/api/v1/tts/generate
EASYVOICE_USERNAME=your_username_here
EASYVOICE_PASSWORD=your_password_here
EASYVOICE_VOICE=zh-CN-YunxiNeural
EASYVOICE_RATE=0%
EASYVOICE_PITCH=0Hz
EASYVOICE_VOLUME=0%

# 系统提示词（可选）
SYSTEM_PROMPT="你是一个专业的PPT讲解专家..."

# 字幕配置 - 新功能！
SUBTITLE_ENABLED=false              # 全局默认是否启用字幕
SUBTITLE_FONT_SIZE=24              # 字幕字体大小（像素）
SUBTITLE_FONT_COLOR=#FFFFFF        # 字幕字体颜色（十六进制）
SUBTITLE_BACKGROUND_COLOR=         # 字幕背景颜色（留空表示无背景）
SUBTITLE_POSITION=bottom           # 字幕位置：bottom/top/center
SUBTITLE_FONT_FAMILY=Arial         # 字幕字体族
SUBTITLE_OUTLINE=2                 # 字幕轮廓宽度（像素）
SUBTITLE_SHADOW=true               # 是否添加字幕阴影

# 工具路径（如果不在PATH中）
LIBREOFFICE_PATH=libreoffice
FFMPEG_PATH=ffmpeg
```

### 运行服务

```bash
# 克隆项目
git clone <repository-url>
cd ppt-narrator

# 安装Go依赖
go mod tidy

# 运行服务
go run cmd/server/main.go
```

服务将在 `http://localhost:8080` 启动

## 🔐 数字员工平台集成

### MCP Pipeline Server

本项目包含一个MCP Pipeline Server，支持与数字员工平台的集成，提供以下功能：

- **双重鉴权支持**：支持传统访问密钥和数字员工官方Bearer Token鉴权
- **透明文件处理**：自动处理文件上传下载到数字员工平台
- **详细日志记录**：记录所有HTTP请求的详细信息

### 启用数字员工鉴权

#### 方式1：使用环境变量

```bash
# 启用数字员工集成
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export DSTAFF_ENDPOINT_URL=http://*********:8800
export MCP_DEBUG=true

# 启动MCP Pipeline Server
docker-compose up old-mcp-pipeline-server
```

#### 方式2：使用配置文件

```bash
# 复制配置模板
cp .env.dstaff .env

# 编辑配置文件
# 设置 DSTAFF_ENABLED=true
# 设置 DSTAFF_USE_OFFICIAL_AUTH=true

# 启动服务
docker-compose --env-file .env up old-mcp-pipeline-server
```

#### 方式3：使用快速启动脚本

```bash
# Windows
start-with-dstaff.bat

# Linux/Mac
chmod +x start-with-dstaff.sh
./start-with-dstaff.sh
```

### 鉴权模式

#### 数字员工官方鉴权（推荐生产环境）
- 所有请求需要Bearer Token
- Token通过数字员工API验证
- 支持透明文件上传下载

```bash
curl -H "Authorization: Bearer your_token_here" \
     http://localhost:48080/
```

#### 传统鉴权（向后兼容）
- 仅/sse端点需要访问密钥
- 其他端点无需鉴权

```bash
curl "http://localhost:48080/sse?key=test123"
```

### 可用的MCP工具

- `upload_file`: 上传PPT文件并开始生成解说视频
- `get_progress`: 查询PPT解说视频生成任务的进度
- `get_download_url`: 获取已完成的PPT解说视频的下载链接

### 透明文件处理

当启用数字员工集成时，系统会自动处理：

- **文件上传**：POST请求带multipart/form-data自动上传到数字员工平台
- **文件下载**：GET /download/{task_id}/{file_path}自动从数字员工平台下载

详细配置说明请参考：[MCP Pipeline Server文档](./mcp-pipeline-server/README.md)

## API文档

### 项目管理

#### 上传PPT文件
```http
POST /api/v1/projects/upload
Content-Type: multipart/form-data

name: "我的演示"
description: "项目描述"
file: [PPT文件]
```

#### 获取项目列表
```http
GET /api/v1/projects/
```

#### 获取项目详情
```http
GET /api/v1/projects/{projectId}
```

#### 获取处理进度
```http
GET /api/v1/projects/{projectId}/progress
```

#### 获取截图
```http
GET /api/v1/projects/{projectId}/screenshots/{slideNumber}
```

### 讲述稿生成

#### 生成讲述稿
```http
POST /api/v1/narration/{projectId}/generate
Content-Type: application/json

{
  "user_requirements": "请生成专业的技术讲解，适合工程师听众",
  "style": "professional",
  "language": "zh-CN"
}
```

#### 获取生成进度
```http
GET /api/v1/narration/{projectId}/progress
```

#### 获取讲述稿
```http
GET /api/v1/narration/{projectId}
```

### 记忆管理

#### 添加记忆
```http
POST /api/v1/memory/{projectId}
Content-Type: application/json

{
  "key": "target_audience",
  "value": "技术工程师",
  "type": "context"
}
```

#### 获取所有记忆
```http
GET /api/v1/memory/{projectId}
```

#### 更新记忆
```http
PUT /api/v1/memory/{projectId}/{key}
Content-Type: application/json

{
  "value": "新的值"
}
```

### 音频生成

#### 生成音频
```http
POST /api/v1/audio/{projectId}/generate
```

#### 获取音频进度
```http
GET /api/v1/audio/{projectId}/progress
```

#### 下载音频
```http
GET /api/v1/audio/{projectId}
```

### 视频生成

#### 生成视频
```http
POST /api/v1/video/{projectId}/generate
Content-Type: application/json

{
  "output_format": "mp4",
  "quality": "high",
  "fps": 1,
  "resolution": "1920x1080"
}
```

#### 获取视频进度
```http
GET /api/v1/video/{projectId}/progress
```

#### 下载视频
```http
GET /api/v1/video/{projectId}
```

### 系统信息

#### 获取AI提供商信息
```http
GET /api/v1/system/ai/info
```

#### 验证FFmpeg
```http
GET /api/v1/system/ffmpeg/validate
```

## 使用流程

### 完整工作流程

1. **上传PPT文件**
   ```bash
   curl -X POST http://localhost:8080/api/v1/projects/upload \
     -F "name=我的演示" \
     -F "description=测试项目" \
     -F "file=@presentation.pptx"
   ```

2. **等待截图生成完成**
   ```bash
   curl http://localhost:8080/api/v1/projects/{projectId}/progress
   ```

3. **添加记忆信息（可选）**
   ```bash
   curl -X POST http://localhost:8080/api/v1/memory/{projectId} \
     -H "Content-Type: application/json" \
     -d '{"key": "target_audience", "value": "技术工程师"}'
   ```

4. **生成讲述稿**
   ```bash
   curl -X POST http://localhost:8080/api/v1/narration/{projectId}/generate \
     -H "Content-Type: application/json" \
     -d '{"user_requirements": "请生成专业的技术讲解"}'
   ```

5. **生成音频**
   ```bash
   curl -X POST http://localhost:8080/api/v1/audio/{projectId}/generate
   ```

6. **生成最终视频**
   ```bash
   curl -X POST http://localhost:8080/api/v1/video/{projectId}/generate
   ```

7. **下载视频**
   ```bash
   curl -O http://localhost:8080/api/v1/video/{projectId}
   ```

## 配置说明

### 系统提示词配置

通过 `SYSTEM_PROMPT` 环境变量可以自定义AI的行为：

```bash
export SYSTEM_PROMPT="你是一个专业的PPT讲解专家，擅长为PPT内容创建生动、有趣且富有教育意义的讲述稿。

你的任务是：
1. 仔细分析每一页PPT的内容
2. 生成自然流畅的讲述稿
3. 确保前后连贯，逻辑清晰
4. 适合目标听众

请根据用户要求和记忆中的上下文信息生成专业的讲述稿。"
```

### TTS配置

支持多种TTS提供商：

- **OpenAI TTS**: 高质量，支持多种语音
- **Azure TTS**: 企业级，多语言支持
- **本地TTS**: 离线使用，隐私保护

### 记忆系统

AI记忆系统支持以下类型：

- `context`: 上下文信息
- `preference`: 用户偏好
- `fact`: 事实信息
- `requirement`: 需求信息

## 故障排除

### 常见问题

1. **LibreOffice转换失败**
   - 确保LibreOffice已正确安装
   - 检查文件权限
   - 尝试手动转换测试

2. **FFmpeg视频生成失败**
   - 验证FFmpeg安装：`ffmpeg -version`
   - 检查输出目录权限
   - 查看错误日志

3. **OpenAI API调用失败**
   - 验证API Key是否正确
   - 检查网络连接
   - 确认API配额

4. **MiniMax TTS音频生成失败**
   - 验证MiniMax TTS API Key和Group ID
   - 检查网络连接到MiniMax服务
   - 确认FFmpeg正确安装
   - 检查临时目录写权限

### 日志查看

服务运行时会输出详细日志，包括：
- 请求处理日志
- 错误信息
- 处理进度

## 🎵 MiniMax TTS 音频生成功能

### 功能特性

1. **智能停顿处理**
   - 自动识别讲稿中的停顿标记：`[停顿1秒]`、`[停顿2秒]`等
   - 将讲稿分割成多个语音片段
   - 在指定位置插入相应时长的静音

2. **高质量语音合成**
   - 支持MiniMax同步语音合成API
   - 20+种音色选择（男声、女声、主持人、有声书等）
   - 7种情感控制（高兴、悲伤、愤怒、惊讶等）
   - 4种模型选择（HD、Turbo、稳定版等）

3. **专业音频处理**
   - 使用FFmpeg进行音频合并
   - 支持多种音频格式和采样率
   - 自动生成完整的讲解音频文件

### 配置示例

```bash
# 基础配置
TTS_PROVIDER=minimax
MINIMAX_TTS_API_KEY=your_api_key
MINIMAX_TTS_GROUP_ID=your_group_id

# 音色和风格配置
MINIMAX_TTS_MODEL=speech-02-hd          # HD高质量模型
MINIMAX_TTS_VOICE_ID=female-tianmei     # 甜美女性音色
MINIMAX_TTS_EMOTION=happy               # 高兴情感
TTS_SPEED=1.0                           # 正常语速
```

### 音频生成流程

1. **文本解析** - 识别讲稿中的停顿标记
2. **片段分割** - 将讲稿分成文本片段和停顿片段
3. **音频生成** - 为每个文本片段调用MiniMax TTS API
4. **静音生成** - 为每个停顿标记生成对应时长的静音
5. **音频合并** - 使用FFmpeg按顺序串联所有音频片段
6. **输出完整音频** - 生成最终的讲解音频文件

### 详细文档

更多详细信息请参考：
- [MiniMax TTS 使用指南](./MINIMAX_TTS_GUIDE.md)
- [配置示例文件](./.env.minimax.example)

## 🔄 步骤隔离和错误恢复

### 功能特性

系统采用步骤隔离设计，确保每个步骤的失败不会影响前面已成功的步骤：

1. **截图生成** → **讲稿生成** → **音频生成** → **视频生成**
2. 每个步骤独立，失败时不会回滚前面的成功状态
3. 支持从任何失败点重新开始，无需重复前面的步骤

### 状态管理

- `completed` - 截图生成完成
- `narration_ready` - 讲稿生成完成
- `narration_failed` - 讲稿生成失败（可重试，不影响截图）
- `audio_ready` - 音频生成完成
- `audio_failed` - 音频生成失败（可重试，不影响讲稿）
- `video_ready` - 视频生成完成
- `video_failed` - 视频生成失败（可重试，不影响音频）

### 错误恢复示例

```bash
# 场景：音频生成失败
# 1. 讲稿已生成成功 (narration_ready)
# 2. 音频生成失败 (audio_failed)
# 3. 修复配置后重试音频生成
curl -X POST http://localhost:8080/api/v1/audio/{project_id}/generate

# 系统会：
# - 检查讲稿是否存在（✅ 仍然可用）
# - 重新生成音频，不影响已有讲稿
# - 成功后状态变为 audio_ready
```

### 测试步骤隔离

```bash
# 运行步骤隔离测试
go run test_step_isolation.go

# 运行API测试
# Windows
test_api_step_isolation.bat

# Linux/macOS
chmod +x test_api_step_isolation.sh
./test_api_step_isolation.sh
```

## 开发

### 项目结构
```
ppt-narrator/
├── backend/            # Go后端服务
│   ├── cmd/server/     # 主程序入口
│   ├── internal/       # 内部包
│   │   ├── config/     # 配置管理
│   │   ├── models/     # 数据模型
│   │   ├── services/   # 业务逻辑
│   │   ├── handlers/   # HTTP处理器
│   │   └── utils/      # 工具函数
│   ├── go.mod          # Go模块文件
│   └── go.sum          # Go依赖锁定文件
├── mcp/                # MCP Pipeline服务
│   ├── internal/       # MCP内部包
│   ├── examples/       # 使用示例
│   └── main.go         # MCP主程序
├── docs/               # 项目文档
│   ├── API_DOCUMENTATION.md
│   ├── DOCKER_SETUP.md
│   └── ...             # 其他文档
├── tests/              # 测试文件
│   ├── test_*.go       # Go测试文件
│   ├── test_*.py       # Python测试脚本
│   └── test_*.sh       # Shell测试脚本
├── tools/              # 工具脚本
│   ├── build-docker.*  # Docker构建脚本
│   ├── start-with-*.*  # 启动脚本
│   └── *.py            # Python工具
├── scripts/            # 部署脚本
├── uploads/            # 上传文件目录
├── screenshots/        # 截图目录
├── videos/             # 视频输出目录
├── temp/               # 临时文件目录
├── docker-compose.yml  # Docker编排文件
├── Dockerfile          # Docker镜像构建文件
├── Makefile            # 构建配置
└── README.md           # 项目说明
```

### 扩展开发

1. **添加新的TTS提供商**
   - 在 `tts_service.go` 中添加新的提供商实现
   - 更新配置选项

2. **自定义AI模型**
   - 修改 `ai_service.go` 中的模型调用
   - 支持本地模型部署

3. **增强记忆系统**
   - 扩展记忆类型
   - 添加记忆搜索功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
