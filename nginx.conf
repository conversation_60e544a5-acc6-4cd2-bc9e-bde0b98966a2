events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;  # Allow large PPT file uploads

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

    # Upstream backends
    upstream ppt_narrator_backend {
        server ppt-narrator:8080;
        keepalive 32;
    }

    upstream business_api {
        server business-api:8081;
        keepalive 32;
    }

    upstream user_frontend {
        server user-frontend:80;
        keepalive 16;
    }

    upstream admin_dashboard {
        server admin-dashboard:80;
        keepalive 16;
    }

    server {
        listen 80;
        server_name localhost;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Health check endpoint
        location /health {
            proxy_pass http://ppt_narrator_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Business API endpoints with rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;

            proxy_pass http://business_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Timeouts for long-running operations
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        # Upload endpoints with stricter rate limiting
        location /api/v1/projects/upload-and-process {
            limit_req zone=upload burst=5 nodelay;

            proxy_pass http://business_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Extended timeouts for file uploads and processing
            proxy_connect_timeout 60s;
            proxy_send_timeout 1800s;  # 30 minutes
            proxy_read_timeout 1800s;  # 30 minutes

            # Large file upload settings
            client_max_body_size 100M;
            client_body_timeout 300s;
        }

        # Static file serving with caching
        location /uploads/ {
            alias /var/www/uploads/;
            expires 1h;
            add_header Cache-Control "public, immutable";
        }

        location /screenshots/ {
            alias /var/www/screenshots/;
            expires 1h;
            add_header Cache-Control "public, immutable";
        }

        location /videos/ {
            alias /var/www/videos/;
            expires 1h;
            add_header Cache-Control "public, immutable";
            
            # Enable range requests for video streaming
            add_header Accept-Ranges bytes;
        }

        # Admin dashboard
        location /admin/ {
            proxy_pass http://admin_dashboard/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Original PPT generator (for backward compatibility)
        location /generate {
            proxy_pass http://ppt_narrator_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Extended timeouts for processing
            proxy_connect_timeout 60s;
            proxy_send_timeout 1800s;
            proxy_read_timeout 1800s;
        }

        # User frontend (default)
        location / {
            proxy_pass http://user_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTPS server (uncomment and configure SSL certificates)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_session_timeout 1d;
    #     ssl_session_cache shared:MozTLS:10m;
    #     ssl_session_tickets off;
    #
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=63072000" always;
    #
    #     # Same location blocks as HTTP server
    #     # ... (copy from above)
    # }
}
