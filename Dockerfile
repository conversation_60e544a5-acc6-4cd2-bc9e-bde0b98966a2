# Build stage
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY backend/go.mod backend/go.sum ./

# Download dependencies (this layer will be cached if go.mod/go.sum don't change)
RUN go mod download && go mod verify

# Copy source code
COPY backend/ .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main cmd/server/main.go

# Verify the binary (just check if it exists and is executable)
RUN ls -la ./main && echo "Binary built successfully"

# Runtime stage
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=UTC \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh

# Use USTC mirror for faster downloads in China
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libreoffice \
    libreoffice-writer \
    libreoffice-impress \
    libreoffice-calc \
    libreoffice-l10n-zh-cn \
    ffmpeg \
    imagemagick \
    poppler-utils \
    curl \
    netcat-openbsd \
    ca-certificates \
    fonts-liberation \
    fonts-dejavu-core \
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    fonts-arphic-ukai \
    fonts-arphic-uming \
    fontconfig \
    locales libreoffice-java-common default-jre \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure Chinese locale
RUN locale-gen zh_CN.UTF-8 && \
    update-locale LANG=zh_CN.UTF-8

# Configure fontconfig for Chinese fonts priority
RUN mkdir -p /etc/fonts/conf.d && \
    echo '<?xml version="1.0"?>' > /etc/fonts/local.conf && \
    echo '<!DOCTYPE fontconfig SYSTEM "fonts.dtd">' >> /etc/fonts/local.conf && \
    echo '<fontconfig>' >> /etc/fonts/local.conf && \
    echo '  <alias>' >> /etc/fonts/local.conf && \
    echo '    <family>serif</family>' >> /etc/fonts/local.conf && \
    echo '    <prefer>' >> /etc/fonts/local.conf && \
    echo '      <family>Noto Serif CJK SC</family>' >> /etc/fonts/local.conf && \
    echo '      <family>WenQuanYi Zen Hei</family>' >> /etc/fonts/local.conf && \
    echo '    </prefer>' >> /etc/fonts/local.conf && \
    echo '  </alias>' >> /etc/fonts/local.conf && \
    echo '  <alias>' >> /etc/fonts/local.conf && \
    echo '    <family>sans-serif</family>' >> /etc/fonts/local.conf && \
    echo '    <prefer>' >> /etc/fonts/local.conf && \
    echo '      <family>Noto Sans CJK SC</family>' >> /etc/fonts/local.conf && \
    echo '      <family>WenQuanYi Zen Hei</family>' >> /etc/fonts/local.conf && \
    echo '    </prefer>' >> /etc/fonts/local.conf && \
    echo '  </alias>' >> /etc/fonts/local.conf && \
    echo '  <alias>' >> /etc/fonts/local.conf && \
    echo '    <family>monospace</family>' >> /etc/fonts/local.conf && \
    echo '    <prefer>' >> /etc/fonts/local.conf && \
    echo '      <family>Noto Sans Mono CJK SC</family>' >> /etc/fonts/local.conf && \
    echo '      <family>WenQuanYi Zen Hei Mono</family>' >> /etc/fonts/local.conf && \
    echo '    </prefer>' >> /etc/fonts/local.conf && \
    echo '  </alias>' >> /etc/fonts/local.conf && \
    echo '</fontconfig>' >> /etc/fonts/local.conf && \
    fc-cache -fv

# Configure ImageMagick to allow PDF operations
RUN sed -i 's/rights="none" pattern="PDF"/rights="read|write" pattern="PDF"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/<policy domain="coder" rights="none" pattern="PDF" \/>/<policy domain="coder" rights="read|write" pattern="PDF" \/>/' /etc/ImageMagick-6/policy.xml && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/*

# Create app user with specific UID/GID for better security
RUN groupadd -r -g 1001 appgroup && \
    useradd -r -u 1001 -g appgroup -s /bin/false -c "App User" appuser

# Set working directory
WORKDIR /app

# Copy timezone data and certificates from builder
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy binary from builder stage
COPY --from=builder /app/main .

# Copy scripts
COPY scripts/ ./scripts/

# Create necessary directories with proper permissions
RUN mkdir -p uploads screenshots videos temp logs audio work && \
    chown -R appuser:appgroup /app && \
    chmod -R 755 /app && \
    chmod +x /app/main && \
    chmod +x /app/scripts/*.sh

# Create a non-root user's home directory
RUN mkdir -p /home/<USER>
    chown appuser:appgroup /home/<USER>

# Switch to app user
USER appuser

# Set environment variables for the application
ENV HOME=/home/<USER>
    PATH=/app:$PATH

# Expose port
EXPOSE 8080

# Add labels for better container management
LABEL maintainer="PPT Narrator Team" \
      version="1.0.0" \
      description="PPT Narrator - Intelligent PPT narration service" \
      org.opencontainers.image.title="PPT Narrator" \
      org.opencontainers.image.description="Intelligent PPT narration service with AI-powered script generation" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="PPT Narrator Team"

# Health check with more comprehensive testing
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application with entrypoint script
ENTRYPOINT ["./scripts/docker-entrypoint.sh"]
CMD ["./main"]
