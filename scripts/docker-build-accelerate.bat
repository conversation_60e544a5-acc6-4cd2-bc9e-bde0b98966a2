@echo off
echo 🚀 Docker构建加速脚本
echo =====================

REM 停止当前构建（如果正在运行）
echo 停止当前构建进程...
docker-compose down 2>nul

REM 清理构建缓存（可选）
echo 清理Docker构建缓存...
docker builder prune -f

REM 设置构建加速环境变量
echo 设置构建加速环境...
set DOCKER_BUILDKIT=1
set COMPOSE_DOCKER_CLI_BUILD=1

REM 使用并行构建和缓存
echo 开始加速构建...
docker-compose build --parallel --no-cache

if %errorlevel% neq 0 (
    echo ❌ 构建失败，尝试备用方案...
    
    REM 备用方案：使用原始Dockerfile但优化参数
    echo 使用备用构建方案...
    docker build --no-cache --progress=plain -t ppt-narrator:latest .
    
    if %errorlevel% neq 0 (
        echo ❌ 备用构建也失败了
        echo 💡 建议：
        echo   1. 检查网络连接
        echo   2. 尝试使用VPN
        echo   3. 手动下载依赖
        pause
        exit /b 1
    )
)

echo ✅ 构建完成！
echo 🚀 启动服务...
docker-compose up -d

echo 📊 查看服务状态...
docker-compose ps

echo 📝 查看日志...
docker-compose logs --tail=50 -f

pause
