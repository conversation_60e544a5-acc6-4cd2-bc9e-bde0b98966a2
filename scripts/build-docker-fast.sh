#!/bin/bash

# Docker快速构建脚本 - 针对中国网络环境优化
set -e

echo "🚀 PPT Narrator Docker 快速构建脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    print_error "Docker未运行，请启动Docker"
    exit 1
fi

# 设置变量
IMAGE_NAME="ppt-narrator"
TAG="latest"
DOCKERFILE="Dockerfile.fast"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -t, --tag TAG        设置镜像标签 (默认: latest)"
            echo "  -n, --name NAME      设置镜像名称 (默认: ppt-narrator)"
            echo "  -f, --file FILE      指定Dockerfile (默认: Dockerfile.fast)"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"

print_status "开始构建Docker镜像: ${FULL_IMAGE_NAME}"
print_status "使用Dockerfile: ${DOCKERFILE}"

# 检查Dockerfile是否存在
if [ ! -f "$DOCKERFILE" ]; then
    print_error "Dockerfile不存在: $DOCKERFILE"
    exit 1
fi

# 显示构建信息
print_status "构建配置:"
echo "  镜像名称: ${FULL_IMAGE_NAME}"
echo "  Dockerfile: ${DOCKERFILE}"
echo "  构建上下文: $(pwd)"

# 开始构建
print_status "开始Docker构建..."
start_time=$(date +%s)

# 使用BuildKit加速构建
export DOCKER_BUILDKIT=1

# 构建命令
docker build \
    --progress=plain \
    --no-cache \
    -f "$DOCKERFILE" \
    -t "$FULL_IMAGE_NAME" \
    . || {
    print_error "Docker构建失败"
    exit 1
}

# 计算构建时间
end_time=$(date +%s)
build_time=$((end_time - start_time))
minutes=$((build_time / 60))
seconds=$((build_time % 60))

print_success "Docker镜像构建完成！"
print_success "构建时间: ${minutes}分${seconds}秒"
print_success "镜像名称: ${FULL_IMAGE_NAME}"

# 显示镜像信息
print_status "镜像信息:"
docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 提供运行建议
echo ""
print_status "运行建议:"
echo "  启动容器:"
echo "    docker run -d -p 8080:8080 --name ppt-narrator-app ${FULL_IMAGE_NAME}"
echo ""
echo "  查看日志:"
echo "    docker logs -f ppt-narrator-app"
echo ""
echo "  停止容器:"
echo "    docker stop ppt-narrator-app"
echo ""
echo "  删除容器:"
echo "    docker rm ppt-narrator-app"

print_success "构建脚本执行完成！"
