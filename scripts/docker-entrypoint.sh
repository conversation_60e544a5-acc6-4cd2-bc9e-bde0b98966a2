#!/bin/bash

# Docker entrypoint script for PPT Narrator
set -e

# Log function
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1" >&2
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# Wait for database if DATABASE_URL is set
if [ -n "$DATABASE_URL" ]; then
    log_info "Waiting for database to be ready..."
    
    # Extract database connection details from DATABASE_URL
    # Format: postgres://user:password@host:port/database?options
    if [[ $DATABASE_URL =~ postgres://([^:]+):([^@]+)@([^:]+):([0-9]+)/([^?]+) ]]; then
        DB_USER="${BASH_REMATCH[1]}"
        DB_PASSWORD="${BASH_REMATCH[2]}"
        DB_HOST="${BASH_REMATCH[3]}"
        DB_PORT="${BASH_REMATCH[4]}"
        DB_NAME="${BASH_REMATCH[5]}"
        
        log_info "Database config: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
        
        # Wait for database connection
        for i in {1..30}; do
            if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
                log_success "Database connection successful"
                break
            fi
            log_info "Waiting for database connection... ($i/30)"
            sleep 2
        done
        
        if ! nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
            log_error "Database connection timeout"
            exit 1
        fi
    else
        log_info "Invalid DATABASE_URL format, skipping database wait"
    fi
else
    log_info "DATABASE_URL not set, using SQLite"
fi

# Create necessary directories
log_info "Creating application directories..."
mkdir -p /app/uploads /app/screenshots /app/videos /app/temp /app/audio /app/work

# Set proper permissions
log_info "Setting directory permissions..."
chown -R appuser:appgroup /app/uploads /app/screenshots /app/videos /app/temp /app/audio /app/work 2>/dev/null || true

log_info "Starting PPT Narrator service..."

# Execute the main command
exec "$@"
