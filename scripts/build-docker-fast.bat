@echo off
setlocal enabledelayedexpansion

REM Docker快速构建脚本 - Windows版本
echo PPT Narrator Docker 快速构建脚本
echo ==================================

REM 设置默认值
set IMAGE_NAME=ppt-narrator
set TAG=latest
set DOCKERFILE=Dockerfile.fast

REM 解析命令行参数
:parse_args
if "%~1"=="" goto start_build
if "%~1"=="-t" (
    set TAG=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--tag" (
    set TAG=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-n" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--name" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-f" (
    set DOCKERFILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--file" (
    set DOCKERFILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help
echo 未知参数: %~1
goto show_help

:show_help
echo 用法: %0 [选项]
echo 选项:
echo   -t, --tag TAG        设置镜像标签 (默认: latest)
echo   -n, --name NAME      设置镜像名称 (默认: ppt-narrator)
echo   -f, --file FILE      指定Dockerfile (默认: Dockerfile.fast)
echo   -h, --help           显示帮助信息
exit /b 0

:start_build
set FULL_IMAGE_NAME=%IMAGE_NAME%:%TAG%

echo 开始构建Docker镜像: %FULL_IMAGE_NAME%
echo 使用Dockerfile: %DOCKERFILE%

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker未安装，请先安装Docker
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker未运行，请启动Docker
    exit /b 1
)

REM 检查Dockerfile是否存在
if not exist "%DOCKERFILE%" (
    echo ERROR: Dockerfile不存在: %DOCKERFILE%
    exit /b 1
)

echo 构建配置:
echo   镜像名称: %FULL_IMAGE_NAME%
echo   Dockerfile: %DOCKERFILE%
echo   构建上下文: %CD%

echo 开始Docker构建...

REM 记录开始时间
for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
    set /a "start=(((%%a*60)+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100"
)

REM 设置BuildKit加速构建
set DOCKER_BUILDKIT=1

REM 构建命令
docker build --progress=plain --no-cache -f "%DOCKERFILE%" -t "%FULL_IMAGE_NAME%" .

if %errorlevel% neq 0 (
    echo ERROR: Docker构建失败
    exit /b 1
)

REM 计算构建时间
for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
    set /a "end=(((%%a*60)+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100"
)
set /a elapsed=end-start
set /a minutes=elapsed/6000
set /a seconds=(elapsed%%6000)/100

echo SUCCESS: Docker镜像构建完成！
echo SUCCESS: 构建时间: %minutes%分%seconds%秒
echo SUCCESS: 镜像名称: %FULL_IMAGE_NAME%

echo 镜像信息:
docker images %IMAGE_NAME%

echo.
echo 运行建议:
echo   启动容器:
echo     docker run -d -p 8080:8080 --name ppt-narrator-app %FULL_IMAGE_NAME%
echo.
echo   查看日志:
echo     docker logs -f ppt-narrator-app
echo.
echo   停止容器:
echo     docker stop ppt-narrator-app
echo.
echo   删除容器:
echo     docker rm ppt-narrator-app

echo SUCCESS: 构建脚本执行完成！
pause
