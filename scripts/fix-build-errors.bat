@echo off
echo 🔧 修复构建错误脚本
echo ==================

echo 1. 检查Go语法错误...
go vet ./...

if %errorlevel% neq 0 (
    echo ❌ 发现语法错误，请检查代码
    pause
    exit /b 1
)

echo 2. 检查未使用的导入...
go mod tidy

echo 3. 格式化代码...
go fmt ./...

echo 4. 测试编译...
go build -o test-build.exe cmd/server/main.go

if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
) else (
    echo ✅ 编译成功
    del test-build.exe 2>nul
)

echo 5. 现在可以重新构建Docker镜像...
echo 运行以下命令：
echo   docker-compose build --no-cache
echo   或者
echo   docker build --no-cache -t ppt-narrator:latest .

pause
