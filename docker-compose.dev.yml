version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=ppt_narrator
      - POSTGRES_USER=ppt_narrator
      - POSTGRES_PASSWORD=ppt_narrator123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - LC_ALL=C.UTF-8
      - LANG=C.UTF-8
    ports:
      - "35432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ppt_narrator -d ppt_narrator"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  mcp-pipeline-server:
    build: 
      context: ./mcp
      dockerfile: Dockerfile
    ports:
      - "48080:48080"
      - "48081:48081"
    environment:
      - PORT=48080
      - PPT_NARRATOR_URL=http://ppt-narrator:8080
      - MCP_ACCESS_KEY=test123
      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-false}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-http://*********:8800}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-false}
      - MCP_DEBUG=${MCP_DEBUG:-true}
    restart: unless-stopped
    depends_on:
      - ppt-narrator

  ppt-narrator:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "38080:8080"
    environment:
      - PORT=8080
      - DATABASE_URL=*****************************************************/ppt_narrator?sslmode=disable&client_encoding=UTF8
      - UPLOAD_DIR=/app/uploads
      - SCREENSHOT_DIR=/app/screenshots
      - VIDEO_DIR=/app/videos
      - TEMP_DIR=/app/temp
      - AI_PROVIDER=${AI_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - MINIMAX_GROUP_ID=${MINIMAX_GROUP_ID}
      - MINIMAX_MODEL=${MINIMAX_MODEL:-abab6.5s-chat}
      - TTS_PROVIDER=${TTS_PROVIDER:-openai}
      - TTS_VOICE=${TTS_VOICE:-alloy}
      - TTS_SPEED=${TTS_SPEED:-1.0}
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID}
      - MINIMAX_TTS_MODEL=${MINIMAX_TTS_MODEL:-speech-02-hd}
      - MINIMAX_TTS_VOICE_ID=${MINIMAX_TTS_VOICE_ID:-male-qn-qingse}
      # EasyVoice TTS 配置
      - EASYVOICE_API_URL=${EASYVOICE_API_URL:-https://easyvoice.wetolink.com/api/v1/tts/generate}
      - EASYVOICE_USERNAME=${EASYVOICE_USERNAME:-}
      - EASYVOICE_PASSWORD=${EASYVOICE_PASSWORD:-}
      - EASYVOICE_VOICE=${EASYVOICE_VOICE:-zh-CN-YunxiNeural}
      - EASYVOICE_RATE=${EASYVOICE_RATE:-0%}
      - EASYVOICE_PITCH=${EASYVOICE_PITCH:-0Hz}
      - EASYVOICE_VOLUME=${EASYVOICE_VOLUME:-0%}
      - LIBREOFFICE_PATH=libreoffice
      - FFMPEG_PATH=ffmpeg
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-}
      - NARRATOR_ROLE=${NARRATOR_ROLE:-资深教授}
      - NARRATOR_STYLE=${NARRATOR_STYLE:-亲切自然}
      - TARGET_AUDIENCE=${TARGET_AUDIENCE:-大学生}
      - SPEAKING_TONE=${SPEAKING_TONE:-轻松友好}
      - SPEECH_NATURALNESS=${SPEECH_NATURALNESS:-高度口语化}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - TEMPERATURE=${TEMPERATURE:-0.7}
    volumes:
      - ./backend:/app
      - ppt_uploads_dev:/app/uploads
      - ppt_screenshots_dev:/app/screenshots
      - ppt_videos_dev:/app/videos
      - ppt_temp_dev:/app/temp
      - ppt_audio_dev:/app/audio
      - ppt_work_dev:/app/work
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data_dev:
  ppt_uploads_dev:
  ppt_screenshots_dev:
  ppt_videos_dev:
  ppt_temp_dev:
  ppt_audio_dev:
  ppt_work_dev:
