# PPT Narrator 提示词优化指南

## 问题分析

### 原有问题
你提到的讲稿问题确实存在：
```
"嘿，大家好，咱们今天继续聊聊SQL注入的有趣部分哦 [停顿1秒]..."
```

**主要问题：**
1. ❌ 每张PPT都用"大家好"开场
2. ❌ 把每页当作独立的课程来讲
3. ❌ 缺乏页面间的自然衔接
4. ❌ 过度使用课程化语言

## 优化方案

### 1. 强调PPT的连贯性

**新增提示词：**
```
## 重要理解
**这是一个连贯的PPT演示，不是独立的课程！**
- 每张幻灯片都是整体内容的一部分，需要自然衔接
- 避免每页都用"大家好"、"今天我们来讲"等开场白
- 根据页面位置调整语调：开头介绍、中间展开、结尾总结
- 用承上启下的语言连接前后内容
```

### 2. 根据页面位置提供指导

**动态指导：**
- **第一页**：适合用开场白介绍主题，设定整体基调
- **中间页**：承接上文，展开当前要点，为下文做铺垫
- **最后页**：适合总结要点，给出结论或展望

### 3. 明确禁止的表达

**严格禁止：**
```
❌ 每页都用开场白: "大家好"、"嘿，大家好"、"同学们好"
❌ 把每页当独立课程: "今天我们来讲"、"咱们今天继续聊聊"
❌ 刻意的课程化语言: "让我们一起来学习"、"接下来我们将要讲解"
❌ 过度的总结语: "怎么样，是不是觉得这个小技巧特别实用"
```

### 4. 提供良好示例

**第一页示例：**
```
✅ "咱们来看看SQL注入这个话题 [停顿1秒]。这个东西呢，在网络安全里头还挺重要的 [停顿1.5秒]..."
```

**中间页示例：**
```
✅ "刚才我们说到了基础概念 [停顿1秒]，现在呢，我们来看看一个更具体的技术 [停顿1.5秒]..."
```

## 预期效果

### 优化前（问题示例）
```
第1页: "嘿，大家好，咱们今天来聊聊SQL注入..."
第2页: "大家好，今天我们继续学习SQL注入的原理..."
第3页: "同学们好，现在我们来讲group_concat函数..."
```

### 优化后（期望效果）
```
第1页: "咱们来看看SQL注入这个话题 [停顿1秒]。这个东西呢，在网络安全里头还挺重要的..."

第2页: "刚才说到了SQL注入的基本概念 [停顿1秒]，现在我们深入一点，看看它的具体原理 [停顿1.5秒]..."

第3页: "说到SQL注入的技术细节 [停顿1秒]，有个函数特别有用，叫group_concat [停顿1.5秒]..."
```

## 测试方法

### 1. 运行测试脚本
```bash
go run test_prompt_optimization.go
```

### 2. 检查生成的讲稿

**好的指标：**
- ✅ 第一页有自然的开场
- ✅ 中间页有承上启下的过渡
- ✅ 最后页有自然的总结
- ✅ 包含丰富的口语词汇
- ✅ 有适当的停顿标记
- ✅ 长度控制在200-400字

**问题指标：**
- ❌ 每页都有"大家好"
- ❌ 重复的开场白
- ❌ 缺少页面间的衔接
- ❌ 过度的课程化语言

### 3. API测试

```bash
# 生成讲稿
curl -X POST "http://localhost:8080/api/v1/narration/{projectId}/generate" \
  -H "Content-Type: application/json" \
  -d '{"user_requirements": "专业技术讲解，适合大学生，要求自然连贯"}'

# 检查结果
curl -X GET "http://localhost:8080/api/v1/narration/{projectId}"
```

## 配置调整

### 环境变量优化
```bash
# 讲师角色
NARRATOR_ROLE=技术专家

# 讲解风格
NARRATOR_STYLE=自然连贯

# 目标听众
TARGET_AUDIENCE=技术学习者

# 语调风格
SPEAKING_TONE=专业友好

# 口语化程度
SPEECH_NATURALNESS=高度口语化
```

### 系统提示词
可以通过 `SYSTEM_PROMPT` 环境变量进一步定制：
```bash
SYSTEM_PROMPT="你是一位经验丰富的技术讲师，正在进行一场连贯的PPT演示。请确保每张幻灯片的讲解都自然衔接，避免重复的开场白，让整个演示听起来像一个完整的技术分享。"
```

## 实施步骤

### 1. 立即生效
修改已经在代码中生效，重新构建Docker镜像即可：
```bash
docker-compose build
docker-compose up -d
```

### 2. 测试现有项目
对于已有的项目，可以重新生成讲稿：
```bash
# 检查项目状态
curl -X GET "http://localhost:8080/api/v1/projects/{projectId}"

# 如果状态允许，重新生成讲稿
curl -X POST "http://localhost:8080/api/v1/narration/{projectId}/generate" \
  -H "Content-Type: application/json" \
  -d '{"user_requirements": "请生成自然连贯的讲稿，避免每页都用开场白"}'
```

### 3. 监控效果
```bash
# 获取生成的讲稿
curl -X GET "http://localhost:8080/api/v1/narration/{projectId}"

# 检查每页的开头是否还有"大家好"等问题
```

## 进一步优化建议

### 1. 上下文记忆
系统已经实现了前置讲稿的传递，确保AI能够了解前面的内容。

### 2. 主题一致性
可以在用户需求中明确主题，例如：
```json
{
  "user_requirements": "这是一个关于SQL注入的技术分享，请确保讲解连贯，从基础概念逐步深入到具体技术细节"
}
```

### 3. 风格定制
根据不同的PPT主题调整讲解风格：
- 技术教程：专业但易懂
- 商业演示：简洁有力
- 学术报告：严谨详细

## 常见问题

### Q: 如何确保所有页面都不会有重复开场？
A: 新的提示词明确禁止了这些表达，并提供了具体的替代方案。

### Q: 如何处理第一页的开场？
A: 第一页仍然可以有开场，但会更自然，如"咱们来看看..."而不是"大家好，今天我们来讲..."

### Q: 中间页如何自然衔接？
A: 使用承上启下的语言，如"刚才说到了..."、"接着我们来看..."等。

### Q: 如何验证优化效果？
A: 运行测试脚本，检查生成的讲稿是否符合预期模式。

这个优化应该能显著改善你提到的问题，让PPT讲解更加自然连贯！
