# MiniMax TTS 音频生成功能使用指南

## 功能概述

本系统现已支持 MiniMax TTS 接口，可以将讲稿按停顿标记分割，生成高质量的中文语音，并自动串联成完整的音频文件。

## 主要特性

### 1. 智能停顿处理
- 自动识别讲稿中的停顿标记：`[停顿1秒]`、`[停顿2秒]`、`[停顿1.5秒]` 等
- 将讲稿分割成多个语音片段
- 在指定位置插入相应时长的静音

### 2. MiniMax TTS 集成
- 支持 MiniMax 同步语音合成 API
- 多种音色选择（男声、女声、情感语调等）
- 高质量 MP3 音频输出
- 支持语速、音量、音调调节

### 3. 音频处理
- 自动合并多个音频片段
- 使用 FFmpeg 进行专业音频处理
- 支持多种音频格式和采样率

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# TTS 提供商选择
TTS_PROVIDER=minimax

# MiniMax TTS 配置
MINIMAX_TTS_API_KEY=your_minimax_tts_api_key_here
MINIMAX_TTS_GROUP_ID=your_minimax_tts_group_id_here
MINIMAX_TTS_MODEL=speech-02-hd
MINIMAX_TTS_VOICE_ID=male-qn-qingse
MINIMAX_TTS_EMOTION=happy

# 通用 TTS 配置
TTS_SPEED=1.0
```

### 可用音色列表

#### 系统音色
- `male-qn-qingse` - 青涩青年音色
- `male-qn-jingying` - 精英青年音色
- `male-qn-badao` - 霸道青年音色
- `male-qn-daxuesheng` - 青年大学生音色
- `female-shaonv` - 少女音色
- `female-yujie` - 御姐音色
- `female-chengshu` - 成熟女性音色
- `female-tianmei` - 甜美女性音色
- `presenter_male` - 男性主持人
- `presenter_female` - 女性主持人

#### 有声书音色
- `audiobook_male_1` - 男性有声书1
- `audiobook_male_2` - 男性有声书2
- `audiobook_female_1` - 女性有声书1
- `audiobook_female_2` - 女性有声书2

#### 特色音色
- `clever_boy` - 聪明男童
- `cute_boy` - 可爱男童
- `lovely_girl` - 萌萌女童
- `cartoon_pig` - 卡通猪小琪

### 可用情感
- `happy` - 高兴
- `sad` - 悲伤
- `angry` - 愤怒
- `fearful` - 害怕
- `disgusted` - 厌恶
- `surprised` - 惊讶
- `calm` - 中性

### 可用模型
- `speech-02-hd` - 最新HD模型，音质突出
- `speech-02-turbo` - 最新Turbo模型，性能出色
- `speech-01-hd` - 稳定版HD模型
- `speech-01-turbo` - 稳定版Turbo模型

## 使用方法

### 1. 讲稿格式

在生成的讲稿中，系统会自动插入停顿标记：

```text
嘿，大家好，咱们今天呢要聊聊一个挺重要的话题 [停顿1秒]，叫做"SQL注入基础"。
嗯，你可能会问，SQL注入是啥？[停顿2秒] 说白了呢，其实就是一种网络攻击技术。

那为什么我们要关心这个呢？[停顿2秒] 这样想啊，咱们每天在网上各种操作，
背后都是数据库在运作 [停顿1.5秒]。
```

### 2. API 调用

音频生成会在讲稿生成完成后自动触发：

```bash
# 生成讲稿
curl -X POST http://localhost:8080/api/v1/narration/{project_id}/generate \
  -H "Content-Type: application/json" \
  -d '{"user_requirements": "专业技术讲解"}'

# 生成音频（讲稿完成后自动调用）
curl -X POST http://localhost:8080/api/v1/audio/{project_id}/generate
```

### 3. Docker 部署

使用 Docker Compose 部署时，确保环境变量正确配置：

```yaml
services:
  ppt-narrator:
    environment:
      - TTS_PROVIDER=minimax
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID}
      - MINIMAX_TTS_MODEL=speech-02-hd
      - MINIMAX_TTS_VOICE_ID=female-tianmei
      - MINIMAX_TTS_EMOTION=happy
```

## 测试功能

运行测试脚本验证配置：

```bash
cd ppt-narrator
go run test_audio_generation.go
```

测试脚本会：
1. 解析带停顿标记的文本
2. 验证 MiniMax TTS 配置
3. 生成测试音频
4. 创建静音音频片段

## 故障排除

### 常见问题

1. **API Key 错误**
   ```
   错误: MiniMax configuration error: MINIMAX_TTS_API_KEY is required
   解决: 检查 MINIMAX_TTS_API_KEY 环境变量是否正确设置
   ```

2. **Group ID 错误**
   ```
   错误: MiniMax configuration error: MINIMAX_TTS_GROUP_ID is required
   解决: 检查 MINIMAX_TTS_GROUP_ID 环境变量是否正确设置
   ```

3. **FFmpeg 未找到**
   ```
   错误: failed to generate silence: exec: "ffmpeg": executable file not found
   解决: 安装 FFmpeg 或设置 FFMPEG_PATH 环境变量
   ```

4. **音频合并失败**
   ```
   错误: failed to combine audio files
   解决: 检查 FFmpeg 是否正确安装，临时目录是否有写权限
   ```

### 调试模式

启用详细日志：

```bash
export GIN_MODE=debug
export LOG_LEVEL=debug
```

### 音频文件位置

生成的音频文件保存在：
- 单个幻灯片音频：`videos/{project_id}/audio/slide_001.mp3`
- 音频片段：`videos/{project_id}/audio/slide_001/segment_001.mp3`
- 停顿音频：`videos/{project_id}/audio/slide_001/pause_002.mp3`
- 合并音频：`videos/{project_id}/audio/combined.mp3`

## 性能优化

### 建议配置

1. **生产环境**：使用 `speech-02-hd` 模型获得最佳音质
2. **开发环境**：使用 `speech-02-turbo` 模型获得更快速度
3. **语速设置**：建议使用 1.0-1.2 之间的语速
4. **并发控制**：避免同时生成多个项目的音频

### 缓存策略

系统会自动缓存：
- 相同文本的音频片段
- 相同时长的静音片段
- 合并后的完整音频

## API 参考

详细的 API 文档请参考：
- [TTS Service API](./docs/api/tts.md)
- [Audio Processing API](./docs/api/audio.md)
- [Project Management API](./docs/api/projects.md)

## 更新日志

- **v1.2.0**: 新增 MiniMax TTS 支持
- **v1.2.1**: 优化停顿标记解析
- **v1.2.2**: 改进音频合并算法
- **v1.2.3**: 添加更多音色选择
