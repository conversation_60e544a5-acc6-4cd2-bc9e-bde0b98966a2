# 产物下载API使用指南

## 🎯 概述

PPT Narrator处理完成后，会生成多种产物文件，包括视频、音频、截图和讲稿等。下载API提供了便捷的方式来获取这些产物。

## 📋 可下载的产物类型

### 1. 视频文件 (MP4)
- **描述**: 最终生成的演示视频，包含幻灯片和语音解说
- **格式**: MP4
- **用途**: 完整的演示视频，可直接播放或分享

### 2. 音频文件 (MP3)
- **描述**: 合并后的完整语音解说
- **格式**: MP3
- **用途**: 纯音频版本，可用于播客或音频学习

### 3. 截图文件 (PNG)
- **描述**: 每张幻灯片的高清截图
- **格式**: PNG (打包为ZIP)
- **用途**: 静态图片，可用于文档或网页展示

### 4. 讲稿文本 (TXT)
- **描述**: AI生成的完整讲稿内容
- **格式**: 纯文本
- **用途**: 演讲稿参考、字幕制作等

### 5. 完整包 (ZIP)
- **描述**: 包含所有可用产物的压缩包
- **格式**: ZIP
- **用途**: 一次性下载所有资源

## 🔗 API端点

### 1. 获取下载信息

**GET** `/api/v1/download/{projectId}/info`

查看项目的所有可下载产物信息。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/info"
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
    "project_name": "季度业务汇报",
    "status": "completed",
    "downloads": {
      "video": {
        "available": true,
        "size": 15728640,
        "url": "/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/video"
      },
      "audio": {
        "available": true,
        "size": 2097152,
        "url": "/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/audio"
      },
      "screenshots": {
        "available": true,
        "count": 8,
        "size": 5242880,
        "url": "/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/screenshots"
      },
      "narration": {
        "available": true,
        "size": 4096,
        "url": "/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/narration"
      },
      "complete": {
        "available": true,
        "url": "/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/all"
      }
    }
  }
}
```

### 2. 下载视频文件

**GET** `/api/v1/download/{projectId}/video`

下载最终生成的MP4视频文件。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/video" \
  -o "presentation_video.mp4"
```

#### 响应
- **成功**: 直接返回MP4文件流
- **失败**: 返回JSON错误信息

### 3. 下载音频文件

**GET** `/api/v1/download/{projectId}/audio`

下载合并后的MP3音频文件。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/audio" \
  -o "presentation_audio.mp3"
```

### 4. 下载截图文件

**GET** `/api/v1/download/{projectId}/screenshots`

下载所有幻灯片截图，打包为ZIP文件。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/screenshots" \
  -o "presentation_screenshots.zip"
```

#### ZIP文件结构
```
presentation_screenshots.zip
├── slide_001.png
├── slide_002.png
├── slide_003.png
└── ...
```

### 5. 下载讲稿文本

**GET** `/api/v1/download/{projectId}/narration`

下载AI生成的讲稿文本文件。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/narration" \
  -o "presentation_narration.txt"
```

### 6. 下载完整包

**GET** `/api/v1/download/{projectId}/all`

下载包含所有可用产物的ZIP压缩包。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/download/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/all" \
  -o "presentation_complete.zip"
```

#### ZIP文件结构
```
presentation_complete.zip
├── video.mp4                    # 最终视频
├── audio.mp3                    # 合并音频
├── narration.txt                # 讲稿文本
├── original_presentation.pptx   # 原始PPT文件
└── screenshots/                 # 截图文件夹
    ├── slide_001.png
    ├── slide_002.png
    └── ...
```

## 💻 前端集成示例

### JavaScript/React示例

```javascript
class DownloadManager {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  // 获取下载信息
  async getDownloadInfo(projectId) {
    const response = await fetch(`${this.baseUrl}/api/v1/download/${projectId}/info`);
    return await response.json();
  }

  // 下载文件
  async downloadFile(projectId, type, filename) {
    const url = `${this.baseUrl}/api/v1/download/${projectId}/${type}`;
    
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Download failed');
      }

      // 创建下载链接
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
      
      return true;
    } catch (error) {
      console.error('Download error:', error);
      throw error;
    }
  }

  // 下载视频
  async downloadVideo(projectId, projectName) {
    return this.downloadFile(projectId, 'video', `${projectName}_video.mp4`);
  }

  // 下载音频
  async downloadAudio(projectId, projectName) {
    return this.downloadFile(projectId, 'audio', `${projectName}_audio.mp3`);
  }

  // 下载截图
  async downloadScreenshots(projectId, projectName) {
    return this.downloadFile(projectId, 'screenshots', `${projectName}_screenshots.zip`);
  }

  // 下载讲稿
  async downloadNarration(projectId, projectName) {
    return this.downloadFile(projectId, 'narration', `${projectName}_narration.txt`);
  }

  // 下载完整包
  async downloadAll(projectId, projectName) {
    return this.downloadFile(projectId, 'all', `${projectName}_complete.zip`);
  }
}

// 使用示例
const downloadManager = new DownloadManager();

// 在React组件中使用
function DownloadPanel({ projectId, projectName }) {
  const [downloadInfo, setDownloadInfo] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDownloadInfo();
  }, [projectId]);

  const loadDownloadInfo = async () => {
    try {
      const info = await downloadManager.getDownloadInfo(projectId);
      setDownloadInfo(info.data);
    } catch (error) {
      console.error('Failed to load download info:', error);
    }
  };

  const handleDownload = async (type) => {
    setLoading(true);
    try {
      switch (type) {
        case 'video':
          await downloadManager.downloadVideo(projectId, projectName);
          break;
        case 'audio':
          await downloadManager.downloadAudio(projectId, projectName);
          break;
        case 'screenshots':
          await downloadManager.downloadScreenshots(projectId, projectName);
          break;
        case 'narration':
          await downloadManager.downloadNarration(projectId, projectName);
          break;
        case 'all':
          await downloadManager.downloadAll(projectId, projectName);
          break;
      }
      alert('下载成功！');
    } catch (error) {
      alert(`下载失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!downloadInfo) return <div>加载中...</div>;

  return (
    <div className="download-panel">
      <h3>下载产物</h3>
      
      {downloadInfo.downloads.video.available && (
        <button 
          onClick={() => handleDownload('video')} 
          disabled={loading}
          className="download-btn video"
        >
          📹 下载视频 ({(downloadInfo.downloads.video.size / 1024 / 1024).toFixed(1)}MB)
        </button>
      )}

      {downloadInfo.downloads.audio.available && (
        <button 
          onClick={() => handleDownload('audio')} 
          disabled={loading}
          className="download-btn audio"
        >
          🎵 下载音频 ({(downloadInfo.downloads.audio.size / 1024 / 1024).toFixed(1)}MB)
        </button>
      )}

      {downloadInfo.downloads.screenshots.available && (
        <button 
          onClick={() => handleDownload('screenshots')} 
          disabled={loading}
          className="download-btn screenshots"
        >
          🖼️ 下载截图 ({downloadInfo.downloads.screenshots.count}张)
        </button>
      )}

      {downloadInfo.downloads.narration.available && (
        <button 
          onClick={() => handleDownload('narration')} 
          disabled={loading}
          className="download-btn narration"
        >
          📝 下载讲稿
        </button>
      )}

      {downloadInfo.downloads.complete.available && (
        <button 
          onClick={() => handleDownload('all')} 
          disabled={loading}
          className="download-btn complete"
        >
          📦 下载完整包
        </button>
      )}
    </div>
  );
}
```

## 🔧 错误处理

### 常见错误响应

```json
{
  "success": false,
  "error": "Project not found"
}
```

```json
{
  "success": false,
  "error": "Video not available for this project"
}
```

```json
{
  "success": false,
  "error": "Video file not found on disk"
}
```

### 错误处理建议

1. **检查项目状态**: 确保项目已完成处理
2. **验证文件存在**: 使用下载信息API检查文件可用性
3. **处理网络错误**: 实现重试机制
4. **用户友好提示**: 提供清晰的错误信息

## 🎯 最佳实践

### 1. 下载前检查
```javascript
// 先检查下载信息
const info = await downloadManager.getDownloadInfo(projectId);
if (info.data.downloads.video.available) {
  await downloadManager.downloadVideo(projectId, projectName);
} else {
  alert('视频还未生成完成');
}
```

### 2. 批量下载
```javascript
// 下载所有可用产物
async function downloadAllAvailable(projectId, projectName) {
  const info = await downloadManager.getDownloadInfo(projectId);
  const downloads = info.data.downloads;
  
  const promises = [];
  if (downloads.video.available) {
    promises.push(downloadManager.downloadVideo(projectId, projectName));
  }
  if (downloads.audio.available) {
    promises.push(downloadManager.downloadAudio(projectId, projectName));
  }
  // ... 其他类型
  
  await Promise.all(promises);
}
```

### 3. 进度显示
```javascript
// 显示文件大小和下载进度
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
```

现在你可以通过这些API端点轻松下载PPT Narrator生成的所有产物！🎉
