# PPT Narrator - PostgreSQL Migration Guide

## 概述

PPT Narrator 服务已从 SQLite 迁移到 PostgreSQL，以提供更好的性能、并发性和可扩展性。

## 主要变更

### 1. 数据库配置
- **旧配置**: `DATABASE_PATH` (SQLite 文件路径)
- **新配置**: `DATABASE_URL` (PostgreSQL 连接字符串)

### 2. Docker Compose 配置
新增了 PostgreSQL 服务，并更新了相关配置。

### 3. 向后兼容性
服务仍然支持 SQLite，如果没有设置 `DATABASE_URL`，将回退到使用 `DATABASE_PATH`。

## 快速开始

### 1. 使用 Docker Compose 启动服务

```bash
# 在 ppt-narrator 目录下
docker-compose up -d
```

这将启动：
- PostgreSQL 数据库 (端口 15433)
- PPT Narrator 服务 (端口 38080)
- MCP Pipeline 服务器 (端口 48080)

### 2. 检查服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看日志
docker-compose logs ppt-narrator
docker-compose logs postgres
```

### 3. 健康检查

```bash
# 检查 PPT Narrator 服务
curl http://localhost:38080/health

# 检查数据库连接
docker-compose exec postgres pg_isready -U ppt_narrator -d ppt_narrator
```

## 环境变量配置

### 数据库配置
```bash
# PostgreSQL (推荐)
DATABASE_URL=*****************************************************/ppt_narrator?sslmode=disable

# SQLite (向后兼容)
DATABASE_PATH=./ppt-narrator.db
```

### 其他重要配置
```bash
# 服务端口
PORT=8080

# 目录配置
UPLOAD_DIR=/app/uploads
SCREENSHOT_DIR=/app/screenshots
VIDEO_DIR=/app/videos
TEMP_DIR=/app/temp

# AI 配置
AI_PROVIDER=openai
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4.1
OPENAI_BASE_URL=https://oneapi.wetolink.com/v1

# TTS 配置
TTS_PROVIDER=minimax
MINIMAX_TTS_API_KEY=your_tts_api_key_here
```

## 数据迁移

如果你有现有的 SQLite 数据需要迁移到 PostgreSQL：

1. **导出 SQLite 数据**
2. **转换为 PostgreSQL 格式**
3. **导入到新的 PostgreSQL 数据库**

详细的迁移脚本将在后续版本中提供。

## 故障排除

### 1. 数据库连接失败
```bash
# 检查 PostgreSQL 服务状态
docker-compose logs postgres

# 检查网络连接
docker-compose exec ppt-narrator nc -z postgres 5432
```

### 2. 权限问题
```bash
# 重新设置目录权限
docker-compose exec ppt-narrator chown -R appuser:appgroup /app/uploads
```

### 3. 服务启动失败
```bash
# 查看详细日志
docker-compose logs --tail=50 ppt-narrator

# 重新构建镜像
docker-compose build --no-cache ppt-narrator
```

## 性能优化

### PostgreSQL 配置优化
可以通过环境变量或配置文件调整 PostgreSQL 性能参数：

```yaml
postgres:
  environment:
    - POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements
    - POSTGRES_MAX_CONNECTIONS=100
    - POSTGRES_SHARED_BUFFERS=256MB
```

### 连接池配置
考虑使用连接池来优化数据库连接管理。

## 监控和日志

### 数据库监控
```bash
# 查看数据库连接
docker-compose exec postgres psql -U ppt_narrator -d ppt_narrator -c "SELECT * FROM pg_stat_activity;"

# 查看表大小
docker-compose exec postgres psql -U ppt_narrator -d ppt_narrator -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname='public';"
```

### 应用日志
```bash
# 实时查看日志
docker-compose logs -f ppt-narrator

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" ppt-narrator
```

## 备份和恢复

### 数据库备份
```bash
# 创建备份
docker-compose exec postgres pg_dump -U ppt_narrator ppt_narrator > backup.sql

# 恢复备份
docker-compose exec -T postgres psql -U ppt_narrator ppt_narrator < backup.sql
```

## 支持

如果遇到问题，请：
1. 检查日志文件
2. 验证环境变量配置
3. 确认网络连接
4. 查看 GitHub Issues
