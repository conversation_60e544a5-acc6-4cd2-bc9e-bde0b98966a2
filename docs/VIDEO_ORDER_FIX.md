# 视频生成顺序修复说明

## 问题描述

在视频生成过程中发现了幻灯片顺序错误的问题：

**问题现象：**
```bash
# 错误的顺序
ffmpeg ... slide-1.png ... slide_002.mp3 ... segment_001.mp4
ffmpeg ... slide-10.png ... slide_003.mp3 ... segment_002.mp4
```

**预期顺序：**
```bash
# 正确的顺序
ffmpeg ... slide-1.png ... slide_001.mp3 ... segment_001.mp4
ffmpeg ... slide-2.png ... slide_002.mp3 ... segment_002.mp4
```

## 根本原因

问题出现在视频服务的文件命名逻辑中：

### 原始代码问题
```go
// 错误的实现
segmentPath := filepath.Join(tempDir, fmt.Sprintf("segment_%03d.mp4", slide.SlideNumber))
```

这种方式会导致：
- 如果幻灯片编号不连续（如1, 10, 2, 3...），文件名也会不连续
- 虽然数据库查询已经按 `slide_number` 排序，但文件名的不连续会影响后续处理

## 解决方案

### 1. 数据库查询确认
首先确认数据库查询已经正确排序：
```go
// database_store.go 第144行
func (s *DatabaseStoreService) GetSlidesByProject(projectID string) ([]*models.PPTSlide, error) {
    var slides []*models.PPTSlide
    if err := s.db.Where("project_id = ?", projectID).Order("slide_number").Find(&slides).Error; err != nil {
        return nil, fmt.Errorf("failed to get slides: %w", err)
    }
    return slides, nil
}
```

### 2. 修复文件命名逻辑
使用连续的索引而不是幻灯片编号：

```go
// 修复后的实现
segmentIndex := 1 // 使用连续编号
for _, slide := range slides {
    if slide.ScreenshotPath == "" {
        continue
    }
    
    // 使用连续编号确保顺序一致
    segmentPath := filepath.Join(tempDir, fmt.Sprintf("segment_%03d.mp4", segmentIndex))
    
    // 创建视频片段
    err := s.createSlideVideoSegment(slide.ScreenshotPath, slide.NarrationAudio, segmentPath, duration, fps, resolution)
    if err != nil {
        return fmt.Errorf("failed to create video segment for slide %d: %w", slide.SlideNumber, err)
    }
    
    videoSegments = append(videoSegments, segmentPath)
    segmentIndex++
}
```

## 修复效果

### 修复前
```bash
# 可能的错误顺序
segment_001.mp4 (slide 1)
segment_010.mp4 (slide 10) ❌ 错误！应该是slide 2
segment_002.mp4 (slide 2)
segment_003.mp4 (slide 3)
```

### 修复后
```bash
# 正确的顺序
segment_001.mp4 (slide 1)
segment_002.mp4 (slide 2) ✅ 正确！
segment_003.mp4 (slide 3)
segment_004.mp4 (slide 4)
```

## 技术细节

### 1. 数据流程
```
数据库查询 → 按slide_number排序 → 循环处理 → 连续文件命名 → FFmpeg合并
```

### 2. 关键修改点
- **文件**: `internal/services/video_service.go`
- **方法**: `createVideoFromSlides`
- **行数**: 146-177

### 3. 确保顺序的多重保障
1. **数据库层面**: `ORDER BY slide_number` 确保查询结果有序
2. **处理层面**: 使用连续的 `segmentIndex` 确保文件名有序
3. **合并层面**: `videoSegments` 数组按处理顺序添加，确保FFmpeg输入有序

## 验证方法

### 1. 检查进程
```bash
# 在容器中检查FFmpeg进程
docker exec -it <container_id> ps -ef | grep ffmpeg

# 应该看到正确的顺序：
# slide-1.png ... slide_001.mp3 ... segment_001.mp4
# slide-2.png ... slide_002.mp3 ... segment_002.mp4
```

### 2. 检查临时文件
```bash
# 检查临时目录中的文件顺序
docker exec -it <container_id> ls -la /app/temp/video_<project_id>/

# 应该看到：
# segment_001.mp4
# segment_002.mp4
# segment_003.mp4
```

### 3. 检查视频列表文件
```bash
# 检查FFmpeg合并时使用的文件列表
docker exec -it <container_id> cat /app/temp/video_<project_id>/video_list.txt

# 应该看到正确的顺序：
# file 'segment_001.mp4'
# file 'segment_002.mp4'
# file 'segment_003.mp4'
```

## 部署说明

### 1. 重新构建镜像
```bash
docker build -t ppt-narrator-fixed-order .
```

### 2. 更新服务
```bash
# 停止当前服务
docker-compose down

# 更新镜像标签（如果需要）
# 编辑 docker-compose.yml 中的 image 字段

# 重新启动服务
docker-compose up -d
```

### 3. 验证修复
对于新的视频生成请求，应该能看到正确的幻灯片顺序。

## 注意事项

1. **现有项目**: 已经生成的视频不会受到影响，只有新的视频生成请求会使用修复后的逻辑。

2. **兼容性**: 修复不会影响其他功能，只是改进了文件命名的逻辑。

3. **性能**: 修复不会影响性能，只是确保了处理顺序的正确性。

## 相关文件

- `internal/services/video_service.go` - 主要修复文件
- `internal/services/database_store.go` - 数据库查询排序
- `VIDEO_ORDER_FIX.md` - 本说明文档

这个修复确保了视频生成时幻灯片的正确顺序，解决了 1→10→2→3 的错误排序问题。
