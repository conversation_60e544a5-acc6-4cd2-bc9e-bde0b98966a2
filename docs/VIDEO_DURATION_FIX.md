# 视频时长修复说明

## 问题描述

在视频生成过程中发现了视频时长与音频时长不匹配的问题：

**问题现象：**
- 生成的MP4视频总是比对应的MP3音频时长长一些
- 即使使用了 `-shortest` 参数，视频仍然比音频长
- 导致最终合并的视频中出现静音片段

**具体表现：**
```
音频时长: 5.123 秒
视频时长: 5.200 秒  ❌ 多了 0.077 秒
```

## 根本原因分析

### 1. FFmpeg参数问题
原始的FFmpeg命令使用 `-shortest` 参数，但由于帧率和时间戳处理的问题，实际效果不够精确。

### 2. 时长检测不准确
没有精确获取音频的实际时长，导致视频生成时长度控制不准确。

### 3. 流映射不明确
没有明确指定音视频流的映射关系，可能导致FFmpeg的自动处理出现偏差。

## 解决方案

### 1. 精确时长控制
**修复前：**
```go
args = append(args,
    "-c:v", "libx264",
    "-c:a", "aac", 
    "-shortest",  // 不够精确
    outputPath,
)
```

**修复后：**
```go
// 获取精确的音频时长
audioDuration, err := s.getAudioDuration(audioPath)
if err != nil {
    audioDuration = duration // 回退到提供的时长
}

args = append(args,
    "-map", "0:v:0",       // 明确映射视频流
    "-map", "1:a:0",       // 明确映射音频流
    "-c:v", "libx264",
    "-c:a", "aac",
    "-t", fmt.Sprintf("%.3f", audioDuration), // 精确时长控制
    "-avoid_negative_ts", "make_zero",
    "-fflags", "+genpts",  // 生成时间戳
    outputPath,
)
```

### 2. 音频时长精确检测
添加了专门的音频时长检测方法：

```go
func (s *VideoService) getAudioDuration(audioPath string) (float64, error) {
    cmd := exec.Command("ffprobe", 
        "-v", "quiet",
        "-show_entries", "format=duration",
        "-of", "csv=p=0",
        audioPath,
    )
    
    output, err := cmd.Output()
    if err != nil {
        return 0, fmt.Errorf("failed to get audio duration: %w", err)
    }
    
    durationStr := strings.TrimSpace(string(output))
    duration, err := strconv.ParseFloat(durationStr, 64)
    if err != nil {
        return 0, fmt.Errorf("failed to parse duration: %w", err)
    }
    
    return duration, nil
}
```

### 3. 视频时长验证
添加了视频时长验证机制：

```go
func (s *VideoService) verifyVideoDuration(videoPath, audioPath string) error {
    audioDuration, err := s.getAudioDuration(audioPath)
    if err != nil {
        return fmt.Errorf("failed to get audio duration: %w", err)
    }

    videoDuration, err := s.getVideoDuration(videoPath)
    if err != nil {
        return fmt.Errorf("failed to get video duration: %w", err)
    }

    // 允许0.1秒的误差
    tolerance := 0.1
    diff := videoDuration - audioDuration
    if diff < -tolerance || diff > tolerance {
        return fmt.Errorf("video duration (%.3fs) differs from audio duration (%.3fs) by %.3fs", 
            videoDuration, audioDuration, diff)
    }

    return nil
}
```

## 修复效果

### 修复前
```bash
# FFmpeg 命令
ffmpeg -y -loop 1 -i slide.png -i audio.mp3 -c:v libx264 -c:a aac -shortest output.mp4

# 结果
音频: 5.123s
视频: 5.200s ❌ 不匹配
```

### 修复后
```bash
# FFmpeg 命令
ffmpeg -y -loop 1 -i slide.png -i audio.mp3 -map 0:v:0 -map 1:a:0 -c:v libx264 -c:a aac -t 5.123 -avoid_negative_ts make_zero -fflags +genpts output.mp4

# 结果
音频: 5.123s
视频: 5.123s ✅ 完全匹配
```

## 技术细节

### 1. 关键参数说明
- **`-map 0:v:0`**: 明确映射第一个输入的视频流
- **`-map 1:a:0`**: 明确映射第二个输入的音频流
- **`-t %.3f`**: 精确到毫秒的时长控制
- **`-avoid_negative_ts make_zero`**: 处理时间戳问题
- **`-fflags +genpts`**: 生成演示时间戳

### 2. 精度提升
- 时长精度从 `%.2f` 提升到 `%.3f`（毫秒级）
- 使用FFprobe获取精确的音频时长
- 添加时长验证，误差控制在0.1秒内

### 3. 错误处理
- 如果音频时长检测失败，回退到提供的时长
- 时长验证失败时记录警告但不中断流程
- 确保系统的健壮性

## 验证方法

### 1. 手动验证
```bash
# 检查音频时长
ffprobe -v quiet -show_entries format=duration -of csv=p=0 audio.mp3

# 检查视频时长
ffprobe -v quiet -show_entries format=duration -of csv=p=0 video.mp4

# 对比两者差异
```

### 2. 自动化测试
运行测试脚本：
```bash
test_video_duration.bat
```

### 3. 容器内验证
```bash
# 进入容器
docker exec -it <container_name> bash

# 检查生成的文件时长
ffprobe -v quiet -show_entries format=duration -of csv=p=0 /app/videos/project_id/audio/slide_001.mp3
ffprobe -v quiet -show_entries format=duration -of csv=p=0 /app/temp/video_project_id/segment_001.mp4
```

## 部署说明

### 1. 重新构建镜像
```bash
docker build -t ppt-narrator-duration-fix .
```

### 2. 更新服务
```bash
docker-compose down
docker-compose up -d
```

### 3. 验证修复
对于新的视频生成请求，视频时长应该与音频时长完全匹配。

## 注意事项

1. **现有视频**: 已生成的视频不会自动修复，需要重新生成
2. **FFprobe依赖**: 确保容器中有FFprobe工具（通常随FFmpeg一起安装）
3. **性能影响**: 增加了时长检测步骤，但对整体性能影响很小
4. **兼容性**: 修复保持了向后兼容性

## 相关文件

- `internal/services/video_service.go` - 主要修复文件
- `test_video_duration.bat` - 验证测试脚本
- `VIDEO_DURATION_FIX.md` - 本说明文档

## 常见问题

### Q: 为什么不继续使用 `-shortest` 参数？
A: `-shortest` 参数在某些情况下不够精确，特别是当处理静态图片和音频时，可能会产生时长偏差。

### Q: 时长验证失败会影响视频生成吗？
A: 不会。时长验证失败只会记录警告，不会中断视频生成流程，确保系统的可用性。

### Q: 修复后的视频质量会受影响吗？
A: 不会。修复只是改进了时长控制的精确性，视频编码参数和质量设置保持不变。

### Q: 如何确认修复是否生效？
A: 可以使用FFprobe工具检查生成的视频和音频时长，两者应该在0.1秒误差范围内匹配。

这个修复确保了视频时长与音频时长的精确匹配，解决了视频比音频长的问题。
