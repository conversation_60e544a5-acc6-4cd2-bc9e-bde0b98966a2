# 音频自然化增强功能

## 🎯 核心目标

让音频合成更像真人说话，包括：
1. 音频片段之间的淡入淡出效果
2. 模拟人说话的自然感觉
3. 添加换气声等自然声音
4. 创造真实的对话环境

## 🎵 主要改进

### 1. 自然音频合成

#### **替换简单拼接为智能混合**
```go
// 修改前：简单文件拼接
func (ap *AudioProcessor) CombineAudioFiles(inputFiles []string, outputPath string) error {
    // 使用FFmpeg concat直接拼接，没有过渡效果
    cmd := exec.Command(ap.ffmpegPath, "-f", "concat", "-safe", "0", "-i", listFile, "-c", "copy", "-y", outputPath)
}

// 修改后：智能自然混合
func (ap *AudioProcessor) CombineAudioFiles(inputFiles []string, outputPath string) error {
    // 使用高级音频混合，包含交叉淡化和自然效果
    return ap.combineWithNaturalTransitions(inputFiles, outputPath)
}
```

#### **自然语音滤镜链**
```go
func (ap *AudioProcessor) buildNaturalVoiceFilter() string {
    filters := []string{
        // 1. 温和的淡入淡出，避免爆音
        "afade=t=in:ss=0:d=0.1",
        "afade=t=out:st=0:d=0.1",
        
        // 2. 动态范围压缩（模拟自然语音）
        "acompressor=threshold=0.1:ratio=3:attack=5:release=50",
        
        // 3. 微妙的EQ增强语音自然度
        "equalizer=f=200:width_type=h:width=50:g=2",    // 增强低中频温暖感
        "equalizer=f=3000:width_type=h:width=1000:g=1", // 轻微存在感增强
        
        // 4. 添加非常微妙的房间音调/环境音
        "highpass=f=80",   // 移除极低频
        "lowpass=f=8000",  // 移除刺耳高频
        
        // 5. 温和的标准化
        "loudnorm=I=-16:TP=-1.5:LRA=11",
    }
    
    return strings.Join(filters, ",")
}
```

### 2. 智能交叉淡化

#### **根据音频类型调整淡化时长**
```go
func (ap *AudioProcessor) determineCrossfadeDuration(file1, file2 string) string {
    // 检查是否为停顿/静音
    if strings.Contains(file1, "pause_") || strings.Contains(file2, "pause_") {
        return "0.3" // 停顿使用较短的交叉淡化
    }
    
    // 检查是否为语音片段之间的转换
    if strings.Contains(file1, "segment_") && strings.Contains(file2, "segment_") {
        return "0.15" // 语音间使用很短的交叉淡化
    }
    
    return "0.2" // 默认交叉淡化时长
}
```

#### **复杂滤镜链构建**
```go
func (ap *AudioProcessor) buildNaturalMixingFilter(inputFiles []string) string {
    // 为每个输入应用自然语音效果
    for i := 0; i < len(inputFiles); i++ {
        processedLabel := fmt.Sprintf("[processed%d]", i)
        filterParts = append(filterParts, 
            fmt.Sprintf("[%d:a]%s%s", i, ap.buildNaturalVoiceFilter(), processedLabel))
    }
    
    // 在片段之间创建交叉淡化过渡
    for i := 1; i < len(inputFiles); i++ {
        crossfadeDuration := ap.determineCrossfadeDuration(inputFiles[i-1], inputFiles[i])
        filterParts = append(filterParts,
            fmt.Sprintf("%s%s acrossfade=d=%s:c1=tri:c2=tri%s", 
                currentLabel, nextLabel, crossfadeDuration, mixedLabel))
    }
    
    // 添加最终处理和呼吸效果
    filterParts = append(filterParts,
        fmt.Sprintf("%s%s[final]", currentLabel, ap.buildFinalNaturalProcessing()))
}
```

### 3. 自然停顿生成

#### **替换纯静音为自然停顿**
```go
// 修改前：生成纯静音
func (ap *AudioProcessor) GenerateSilence(duration float64, outputPath string) error {
    cmd := exec.Command(ap.ffmpegPath,
        "-f", "lavfi",
        "-i", "anullsrc=channel_layout=mono:sample_rate=32000",
        "-t", fmt.Sprintf("%.2f", duration),
        "-c:a", "mp3", "-b:a", "128k", "-y", outputPath)
}

// 修改后：生成自然停顿
func (ap *AudioProcessor) GenerateSilence(duration float64, outputPath string) error {
    return ap.GenerateNaturalPause(duration, outputPath)
}
```

#### **分层停顿处理**
```go
func (ap *AudioProcessor) GenerateNaturalPause(duration float64, outputPath string) error {
    // 根据时长创建不同类型的停顿
    if duration <= 0.5 {
        return ap.generateShortPause(duration, outputPath)      // 短停顿：微妙房间音调
    } else if duration <= 2.0 {
        return ap.generateMediumPause(duration, outputPath)     // 中停顿：房间音调+呼吸声
    } else {
        return ap.generateLongPause(duration, outputPath)       // 长停顿：多个呼吸声
    }
}
```

### 4. 呼吸声和环境音

#### **短停顿：微妙房间音调**
```go
func (ap *AudioProcessor) generateShortPause(duration float64, outputPath string) error {
    cmd := exec.Command(ap.ffmpegPath,
        "-f", "lavfi",
        "-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", duration)+":sample_rate=32000:amplitude=0.005:color=pink",
        "-af", "highpass=f=200,lowpass=f=2000,volume=0.02,afade=t=in:ss=0:d=0.1,afade=t=out:st=0:d=0.1",
        "-c:a", "mp3", "-b:a", "64k", "-y", outputPath)
}
```

#### **中等停顿：房间音调+呼吸声**
```go
func (ap *AudioProcessor) generateMediumPause(duration float64, outputPath string) error {
    // 生成基础房间音调
    roomTonePath := filepath.Join(ap.tempDir, fmt.Sprintf("room_tone_%d.mp3", time.Now().UnixNano()))
    cmd1 := exec.Command(ap.ffmpegPath,
        "-f", "lavfi",
        "-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", duration)+":sample_rate=32000:amplitude=0.008:color=pink",
        "-af", "highpass=f=150,lowpass=f=3000,volume=0.03",
        "-c:a", "mp3", "-b:a", "64k", "-y", roomTonePath)
    
    // 如果停顿足够长，添加微妙的呼吸声
    if duration > 1.0 {
        breathPath := filepath.Join(ap.tempDir, fmt.Sprintf("breath_%d.mp3", time.Now().UnixNano()))
        cmd2 := exec.Command(ap.ffmpegPath,
            "-f", "lavfi",
            "-i", "anoisesrc=duration=0.3:sample_rate=32000:amplitude=0.01:color=brown",
            "-af", "highpass=f=80,lowpass=f=800,volume=0.08,afade=t=in:ss=0:d=0.1,afade=t=out:st=0:d=0.1",
            "-c:a", "mp3", "-b:a", "64k", "-y", breathPath)
        
        // 混合房间音调和呼吸声
        cmd3 := exec.Command(ap.ffmpegPath,
            "-i", roomTonePath, "-i", breathPath,
            "-filter_complex", "[0:a][1:a]amix=inputs=2:duration=first:dropout_transition=0",
            "-c:a", "mp3", "-b:a", "64k", "-y", outputPath)
    }
}
```

#### **长停顿：多个呼吸声**
```go
func (ap *AudioProcessor) generateLongPause(duration float64, outputPath string) error {
    // 为长停顿添加1-2个呼吸声
    numBreaths := 1
    if duration > 4.0 {
        numBreaths = 2
    }
    
    for i := 0; i < numBreaths; i++ {
        breathPath := filepath.Join(ap.tempDir, fmt.Sprintf("long_breath_%d_%d.mp3", time.Now().UnixNano(), i))
        
        // 在不同位置放置呼吸声
        breathDuration := 0.4
        breathCmd := exec.Command(ap.ffmpegPath,
            "-f", "lavfi",
            "-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", breathDuration)+":sample_rate=32000:amplitude=0.012:color=brown",
            "-af", "highpass=f=60,lowpass=f=1200,volume=0.1,afade=t=in:ss=0:d=0.15,afade=t=out:st=0:d=0.15",
            "-c:a", "mp3", "-b:a", "64k", "-y", breathPath)
    }
}
```

### 5. 最终自然处理

#### **添加房间混响和最终压缩**
```go
func (ap *AudioProcessor) buildFinalNaturalProcessing() string {
    filters := []string{
        // 添加微妙的混响营造自然房间声音
        "aecho=0.8:0.88:20:0.4",
        
        // 最终温和压缩
        "acompressor=threshold=0.05:ratio=2:attack=10:release=80",
        
        // 最终标准化
        "loudnorm=I=-18:TP=-2:LRA=7",
        
        // 非常温和的最终淡化
        "afade=t=in:ss=0:d=0.05",
        "afade=t=out:st=0:d=0.05",
    }
    
    return "," + strings.Join(filters, ",")
}
```

## 🎨 音频效果层次

### 1. 基础层：自然语音处理
- **淡入淡出**：避免爆音和突兀切换
- **动态压缩**：模拟自然语音的动态范围
- **频率均衡**：增强语音的温暖感和存在感
- **噪声过滤**：移除不自然的频率

### 2. 过渡层：智能交叉淡化
- **短交叉淡化**：语音片段间（0.15秒）
- **中交叉淡化**：一般过渡（0.2秒）
- **长交叉淡化**：停顿过渡（0.3秒）

### 3. 环境层：自然停顿和呼吸
- **短停顿**：微妙粉红噪声（≤0.5秒）
- **中停顿**：房间音调+呼吸声（0.5-2秒）
- **长停顿**：多个呼吸声+丰富环境音（>2秒）

### 4. 最终层：房间混响和标准化
- **微妙混响**：营造自然房间感
- **最终压缩**：统一音量动态
- **智能标准化**：优化播放音量

## 📊 技术参数

### 音频质量设置
```
采样率: 32000 Hz
比特率: 128k (语音), 64k (环境音)
格式: MP3
声道: 单声道
```

### 滤镜参数
```
淡入淡出: 0.05-0.1秒
压缩比: 2:1 到 3:1
EQ增益: 1-2dB
混响延迟: 20ms
环境音音量: 2-10%
```

### 呼吸声参数
```
频率范围: 60-1200 Hz
音量: 8-10%
时长: 0.3-0.4秒
淡入淡出: 0.1-0.15秒
```

## 🔧 使用方法

### 1. 自动应用
所有音频合成都会自动应用自然化处理，无需额外配置。

### 2. 停顿处理
```go
// 系统会根据停顿时长自动选择合适的处理方式
ap.GenerateSilence(0.3, outputPath)  // 短停顿：微妙房间音调
ap.GenerateSilence(1.5, outputPath)  // 中停顿：房间音调+呼吸声
ap.GenerateSilence(3.0, outputPath)  // 长停顿：多个呼吸声
```

### 3. 音频合成
```go
// 智能合成多个音频片段
inputFiles := []string{"segment1.mp3", "pause1.mp3", "segment2.mp3"}
ap.CombineAudioFiles(inputFiles, "final_natural.mp3")
```

## 🎯 预期效果

### 听觉体验改进
1. **消除机械感**：不再有突兀的音频切换
2. **增加真实感**：像真人在自然环境中说话
3. **提升沉浸感**：微妙的环境音和呼吸声
4. **优化舒适度**：温和的动态范围和频率响应

### 技术指标提升
1. **音频连续性**：完全消除片段间的断裂感
2. **动态一致性**：统一的音量和动态范围
3. **频率平衡**：自然的语音频率分布
4. **环境真实性**：微妙但有效的房间感

## 🚀 部署说明

### 1. 重新构建镜像
```bash
docker build -t ppt-narrator-natural-audio .
```

### 2. 更新服务
```bash
docker-compose down
docker-compose up -d
```

### 3. 验证效果
生成新的音频文件，检查是否具备：
- 平滑的音频过渡
- 自然的停顿声音
- 温暖的语音音质
- 微妙的环境感

## 🔍 监控建议

### 1. 音频质量检查
- 检查是否有爆音或突兀切换
- 验证呼吸声是否自然
- 确认音量动态是否合适

### 2. 性能监控
- 监控音频处理时间
- 检查临时文件清理
- 观察内存使用情况

### 3. 用户反馈
- 收集对音频自然度的评价
- 统计用户满意度变化
- 持续优化参数设置

这个音频自然化增强功能让PPT Narrator生成的音频更加真实自然，大大提升了听众的体验质量！
