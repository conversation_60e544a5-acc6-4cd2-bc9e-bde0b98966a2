# 数字员工平台集成配置指南

本文档详细说明如何配置PPT Narrator与数字员工平台的集成，包括鉴权设置和文件处理功能。

## 🎯 功能概述

数字员工平台集成提供以下功能：

- **官方鉴权支持**：使用Bearer Token进行身份验证
- **透明文件处理**：自动处理文件上传下载
- **MCP工具集成**：通过MCP协议提供PPT处理服务
- **详细日志记录**：记录所有请求的详细信息

## 🚀 快速开始

### 方式1：使用快速启动脚本

**Windows:**
```cmd
start-with-dstaff.bat
```

**Linux/Mac:**
```bash
chmod +x start-with-dstaff.sh
./start-with-dstaff.sh
```

### 方式2：使用环境变量

```bash
# 设置数字员工集成环境变量
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export DSTAFF_ENDPOINT_URL=http://*********:8800
export MCP_DEBUG=true

# 启动服务
docker-compose up old-mcp-pipeline-server
```

### 方式3：使用配置文件

```bash
# 复制配置模板
cp .env.dstaff .env

# 编辑配置文件，设置相应的值
# 启动服务
docker-compose --env-file .env up old-mcp-pipeline-server
```

## ⚙️ 环境变量配置

### 数字员工平台配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DSTAFF_ENABLED` | `false` | 是否启用数字员工平台集成 |
| `DSTAFF_ENDPOINT_URL` | `http://*********:8800` | 数字员工平台端点URL |
| `DSTAFF_USE_OFFICIAL_AUTH` | `false` | 是否使用数字员工官方鉴权 |

### 调试配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_DEBUG` | `false` | 是否启用详细的请求日志 |

### 传统鉴权配置（可选）

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_ACCESS_KEY` | - | 传统访问密钥（仅对/sse端点有效） |

## 🔐 鉴权模式

### 模式1：数字员工官方鉴权（推荐生产环境）

```bash
DSTAFF_ENABLED=true
DSTAFF_USE_OFFICIAL_AUTH=true
```

**特点：**
- 所有请求都需要Bearer Token
- Token通过数字员工API验证
- 支持透明文件上传下载
- 最高安全级别

**使用示例：**
```bash
curl -H "Authorization: Bearer your_token_here" \
     http://localhost:48080/
```

### 模式2：数字员工集成 + 传统鉴权（开发环境）

```bash
DSTAFF_ENABLED=true
DSTAFF_USE_OFFICIAL_AUTH=false
MCP_ACCESS_KEY=your_secret_key
```

**特点：**
- 启用文件上传下载功能
- 使用传统访问密钥鉴权
- 适合内部开发和测试

### 模式3：传统鉴权（向后兼容）

```bash
DSTAFF_ENABLED=false
MCP_ACCESS_KEY=your_secret_key
```

**特点：**
- 仅/sse端点需要验证
- 其他端点无需鉴权
- 保持向后兼容

## 🛠️ MCP工具

启用数字员工集成后，可使用以下MCP工具：

### upload_file
上传PPT文件并开始生成解说视频

**参数：**
- `name`: PPT文件名称
- `file_url`: PPT文件URL
- `user_requirements`: 用户需求（可选）

### get_progress
查询PPT解说视频生成任务的进度

**参数：**
- `project_id`: 项目ID
- `include_steps`: 是否包含详细步骤（可选）

### get_download_url
获取已完成的PPT解说视频的下载链接

**参数：**
- `project_id`: 项目ID

## 📁 透明文件处理

### 文件上传

当发送包含`multipart/form-data`的POST请求时，系统会自动上传到数字员工平台：

```bash
curl -X POST \
  -H "Authorization: Bearer your_token" \
  -F "file=@presentation.pptx" \
  -F "project_name=my_project" \
  -F "task_id=task_123" \
  http://localhost:48080/
```

### 文件下载

通过GET请求下载文件：

```bash
curl -H "Authorization: Bearer your_token" \
  http://localhost:48080/download/task_123/videos/output.mp4 \
  -o downloaded_video.mp4
```

## 🧪 测试配置

### 运行测试脚本

**Windows:**
```cmd
test-dstaff-auth.bat
```

**Linux/Mac:**
```bash
chmod +x test-dstaff-auth.sh
./test-dstaff-auth.sh
```

### 手动测试

1. **测试无鉴权访问：**
   ```bash
   curl http://localhost:48080/
   ```

2. **测试Bearer Token鉴权：**
   ```bash
   curl -H "Authorization: Bearer your_token" http://localhost:48080/
   ```

3. **测试MCP工具：**
   ```bash
   curl -H "Authorization: Bearer your_token" \
        -H "Content-Type: application/json" \
        -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' \
        http://localhost:48080/
   ```

## 📊 日志监控

启用`MCP_DEBUG=true`后，系统会输出详细的请求日志：

```
=== HTTP Request ===
Method: POST
Path: /
Full URL: /
Remote Addr: 127.0.0.1:54321
Headers:
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
Request Body (JSON):
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {...}
}
===================
```

## 🚨 故障排除

### 常见问题

1. **Token验证失败**
   - 检查`DSTAFF_ENDPOINT_URL`是否正确
   - 确认Token是否有效
   - 检查网络连接

2. **文件上传失败**
   - 确认文件路径正确
   - 检查Token和TaskID
   - 查看服务器日志

3. **服务无法启动**
   - 检查环境变量配置
   - 确认ppt-narrator服务正常运行
   - 检查端口48080是否被占用

### 调试步骤

1. **启用详细日志：**
   ```bash
   export MCP_DEBUG=true
   ```

2. **查看服务日志：**
   ```bash
   docker-compose logs -f old-mcp-pipeline-server
   ```

3. **运行测试脚本：**
   ```bash
   ./test-dstaff-auth.sh
   ```

## 📝 最佳实践

1. **生产环境**：使用数字员工官方鉴权
2. **开发环境**：可以使用传统鉴权或禁用鉴权
3. **安全性**：定期更换Token，使用HTTPS传输
4. **监控**：启用日志记录，监控请求状态
5. **测试**：定期运行测试脚本验证配置

## 🔗 相关文档

- [MCP Pipeline Server详细文档](./mcp-pipeline-server/README.md)
- [DStaff集成说明](./mcp-pipeline-server/DSTAFF_INTEGRATION.md)
- [使用示例](./mcp-pipeline-server/USAGE_EXAMPLES.md)
- [调试指南](./mcp-pipeline-server/DEBUG_GUIDE.md)
