# Swagger Docker Build Fix

## 问题描述

在Docker构建过程中遇到错误：
```
package ppt-narrator/docs is not in std
```

这是因为Swagger文档需要在构建前生成。

## 解决方案

### 方案1：使用修改后的Dockerfile（推荐）

已经修改了Dockerfile，在构建过程中自动生成Swagger文档：

```bash
# 构建Docker镜像
docker build -t ppt-narrator .
```

### 方案2：手动生成文档后构建

如果方案1不工作，可以手动生成文档：

```bash
# 1. 安装swag工具
go install github.com/swaggo/swag/cmd/swag@latest

# 2. 生成Swagger文档
swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal

# 3. 构建Docker镜像
docker build -t ppt-narrator .
```

### 方案3：使用构建脚本

使用提供的构建脚本：

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
```

**Windows:**
```cmd
build.bat
```

### 方案4：临时禁用Swagger（快速修复）

如果需要快速构建而不需要Swagger文档，可以临时注释掉main.go中的导入：

```go
// import _ "ppt-narrator/docs" // 临时注释掉
```

然后注释掉Swagger路由：
```go
// router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
```

## 验证修复

构建成功后，启动服务器并访问：
- API健康检查: http://localhost:8080/health
- Swagger UI: http://localhost:8080/swagger/index.html

## 文件修改说明

1. **Dockerfile**: 添加了Swagger文档生成步骤
2. **.dockerignore**: 移除了docs/目录的排除
3. **build.sh/build.bat**: 创建了构建脚本
4. **main.go**: 优化了Swagger导入

## 注意事项

- 确保docs/目录存在且包含docs.go文件
- 如果swag命令失败，Dockerfile会创建一个最小的docs.go文件
- 构建脚本会自动处理所有依赖和文档生成
