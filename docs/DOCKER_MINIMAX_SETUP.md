# Docker + MiniMax TTS 快速部署指南

## 🚀 快速开始

### 1. 准备 MiniMax API 凭据

访问 [MiniMax 开放平台](https://platform.minimaxi.com/) 获取：
- `API Key`
- `Group ID`

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.docker.example .env
```

编辑 `.env` 文件，填入你的 MiniMax 凭据：
```bash
# 必填配置
MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_minimax_group_id_here

# 可选：选择你喜欢的音色和风格
MINIMAX_TTS_VOICE_ID=female-tianmei  # 甜美女声
MINIMAX_TTS_EMOTION=happy            # 高兴情感
NARRATOR_STYLE=亲切自然               # 讲解风格
```

### 3. 启动服务

```bash
# 构建并启动服务
docker-compose up --build

# 或者后台运行
docker-compose up -d --build
```

### 4. 访问服务

打开浏览器访问：http://localhost:38080

## 🎵 音色选择指南

### 男声音色
- `male-qn-qingse` - 青涩青年音色（默认）
- `male-qn-jingying` - 精英青年音色（推荐商务）
- `male-qn-badao` - 霸道青年音色
- `male-qn-daxuesheng` - 青年大学生音色
- `presenter_male` - 男性主持人
- `audiobook_male_1` - 男性有声书1（推荐教学）

### 女声音色
- `female-tianmei` - 甜美女性音色（推荐）
- `female-yujie` - 御姐音色
- `female-chengshu` - 成熟女性音色
- `female-shaonv` - 少女音色
- `presenter_female` - 女性主持人
- `audiobook_female_1` - 女性有声书1（推荐教学）

### 情感选择
- `happy` - 高兴（默认，推荐）
- `calm` - 中性（适合严肃内容）
- `surprised` - 惊讶（适合互动内容）

## 🎯 推荐配置组合

### 专业教学风格
```bash
MINIMAX_TTS_VOICE_ID=audiobook_male_1
MINIMAX_TTS_EMOTION=calm
NARRATOR_ROLE=资深教授
NARRATOR_STYLE=严谨专业
TARGET_AUDIENCE=大学生
```

### 亲切导师风格
```bash
MINIMAX_TTS_VOICE_ID=female-tianmei
MINIMAX_TTS_EMOTION=happy
NARRATOR_ROLE=技术导师
NARRATOR_STYLE=亲切自然
TARGET_AUDIENCE=职场新人
```

### 商务演示风格
```bash
MINIMAX_TTS_VOICE_ID=male-qn-jingying
MINIMAX_TTS_EMOTION=calm
NARRATOR_ROLE=行业专家
NARRATOR_STYLE=简洁明了
TARGET_AUDIENCE=管理人员
```

## 🔧 高级配置

### 自定义端口
```bash
# 在 docker-compose.yml 中修改端口映射
ports:
  - "8080:8080"  # 改为你想要的端口
```

### 性能优化
```bash
# 在 .env 中调整参数
TTS_SPEED=1.1          # 稍快语速
MAX_TOKENS=6000        # 更长内容支持
TEMPERATURE=0.5        # 更稳定输出
```

### 存储配置
```bash
# 持久化数据存储
volumes:
  - ./data:/app/data           # 数据库
  - ./uploads:/app/uploads     # 上传文件
  - ./videos:/app/videos       # 生成的音频和视频
```

## 🐛 故障排除

### 1. 服务启动失败
```bash
# 检查日志
docker-compose logs ppt-narrator

# 常见问题：
# - 端口被占用：修改 docker-compose.yml 中的端口
# - API Key 错误：检查 .env 文件中的配置
```

### 2. 音频生成失败
```bash
# 检查 MiniMax API 配置
curl -X POST "https://api.minimaxi.com/v1/t2a_v2?GroupId=YOUR_GROUP_ID" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"speech-02-hd","text":"测试","stream":false}'
```

### 3. FFmpeg 相关错误
```bash
# 进入容器检查 FFmpeg
docker-compose exec ppt-narrator ffmpeg -version

# 检查音频文件权限
docker-compose exec ppt-narrator ls -la /app/videos/
```

## 📊 监控和日志

### 查看实时日志
```bash
# 查看所有日志
docker-compose logs -f

# 只查看应用日志
docker-compose logs -f ppt-narrator
```

### 健康检查
```bash
# 检查服务状态
curl http://localhost:38080/health

# 检查容器状态
docker-compose ps
```

## 🔄 更新和维护

### 更新服务
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build -d
```

### 清理资源
```bash
# 停止服务
docker-compose down

# 清理未使用的镜像
docker system prune -f

# 清理所有相关资源
docker-compose down --rmi all --volumes
```

## 📞 获取帮助

- 查看详细文档：[MINIMAX_TTS_GUIDE.md](./MINIMAX_TTS_GUIDE.md)
- 配置参考：[.env.minimax.example](./.env.minimax.example)
- 测试功能：运行 `test_minimax_tts.bat` 或 `test_minimax_tts.sh`
