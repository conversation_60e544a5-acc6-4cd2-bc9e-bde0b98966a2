# 视频生成强制重启功能指南

## 问题背景

当程序重启或异常中断时，项目状态可能卡在 `generating_video` 状态，但实际上视频生成进程已经停止。这种情况下，正常的视频生成API会拒绝请求：

```json
{
    "success": false,
    "error": "Project status 'generating_video' is not ready for video generation. Expected: [audio_ready video_failed]. Current project info: ID=xxx, Status=generating_video. Use ?force=true to force restart."
}
```

## 解决方案

我们提供了三种方式来处理这种情况：

### 1. 使用 force 参数（推荐）

在正常的生成或重试API中添加 `?force=true` 参数：

```bash
# 强制重新开始视频生成
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/generate?force=true" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'

# 强制重试视频生成
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/retry?force=true" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'
```

### 2. 使用专用的强制重启API

```bash
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/force-restart" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'
```

### 3. 先诊断再决定

```bash
# 先诊断项目状态
curl -X GET "http://localhost:8080/api/v1/video/{projectId}/diagnose"

# 根据诊断结果决定使用哪种方式
```

## API 详细说明

### 1. 强制参数支持

#### 生成视频 (带强制参数)
```http
POST /api/v1/video/{projectId}/generate?force=true
Content-Type: application/json

{
  "fps": 1,
  "resolution": "1280x720",
  "output_format": "mp4"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "Video generation started",
  "data": {
    "project_id": "project-123",
    "status": "generating",
    "request": {...}
  }
}
```

#### 重试视频 (带强制参数)
```http
POST /api/v1/video/{projectId}/retry?force=true
Content-Type: application/json

{
  "fps": 1,
  "resolution": "1280x720",
  "output_format": "mp4"
}
```

### 2. 专用强制重启API

```http
POST /api/v1/video/{projectId}/force-restart
Content-Type: application/json

{
  "fps": 1,
  "resolution": "1280x720",
  "output_format": "mp4"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "Video generation force restarted. Found 3 slides with audio. Original status: generating_video",
  "data": {
    "project_id": "project-123",
    "status": "generating_video",
    "original_status": "generating_video",
    "slides_with_audio": 3,
    "total_slides": 5,
    "force_restart": true
  }
}
```

### 3. 项目诊断API

```http
GET /api/v1/video/{projectId}/diagnose
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "project_id": "project-123",
    "project_name": "My Presentation",
    "current_status": "generating_video",
    "error_message": "",
    "audio_path": "/app/videos/project-123/combined_audio.mp3",
    "video_path": "",
    "total_slides": 5,
    "slides_with_screenshots": 5,
    "slides_with_narration": 5,
    "slides_with_audio": 3,
    "can_generate_video": false,
    "readiness_check": {
      "status_ok": false,
      "has_audio": true,
      "audio_count": 3,
      "required_states": ["audio_ready", "video_failed"]
    },
    "recommendations": [
      "Project status is 'generating_video', expected 'audio_ready' or 'video_failed'"
    ]
  }
}
```

## 工作流程

### 场景1：程序重启后状态卡住

```bash
# 1. 检查项目状态
curl -X GET "http://localhost:8080/api/v1/projects/{projectId}"
# 发现状态为 "generating_video"

# 2. 尝试正常生成（会失败）
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/generate" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'
# 返回错误，提示使用 ?force=true

# 3. 使用强制参数重新开始
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/generate?force=true" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'
# 成功启动
```

### 场景2：使用诊断API

```bash
# 1. 诊断项目
curl -X GET "http://localhost:8080/api/v1/video/{projectId}/diagnose"

# 2. 根据诊断结果决定
# 如果 can_generate_video 为 false 且有音频文件，使用强制重启
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/force-restart" \
  -H "Content-Type: application/json" \
  -d '{"fps":1,"resolution":"1280x720","output_format":"mp4"}'
```

## 安全机制

### 1. 音频文件检查
强制重启仍然会检查是否有音频文件：

```json
{
  "success": false,
  "error": "No audio files found. Please generate audio first."
}
```

### 2. 状态重置
强制重启会将项目状态重置为 `audio_ready`：

```
generating_video → audio_ready → generating_video
```

### 3. 日志记录
系统会记录强制重启操作：

```
Force restarting video generation for project xxx (original status: generating_video -> audio_ready)
```

## 支持的状态转换

### 正常情况
- `audio_ready` → `generating_video` ✅
- `video_failed` → `generating_video` ✅

### 强制重启情况
- `generating_video` → `audio_ready` → `generating_video` ✅
- `narration_ready` → `audio_ready` → `generating_video` ✅
- 任何状态 → `audio_ready` → `generating_video` ✅ (如果有音频文件)

## 最佳实践

### 1. 优先使用 force 参数
```bash
# 推荐：在原有API上加 force 参数
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/generate?force=true"

# 而不是：使用专用API
curl -X POST "http://localhost:8080/api/v1/video/{projectId}/force-restart"
```

### 2. 先诊断再操作
```bash
# 1. 先了解项目状态
curl -X GET "http://localhost:8080/api/v1/video/{projectId}/diagnose"

# 2. 根据诊断结果选择合适的操作
```

### 3. 监控日志
检查系统日志确认强制重启是否成功：
```bash
docker-compose logs -f ppt-narrator | grep "Force restarting"
```

## 错误处理

### 常见错误

#### 1. 没有音频文件
```json
{
  "success": false,
  "error": "No audio files found. Please generate audio first."
}
```

**解决方案：** 先生成音频，再尝试视频生成。

#### 2. 项目不存在
```json
{
  "success": false,
  "error": "Project not found"
}
```

**解决方案：** 检查项目ID是否正确。

#### 3. 数据库更新失败
```json
{
  "success": false,
  "error": "Failed to reset project status: ..."
}
```

**解决方案：** 检查数据库连接和权限。

## 监控和调试

### 1. 检查项目状态
```bash
curl -X GET "http://localhost:8080/api/v1/projects/{projectId}"
```

### 2. 查看详细诊断
```bash
curl -X GET "http://localhost:8080/api/v1/video/{projectId}/diagnose"
```

### 3. 监控生成进度
```bash
curl -X GET "http://localhost:8080/api/v1/video/{projectId}/progress"
```

### 4. 查看系统日志
```bash
docker-compose logs -f ppt-narrator
```

这个强制重启功能确保了即使在程序重启或异常中断的情况下，用户也能够恢复视频生成流程，大大提高了系统的可靠性和用户体验。
