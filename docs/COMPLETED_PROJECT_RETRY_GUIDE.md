# 已完成项目重试功能使用指南

## 🎯 功能概述

现在PPT Narrator支持对**已完成的项目**进行重新生成，用户可以选择从任意阶段重新开始处理，无需重新上传PPT文件。这对于以下场景非常有用：

- 🔄 **重新生成讲稿**: 使用不同的用户需求生成新的讲稿内容
- 🎵 **重新合成音频**: 修复音频问题或使用不同的TTS设置
- 🎬 **重新制作视频**: 更新视频参数或修复视频问题
- 📸 **重新生成截图**: 使用不同的截图设置

## 🔗 API端点

### 1. 获取重试选项

**GET** `/api/v1/pipeline/{projectId}/retry-options`

查看项目的重试选项和可用阶段。

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/retry-options"
```

#### 响应示例（已完成项目）
```json
{
  "success": true,
  "data": {
    "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
    "project_name": "【赛前直播】赛制讲解PPT",
    "project_status": "completed",
    "can_retry": true,
    "retry_type": "regenerate",
    "retry_message": "项目已完成，可以重新生成任意阶段的内容",
    "current_stage": "completed",
    "is_running": false,
    "available_stages": [
      {
        "stage": "screenshot",
        "name": "截图生成",
        "description": "重新生成PPT幻灯片截图",
        "progress": 0
      },
      {
        "stage": "narration",
        "name": "讲稿生成", 
        "description": "重新生成AI讲稿内容",
        "progress": 20
      },
      {
        "stage": "audio",
        "name": "音频合成",
        "description": "重新生成语音音频文件",
        "progress": 40
      },
      {
        "stage": "video",
        "name": "视频制作",
        "description": "重新生成最终演示视频",
        "progress": 70
      }
    ]
  }
}
```

#### 响应示例（正在运行的项目）
```json
{
  "success": true,
  "data": {
    "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
    "project_name": "【赛前直播】赛制讲解PPT",
    "project_status": "processing",
    "can_retry": false,
    "current_stage": "audio",
    "is_running": true,
    "retry_message": "项目正在处理中（audio阶段），无法重试",
    "available_stages": []
  }
}
```

### 2. 执行重试/重新生成

**POST** `/api/v1/pipeline/{projectId}/retry`

从指定阶段重新开始处理。

#### 请求体
```json
{
  "stage": "audio",
  "user_requirements": "请生成更加专业和正式的讲稿内容"
}
```

#### 参数说明
- `stage`: 要重新开始的阶段 (`screenshot`, `narration`, `audio`, `video`)
- `user_requirements`: 用户需求（可选，主要用于讲稿生成阶段）

#### 示例请求
```bash
curl -X POST "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/retry" \
  -H "Content-Type: application/json" \
  -d '{
    "stage": "narration",
    "user_requirements": "请生成更加生动有趣的讲稿，适合年轻观众"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "message": "Pipeline regeneration started from narration stage",
  "project_status": "completed",
  "retry_stage": "narration"
}
```

## 📋 重试阶段说明

### 1. 截图生成 (screenshot)
- **作用**: 重新生成PPT幻灯片的截图
- **适用场景**: 
  - 截图质量不满意
  - 需要不同的截图参数
  - 原始截图文件损坏
- **影响**: 会重新生成所有后续阶段（讲稿、音频、视频）

### 2. 讲稿生成 (narration)
- **作用**: 重新生成AI讲稿内容
- **适用场景**:
  - 讲稿内容不满意
  - 需要不同风格的讲稿
  - 更改用户需求
- **影响**: 会重新生成音频和视频
- **特殊参数**: 支持`user_requirements`参数

### 3. 音频合成 (audio)
- **作用**: 重新生成语音音频文件
- **适用场景**:
  - 音频质量问题
  - 音频无声问题
  - 需要不同的TTS设置
- **影响**: 会重新生成视频

### 4. 视频制作 (video)
- **作用**: 重新生成最终演示视频
- **适用场景**:
  - 视频质量不满意
  - 需要不同的视频参数
  - 视频文件损坏
- **影响**: 仅重新生成视频文件

## 💻 前端集成示例

### JavaScript/React示例

```javascript
class RetryManager {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  // 获取重试选项
  async getRetryOptions(projectId) {
    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/retry-options`);
    return await response.json();
  }

  // 执行重试
  async retryFromStage(projectId, stage, userRequirements = '') {
    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/retry`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        stage: stage,
        user_requirements: userRequirements
      })
    });
    return await response.json();
  }

  // 监控进度
  async monitorProgress(projectId, onProgress) {
    const checkProgress = async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/progress`);
        const data = await response.json();
        
        if (data.success) {
          const progress = data.progress;
          onProgress(progress);
          
          // 如果未完成，继续监控
          if (progress.current_stage !== 'completed' && progress.current_stage !== 'failed') {
            setTimeout(checkProgress, 2000); // 2秒后再次检查
          }
        }
      } catch (error) {
        console.error('Progress monitoring error:', error);
      }
    };
    
    checkProgress();
  }
}

// React组件示例
function RetryPanel({ projectId, projectName, onRetryStart, onRetryComplete }) {
  const [retryOptions, setRetryOptions] = useState(null);
  const [loading, setLoading] = useState(false);
  const [userRequirements, setUserRequirements] = useState('');
  const [selectedStage, setSelectedStage] = useState('');

  const retryManager = new RetryManager();

  useEffect(() => {
    loadRetryOptions();
  }, [projectId]);

  const loadRetryOptions = async () => {
    try {
      const options = await retryManager.getRetryOptions(projectId);
      setRetryOptions(options.data);
    } catch (error) {
      console.error('Failed to load retry options:', error);
    }
  };

  const handleRetry = async () => {
    if (!selectedStage) {
      alert('请选择要重试的阶段');
      return;
    }

    setLoading(true);
    onRetryStart && onRetryStart();

    try {
      const result = await retryManager.retryFromStage(projectId, selectedStage, userRequirements);
      
      if (result.success) {
        alert(`${result.message}`);
        
        // 开始监控进度
        retryManager.monitorProgress(projectId, (progress) => {
          console.log('Progress:', progress);
          
          if (progress.current_stage === 'completed') {
            onRetryComplete && onRetryComplete(true);
            setLoading(false);
          } else if (progress.current_stage === 'failed') {
            onRetryComplete && onRetryComplete(false);
            setLoading(false);
          }
        });
      } else {
        alert(`重试失败: ${result.error}`);
        setLoading(false);
      }
    } catch (error) {
      alert(`重试异常: ${error.message}`);
      setLoading(false);
    }
  };

  if (!retryOptions) return <div>加载中...</div>;

  if (!retryOptions.can_retry) {
    return (
      <div className="retry-panel disabled">
        <h3>重试选项</h3>
        <p>{retryOptions.retry_message}</p>
      </div>
    );
  }

  return (
    <div className="retry-panel">
      <h3>重新生成内容</h3>
      <p>{retryOptions.retry_message}</p>
      
      <div className="stage-selection">
        <label>选择重新开始的阶段:</label>
        <select 
          value={selectedStage} 
          onChange={(e) => setSelectedStage(e.target.value)}
          disabled={loading}
        >
          <option value="">请选择阶段</option>
          {retryOptions.available_stages.map(stage => (
            <option key={stage.stage} value={stage.stage}>
              {stage.name} - {stage.description}
            </option>
          ))}
        </select>
      </div>

      {selectedStage === 'narration' && (
        <div className="user-requirements">
          <label>用户需求 (可选):</label>
          <textarea
            value={userRequirements}
            onChange={(e) => setUserRequirements(e.target.value)}
            placeholder="请描述您对讲稿的具体要求..."
            disabled={loading}
          />
        </div>
      )}

      <button 
        onClick={handleRetry} 
        disabled={loading || !selectedStage}
        className="retry-btn"
      >
        {loading ? '处理中...' : '开始重新生成'}
      </button>
    </div>
  );
}
```

## 🧪 测试工具

### 使用测试脚本

```bash
python test_retry_completed_project.py
```

该脚本提供完整的交互式测试流程：

1. ✅ 健康检查
2. 📊 获取项目状态
3. 🔄 获取重试选项
4. 📋 选择重试阶段
5. ⚙️ 配置用户需求（如需要）
6. 🚀 执行重试
7. 👀 监控进度
8. 📈 显示最终结果

### 手动测试命令

```bash
# 1. 检查项目状态
curl "http://localhost:8080/api/v1/pipeline/{projectId}/status"

# 2. 获取重试选项
curl "http://localhost:8080/api/v1/pipeline/{projectId}/retry-options"

# 3. 从音频阶段重试
curl -X POST "http://localhost:8080/api/v1/pipeline/{projectId}/retry" \
  -H "Content-Type: application/json" \
  -d '{"stage": "audio"}'

# 4. 从讲稿阶段重试（带用户需求）
curl -X POST "http://localhost:8080/api/v1/pipeline/{projectId}/retry" \
  -H "Content-Type: application/json" \
  -d '{
    "stage": "narration",
    "user_requirements": "请生成更加专业和详细的讲稿内容"
  }'

# 5. 监控进度
curl "http://localhost:8080/api/v1/pipeline/{projectId}/progress"
```

## 🎯 使用场景示例

### 场景1: 重新生成讲稿
```bash
# 用户对当前讲稿不满意，想要更生动的内容
curl -X POST "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/retry" \
  -H "Content-Type: application/json" \
  -d '{
    "stage": "narration",
    "user_requirements": "请生成更加生动有趣的讲稿，使用通俗易懂的语言，适合普通观众理解"
  }'
```

### 场景2: 修复音频问题
```bash
# 音频合成后没有声音，重新生成音频
curl -X POST "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/retry" \
  -H "Content-Type: application/json" \
  -d '{"stage": "audio"}'
```

### 场景3: 重新制作视频
```bash
# 视频质量不满意，仅重新生成视频
curl -X POST "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/retry" \
  -H "Content-Type: application/json" \
  -d '{"stage": "video"}'
```

## 🔧 注意事项

### 1. 项目状态检查
- ✅ **已完成项目**: 支持从任意阶段重新生成
- ✅ **失败项目**: 支持从任意阶段重试
- ❌ **正在运行项目**: 不支持重试，需要等待完成或取消

### 2. 数据保护
- 🔄 重新生成会**覆盖**对应阶段及后续阶段的文件
- 💾 建议在重新生成前先**下载**当前版本的文件
- 📁 原始PPT文件不会被影响

### 3. 性能考虑
- ⏱️ 重新生成的时间取决于选择的起始阶段
- 🔄 从早期阶段开始会需要更长时间
- 💡 如果只是音频或视频问题，建议从对应阶段开始

### 4. 用户需求参数
- 📝 主要用于`narration`阶段
- 🎯 提供具体、清晰的需求描述
- 💡 可以指定语言风格、目标受众、内容重点等

现在你可以轻松地对已完成的项目进行重新生成，无需重新上传PPT文件！🎉
