# EasyVoice TTS 语音合成功能使用指南

## 功能概述

本系统现已支持 EasyVoice TTS 接口，提供高质量的多语言语音合成服务，特别适合中文语音生成。

## 主要特性

### 1. 多语言支持
- 支持中文（普通话）多种音色
- 基于 Azure TTS 技术的高质量语音合成
- 支持男声、女声多种选择

### 2. 灵活的语音参数调节
- **语速调节**: 支持 -50% 到 +200% 的语速调整
- **音调调节**: 支持 -50Hz 到 +50Hz 的音调调整  
- **音量调节**: 支持 -50% 到 +50% 的音量调整

### 3. 简单的认证方式
- 使用基础 HTTP 认证（用户名/密码）
- 无需复杂的 API Key 管理

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# TTS 提供商选择
TTS_PROVIDER=easyvoice

# EasyVoice TTS 配置
EASYVOICE_API_URL=https://easyvoice.wetolink.com/api/v1/tts/generate
EASYVOICE_USERNAME=your_username_here
EASYVOICE_PASSWORD=your_password_here
EASYVOICE_VOICE=zh-CN-YunxiNeural
EASYVOICE_RATE=0%
EASYVOICE_PITCH=0Hz
EASYVOICE_VOLUME=0%
```

### 配置参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `EASYVOICE_API_URL` | EasyVoice API 地址 | `https://easyvoice.wetolink.com/api/v1/tts/generate` | - |
| `EASYVOICE_USERNAME` | 用户名 | - | `your_username` |
| `EASYVOICE_PASSWORD` | 密码 | - | `your_password` |
| `EASYVOICE_VOICE` | 语音模型 | `zh-CN-YunxiNeural` | `zh-CN-XiaoxiaoNeural` |
| `EASYVOICE_RATE` | 语速调节 | `0%` | `+20%`, `-10%` |
| `EASYVOICE_PITCH` | 音调调节 | `0Hz` | `+5Hz`, `-3Hz` |
| `EASYVOICE_VOLUME` | 音量调节 | `0%` | `+10%`, `-5%` |

## 可用语音模型

### 男声音色
- `zh-CN-YunxiNeural` - 云希（推荐，自然亲切）
- `zh-CN-YunyangNeural` - 云扬（成熟稳重）
- `zh-CN-YunjianNeural` - 云健（活力充沛）
- `zh-CN-YunfengNeural` - 云枫（温和儒雅）
- `zh-CN-YunhaoNeural` - 云皓（清朗大气）
- `zh-CN-YunjieNeural` - 云杰（专业商务）
- `zh-CN-YunxiaNeural` - 云夏（年轻活泼）
- `zh-CN-YunyeNeural` - 云野（磁性深沉）
- `zh-CN-YunzeNeural` - 云泽（温暖亲和）

### 女声音色
- `zh-CN-XiaoxiaoNeural` - 晓晓（甜美可爱）
- `zh-CN-XiaohanNeural` - 晓涵（知性优雅）
- `zh-CN-XiaomengNeural` - 晓梦（温柔细腻）
- `zh-CN-XiaomoNeural` - 晓墨（清新自然）
- `zh-CN-XiaoqiuNeural` - 晓秋（成熟知性）
- `zh-CN-XiaoruiNeural` - 晓睿（聪慧理性）
- `zh-CN-XiaoshuangNeural` - 晓双（活泼开朗）
- `zh-CN-XiaoxuanNeural` - 晓萱（温婉大方）
- `zh-CN-XiaoyanNeural` - 晓颜（青春靓丽）
- `zh-CN-XiaoyouNeural` - 晓悠（悠然自得）
- `zh-CN-XiaozhenNeural` - 晓甄（端庄典雅）

## API 使用方法

### 1. 获取可用的 TTS 提供商

```bash
GET /api/v1/tts/providers
```

响应示例：
```json
{
  "success": true,
  "data": {
    "providers": [
      {
        "name": "easyvoice",
        "display_name": "EasyVoice TTS",
        "description": "EasyVoice的多语言语音合成服务",
        "voices": ["zh-CN-YunxiNeural", "zh-CN-XiaoxiaoNeural", ...],
        "available": true,
        "current": true
      }
    ],
    "current_provider": "easyvoice"
  }
}
```

### 2. 切换 TTS 提供商

```bash
POST /api/v1/tts/provider
Content-Type: application/json

{
  "provider": "easyvoice",
  "voice": "zh-CN-YunxiNeural"
}
```

### 3. 测试 TTS 提供商

```bash
GET /api/v1/tts/test/easyvoice
```

## 使用流程

### 1. 配置 EasyVoice 凭据
确保在环境变量中正确配置了用户名和密码。

### 2. 选择语音模型
根据项目需求选择合适的男声或女声模型。

### 3. 调整语音参数
- 对于快节奏内容，可以设置 `EASYVOICE_RATE=+10%`
- 对于严肃内容，可以设置 `EASYVOICE_PITCH=-2Hz`
- 对于背景音乐场景，可以设置 `EASYVOICE_VOLUME=-10%`

### 4. 生成音频
使用标准的音频生成 API：

```bash
POST /api/v1/audio/{projectId}/generate
```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查用户名和密码是否正确
   - 确认账户是否有足够的配额

2. **音频生成失败**
   - 检查网络连接
   - 验证 API URL 是否正确
   - 确认文本内容不包含特殊字符

3. **音质问题**
   - 尝试不同的语音模型
   - 调整语速和音调参数
   - 检查原始文本的标点符号

### 调试模式

启用详细日志：

```bash
export GIN_MODE=debug
export LOG_LEVEL=debug
```

### 音频文件位置

生成的音频文件保存在：
- 单个幻灯片音频：`videos/{project_id}/audio/slide_001.mp3`
- 合并音频：`videos/{project_id}/audio/combined.mp3`

## 性能优化

### 建议配置

1. **生产环境**：使用稳定的语音模型如 `zh-CN-YunxiNeural`
2. **开发环境**：可以使用相同配置进行测试
3. **语速设置**：建议使用 -10% 到 +20% 之间的调整
4. **并发控制**：避免同时生成多个项目的音频

### 缓存策略

系统会自动缓存：
- 相同文本和参数的音频片段
- 合并后的完整音频

## 与其他 TTS 提供商的比较

| 特性 | EasyVoice | OpenAI | MiniMax |
|------|-----------|---------|---------|
| 中文支持 | ✅ 优秀 | ✅ 良好 | ✅ 优秀 |
| 音色选择 | ✅ 丰富 | ⚠️ 有限 | ✅ 丰富 |
| 参数调节 | ✅ 灵活 | ⚠️ 有限 | ✅ 灵活 |
| 认证方式 | ✅ 简单 | ⚠️ API Key | ⚠️ 复杂 |
| 价格 | ✅ 经济 | ⚠️ 较高 | ✅ 合理 |

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持基础语音合成功能
- 集成 20+ 种中文语音模型
- 支持语速、音调、音量调节

## 技术支持

如有问题，请参考：
- [项目 GitHub Issues](https://github.com/your-repo/issues)
- [EasyVoice 官方文档](https://easyvoice.wetolink.com/docs)
- [API 参考文档](./docs/api/tts.md)
