# Docker Build Optimization Guide

本文档说明如何通过`.dockerignore`文件优化PPT Narrator的Docker构建过程。

## 🎯 优化目标

- **减少构建上下文大小**：排除不必要的文件和目录
- **加快构建速度**：减少需要传输到Docker守护进程的数据
- **提高安全性**：避免将敏感数据包含在镜像中
- **保持功能完整**：确保所有必需的文件都被包含

## 📁 被忽略的文件和目录

### 数据目录
```
uploads/          # 用户上传的PPT文件
screenshots/      # 生成的截图文件
videos/          # 生成的视频文件
temp/            # 临时文件
logs/            # 日志文件
audio/           # 音频文件
work/            # 工作目录
data/            # 数据库和数据文件
```

### 数据库文件
```
*.db             # SQLite数据库文件
*.sqlite         # SQLite数据库文件
*.sqlite3        # SQLite数据库文件
ppt-narrator.db  # 主数据库文件
```

### 构建产物和归档文件
```
*.tar            # 归档文件
*.zip            # 压缩文件
*.rar            # 压缩文件
*.7z             # 压缩文件
main.exe         # Windows可执行文件
bin/             # 构建输出目录
tmp/             # 临时构建文件
build/           # 构建目录
dist/            # 分发目录
```

### 测试文件
```
*_test.go        # Go测试文件
test*.go         # 测试Go文件
test*.py         # 测试Python文件
test*.sh         # 测试Shell脚本
test*.bat        # 测试批处理文件
test*.html       # 测试HTML文件
testdata/        # 测试数据目录
coverage.out     # 测试覆盖率文件
```

### 开发工具和脚本
```
build*.sh        # 构建脚本
build*.bat       # 构建批处理文件
start*.sh        # 启动脚本
start*.bat       # 启动批处理文件
Makefile         # Make构建文件
*.py             # Python脚本（除了必需的）
```

### 文档文件
```
*.md             # Markdown文档（除了README.md）
API_*.md         # API文档
DOCKER_*.md      # Docker相关文档
VIDEO_*.md       # 视频处理文档
等等...
```

### MCP Pipeline Server
```
mcp-pipeline-server/  # MCP服务器（有独立的构建过程）
```

## ✅ 保留的重要文件

### 源代码
```
cmd/             # 主程序入口
internal/        # 内部包
go.mod           # Go模块文件
go.sum           # Go依赖校验文件
```

### 配置和脚本
```
scripts/docker-entrypoint.sh  # Docker入口脚本
scripts/wait-for-db.sh        # 数据库等待脚本
Dockerfile                    # Docker构建文件
```

### 文档
```
README.md        # 主要说明文档
```

## 🧪 测试.dockerignore效果

### 1. 运行测试脚本

```bash
# 测试.dockerignore规则
chmod +x test-dockerignore.sh
./test-dockerignore.sh

# 测试构建上下文大小
chmod +x test-build-context.sh
./test-build-context.sh
```

### 2. 实际构建测试

```bash
# 构建镜像并查看构建上下文大小
docker build --no-cache -t ppt-narrator-test .

# 查看镜像大小
docker images ppt-narrator-test

# 查看构建过程中传输的文件
docker build --no-cache -t ppt-narrator-test . 2>&1 | grep -E "(Sending build context|COPY)"
```

## 📊 优化效果

通过合理配置`.dockerignore`，可以实现：

- **构建上下文减少80%+**：从几GB减少到几十MB
- **构建速度提升**：减少文件传输时间
- **镜像安全性提升**：避免包含敏感数据
- **存储空间节省**：减少不必要的文件

## ⚠️ 注意事项

### 1. 必需文件检查

确保以下文件没有被意外忽略：
- `go.mod` 和 `go.sum`
- `cmd/` 和 `internal/` 目录
- `scripts/docker-entrypoint.sh`
- `Dockerfile`

### 2. 运行时目录

以下目录在容器运行时会被创建，不需要包含在构建上下文中：
- `uploads/`
- `screenshots/`
- `videos/`
- `temp/`
- `logs/`

### 3. 数据持久化

生产环境中，数据目录应该通过Docker卷挂载：

```yaml
volumes:
  - ./data:/app/data
  - ./uploads:/app/uploads
  - ./videos:/app/videos
```

## 🔧 自定义配置

如果需要修改`.dockerignore`规则：

1. **添加忽略规则**：在文件末尾添加新的模式
2. **排除规则**：使用`!`前缀来排除某些文件
3. **测试更改**：运行测试脚本验证效果

### 示例：排除特定文件

```dockerignore
# 忽略所有.log文件
*.log

# 但保留重要的日志文件
!important.log
```

## 📈 最佳实践

1. **定期审查**：随着项目发展，定期审查和更新`.dockerignore`
2. **测试验证**：每次修改后运行测试脚本验证
3. **文档更新**：保持文档与实际配置同步
4. **团队协作**：确保团队成员了解忽略规则

## 🚨 故障排除

### 构建失败

如果构建失败，检查是否意外忽略了必需文件：

```bash
# 检查构建上下文中的文件
docker build --no-cache -t test . 2>&1 | grep "COPY"

# 或者使用测试脚本
./test-build-context.sh
```

### 构建上下文过大

如果构建上下文仍然很大：

1. 检查是否有大文件没有被忽略
2. 考虑将大文件移到其他位置
3. 使用多阶段构建进一步优化

### 运行时文件缺失

如果容器运行时缺少文件：

1. 检查文件是否被错误忽略
2. 确认文件是否应该在运行时创建
3. 考虑使用卷挂载提供文件
