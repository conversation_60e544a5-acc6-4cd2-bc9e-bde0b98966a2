# 全流程处理API使用指南

## 🚀 概述

全流程处理API让你可以一键上传PPTX文件，自动完成截图→讲稿→语音→视频的完整流程，支持进度查看和断点重试。

## 📋 API端点

### 1. 上传并开始全流程处理

**POST** `/api/v1/pipeline/upload-and-process`

#### 请求参数
- **Content-Type**: `multipart/form-data`
- **file**: PPTX文件 (必需)
- **user_requirements**: 用户需求描述 (可选)
- **project_name**: 项目名称 (可选，默认使用文件名)

#### 示例请求
```bash
curl -X POST "http://localhost:8080/api/v1/pipeline/upload-and-process" \
  -F "file=@presentation.pptx" \
  -F "user_requirements=请生成专业的商务演示讲稿，语调正式，适合企业汇报" \
  -F "project_name=季度业务汇报"
```

#### 响应示例
```json
{
  "success": true,
  "message": "File uploaded successfully and processing started",
  "project_id": "550e8400-e29b-41d4-a716-446655440000",
  "project": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "季度业务汇报",
    "status": "processing",
    "file_path": "/app/uploads/550e8400-e29b-41d4-a716-446655440000/presentation.pptx",
    "original_file_name": "presentation.pptx",
    "file_size": 2048576,
    "created_at": "2025-08-03T10:00:00Z",
    "updated_at": "2025-08-03T10:00:00Z"
  }
}
```

### 2. 查看处理进度

**GET** `/api/v1/pipeline/{projectId}/progress`

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/pipeline/550e8400-e29b-41d4-a716-446655440000/progress"
```

#### 响应示例
```json
{
  "success": true,
  "progress": {
    "project_id": "550e8400-e29b-41d4-a716-446655440000",
    "current_stage": "narration",
    "progress": 35.0,
    "message": "Generating narration for slide 3/8",
    "start_time": "2025-08-03T10:00:00Z",
    "last_update": "2025-08-03T10:02:30Z",
    "estimated_time": "00:05:30",
    "stage_details": {
      "total_slides": 8,
      "processed_slides": 3,
      "current_slide": 3
    },
    "can_retry": false
  }
}
```

### 3. 断点重试

**POST** `/api/v1/pipeline/{projectId}/retry`

#### 请求体
```json
{
  "stage": "narration",
  "user_requirements": "请重新生成更加详细的讲稿"
}
```

#### 支持的重试阶段
- `screenshot`: 从截图生成开始
- `narration`: 从讲稿生成开始
- `audio`: 从音频生成开始
- `video`: 从视频生成开始

#### 示例请求
```bash
curl -X POST "http://localhost:8080/api/v1/pipeline/550e8400-e29b-41d4-a716-446655440000/retry" \
  -H "Content-Type: application/json" \
  -d '{
    "stage": "narration",
    "user_requirements": "请重新生成更加详细的讲稿，增加更多技术细节"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "message": "Pipeline retry started from narration stage"
}
```

### 4. 取消处理

**POST** `/api/v1/pipeline/{projectId}/cancel`

#### 示例请求
```bash
curl -X POST "http://localhost:8080/api/v1/pipeline/550e8400-e29b-41d4-a716-446655440000/cancel"
```

#### 响应示例
```json
{
  "success": true,
  "message": "Pipeline cancelled successfully"
}
```

### 5. 获取项目状态

**GET** `/api/v1/pipeline/{projectId}/status`

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/pipeline/550e8400-e29b-41d4-a716-446655440000/status"
```

#### 响应示例
```json
{
  "success": true,
  "status": {
    "project_id": "550e8400-e29b-41d4-a716-446655440000",
    "project_name": "季度业务汇报",
    "project_status": "audio_ready",
    "is_running": true,
    "slide_count": 8,
    "screenshot_count": 8,
    "narration_count": 8,
    "audio_count": 8,
    "has_video": false,
    "created_at": "2025-08-03T10:00:00Z",
    "updated_at": "2025-08-03T10:05:00Z",
    "progress": {
      "current_stage": "video",
      "progress": 85.0,
      "message": "Generating final video..."
    }
  }
}
```

### 6. 查看活跃的处理任务

**GET** `/api/v1/pipeline/active`

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/pipeline/active"
```

#### 响应示例
```json
{
  "success": true,
  "pipelines": [
    {
      "project_id": "550e8400-e29b-41d4-a716-446655440000",
      "project_name": "季度业务汇报",
      "status": "generating_video",
      "progress": {
        "current_stage": "video",
        "progress": 85.0,
        "message": "Generating final video..."
      }
    }
  ],
  "count": 1
}
```

## 🔄 处理阶段说明

### 阶段流程
1. **upload** - 文件上传完成
2. **screenshot** - 生成PPT截图 (0-20%)
3. **narration** - 生成讲稿内容 (20-40%)
4. **audio** - 生成语音文件 (40-70%)
5. **video** - 生成最终视频 (70-100%)
6. **completed** - 处理完成
7. **failed** - 处理失败

### 状态说明
- **processing** - 正在处理中
- **screenshots_ready** - 截图生成完成
- **narration_ready** - 讲稿生成完成
- **audio_ready** - 音频生成完成
- **completed** - 全部完成
- **failed** - 处理失败

## 🛠️ 使用示例

### 完整流程示例

```bash
#!/bin/bash

# 1. 上传文件并开始处理
echo "上传文件并开始处理..."
RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/pipeline/upload-and-process" \
  -F "file=@presentation.pptx" \
  -F "user_requirements=请生成专业的技术演示讲稿" \
  -F "project_name=技术分享")

PROJECT_ID=$(echo $RESPONSE | jq -r '.project_id')
echo "项目ID: $PROJECT_ID"

# 2. 监控处理进度
echo "监控处理进度..."
while true; do
  PROGRESS=$(curl -s "http://localhost:8080/api/v1/pipeline/$PROJECT_ID/progress")
  STAGE=$(echo $PROGRESS | jq -r '.progress.current_stage')
  PERCENT=$(echo $PROGRESS | jq -r '.progress.progress')
  MESSAGE=$(echo $PROGRESS | jq -r '.progress.message')
  
  echo "[$STAGE] $PERCENT% - $MESSAGE"
  
  if [ "$STAGE" = "completed" ]; then
    echo "处理完成！"
    break
  elif [ "$STAGE" = "failed" ]; then
    echo "处理失败，尝试重试..."
    curl -s -X POST "http://localhost:8080/api/v1/pipeline/$PROJECT_ID/retry" \
      -H "Content-Type: application/json" \
      -d '{"stage": "screenshot", "user_requirements": "请重新处理"}'
  fi
  
  sleep 10
done

# 3. 获取最终结果
echo "获取最终结果..."
curl -s "http://localhost:8080/api/v1/projects/$PROJECT_ID" | jq '.'
```

### JavaScript示例

```javascript
class PPTNarratorClient {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  async uploadAndProcess(file, userRequirements = '', projectName = '') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_requirements', userRequirements);
    formData.append('project_name', projectName);

    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/upload-and-process`, {
      method: 'POST',
      body: formData
    });

    return await response.json();
  }

  async getProgress(projectId) {
    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/progress`);
    return await response.json();
  }

  async retry(projectId, stage, userRequirements = '') {
    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/retry`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ stage, user_requirements: userRequirements })
    });

    return await response.json();
  }

  async cancel(projectId) {
    const response = await fetch(`${this.baseUrl}/api/v1/pipeline/${projectId}/cancel`, {
      method: 'POST'
    });

    return await response.json();
  }

  async monitorProgress(projectId, onProgress, onComplete, onError) {
    const checkProgress = async () => {
      try {
        const result = await this.getProgress(projectId);
        const progress = result.progress;

        onProgress(progress);

        if (progress.current_stage === 'completed') {
          onComplete(progress);
        } else if (progress.current_stage === 'failed') {
          onError(progress);
        } else {
          setTimeout(checkProgress, 5000); // 5秒后再次检查
        }
      } catch (error) {
        onError(error);
      }
    };

    checkProgress();
  }
}

// 使用示例
const client = new PPTNarratorClient();

// 上传并处理
const fileInput = document.getElementById('file-input');
const file = fileInput.files[0];

client.uploadAndProcess(file, '请生成专业的演示讲稿', '我的演示')
  .then(result => {
    if (result.success) {
      const projectId = result.project_id;
      
      // 监控进度
      client.monitorProgress(
        projectId,
        (progress) => {
          console.log(`[${progress.current_stage}] ${progress.progress}% - ${progress.message}`);
          // 更新UI进度条
          updateProgressBar(progress.progress);
        },
        (progress) => {
          console.log('处理完成！');
          // 显示完成界面
          showCompletionPage(projectId);
        },
        (error) => {
          console.error('处理失败:', error);
          // 显示错误信息和重试选项
          showErrorPage(projectId, error);
        }
      );
    }
  });
```

## 🎯 最佳实践

### 1. 错误处理
- 始终检查API响应的`success`字段
- 对于失败的请求，使用断点重试功能
- 实现适当的超时和重试机制

### 2. 进度监控
- 使用轮询方式定期检查进度
- 建议轮询间隔为5-10秒
- 在UI中显示详细的进度信息

### 3. 用户体验
- 提供清晰的进度指示器
- 允许用户取消长时间运行的任务
- 在失败时提供重试选项

### 4. 性能优化
- 对于大文件，考虑分块上传
- 实现客户端缓存避免重复请求
- 使用WebSocket进行实时进度更新（未来版本）

## 🔧 故障排除

### 常见错误
1. **文件格式不支持** - 确保上传的是.pptx文件
2. **文件过大** - 检查文件大小限制（默认100MB）
3. **处理超时** - 使用断点重试功能
4. **资源不足** - 检查服务器资源使用情况

### 调试技巧
1. 检查服务器日志
2. 使用详细的错误信息
3. 验证API端点和参数
4. 测试网络连接

这个全流程API让PPT到视频的转换变得简单高效，支持完整的生命周期管理！🎉
