# 详细错误记录系统使用指南

## 🎯 概述

为了更好地调试和解决问题，我们实现了一个全面的错误记录系统，提供详细的错误信息、堆栈跟踪和上下文信息。

## 🔍 错误记录功能

### 1. 详细错误结构

```go
type DetailedError struct {
    Message     string            `json:"message"`      // 错误消息
    Stage       PipelineStage     `json:"stage"`        // 失败的阶段
    Timestamp   time.Time         `json:"timestamp"`    // 错误发生时间
    StackTrace  string            `json:"stack_trace"`  // 堆栈跟踪
    Context     map[string]string `json:"context"`      // 上下文信息
    Retryable   bool              `json:"retryable"`    // 是否可重试
    RetryCount  int               `json:"retry_count"`  // 重试次数
    LastRetry   *time.Time        `json:"last_retry"`   // 最后重试时间
}
```

### 2. 增强的进度信息

```go
type PipelineProgress struct {
    // ... 原有字段
    Error         string         `json:"error,omitempty"`
    DetailedError *DetailedError `json:"detailed_error,omitempty"`
    // ... 其他字段
}
```

## 📋 API端点

### 1. 获取详细错误信息

**GET** `/api/v1/pipeline/{projectId}/error`

#### 示例请求
```bash
curl -X GET "http://localhost:8080/api/v1/pipeline/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/error"
```

#### 响应示例
```json
{
  "success": true,
  "detailed_error": {
    "message": "failed to combine audio files with natural transitions: exit status 1",
    "stage": "audio",
    "timestamp": "2025-08-03T11:40:12.023242747Z",
    "stack_trace": "goroutine 123 [running]:\nppt-narrator/internal/services.(*PipelineService).getStackTrace(...)\n\t/app/internal/services/pipeline_service.go:385\n...",
    "context": {
      "stage": "audio",
      "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
      "retry": "true"
    },
    "retryable": true,
    "retry_count": 1,
    "last_retry": "2025-08-03T11:40:15.123456789Z"
  },
  "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
  "current_stage": "failed",
  "progress": 70
}
```

### 2. 获取进度信息（包含详细错误）

**GET** `/api/v1/pipeline/{projectId}/progress`

现在的进度API也会包含详细错误信息：

```json
{
  "success": true,
  "progress": {
    "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
    "current_stage": "failed",
    "progress": 70,
    "message": "Audio generation failed: failed to combine audio files...",
    "error": "failed to combine audio files with natural transitions: exit status 1",
    "detailed_error": {
      "message": "failed to combine audio files with natural transitions: exit status 1",
      "stage": "audio",
      "timestamp": "2025-08-03T11:40:12.023242747Z",
      "context": {
        "stage": "audio",
        "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648"
      },
      "retryable": true,
      "retry_count": 0
    },
    "can_retry": true,
    "retry_from": "audio"
  }
}
```

## 📊 服务器端日志记录

### 1. 详细错误报告

服务器会自动记录详细的错误信息：

```
=== DETAILED ERROR REPORT ===
Project ID: ffcdac19-5ed3-47db-a5a9-6dfa3024b648
Stage: audio
Message: failed to combine audio files with natural transitions: exit status 1
Timestamp: 2025-08-03T11:40:12Z
Retryable: true
Retry Count: 0
Context:
  stage: audio
  project_id: ffcdac19-5ed3-47db-a5a9-6dfa3024b648
Stack Trace:
goroutine 123 [running]:
ppt-narrator/internal/services.(*PipelineService).getStackTrace(...)
    /app/internal/services/pipeline_service.go:385
ppt-narrator/internal/services.(*PipelineService).updateProgressWithDetailedError(...)
    /app/internal/services/pipeline_service.go:350
...
=== END ERROR REPORT ===
```

### 2. 音频处理专用日志

对于音频处理错误，会记录更详细的信息：

```
=== AUDIO COMBINATION ERROR ===
Project ID: ffcdac19-5ed3-47db-a5a9-6dfa3024b648
Audio files count: 3
Output path: /app/uploads/ffcdac19-5ed3-47db-a5a9-6dfa3024b648/audio/combined.mp3
Error: failed to combine audio files with natural transitions: exit status 1
Audio file 1: /app/uploads/.../audio/slide_001.mp3
  Size: 245760 bytes
  Modified: 2025-08-03T11:40:10Z
Audio file 2: /app/uploads/.../audio/slide_002.mp3
  Size: 189440 bytes
  Modified: 2025-08-03T11:40:11Z
Audio file 3: /app/uploads/.../audio/slide_003.mp3
  Size: 167936 bytes
  Modified: 2025-08-03T11:40:12Z
=== END AUDIO ERROR ===
```

## 🛠️ 使用场景

### 1. 开发调试

```bash
# 获取详细错误信息
curl "http://localhost:8080/api/v1/pipeline/{projectId}/error" | jq '.'

# 查看服务器日志
docker-compose logs ppt-narrator | grep "DETAILED ERROR REPORT" -A 20
```

### 2. 用户界面集成

```javascript
async function getDetailedError(projectId) {
    try {
        const response = await fetch(`/api/v1/pipeline/${projectId}/error`);
        const data = await response.json();
        
        if (data.success && data.detailed_error) {
            const error = data.detailed_error;
            
            // 显示用户友好的错误信息
            showErrorDialog({
                title: `${error.stage} 阶段失败`,
                message: error.message,
                timestamp: new Date(error.timestamp).toLocaleString(),
                retryable: error.retryable,
                retryCount: error.retry_count
            });
            
            // 发送详细信息给开发团队
            if (error.stack_trace) {
                sendErrorReport({
                    projectId: projectId,
                    error: error,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                });
            }
        }
    } catch (err) {
        console.error('Failed to get detailed error:', err);
    }
}
```

### 3. 自动重试逻辑

```javascript
async function handleFailedPipeline(projectId) {
    const errorData = await getDetailedError(projectId);
    
    if (errorData.detailed_error && errorData.detailed_error.retryable) {
        const retryCount = errorData.detailed_error.retry_count;
        
        if (retryCount < 3) {
            // 自动重试
            console.log(`Attempting retry ${retryCount + 1}/3`);
            await retryPipeline(projectId, errorData.detailed_error.stage);
        } else {
            // 达到最大重试次数，通知用户
            showMaxRetriesReached(projectId, errorData.detailed_error);
        }
    }
}
```

## 🔧 故障排除指南

### 1. 音频处理错误

**错误特征**: `failed to combine audio files with natural transitions: exit status 1`

**调试步骤**:
1. 检查详细错误信息中的音频文件列表
2. 验证每个音频文件是否存在且大小正常
3. 查看FFmpeg相关的错误输出
4. 尝试使用重试功能

```bash
# 获取详细错误
curl "http://localhost:8080/api/v1/pipeline/{projectId}/error"

# 重试音频阶段
curl -X POST "http://localhost:8080/api/v1/pipeline/{projectId}/retry" \
  -H "Content-Type: application/json" \
  -d '{"stage": "audio"}'
```

### 2. 堆栈跟踪分析

堆栈跟踪可以帮助定位问题的确切位置：

```
goroutine 123 [running]:
ppt-narrator/internal/services.(*AudioProcessor).combineWithNaturalTransitions(...)
    /app/internal/services/audio_processor.go:352
ppt-narrator/internal/services.(*TTSService).GenerateAudio(...)
    /app/internal/services/tts_service.go:184
```

这表明问题出现在音频处理器的`combineWithNaturalTransitions`方法中。

### 3. 上下文信息利用

上下文信息提供了错误发生时的环境：

```json
{
  "context": {
    "stage": "audio",
    "project_id": "ffcdac19-5ed3-47db-a5a9-6dfa3024b648",
    "retry": "true",
    "user_requirements": "请生成专业的讲稿"
  }
}
```

## 📈 监控和分析

### 1. 错误统计

```bash
# 统计各阶段的错误频率
docker-compose logs ppt-narrator | grep "DETAILED ERROR REPORT" -A 5 | grep "Stage:" | sort | uniq -c

# 查看最近的错误
docker-compose logs ppt-narrator --since="1h" | grep "DETAILED ERROR REPORT" -A 10
```

### 2. 性能分析

通过时间戳分析处理时间：

```bash
# 分析处理时间
docker-compose logs ppt-narrator | grep -E "(started|completed|failed)" | tail -20
```

## 🎯 最佳实践

### 1. 错误处理

- 始终检查`detailed_error`字段
- 根据`retryable`字段决定是否重试
- 记录重试次数避免无限重试

### 2. 用户体验

- 向用户显示友好的错误消息
- 提供重试选项
- 在后台发送详细错误报告

### 3. 开发调试

- 使用堆栈跟踪定位问题
- 分析上下文信息了解错误环境
- 监控错误趋势和模式

这个详细的错误记录系统让调试变得更加高效，帮助快速定位和解决问题！🔍
