# PPT Narrator 断点续传 API 使用指南

## 概述

PPT Narrator 现在支持断点续传功能，当讲稿生成或音频生成过程中遇到错误（如网络问题、API限制等）时，可以从中断点继续处理，无需重新开始。

## API 端点

### 1. 讲稿生成断点续传

#### 获取讲稿生成进度
```http
GET /api/v1/narration/{projectId}/progress
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "project_id": "project-123",
    "progress": 60,
    "status": "narration_failed",
    "message": "Narration generation failed at slide 3"
  }
}
```

#### 从断点续传讲稿生成
```http
POST /api/v1/narration/{projectId}/resume
Content-Type: application/json

{
  "user_requirements": "专业技术讲解，适合大学生"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "Narration generation resumed from slide 3",
  "data": {
    "current_slide": 3,
    "completed_slides": 2,
    "total_slides": 5
  }
}
```

### 2. 音频生成断点续传

#### 获取音频生成进度
```http
GET /api/v1/audio/{projectId}/progress
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "total_slides": 5,
    "completed_slides": 2,
    "current_slide": 3,
    "can_resume": true,
    "slide_details": [
      {
        "slide_number": 1,
        "title": "Introduction",
        "has_narration": true,
        "has_audio": true,
        "audio_path": "/app/videos/project-123/audio/slide_001.mp3",
        "audio_size": 1024000
      },
      {
        "slide_number": 2,
        "title": "Main Content",
        "has_narration": true,
        "has_audio": true,
        "audio_path": "/app/videos/project-123/audio/slide_002.mp3",
        "audio_size": 2048000
      },
      {
        "slide_number": 3,
        "title": "Details",
        "has_narration": true,
        "has_audio": false,
        "audio_path": "",
        "audio_size": 0
      }
    ]
  }
}
```

#### 从断点续传音频生成
```http
POST /api/v1/audio/{projectId}/resume
```

**响应示例：**
```json
{
  "success": true,
  "message": "Audio generation resumed from slide 3",
  "data": {
    "current_slide": 3,
    "completed_slides": 2,
    "total_slides": 5
  }
}
```

## 使用流程

### 场景1：讲稿生成中断后续传

```bash
# 1. 检查项目状态
curl -X GET "http://localhost:8080/api/v1/projects/{projectId}"

# 2. 如果状态为 "narration_failed"，获取详细进度
curl -X GET "http://localhost:8080/api/v1/narration/{projectId}/progress"

# 3. 如果可以续传，调用续传API
curl -X POST "http://localhost:8080/api/v1/narration/{projectId}/resume" \
  -H "Content-Type: application/json" \
  -d '{"user_requirements": "专业技术讲解"}'

# 4. 监控进度直到完成
curl -X GET "http://localhost:8080/api/v1/narration/{projectId}/progress"
```

### 场景2：音频生成中断后续传

```bash
# 1. 检查项目状态
curl -X GET "http://localhost:8080/api/v1/projects/{projectId}"

# 2. 如果状态为 "audio_failed"，获取音频进度
curl -X GET "http://localhost:8080/api/v1/audio/{projectId}/progress"

# 3. 如果 can_resume 为 true，调用续传API
curl -X POST "http://localhost:8080/api/v1/audio/{projectId}/resume"

# 4. 监控进度直到完成
curl -X GET "http://localhost:8080/api/v1/audio/{projectId}/progress"
```

## 项目状态说明

| 状态 | 描述 | 可执行操作 |
|------|------|-----------|
| `completed` | 截图生成完成 | 可以开始讲稿生成 |
| `generating_narration` | 正在生成讲稿 | 等待完成或失败 |
| `narration_ready` | 讲稿生成完成 | 可以开始音频生成 |
| `narration_failed` | 讲稿生成失败 | 可以续传讲稿生成 |
| `generating_audio` | 正在生成音频 | 等待完成或失败 |
| `audio_ready` | 音频生成完成 | 可以开始视频生成 |
| `audio_failed` | 音频生成失败 | 可以续传音频生成 |
| `video_ready` | 视频生成完成 | 流程完成 |
| `video_failed` | 视频生成失败 | 可以重新生成视频 |

## 错误处理

### 常见错误响应

#### 1. 项目不存在
```json
{
  "success": false,
  "error": "Project not found"
}
```

#### 2. 无法续传
```json
{
  "success": false,
  "error": "Project cannot be resumed. Either no slides are completed or all slides are already completed."
}
```

#### 3. 状态不正确
```json
{
  "success": false,
  "error": "project is not ready for narration generation. Current status: processing"
}
```

### 重试策略

系统内置了智能重试机制：

- **最大重试次数**: 8次（可配置）
- **基础延迟**: 5秒（可配置）
- **最大延迟**: 120秒（可配置）
- **退避策略**: 指数退避 + 随机抖动

可通过环境变量调整：
```bash
TTS_MAX_RETRIES=8
TTS_BASE_DELAY=5
TTS_MAX_DELAY=120
TTS_BACKOFF_FACTOR=2.0
```

## 监控和调试

### 实时监控脚本

```bash
#!/bin/bash
PROJECT_ID="your-project-id"
BASE_URL="http://localhost:8080"

echo "监控项目: $PROJECT_ID"

while true; do
    # 获取项目状态
    STATUS=$(curl -s "$BASE_URL/api/v1/projects/$PROJECT_ID" | jq -r '.data.status')
    echo "[$(date)] 项目状态: $STATUS"
    
    case $STATUS in
        "narration_failed")
            echo "讲稿生成失败，可以续传"
            # 自动续传（可选）
            # curl -X POST "$BASE_URL/api/v1/narration/$PROJECT_ID/resume" -H "Content-Type: application/json" -d '{"user_requirements":"续传"}'
            ;;
        "audio_failed")
            echo "音频生成失败，可以续传"
            # 自动续传（可选）
            # curl -X POST "$BASE_URL/api/v1/audio/$PROJECT_ID/resume"
            ;;
        "narration_ready"|"audio_ready"|"video_ready")
            echo "步骤完成"
            break
            ;;
    esac
    
    sleep 10
done
```

### 日志查看

```bash
# Docker 环境
docker-compose logs -f ppt-narrator

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" ppt-narrator
```

## 最佳实践

1. **定期检查进度**: 每10-30秒检查一次进度，避免过于频繁的请求
2. **错误处理**: 始终检查API响应的 `success` 字段
3. **状态验证**: 在调用续传API前，先检查项目状态和进度
4. **超时处理**: 设置合理的HTTP超时时间（建议30-60秒）
5. **日志记录**: 记录所有API调用和响应，便于问题排查

## 示例代码

### Python 示例

```python
import requests
import time
import json

class PPTNarratorClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
    
    def get_project_status(self, project_id):
        response = requests.get(f"{self.base_url}/api/v1/projects/{project_id}")
        return response.json()
    
    def get_narration_progress(self, project_id):
        response = requests.get(f"{self.base_url}/api/v1/narration/{project_id}/progress")
        return response.json()
    
    def resume_narration(self, project_id, requirements="专业讲解"):
        data = {"user_requirements": requirements}
        response = requests.post(
            f"{self.base_url}/api/v1/narration/{project_id}/resume",
            json=data
        )
        return response.json()
    
    def get_audio_progress(self, project_id):
        response = requests.get(f"{self.base_url}/api/v1/audio/{project_id}/progress")
        return response.json()
    
    def resume_audio(self, project_id):
        response = requests.post(f"{self.base_url}/api/v1/audio/{project_id}/resume")
        return response.json()
    
    def monitor_project(self, project_id, auto_resume=False):
        """监控项目进度并可选择自动续传"""
        while True:
            status_resp = self.get_project_status(project_id)
            if not status_resp.get('success'):
                print(f"获取状态失败: {status_resp.get('error')}")
                break
            
            status = status_resp['data']['status']
            print(f"[{time.strftime('%H:%M:%S')}] 项目状态: {status}")
            
            if status == "narration_failed" and auto_resume:
                print("自动续传讲稿生成...")
                resume_resp = self.resume_narration(project_id)
                if resume_resp.get('success'):
                    print("续传成功")
                else:
                    print(f"续传失败: {resume_resp.get('error')}")
            
            elif status == "audio_failed" and auto_resume:
                print("自动续传音频生成...")
                resume_resp = self.resume_audio(project_id)
                if resume_resp.get('success'):
                    print("续传成功")
                else:
                    print(f"续传失败: {resume_resp.get('error')}")
            
            elif status in ["video_ready", "completed"]:
                print("项目完成！")
                break
            
            time.sleep(10)

# 使用示例
client = PPTNarratorClient()
client.monitor_project("your-project-id", auto_resume=True)
```

这个API指南提供了完整的断点续传功能使用方法，包括API调用、错误处理、监控和最佳实践。
