#!/bin/bash

# PPT Narrator 部署脚本
# 用于快速部署整个PPT Narrator商业服务平台

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p uploads
    mkdir -p videos
    mkdir -p temp
    mkdir -p screenshots
    mkdir -p audio
    mkdir -p work
    mkdir -p logs
    
    # 设置目录权限
    chmod 755 uploads videos temp screenshots audio work logs
    
    log_success "目录创建完成"
}

# 生成环境配置文件
generate_env_files() {
    log_info "生成环境配置文件..."
    
    # 生成随机JWT密钥
    JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || echo "your-super-secret-jwt-key-$(date +%s)")
    
    # 生成数据库密码
    DB_PASSWORD=$(openssl rand -base64 16 2>/dev/null || echo "ppt_narrator_$(date +%s)")
    
    # 创建 .env 文件
    cat > .env << EOF
# PPT Narrator 环境配置

# 数据库配置
POSTGRES_DB=ppt_narrator
POSTGRES_USER=ppt_narrator
POSTGRES_PASSWORD=${DB_PASSWORD}

# JWT配置
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRATION=24h

# 业务配置
DEFAULT_CREDITS=100
PPT_GENERATION_COST=10
MAX_FILE_SIZE=104857600

# 服务端口
BUSINESS_API_PORT=8081
USER_FRONTEND_PORT=3000
ADMIN_DASHBOARD_PORT=3001
PPT_GENERATOR_PORT=8080

# 外部访问地址
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001
API_URL=http://localhost:8081
EOF
    
    log_success "环境配置文件生成完成"
}

# 构建和启动服务
deploy_services() {
    log_info "构建和启动服务..."
    
    # 停止现有服务
    docker-compose down 2>/dev/null || true
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待数据库启动..."
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U ppt_narrator >/dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    # 等待Redis
    log_info "等待Redis启动..."
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    # 等待业务API
    log_info "等待业务API启动..."
    for i in {1..60}; do
        if curl -f http://localhost:8081/health >/dev/null 2>&1; then
            break
        fi
        sleep 3
    done
    
    log_success "所有服务已就绪"
}

# 初始化数据
initialize_data() {
    log_info "初始化系统数据..."
    
    # 等待一段时间确保数据库迁移完成
    sleep 10
    
    log_success "数据初始化完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "PPT Narrator 部署完成！"
    echo
    echo "=========================================="
    echo "           服务访问地址"
    echo "=========================================="
    echo "用户前端:     http://localhost:3000"
    echo "管理员后台:   http://localhost:3001"
    echo "业务API:      http://localhost:8081"
    echo "PPT生成器:    http://localhost:8080"
    echo "=========================================="
    echo
    echo "默认账号信息:"
    echo "管理员账号:   admin / password"
    echo "测试用户:     testuser / password"
    echo
    echo "常用命令:"
    echo "查看服务状态: docker-compose ps"
    echo "查看日志:     docker-compose logs -f [service]"
    echo "停止服务:     docker-compose down"
    echo "重启服务:     docker-compose restart"
    echo
}

# 主函数
main() {
    echo "=========================================="
    echo "       PPT Narrator 部署脚本"
    echo "=========================================="
    echo
    
    check_dependencies
    create_directories
    generate_env_files
    deploy_services
    wait_for_services
    initialize_data
    show_deployment_info
}

# 处理中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
