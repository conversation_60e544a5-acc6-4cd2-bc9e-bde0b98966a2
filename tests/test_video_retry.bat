@echo off
chcp 65001 >nul
echo === PPT Narrator 视频生成重试功能测试 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080

echo 测试目标: 验证视频生成失败后的重试功能
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问
    echo 请先启动服务: docker-compose up -d
    pause
    exit /b 1
)

REM 2. 获取现有项目
echo.
echo 2. 获取现有项目...
curl -s "%BASE_URL%/api/v1/projects" > temp_projects.json 2>nul

if exist temp_projects.json (
    echo 当前项目:
    findstr /C:"name\|id\|status" temp_projects.json 2>nul
    echo.
) else (
    echo ⚠️  无法获取项目列表
)
del temp_projects.json >nul 2>&1

REM 3. 手动输入项目ID进行测试
echo 请选择一个项目进行视频生成重试测试
set /p TEST_PROJECT_ID="请输入项目ID (或按回车跳过): "

if "%TEST_PROJECT_ID%"=="" (
    echo 跳过实际测试，显示API使用方法
    goto :show_usage
)

REM 4. 诊断项目状态
echo.
echo 3. 诊断项目状态...
curl -s "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/diagnose" > temp_diagnose.json 2>nul

if exist temp_diagnose.json (
    echo 项目诊断结果:
    
    REM 显示关键信息
    findstr /C:"current_status\|can_generate_video\|slides_with_audio\|total_slides" temp_diagnose.json
    echo.
    
    REM 检查是否可以生成视频
    findstr /C:"can_generate_video.*true" temp_diagnose.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 项目可以进行视频生成
        set CAN_GENERATE=true
    ) else (
        echo ⚠️  项目当前不能进行视频生成
        echo 建议:
        findstr /C:"recommendations" temp_diagnose.json
        set CAN_GENERATE=false
    )
) else (
    echo ❌ 无法获取项目诊断信息
    set CAN_GENERATE=false
)
del temp_diagnose.json >nul 2>&1

REM 5. 测试视频生成或重试
if "%CAN_GENERATE%"=="true" (
    echo.
    echo 4. 测试视频生成功能...
    
    REM 首先尝试正常生成
    echo 尝试正常视频生成...
    curl -s -X POST "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/generate" ^
        -H "Content-Type: application/json" ^
        -d "{\"fps\":1,\"resolution\":\"1280x720\",\"output_format\":\"mp4\"}" > temp_generate_response.json 2>nul
    
    if exist temp_generate_response.json (
        findstr /C:"success.*true" temp_generate_response.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 视频生成启动成功
        ) else (
            echo ❌ 视频生成启动失败
            echo 错误信息:
            type temp_generate_response.json
            echo.
            
            REM 如果失败，测试重试功能
            echo 测试重试功能...
            curl -s -X POST "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/retry" ^
                -H "Content-Type: application/json" ^
                -d "{\"fps\":1,\"resolution\":\"1280x720\",\"output_format\":\"mp4\"}" > temp_retry_response.json 2>nul
            
            if exist temp_retry_response.json (
                findstr /C:"success.*true" temp_retry_response.json >nul
                if %errorlevel% equ 0 (
                    echo ✅ 视频重试启动成功
                    type temp_retry_response.json
                ) else (
                    echo ❌ 视频重试启动失败
                    type temp_retry_response.json
                )
            )
            del temp_retry_response.json >nul 2>&1
        )
    )
    del temp_generate_response.json >nul 2>&1
    
    REM 6. 监控进度
    echo.
    echo 5. 监控视频生成进度...
    echo 每10秒检查一次进度，按任意键停止监控
    
    :monitor_loop
    timeout /t 10 >nul 2>&1
    curl -s "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/progress" > temp_monitor_progress.json 2>nul
    
    if exist temp_monitor_progress.json (
        echo [%time%] 当前进度:
        findstr /C:"progress\|status\|current_step" temp_monitor_progress.json 2>nul
        
        REM 检查是否完成
        findstr /C:"video_ready\|completed" temp_monitor_progress.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 视频生成完成！
            del temp_monitor_progress.json >nul 2>&1
            goto :test_complete
        )
        
        REM 检查是否失败
        findstr /C:"video_failed\|failed" temp_monitor_progress.json >nul
        if %errorlevel% equ 0 (
            echo ❌ 视频生成失败，可以尝试重试
            del temp_monitor_progress.json >nul 2>&1
            
            REM 自动测试重试功能
            echo 自动测试重试功能...
            curl -s -X POST "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/retry" ^
                -H "Content-Type: application/json" ^
                -d "{\"fps\":1,\"resolution\":\"1280x720\",\"output_format\":\"mp4\"}" > temp_auto_retry.json 2>nul
            
            if exist temp_auto_retry.json (
                findstr /C:"success.*true" temp_auto_retry.json >nul
                if %errorlevel% equ 0 (
                    echo ✅ 自动重试启动成功
                    echo 继续监控...
                ) else (
                    echo ❌ 自动重试失败
                    type temp_auto_retry.json
                    del temp_auto_retry.json >nul 2>&1
                    goto :test_complete
                )
            )
            del temp_auto_retry.json >nul 2>&1
        )
    )
    del temp_monitor_progress.json >nul 2>&1
    
    REM 检查用户是否想停止监控
    echo 按任意键停止监控...
    timeout /t 1 >nul 2>&1
    if not errorlevel 1 goto :test_complete
    
    goto :monitor_loop
)

:test_complete
echo.
echo 6. 最终状态检查...
curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%" > temp_final_status.json 2>nul

if exist temp_final_status.json (
    echo 项目最终状态:
    findstr /C:"status\|video_path\|error_message" temp_final_status.json
    echo.
)
del temp_final_status.json >nul 2>&1

goto :end

:show_usage
echo.
echo === API 使用方法 ===
echo.
echo 1. 诊断项目状态:
echo    GET %BASE_URL%/api/v1/video/{projectId}/diagnose
echo.
echo 2. 正常视频生成:
echo    POST %BASE_URL%/api/v1/video/{projectId}/generate
echo    Content-Type: application/json
echo    {
echo      "fps": 1,
echo      "resolution": "1280x720",
echo      "output_format": "mp4"
echo    }
echo.
echo 3. 重试视频生成:
echo    POST %BASE_URL%/api/v1/video/{projectId}/retry
echo    Content-Type: application/json
echo    {
echo      "fps": 1,
echo      "resolution": "1280x720", 
echo      "output_format": "mp4"
echo    }
echo.
echo 4. 检查视频生成进度:
echo    GET %BASE_URL%/api/v1/video/{projectId}/progress
echo.
echo === 重试功能特性 ===
echo.
echo ✅ 详细错误信息 - 明确告知为什么不能生成视频
echo ✅ 项目诊断 - 检查项目状态和音频文件
echo ✅ 智能重试 - 只允许在合适状态下重试
echo ✅ 状态重置 - 重试时自动重置项目状态
echo ✅ 进度监控 - 实时查看生成进度
echo.
echo === 诊断响应示例 ===
echo {
echo   "success": true,
echo   "data": {
echo     "project_id": "project-123",
echo     "current_status": "audio_ready",
echo     "total_slides": 5,
echo     "slides_with_audio": 3,
echo     "can_generate_video": true,
echo     "readiness_check": {
echo       "status_ok": true,
echo       "has_audio": true,
echo       "audio_count": 3
echo     },
echo     "recommendations": []
echo   }
echo }

:end
echo.
echo === 测试完成 ===
echo.
echo 功能特性:
echo ✅ 详细错误诊断 - 明确显示问题原因
echo ✅ 智能重试机制 - 只在合适状态下允许重试
echo ✅ 状态自动重置 - 重试时自动恢复项目状态
echo ✅ 进度实时监控 - 查看生成进度和状态
echo ✅ 音频文件检查 - 确保有足够的音频文件
echo.
pause
