@echo off
chcp 65001 >nul
echo === PPT Narrator 视频时长修复验证 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080
set TEST_PROJECT_ID=

echo 测试目标: 验证生成的视频时长与音频时长一致
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问
    echo 请先启动服务: docker-compose up -d
    pause
    exit /b 1
)

REM 2. 检查 FFprobe 可用性
echo.
echo 2. 检查 FFprobe 工具...
ffprobe -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FFprobe 可用
) else (
    echo ❌ FFprobe 不可用，无法验证时长
    echo 请安装 FFmpeg 工具包
    pause
    exit /b 1
)

REM 3. 获取现有项目
echo.
echo 3. 获取现有项目...
curl -s "%BASE_URL%/api/v1/projects" > temp_projects.json 2>nul

if exist temp_projects.json (
    echo 当前项目:
    findstr /C:"name\|id\|status" temp_projects.json 2>nul | findstr /C:"video_ready\|audio_ready"
    echo.
) else (
    echo ⚠️  无法获取项目列表
)
del temp_projects.json >nul 2>&1

REM 4. 手动输入项目ID进行测试
echo 请选择一个已生成视频的项目进行测试
set /p TEST_PROJECT_ID="请输入项目ID (或按回车跳过): "

if "%TEST_PROJECT_ID%"=="" (
    echo 跳过实际测试，显示修复说明
    goto :show_fix_info
)

REM 5. 检查项目状态
echo.
echo 4. 检查项目状态...
curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%" > temp_project.json 2>nul

if exist temp_project.json (
    echo 项目信息:
    findstr /C:"name\|status\|video_path" temp_project.json
    echo.
    
    REM 检查是否有视频
    findstr /C:"video_ready\|video_path" temp_project.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 项目有视频文件
        set HAS_VIDEO=true
    ) else (
        echo ⚠️  项目没有视频文件，需要先生成视频
        set HAS_VIDEO=false
    )
) else (
    echo ❌ 无法获取项目信息
    set HAS_VIDEO=false
)
del temp_project.json >nul 2>&1

REM 6. 如果没有视频，尝试生成
if "%HAS_VIDEO%"=="false" (
    echo.
    echo 5. 生成视频进行测试...
    
    curl -s -X POST "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/generate" ^
        -H "Content-Type: application/json" ^
        -d "{\"duration_per_slide\":0,\"fps\":1,\"resolution\":\"1280x720\",\"output_format\":\"mp4\"}" > temp_video_response.json 2>nul
    
    if exist temp_video_response.json (
        findstr /C:"success.*true" temp_video_response.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 视频生成启动成功
            echo 等待视频生成完成...
            
            REM 等待视频生成完成
            :wait_video_loop
            timeout /t 15 >nul 2>&1
            curl -s "%BASE_URL%/api/v1/video/%TEST_PROJECT_ID%/progress" > temp_video_progress.json 2>nul
            
            if exist temp_video_progress.json (
                findstr /C:"progress.*100\|video_ready" temp_video_progress.json >nul
                if %errorlevel% equ 0 (
                    echo ✅ 视频生成完成
                    del temp_video_progress.json >nul 2>&1
                    set HAS_VIDEO=true
                    goto :check_duration
                )
                
                findstr /C:"video_failed\|failed" temp_video_progress.json >nul
                if %errorlevel% equ 0 (
                    echo ❌ 视频生成失败
                    type temp_video_progress.json
                    del temp_video_progress.json >nul 2>&1
                    goto :show_fix_info
                )
                
                echo 生成中...
                del temp_video_progress.json >nul 2>&1
                goto :wait_video_loop
            )
        ) else (
            echo ❌ 视频生成启动失败
            type temp_video_response.json
        )
    )
    del temp_video_response.json >nul 2>&1
)

:check_duration
if "%HAS_VIDEO%"=="true" (
    echo.
    echo 6. 检查视频和音频时长...
    
    REM 获取项目的幻灯片信息
    curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%/slides" > temp_slides.json 2>nul
    
    if exist temp_slides.json (
        echo 分析各幻灯片的音频和视频时长...
        echo.
        
        REM 这里需要通过API获取具体的文件路径，然后使用FFprobe检查
        REM 由于批处理脚本的限制，我们显示如何手动检查
        
        echo 📊 时长对比分析:
        echo.
        echo 请在Docker容器中执行以下命令来检查时长:
        echo.
        echo docker exec -it ^<container_name^> bash
        echo.
        echo # 检查音频时长
        echo ffprobe -v quiet -show_entries format=duration -of csv=p=0 /app/videos/%TEST_PROJECT_ID%/audio/slide_001.mp3
        echo.
        echo # 检查对应视频片段时长
        echo ffprobe -v quiet -show_entries format=duration -of csv=p=0 /app/temp/video_%TEST_PROJECT_ID%/segment_001.mp4
        echo.
        echo # 检查最终合并视频时长
        echo ffprobe -v quiet -show_entries format=duration -of csv=p=0 /app/videos/%TEST_PROJECT_ID%/final.mp4
        echo.
        
        REM 显示幻灯片信息
        echo 项目幻灯片信息:
        findstr /C:"slide_number\|narration_audio\|duration" temp_slides.json 2>nul
        
    ) else (
        echo ❌ 无法获取幻灯片信息
    )
    del temp_slides.json >nul 2>&1
)

goto :end

:show_fix_info
echo.
echo === 视频时长修复说明 ===
echo.
echo 🎯 问题描述:
echo   生成的视频时长总是比音频时长长一些
echo.
echo 🔧 修复方案:
echo   1. 使用精确的音频时长检测
echo   2. 明确指定视频时长参数 -t
echo   3. 使用 -map 参数精确控制输入流
echo   4. 添加时长验证机制
echo.
echo ✅ 修复内容:
echo   - 替换 -shortest 参数为精确的 -t 参数
echo   - 使用 getAudioDuration() 获取准确的音频时长
echo   - 添加 -map 参数明确指定音视频流
echo   - 增加 verifyVideoDuration() 验证方法
echo   - 提高时长精度到毫秒级 (%.3f)
echo.
echo 📋 FFmpeg 命令对比:
echo.
echo   修复前:
echo   ffmpeg -y -loop 1 -i image.png -i audio.mp3 -c:v libx264 -c:a aac -shortest output.mp4
echo.
echo   修复后:
echo   ffmpeg -y -loop 1 -i image.png -i audio.mp3 -map 0:v:0 -map 1:a:0 -c:v libx264 -c:a aac -t 5.123 output.mp4
echo.
echo 🔍 验证方法:
echo   1. 使用 ffprobe 检查音频时长
echo   2. 使用 ffprobe 检查视频时长
echo   3. 对比两者差异，应在 0.1 秒以内
echo.

:end
echo.
echo === 测试完成 ===
echo.
echo 💡 使用建议:
echo 1. 重新生成视频以应用修复
echo 2. 使用 ffprobe 验证时长一致性
echo 3. 如有问题，检查 FFmpeg 和 FFprobe 版本
echo.
echo 📚 相关文档:
echo - VIDEO_DURATION_FIX.md: 详细的修复说明
echo - internal/services/video_service.go: 修复代码
echo.
pause
