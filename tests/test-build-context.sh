#!/bin/bash

# Script to test Docker build context size and content
# This helps verify that .dockerignore is working effectively

echo "=== Docker Build Context Test ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to format file sizes
format_size() {
    local size=$1
    if [ $size -gt 1073741824 ]; then
        echo "$(echo "scale=2; $size/1073741824" | bc)GB"
    elif [ $size -gt 1048576 ]; then
        echo "$(echo "scale=2; $size/1048576" | bc)MB"
    elif [ $size -gt 1024 ]; then
        echo "$(echo "scale=2; $size/1024" | bc)KB"
    else
        echo "${size}B"
    fi
}

echo -e "${BLUE}1. Checking current directory size (before .dockerignore):${NC}"
total_size=$(du -sb . | cut -f1)
echo "Total directory size: $(format_size $total_size)"

echo
echo -e "${BLUE}2. Simulating Docker build context (respecting .dockerignore):${NC}"

# Create a temporary directory to simulate build context
temp_dir=$(mktemp -d)
echo "Creating simulated build context in: $temp_dir"

# Copy files that would be included in Docker build context
# This is a simplified simulation - actual Docker behavior may differ
rsync -av --exclude-from=.dockerignore . "$temp_dir/" > /dev/null 2>&1

build_context_size=$(du -sb "$temp_dir" | cut -f1)
echo "Simulated build context size: $(format_size $build_context_size)"

# Calculate reduction
if [ $total_size -gt 0 ]; then
    reduction_percent=$(echo "scale=2; (($total_size - $build_context_size) * 100) / $total_size" | bc)
    echo -e "${GREEN}Size reduction: $(format_size $(($total_size - $build_context_size))) (${reduction_percent}%)${NC}"
fi

echo
echo -e "${BLUE}3. Files included in build context:${NC}"
echo "=================================="
find "$temp_dir" -type f | sed "s|$temp_dir/||" | sort

echo
echo -e "${BLUE}4. Large directories that should be excluded:${NC}"
echo "==========================================="

# Check if large directories are properly excluded
large_dirs=("uploads" "screenshots" "videos" "temp" "data" "mcp-pipeline-server")
for dir in "${large_dirs[@]}"; do
    if [ -d "$dir" ]; then
        original_size=$(du -sb "$dir" 2>/dev/null | cut -f1)
        if [ -d "$temp_dir/$dir" ]; then
            context_size=$(du -sb "$temp_dir/$dir" 2>/dev/null | cut -f1)
            echo -e "${RED}✗ $dir: $(format_size $original_size) -> $(format_size $context_size) (NOT FULLY EXCLUDED)${NC}"
        else
            echo -e "${GREEN}✓ $dir: $(format_size $original_size) (EXCLUDED)${NC}"
        fi
    fi
done

echo
echo -e "${BLUE}5. Essential files that should be included:${NC}"
echo "========================================"

essential_files=("go.mod" "go.sum" "cmd" "internal" "scripts/docker-entrypoint.sh")
for file in "${essential_files[@]}"; do
    if [ -e "$file" ]; then
        if [ -e "$temp_dir/$file" ]; then
            echo -e "${GREEN}✓ $file (INCLUDED)${NC}"
        else
            echo -e "${RED}✗ $file (MISSING - this could break the build!)${NC}"
        fi
    fi
done

echo
echo -e "${BLUE}6. Recommendations:${NC}"
echo "=================="

if [ $build_context_size -gt 104857600 ]; then  # 100MB
    echo -e "${YELLOW}⚠ Build context is large (>100MB). Consider excluding more files.${NC}"
elif [ $build_context_size -gt 52428800 ]; then  # 50MB
    echo -e "${YELLOW}⚠ Build context is moderate (>50MB). This is acceptable but could be optimized.${NC}"
else
    echo -e "${GREEN}✓ Build context size is reasonable (<50MB).${NC}"
fi

if [ $reduction_percent -gt 80 ]; then
    echo -e "${GREEN}✓ Excellent size reduction (${reduction_percent}%).${NC}"
elif [ $reduction_percent -gt 50 ]; then
    echo -e "${YELLOW}⚠ Good size reduction (${reduction_percent}%).${NC}"
else
    echo -e "${RED}✗ Poor size reduction (${reduction_percent}%). Review .dockerignore.${NC}"
fi

echo
echo -e "${BLUE}7. To perform actual Docker build test:${NC}"
echo "====================================="
echo "docker build --no-cache -t ppt-narrator-test ."
echo "docker images ppt-narrator-test"

# Cleanup
rm -rf "$temp_dir"
echo
echo "Temporary directory cleaned up."
