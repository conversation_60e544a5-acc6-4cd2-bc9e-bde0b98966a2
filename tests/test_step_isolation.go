package main

import (
	"fmt"
	"log"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"
	"time"
)

// TestStepIsolation tests that failed steps don't affect previous successful steps
func main() {
	fmt.Println("=== 步骤隔离测试 ===")
	
	// Load configuration
	cfg := config.Load()
	
	// Initialize database store
	store, err := services.NewDatabaseStoreService(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// Create test project
	project := &models.PPTProject{
		ID:               "test-step-isolation-" + fmt.Sprintf("%d", time.Now().Unix()),
		Name:             "Step Isolation Test",
		Description:      "Testing step isolation functionality",
		Status:           "created",
		OriginalFileName: "test.pptx",
		FilePath:         "/tmp/test.pptx",
		SlideCount:       3,
	}
	
	if err := store.CreateProject(project); err != nil {
		log.Fatalf("Failed to create test project: %v", err)
	}
	
	fmt.Printf("Created test project: %s\n", project.ID)
	
	// Create test slides
	for i := 1; i <= 3; i++ {
		slide := &models.PPTSlide{
			ID:           fmt.Sprintf("slide-%d", i),
			ProjectID:    project.ID,
			SlideNumber:  i,
			Title:        fmt.Sprintf("Test Slide %d", i),
			Content:      fmt.Sprintf("This is test slide %d content", i),
			ScreenshotPath: fmt.Sprintf("/tmp/slide_%d.png", i),
		}
		
		if err := store.CreateSlide(slide); err != nil {
			log.Fatalf("Failed to create test slide %d: %v", i, err)
		}
	}
	
	fmt.Println("Created test slides")
	
	// Test 1: Simulate successful screenshot generation
	fmt.Println("\n1. 模拟截图生成成功...")
	project.Status = "completed"
	project.ScreenshotsPath = "/tmp/screenshots"
	if err := store.UpdateProject(project); err != nil {
		log.Fatalf("Failed to update project status: %v", err)
	}
	fmt.Printf("项目状态: %s\n", project.Status)
	
	// Test 2: Simulate successful narration generation
	fmt.Println("\n2. 模拟讲稿生成成功...")
	aiService := services.NewAIService(cfg, store)
	
	// Manually set narration for slides (simulating successful generation)
	slides, _ := store.GetSlidesByProject(project.ID)
	for i, slide := range slides {
		slide.NarrationText = fmt.Sprintf("这是第%d张幻灯片的讲稿内容 [停顿1秒]。让我们详细介绍一下这个主题 [停顿2秒]。", i+1)
		if err := store.UpdateSlide(slide); err != nil {
			log.Fatalf("Failed to update slide narration: %v", err)
		}
	}
	
	project.Status = "narration_ready"
	project.NarrationScript = "Complete narration script"
	if err := store.UpdateProject(project); err != nil {
		log.Fatalf("Failed to update project status: %v", err)
	}
	fmt.Printf("项目状态: %s\n", project.Status)
	
	// Test 3: Simulate audio generation failure
	fmt.Println("\n3. 模拟音频生成失败...")
	
	// Create TTS service with invalid configuration to force failure
	invalidCfg := *cfg
	invalidCfg.TTSProvider = "minimax"
	invalidCfg.MinimaxTTSAPIKey = "invalid_key"
	invalidCfg.MinimaxTTSGroupID = "invalid_group"
	
	ttsService := services.NewTTSService(&invalidCfg, store)
	
	// Try to generate audio (should fail)
	err = ttsService.GenerateAudio(project.ID)
	if err != nil {
		fmt.Printf("音频生成失败 (预期): %v\n", err)
	}
	
	// Check project status after audio failure
	project, err = store.GetProject(project.ID)
	if err != nil {
		log.Fatalf("Failed to get project: %v", err)
	}
	fmt.Printf("音频失败后项目状态: %s\n", project.Status)
	
	// Test 4: Verify narration is still available
	fmt.Println("\n4. 验证讲稿内容仍然可用...")
	slides, err = store.GetSlidesByProject(project.ID)
	if err != nil {
		log.Fatalf("Failed to get slides: %v", err)
	}
	
	hasNarration := false
	for _, slide := range slides {
		if slide.NarrationText != "" {
			hasNarration = true
			fmt.Printf("幻灯片 %d 讲稿: %s\n", slide.SlideNumber, slide.NarrationText[:50]+"...")
		}
	}
	
	if hasNarration {
		fmt.Println("✅ 讲稿内容保持完整")
	} else {
		fmt.Println("❌ 讲稿内容丢失")
	}
	
	// Test 5: Test retry audio generation with correct config
	fmt.Println("\n5. 测试使用正确配置重试音频生成...")
	
	// Check if we can retry audio generation
	if project.Status == "audio_failed" {
		fmt.Println("✅ 项目状态为 'audio_failed'，可以重试音频生成")
		
		// Simulate checking narration availability
		if hasNarration {
			fmt.Println("✅ 讲稿内容可用，可以进行音频生成")
		}
	} else {
		fmt.Printf("❌ 项目状态为 '%s'，无法重试音频生成\n", project.Status)
	}
	
	// Test 6: Test narration regeneration from failed state
	fmt.Println("\n6. 测试从失败状态重新生成讲稿...")
	
	// Try to regenerate narration (should work)
	err = aiService.GenerateNarration(project.ID, "重新生成讲稿测试")
	if err != nil {
		fmt.Printf("讲稿重新生成失败: %v\n", err)
	} else {
		fmt.Println("✅ 讲稿重新生成成功")
	}
	
	// Final status check
	project, _ = store.GetProject(project.ID)
	fmt.Printf("\n最终项目状态: %s\n", project.Status)
	
	// Test 7: Test state transitions
	fmt.Println("\n7. 测试状态转换逻辑...")
	
	testStates := []struct {
		fromState   string
		operation   string
		shouldWork  bool
		description string
	}{
		{"completed", "narration", true, "从截图完成状态生成讲稿"},
		{"narration_ready", "audio", true, "从讲稿就绪状态生成音频"},
		{"audio_failed", "audio", true, "从音频失败状态重试音频"},
		{"narration_failed", "narration", true, "从讲稿失败状态重试讲稿"},
		{"audio_ready", "video", true, "从音频就绪状态生成视频"},
		{"video_failed", "video", true, "从视频失败状态重试视频"},
		{"created", "narration", false, "从创建状态直接生成讲稿（应该失败）"},
		{"processing", "audio", false, "从处理中状态生成音频（应该失败）"},
	}
	
	for _, test := range testStates {
		fmt.Printf("  测试: %s -> %s (%s)\n", test.fromState, test.operation, test.description)
		
		// Set project to test state
		project.Status = test.fromState
		store.UpdateProject(project)
		
		var err error
		switch test.operation {
		case "narration":
			err = aiService.GenerateNarration(project.ID, "测试")
		case "audio":
			err = ttsService.GenerateAudio(project.ID)
		case "video":
			// Video service test would require more setup
			fmt.Printf("    视频测试跳过（需要更多设置）\n")
			continue
		}
		
		if test.shouldWork {
			if err == nil {
				fmt.Printf("    ✅ 成功（符合预期）\n")
			} else {
				fmt.Printf("    ❌ 失败（不符合预期）: %v\n", err)
			}
		} else {
			if err != nil {
				fmt.Printf("    ✅ 失败（符合预期）: %v\n", err)
			} else {
				fmt.Printf("    ❌ 成功（不符合预期）\n")
			}
		}
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("总结:")
	fmt.Println("- 每个步骤的失败不会影响前面已成功的步骤")
	fmt.Println("- 可以从失败状态重试对应的步骤")
	fmt.Println("- 状态转换逻辑正确工作")
	
	// Cleanup
	fmt.Printf("\n清理测试项目: %s\n", project.ID)
}
