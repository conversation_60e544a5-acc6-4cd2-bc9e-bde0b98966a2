#!/bin/bash

# PPT Narrator API 测试脚本
# 使用方法: ./test_api.sh [ppt_file_path]

set -e

# 配置
BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/v1"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    if curl -s "$BASE_URL/health" > /dev/null; then
        log_success "服务运行正常"
        curl -s "$BASE_URL/health" | jq .
    else
        log_error "服务未运行或不可访问"
        exit 1
    fi
}

# 上传PPT文件
upload_ppt() {
    local file_path="$1"
    
    if [ ! -f "$file_path" ]; then
        log_error "文件不存在: $file_path"
        exit 1
    fi
    
    log_info "上传PPT文件: $file_path"
    
    response=$(curl -s -X POST "$API_BASE/projects/upload" \
        -F "name=测试项目" \
        -F "description=API测试项目" \
        -F "file=@$file_path")
    
    echo "$response" | jq .
    
    # 提取项目ID
    project_id=$(echo "$response" | jq -r '.data.project_id')
    
    if [ "$project_id" != "null" ] && [ -n "$project_id" ]; then
        log_success "文件上传成功，项目ID: $project_id"
        echo "$project_id"
    else
        log_error "文件上传失败"
        exit 1
    fi
}

# 等待截图生成完成
wait_for_screenshots() {
    local project_id="$1"
    local max_attempts=30
    local attempt=0
    
    log_info "等待截图生成完成..."
    
    while [ $attempt -lt $max_attempts ]; do
        response=$(curl -s "$API_BASE/projects/$project_id/progress")
        status=$(echo "$response" | jq -r '.data.status')
        
        echo "尝试 $((attempt + 1))/$max_attempts - 状态: $status"
        
        if [ "$status" = "screenshots_ready" ]; then
            log_success "截图生成完成"
            return 0
        elif [ "$status" = "failed" ]; then
            log_error "截图生成失败"
            echo "$response" | jq .
            exit 1
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "截图生成超时"
    exit 1
}

# 添加记忆信息
add_memory() {
    local project_id="$1"
    
    log_info "添加记忆信息..."
    
    curl -s -X POST "$API_BASE/memory/$project_id" \
        -H "Content-Type: application/json" \
        -d '{
            "key": "target_audience",
            "value": "技术工程师和开发人员",
            "type": "context"
        }' | jq .
    
    curl -s -X POST "$API_BASE/memory/$project_id" \
        -H "Content-Type: application/json" \
        -d '{
            "key": "presentation_style",
            "value": "专业技术讲解，注重实用性",
            "type": "preference"
        }' | jq .
    
    log_success "记忆信息添加完成"
}

# 生成讲述稿
generate_narration() {
    local project_id="$1"
    
    log_info "开始生成讲述稿..."
    
    response=$(curl -s -X POST "$API_BASE/narration/$project_id/generate" \
        -H "Content-Type: application/json" \
        -d '{
            "user_requirements": "请生成专业的技术讲解，适合工程师听众，语言要生动有趣，逻辑清晰",
            "style": "professional",
            "language": "zh-CN"
        }')
    
    echo "$response" | jq .
    log_success "讲述稿生成已开始"
}

# 等待讲述稿生成完成
wait_for_narration() {
    local project_id="$1"
    local max_attempts=60
    local attempt=0
    
    log_info "等待讲述稿生成完成..."
    
    while [ $attempt -lt $max_attempts ]; do
        response=$(curl -s "$API_BASE/narration/$project_id/progress")
        progress=$(echo "$response" | jq -r '.data.progress')
        status=$(echo "$response" | jq -r '.data.status')
        
        echo "尝试 $((attempt + 1))/$max_attempts - 状态: $status, 进度: $progress%"
        
        if [ "$progress" = "100" ] && [ "$status" = "narration_ready" ]; then
            log_success "讲述稿生成完成"
            return 0
        elif [ "$status" = "failed" ]; then
            log_error "讲述稿生成失败"
            echo "$response" | jq .
            exit 1
        fi
        
        sleep 3
        attempt=$((attempt + 1))
    done
    
    log_error "讲述稿生成超时"
    exit 1
}

# 获取讲述稿
get_narration() {
    local project_id="$1"
    
    log_info "获取生成的讲述稿..."
    
    response=$(curl -s "$API_BASE/narration/$project_id")
    echo "$response" | jq .
    
    # 保存讲述稿到文件
    echo "$response" | jq -r '.data.full_narration' > "narration_$project_id.txt"
    log_success "讲述稿已保存到: narration_$project_id.txt"
}

# 生成音频
generate_audio() {
    local project_id="$1"
    
    log_info "开始生成音频..."
    
    response=$(curl -s -X POST "$API_BASE/audio/$project_id/generate")
    echo "$response" | jq .
    log_success "音频生成已开始"
}

# 等待音频生成完成
wait_for_audio() {
    local project_id="$1"
    local max_attempts=60
    local attempt=0
    
    log_info "等待音频生成完成..."
    
    while [ $attempt -lt $max_attempts ]; do
        response=$(curl -s "$API_BASE/audio/$project_id/progress")
        progress=$(echo "$response" | jq -r '.data.progress')
        status=$(echo "$response" | jq -r '.data.status')
        
        echo "尝试 $((attempt + 1))/$max_attempts - 状态: $status, 进度: $progress%"
        
        if [ "$progress" = "100" ] && [ "$status" = "audio_ready" ]; then
            log_success "音频生成完成"
            return 0
        elif [ "$status" = "failed" ]; then
            log_error "音频生成失败"
            echo "$response" | jq .
            exit 1
        fi
        
        sleep 3
        attempt=$((attempt + 1))
    done
    
    log_error "音频生成超时"
    exit 1
}

# 生成视频
generate_video() {
    local project_id="$1"
    
    log_info "开始生成视频..."
    
    response=$(curl -s -X POST "$API_BASE/video/$project_id/generate" \
        -H "Content-Type: application/json" \
        -d '{
            "output_format": "mp4",
            "quality": "high",
            "fps": 1,
            "resolution": "1920x1080"
        }')
    
    echo "$response" | jq .
    log_success "视频生成已开始"
}

# 等待视频生成完成
wait_for_video() {
    local project_id="$1"
    local max_attempts=60
    local attempt=0
    
    log_info "等待视频生成完成..."
    
    while [ $attempt -lt $max_attempts ]; do
        response=$(curl -s "$API_BASE/video/$project_id/progress")
        progress=$(echo "$response" | jq -r '.data.progress')
        status=$(echo "$response" | jq -r '.data.status')
        
        echo "尝试 $((attempt + 1))/$max_attempts - 状态: $status, 进度: $progress%"
        
        if [ "$progress" = "100" ] && [ "$status" = "completed" ]; then
            log_success "视频生成完成"
            return 0
        elif [ "$status" = "failed" ]; then
            log_error "视频生成失败"
            echo "$response" | jq .
            exit 1
        fi
        
        sleep 5
        attempt=$((attempt + 1))
    done
    
    log_error "视频生成超时"
    exit 1
}

# 下载视频
download_video() {
    local project_id="$1"
    
    log_info "下载生成的视频..."
    
    curl -o "video_$project_id.mp4" "$API_BASE/video/$project_id"
    
    if [ -f "video_$project_id.mp4" ]; then
        log_success "视频已下载到: video_$project_id.mp4"
    else
        log_error "视频下载失败"
        exit 1
    fi
}

# 主函数
main() {
    local ppt_file="$1"
    
    if [ -z "$ppt_file" ]; then
        log_error "请提供PPT文件路径"
        echo "使用方法: $0 <ppt_file_path>"
        exit 1
    fi
    
    log_info "开始PPT Narrator API测试"
    log_info "PPT文件: $ppt_file"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请安装: sudo apt install jq"
        exit 1
    fi
    
    # 执行测试流程
    check_health
    
    project_id=$(upload_ppt "$ppt_file")
    wait_for_screenshots "$project_id"
    
    add_memory "$project_id"
    
    generate_narration "$project_id"
    wait_for_narration "$project_id"
    get_narration "$project_id"
    
    generate_audio "$project_id"
    wait_for_audio "$project_id"
    
    generate_video "$project_id"
    wait_for_video "$project_id"
    
    download_video "$project_id"
    
    log_success "测试完成！项目ID: $project_id"
    log_info "生成的文件:"
    log_info "  - 讲述稿: narration_$project_id.txt"
    log_info "  - 视频: video_$project_id.mp4"
}

# 运行主函数
main "$@"
