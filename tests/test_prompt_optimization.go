package main

import (
	"fmt"
	"log"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"
	"strings"
	"time"
)

func main() {
	fmt.Println("=== PPT Narrator 提示词优化测试 ===")
	
	// Load configuration
	cfg := config.Load()
	
	// Initialize database store
	store, err := services.NewDatabaseStoreService(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// Initialize AI service
	aiService := services.NewAIService(cfg, store)
	
	// Create test project
	projectID := fmt.Sprintf("test-prompt-%d", time.Now().Unix())
	project := &models.PPTProject{
		ID:               projectID,
		Name:             "Prompt Optimization Test",
		Description:      "Testing optimized prompts for better narration",
		Status:           "completed",
		OriginalFileName: "sql_injection.pptx",
		FilePath:         "/tmp/sql_injection.pptx",
		SlideCount:       5,
	}
	
	if err := store.CreateProject(project); err != nil {
		log.Fatalf("Failed to create test project: %v", err)
	}
	
	fmt.Printf("Created test project: %s\n", project.ID)
	
	// Create test slides with SQL injection content
	testSlides := []struct {
		slideNumber int
		title       string
		content     string
	}{
		{1, "SQL注入概述", "什么是SQL注入？SQL注入是一种常见的网络攻击技术，攻击者通过在输入字段中插入恶意SQL代码来操控数据库。"},
		{2, "SQL注入原理", "当应用程序没有正确验证用户输入时，攻击者可以插入SQL命令。例如：' OR '1'='1' --"},
		{3, "group_concat()函数", "group_concat()函数可以将多行结果合并为一行。语法：SELECT group_concat(column_name) FROM table_name;"},
		{4, "实际攻击示例", "攻击示例：SELECT group_concat(username,':',password) FROM users; 这样可以获取所有用户名和密码。"},
		{5, "防护措施", "防护SQL注入的方法：1.使用参数化查询 2.输入验证 3.最小权限原则 4.定期安全审计"},
	}
	
	for _, slideData := range testSlides {
		slide := &models.PPTSlide{
			ID:           fmt.Sprintf("slide-%d", slideData.slideNumber),
			ProjectID:    projectID,
			SlideNumber:  slideData.slideNumber,
			Title:        slideData.title,
			Content:      slideData.content,
			ScreenshotPath: fmt.Sprintf("/tmp/slide_%d.png", slideData.slideNumber),
		}
		
		if err := store.CreateSlide(slide); err != nil {
			log.Fatalf("Failed to create test slide %d: %v", slideData.slideNumber, err)
		}
	}
	
	fmt.Printf("Created %d test slides\n", len(testSlides))
	
	// Test prompt generation for each slide
	fmt.Println("\n=== 测试提示词生成 ===")
	
	slides, err := store.GetSlidesByProject(projectID)
	if err != nil {
		log.Fatalf("Failed to get slides: %v", err)
	}
	
	for i, slide := range slides {
		fmt.Printf("\n--- 第%d页提示词 ---\n", slide.SlideNumber)
		
		// Generate prompt for this slide
		prompt := aiService.buildPrompt(slide, i+1, len(slides), "专业技术讲解，适合大学生")
		
		// Show key parts of the prompt
		lines := strings.Split(prompt, "\n")
		inRoleSection := false
		inTaskSection := false
		inRequirementSection := false
		inProhibitedSection := false
		
		for _, line := range lines {
			if strings.Contains(line, "## 角色设定") {
				inRoleSection = true
				fmt.Println("📋 " + line)
				continue
			}
			if strings.Contains(line, "## 当前任务") {
				inRoleSection = false
				inTaskSection = true
				fmt.Println("🎯 " + line)
				continue
			}
			if strings.Contains(line, "## 讲述要求") {
				inTaskSection = false
				inRequirementSection = true
				fmt.Println("📝 " + line)
				continue
			}
			if strings.Contains(line, "## 严格禁止的表达") {
				inRequirementSection = false
				inProhibitedSection = true
				fmt.Println("❌ " + line)
				continue
			}
			if strings.Contains(line, "## 良好示例") {
				inProhibitedSection = false
				fmt.Println("✅ " + line)
				continue
			}
			
			// Show important lines from each section
			if inRoleSection && strings.Contains(line, "连贯的PPT演示") {
				fmt.Println("   🔗 " + strings.TrimSpace(line))
			}
			if inTaskSection && strings.Contains(line, "这是") {
				fmt.Println("   📍 " + strings.TrimSpace(line))
			}
			if inRequirementSection && strings.Contains(line, "自然衔接") {
				fmt.Println("   🔄 " + strings.TrimSpace(line))
			}
			if inProhibitedSection && strings.Contains(line, "每页都用开场白") {
				fmt.Println("   🚫 " + strings.TrimSpace(line))
			}
		}
	}
	
	// Test actual narration generation (if AI service is configured)
	fmt.Println("\n=== 测试实际讲稿生成 ===")
	
	if cfg.AIProvider == "minimax" && cfg.MinimaxAPIKey != "" {
		fmt.Println("检测到MiniMax配置，开始生成测试讲稿...")
		
		// Generate narration for first 2 slides to test continuity
		for i := 0; i < 2 && i < len(slides); i++ {
			slide := slides[i]
			fmt.Printf("\n生成第%d页讲稿...\n", slide.SlideNumber)
			
			narration, err := aiService.generateSlideNarration(projectID, slide, i+1, len(slides))
			if err != nil {
				fmt.Printf("❌ 生成失败: %v\n", err)
				continue
			}
			
			// Update slide with narration for context
			slide.NarrationText = narration
			store.UpdateSlide(slide)
			
			fmt.Printf("✅ 生成成功:\n")
			fmt.Printf("---\n%s\n---\n", narration)
			
			// Analyze the narration
			fmt.Println("📊 讲稿分析:")
			analyzeNarration(narration, slide.SlideNumber)
		}
	} else {
		fmt.Println("⚠️  未配置AI服务，跳过实际生成测试")
		fmt.Println("请配置 MINIMAX_API_KEY 和 MINIMAX_GROUP_ID 来测试实际生成效果")
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("优化要点:")
	fmt.Println("✅ 强调这是连贯的PPT演示，不是独立课程")
	fmt.Println("✅ 根据页面位置提供不同的指导")
	fmt.Println("✅ 禁止每页都用开场白")
	fmt.Println("✅ 要求自然衔接前后内容")
	fmt.Println("✅ 提供具体的好坏示例")
	
	fmt.Printf("\n测试项目ID: %s\n", projectID)
}

func analyzeNarration(narration string, slideNumber int) {
	// Check for problematic patterns
	problems := []string{}
	good := []string{}
	
	// Check for bad opening patterns
	badOpenings := []string{"大家好", "嘿，大家好", "同学们好", "今天我们来讲", "咱们今天继续聊聊"}
	for _, opening := range badOpenings {
		if strings.Contains(narration, opening) {
			problems = append(problems, fmt.Sprintf("包含不当开场白: '%s'", opening))
		}
	}
	
	// Check for good patterns
	if strings.Contains(narration, "[停顿") {
		good = append(good, "包含停顿标记")
	}
	
	// Check for natural transitions (only for non-first slides)
	if slideNumber > 1 {
		naturalTransitions := []string{"刚才", "前面", "刚刚说到", "接着", "继续", "现在"}
		hasTransition := false
		for _, transition := range naturalTransitions {
			if strings.Contains(narration, transition) {
				hasTransition = true
				break
			}
		}
		if hasTransition {
			good = append(good, "包含自然过渡词")
		} else {
			problems = append(problems, "缺少与前文的自然衔接")
		}
	}
	
	// Check for oral language
	oralWords := []string{"嗯", "呃", "那个", "这样吧", "怎么说呢", "话说回来"}
	oralCount := 0
	for _, word := range oralWords {
		oralCount += strings.Count(narration, word)
	}
	if oralCount > 0 {
		good = append(good, fmt.Sprintf("包含%d个口语词", oralCount))
	} else {
		problems = append(problems, "缺少口语化表达")
	}
	
	// Check length
	length := len([]rune(narration))
	if length >= 200 && length <= 400 {
		good = append(good, fmt.Sprintf("长度适中(%d字)", length))
	} else {
		problems = append(problems, fmt.Sprintf("长度不当(%d字)", length))
	}
	
	// Print analysis
	if len(good) > 0 {
		fmt.Printf("   ✅ 优点: %s\n", strings.Join(good, ", "))
	}
	if len(problems) > 0 {
		fmt.Printf("   ⚠️  问题: %s\n", strings.Join(problems, ", "))
	}
}
