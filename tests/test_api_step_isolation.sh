#!/bin/bash

# API 步骤隔离测试脚本

echo "=== PPT Narrator API 步骤隔离测试 ==="

# 配置
BASE_URL="http://localhost:8080"
PROJECT_ID=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 辅助函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    echo "1. 检查服务状态..."
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/health" 2>/dev/null)
    if [ "$response" = "200" ]; then
        log_info "服务运行正常"
    else
        log_error "服务未运行或无法访问，请先启动服务"
        exit 1
    fi
}

# 创建测试项目
create_test_project() {
    echo ""
    echo "2. 创建测试项目..."
    
    # 这里需要实际的项目创建API，暂时使用模拟
    PROJECT_ID="test-api-isolation-$(date +%s)"
    log_info "模拟创建项目: $PROJECT_ID"
    
    # 在实际环境中，这里应该调用项目创建API
    # response=$(curl -s -X POST "$BASE_URL/api/v1/projects" \
    #     -H "Content-Type: application/json" \
    #     -d '{"name":"API Step Isolation Test","description":"Testing API step isolation"}')
}

# 测试讲稿生成API
test_narration_api() {
    echo ""
    echo "3. 测试讲稿生成API..."
    
    # 测试正常状态下的讲稿生成
    log_info "测试正常状态下的讲稿生成"
    response=$(curl -s -X POST "$BASE_URL/api/v1/narration/$PROJECT_ID/generate" \
        -H "Content-Type: application/json" \
        -d '{"user_requirements":"测试讲稿生成"}' 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_info "讲稿生成API调用成功"
    else
        log_warn "讲稿生成API调用失败（可能是项目不存在）"
    fi
}

# 测试音频生成API - 无讲稿状态
test_audio_api_no_narration() {
    echo ""
    echo "4. 测试音频生成API - 无讲稿状态..."
    
    log_info "测试在没有讲稿的情况下生成音频"
    response=$(curl -s -X POST "$BASE_URL/api/v1/audio/$PROJECT_ID/generate" \
        -H "Content-Type: application/json" 2>/dev/null)
    
    # 检查响应是否包含预期的错误消息
    if echo "$response" | grep -q "Narration not ready"; then
        log_info "✅ 正确返回了 'Narration not ready' 错误"
    else
        log_warn "❌ 未返回预期的错误消息"
        echo "实际响应: $response"
    fi
}

# 模拟讲稿生成成功
simulate_narration_success() {
    echo ""
    echo "5. 模拟讲稿生成成功..."
    
    log_info "模拟设置项目状态为 'narration_ready'"
    # 在实际环境中，这里需要通过数据库或管理API来设置状态
    # 这里只是说明测试流程
}

# 测试音频生成API - 有讲稿状态
test_audio_api_with_narration() {
    echo ""
    echo "6. 测试音频生成API - 有讲稿状态..."
    
    log_info "测试在有讲稿的情况下生成音频"
    response=$(curl -s -X POST "$BASE_URL/api/v1/audio/$PROJECT_ID/generate" \
        -H "Content-Type: application/json" 2>/dev/null)
    
    if echo "$response" | grep -q "success.*true"; then
        log_info "✅ 音频生成API调用成功"
    else
        log_warn "❌ 音频生成API调用失败"
        echo "响应: $response"
    fi
}

# 模拟音频生成失败
simulate_audio_failure() {
    echo ""
    echo "7. 模拟音频生成失败..."
    
    log_info "模拟设置项目状态为 'audio_failed'"
    # 在实际环境中，这里需要通过数据库或管理API来设置状态
}

# 测试音频重试API
test_audio_retry() {
    echo ""
    echo "8. 测试音频重试API..."
    
    log_info "测试从 'audio_failed' 状态重试音频生成"
    response=$(curl -s -X POST "$BASE_URL/api/v1/audio/$PROJECT_ID/generate" \
        -H "Content-Type: application/json" 2>/dev/null)
    
    # 应该允许重试，不应该返回 "Narration not ready" 错误
    if echo "$response" | grep -q "Narration not ready"; then
        log_error "❌ 不应该返回 'Narration not ready' 错误"
    else
        log_info "✅ 允许从失败状态重试音频生成"
    fi
}

# 测试视频生成API - 无音频状态
test_video_api_no_audio() {
    echo ""
    echo "9. 测试视频生成API - 无音频状态..."
    
    log_info "测试在没有音频的情况下生成视频"
    response=$(curl -s -X POST "$BASE_URL/api/v1/video/$PROJECT_ID/generate" \
        -H "Content-Type: application/json" \
        -d '{"duration_per_slide":5}' 2>/dev/null)
    
    if echo "$response" | grep -q "Audio not ready"; then
        log_info "✅ 正确返回了 'Audio not ready' 错误"
    else
        log_warn "❌ 未返回预期的错误消息"
        echo "实际响应: $response"
    fi
}

# 测试项目状态API
test_project_status() {
    echo ""
    echo "10. 测试项目状态API..."
    
    log_info "获取项目状态"
    response=$(curl -s "$BASE_URL/api/v1/projects/$PROJECT_ID/status" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_info "项目状态API调用成功"
        echo "状态响应: $response"
    else
        log_warn "项目状态API调用失败"
    fi
}

# 测试错误消息格式
test_error_message_format() {
    echo ""
    echo "11. 测试错误消息格式..."
    
    log_info "验证错误响应格式"
    
    # 测试不存在的项目
    response=$(curl -s -X POST "$BASE_URL/api/v1/audio/nonexistent-project/generate" \
        -H "Content-Type: application/json" 2>/dev/null)
    
    # 检查响应格式
    if echo "$response" | grep -q '"success".*false' && echo "$response" | grep -q '"error"'; then
        log_info "✅ 错误响应格式正确"
    else
        log_warn "❌ 错误响应格式不正确"
        echo "实际响应: $response"
    fi
}

# 主测试流程
main() {
    echo "开始API步骤隔离测试..."
    echo "测试目标: 验证每个步骤的失败不会影响前面已成功的步骤"
    echo ""
    
    check_service
    create_test_project
    test_narration_api
    test_audio_api_no_narration
    simulate_narration_success
    test_audio_api_with_narration
    simulate_audio_failure
    test_audio_retry
    test_video_api_no_audio
    test_project_status
    test_error_message_format
    
    echo ""
    echo "=== 测试总结 ==="
    echo "✅ 测试完成"
    echo ""
    echo "预期行为:"
    echo "1. 无讲稿时音频生成应返回 'Narration not ready' 错误"
    echo "2. 音频失败后应允许重试，不影响讲稿状态"
    echo "3. 无音频时视频生成应返回 'Audio not ready' 错误"
    echo "4. 错误响应应包含 success:false 和具体错误信息"
    echo ""
    echo "注意: 此测试脚本需要服务运行在 $BASE_URL"
    echo "某些测试需要实际的项目数据，可能需要手动设置"
}

# 运行测试
main
