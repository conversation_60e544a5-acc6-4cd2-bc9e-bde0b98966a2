@echo off
echo === DStaff Authentication Test ===
echo.

REM 设置测试参数
set MCP_SERVER_URL=http://localhost:48080
set TEST_TOKEN=your_test_token_here

echo Configuration:
echo   MCP Server URL: %MCP_SERVER_URL%
echo   Test Token: %TEST_TOKEN%
echo.

echo Testing DStaff authentication...
echo.

echo 1. Testing without authentication (should fail if <PERSON>ta<PERSON> auth is enabled):
curl -s -w "HTTP Status: %%{http_code}\n" %MCP_SERVER_URL% -o nul
echo.

echo 2. Testing with Bearer Token:
curl -s -w "HTTP Status: %%{http_code}\n" -H "Authorization: Bearer %TEST_TOKEN%" %MCP_SERVER_URL% -o nul
echo.

echo 3. Testing MCP tools list:
curl -s -w "HTTP Status: %%{http_code}\n" ^
     -H "Authorization: Bearer %TEST_TOKEN%" ^
     -H "Content-Type: application/json" ^
     -d "{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"tools/list\"}" ^
     %MCP_SERVER_URL% -o nul
echo.

echo 4. Testing file upload (multipart/form-data):
echo Creating test file...
echo This is a test file for DStaff integration > test_upload.txt

curl -s -w "HTTP Status: %%{http_code}\n" ^
     -H "Authorization: Bearer %TEST_TOKEN%" ^
     -F "file=@test_upload.txt" ^
     -F "project_name=test_project" ^
     -F "task_id=test_task_123" ^
     %MCP_SERVER_URL% -o nul

echo.
echo 5. Testing file download:
curl -s -w "HTTP Status: %%{http_code}\n" ^
     -H "Authorization: Bearer %TEST_TOKEN%" ^
     %MCP_SERVER_URL%/download/test_task_123/test_upload.txt -o nul

echo.
echo === Test Summary ===
echo.
echo If DStaff authentication is enabled:
echo   - Test 1 should return 401/403 (Unauthorized/Forbidden)
echo   - Tests 2-5 should return 200 (OK) with valid token
echo   - Tests 2-5 should return 401/403 with invalid token
echo.
echo If DStaff authentication is disabled:
echo   - All tests should return 200 (OK) regardless of token
echo.
echo Check the MCP server logs for detailed request information.
echo.

REM 清理测试文件
if exist test_upload.txt del test_upload.txt

pause
