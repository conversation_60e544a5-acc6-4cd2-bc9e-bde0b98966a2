@echo off
chcp 65001 >nul
echo === MiniMax TTS 功能测试 ===
echo.

REM 检查环境变量
echo 1. 检查环境变量配置...

if "%MINIMAX_TTS_API_KEY%"=="" (
    echo ❌ MINIMAX_TTS_API_KEY 未设置
    echo 请设置环境变量: set MINIMAX_TTS_API_KEY=your_api_key
    pause
    exit /b 1
) else (
    echo ✅ MINIMAX_TTS_API_KEY 已设置
)

if "%MINIMAX_TTS_GROUP_ID%"=="" (
    echo ❌ MINIMAX_TTS_GROUP_ID 未设置
    echo 请设置环境变量: set MINIMAX_TTS_GROUP_ID=your_group_id
    pause
    exit /b 1
) else (
    echo ✅ MINIMAX_TTS_GROUP_ID 已设置
)

REM 设置其他必要的环境变量
set TTS_PROVIDER=minimax
set MINIMAX_TTS_MODEL=speech-02-hd
set MINIMAX_TTS_VOICE_ID=male-qn-qingse
set MINIMAX_TTS_EMOTION=happy
set TTS_SPEED=1.0
set FFMPEG_PATH=ffmpeg

echo ✅ 环境变量配置完成

REM 检查 FFmpeg
echo.
echo 2. 检查 FFmpeg 安装...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FFmpeg 已安装
) else (
    echo ❌ FFmpeg 未安装，请先安装 FFmpeg
    echo 下载地址: https://ffmpeg.org/download.html
    echo 或使用 Chocolatey: choco install ffmpeg
    pause
    exit /b 1
)

REM 检查 Go
echo.
echo 3. 检查 Go 安装...
go version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Go 已安装
) else (
    echo ❌ Go 未安装，请先安装 Go
    echo 下载地址: https://golang.org/dl/
    pause
    exit /b 1
)

REM 运行测试
echo.
echo 4. 运行音频生成测试...

REM 检查测试文件是否存在
if not exist "test_audio_generation.go" (
    echo ❌ 测试文件 test_audio_generation.go 不存在
    pause
    exit /b 1
)

REM 运行 Go 测试
echo 正在运行测试...
go run test_audio_generation.go

if %errorlevel% equ 0 (
    echo.
    echo ✅ 测试完成！
    echo.
    echo 生成的测试文件位置:
    echo - 测试音频: .\temp\audio_test\
    echo - 静音音频: .\temp\audio_test\test_silence.mp3
    echo.
    echo 如果使用 MiniMax TTS，还会生成:
    echo - MiniMax 测试音频: .\temp\audio_test\minimax_test.mp3
) else (
    echo ❌ 测试失败，请检查配置和网络连接
    pause
    exit /b 1
)

echo.
echo === 测试完成 ===
echo.
echo 接下来可以：
echo 1. 启动服务: go run cmd/server/main.go
echo 2. 或使用 Docker: docker-compose up
echo 3. 访问 http://localhost:8080 使用完整功能
echo.
pause
