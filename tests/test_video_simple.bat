@echo off
chcp 65001 >nul
echo === PPT Narrator 视频生成功能测试 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080
set TEST_PROJECT_ID=

echo 测试目标: 验证视频生成功能是否正常工作
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问
    echo 请确保服务运行在 %BASE_URL%
    pause
    exit /b 1
)

REM 2. 检查 FFmpeg
echo.
echo 2. 检查 FFmpeg 安装...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FFmpeg 已安装
) else (
    echo ❌ FFmpeg 未安装或不在 PATH 中
    echo 请安装 FFmpeg: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM 3. 检查 FFprobe
echo.
echo 3. 检查 FFprobe 安装...
ffprobe -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FFprobe 已安装
) else (
    echo ⚠️  FFprobe 未安装，可能影响音频时长检测
)

REM 4. 创建测试目录和文件
echo.
echo 4. 创建测试资源...
set TEST_DIR=%TEMP%\ppt_narrator_video_test
if not exist "%TEST_DIR%" mkdir "%TEST_DIR%"
if not exist "%TEST_DIR%\screenshots" mkdir "%TEST_DIR%\screenshots"
if not exist "%TEST_DIR%\audio" mkdir "%TEST_DIR%\audio"

REM 创建测试图片（使用 FFmpeg 生成简单的彩色图片）
echo 创建测试截图...
ffmpeg -f lavfi -i "color=c=red:size=1280x720:duration=1" -vframes 1 -y "%TEST_DIR%\screenshots\slide_001.png" >nul 2>&1
ffmpeg -f lavfi -i "color=c=green:size=1280x720:duration=1" -vframes 1 -y "%TEST_DIR%\screenshots\slide_002.png" >nul 2>&1
ffmpeg -f lavfi -i "color=c=blue:size=1280x720:duration=1" -vframes 1 -y "%TEST_DIR%\screenshots\slide_003.png" >nul 2>&1

if exist "%TEST_DIR%\screenshots\slide_001.png" (
    echo ✅ 测试截图创建成功
) else (
    echo ❌ 测试截图创建失败
    pause
    exit /b 1
)

REM 创建测试音频（使用 FFmpeg 生成简单的音频）
echo 创建测试音频...
ffmpeg -f lavfi -i "sine=frequency=440:duration=3" -y "%TEST_DIR%\audio\slide_001.mp3" >nul 2>&1
ffmpeg -f lavfi -i "sine=frequency=523:duration=4" -y "%TEST_DIR%\audio\slide_002.mp3" >nul 2>&1
ffmpeg -f lavfi -i "sine=frequency=659:duration=2" -y "%TEST_DIR%\audio\slide_003.mp3" >nul 2>&1

if exist "%TEST_DIR%\audio\slide_001.mp3" (
    echo ✅ 测试音频创建成功
) else (
    echo ❌ 测试音频创建失败
    pause
    exit /b 1
)

REM 5. 测试单个幻灯片视频生成
echo.
echo 5. 测试单个幻灯片视频生成...
set OUTPUT_VIDEO=%TEST_DIR%\test_single_slide.mp4

echo 正在生成单个幻灯片视频...
ffmpeg -loop 1 -i "%TEST_DIR%\screenshots\slide_001.png" -i "%TEST_DIR%\audio\slide_001.mp3" -c:v libx264 -c:a aac -r 1 -s 1280x720 -pix_fmt yuv420p -shortest -y "%OUTPUT_VIDEO%" >nul 2>&1

if exist "%OUTPUT_VIDEO%" (
    echo ✅ 单个幻灯片视频生成成功
    for %%F in ("%OUTPUT_VIDEO%") do echo    文件大小: %%~zF 字节
) else (
    echo ❌ 单个幻灯片视频生成失败
)

REM 6. 测试多个幻灯片视频合并
echo.
echo 6. 测试多个幻灯片视频合并...

REM 生成各个幻灯片的视频片段
echo 生成幻灯片视频片段...
ffmpeg -loop 1 -i "%TEST_DIR%\screenshots\slide_001.png" -i "%TEST_DIR%\audio\slide_001.mp3" -c:v libx264 -c:a aac -r 1 -s 1280x720 -pix_fmt yuv420p -shortest -y "%TEST_DIR%\segment_001.mp4" >nul 2>&1
ffmpeg -loop 1 -i "%TEST_DIR%\screenshots\slide_002.png" -i "%TEST_DIR%\audio\slide_002.mp3" -c:v libx264 -c:a aac -r 1 -s 1280x720 -pix_fmt yuv420p -shortest -y "%TEST_DIR%\segment_002.mp4" >nul 2>&1
ffmpeg -loop 1 -i "%TEST_DIR%\screenshots\slide_003.png" -i "%TEST_DIR%\audio\slide_003.mp3" -c:v libx264 -c:a aac -r 1 -s 1280x720 -pix_fmt yuv420p -shortest -y "%TEST_DIR%\segment_003.mp4" >nul 2>&1

REM 创建合并列表
echo file 'segment_001.mp4' > "%TEST_DIR%\video_list.txt"
echo file 'segment_002.mp4' >> "%TEST_DIR%\video_list.txt"
echo file 'segment_003.mp4' >> "%TEST_DIR%\video_list.txt"

REM 合并视频
set COMBINED_VIDEO=%TEST_DIR%\test_combined.mp4
echo 合并视频片段...
ffmpeg -f concat -safe 0 -i "%TEST_DIR%\video_list.txt" -c:v libx264 -c:a aac -preset medium -crf 23 -pix_fmt yuv420p -y "%COMBINED_VIDEO%" >nul 2>&1

if exist "%COMBINED_VIDEO%" (
    echo ✅ 多幻灯片视频合并成功
    for %%F in ("%COMBINED_VIDEO%") do echo    文件大小: %%~zF 字节
    
    REM 获取视频信息
    echo 视频信息:
    ffprobe -v quiet -show_entries format=duration -of csv=p=0 "%COMBINED_VIDEO%" > temp_duration.txt 2>nul
    set /p VIDEO_DURATION=<temp_duration.txt
    del temp_duration.txt >nul 2>&1
    echo    时长: %VIDEO_DURATION% 秒
) else (
    echo ❌ 多幻灯片视频合并失败
)

REM 7. 测试音频时长检测
echo.
echo 7. 测试音频时长检测...
echo 检测各个音频文件的时长:

for %%i in (001 002 003) do (
    ffprobe -v quiet -show_entries format=duration -of csv=p=0 "%TEST_DIR%\audio\slide_%%i.mp3" > temp_audio_duration.txt 2>nul
    set /p AUDIO_DURATION=<temp_audio_duration.txt
    del temp_audio_duration.txt >nul 2>&1
    call echo    slide_%%i.mp3: %%AUDIO_DURATION%% 秒
)

REM 8. 清理和总结
echo.
echo 8. 测试总结...
echo.
echo === 测试结果 ===
echo ✅ 服务状态检查
echo ✅ FFmpeg 可用性检查
echo ✅ 测试资源创建
if exist "%OUTPUT_VIDEO%" (
    echo ✅ 单个幻灯片视频生成
) else (
    echo ❌ 单个幻灯片视频生成
)
if exist "%COMBINED_VIDEO%" (
    echo ✅ 多幻灯片视频合并
) else (
    echo ❌ 多幻灯片视频合并
)
echo.
echo 测试文件位置:
echo - 测试目录: %TEST_DIR%
if exist "%OUTPUT_VIDEO%" echo - 单个视频: %OUTPUT_VIDEO%
if exist "%COMBINED_VIDEO%" echo - 合并视频: %COMBINED_VIDEO%
echo.
echo 注意事项:
echo 1. 实际使用中，图片应该是 PPT 截图
echo 2. 音频应该是 TTS 生成的讲解音频
echo 3. 视频时长会根据音频时长自动调整
echo 4. 支持多种分辨率和帧率设置
echo.

REM 询问是否清理测试文件
set /p CLEANUP="是否删除测试文件? (y/N): "
if /i "%CLEANUP%"=="y" (
    echo 清理测试文件...
    rmdir /s /q "%TEST_DIR%" >nul 2>&1
    echo ✅ 测试文件已清理
)

echo.
echo === 测试完成 ===
pause
