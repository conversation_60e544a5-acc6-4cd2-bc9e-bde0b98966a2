#!/usr/bin/env python3
"""
测试全流程处理API的脚本
"""

import requests
import json
import time
import os
import sys

BASE_URL = "http://localhost:8080"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def create_test_pptx():
    """创建一个测试用的PPTX文件"""
    print("📄 创建测试PPTX文件...")
    
    # 创建一个简单的测试文件（模拟PPTX）
    test_content = b"""PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00"""
    test_file = "test_presentation.pptx"
    
    with open(test_file, "wb") as f:
        f.write(test_content)
    
    print(f"✅ 测试文件已创建: {test_file}")
    return test_file

def test_upload_and_process():
    """测试上传并处理API"""
    print("\n🚀 测试上传并处理API...")
    
    # 创建测试文件
    test_file = create_test_pptx()
    
    try:
        # 准备请求数据
        files = {
            'file': ('test_presentation.pptx', open(test_file, 'rb'), 'application/vnd.openxmlformats-officedocument.presentationml.presentation')
        }
        data = {
            'user_requirements': '请生成专业的测试讲稿',
            'project_name': 'API测试项目'
        }
        
        # 发送请求
        print("📤 发送上传请求...")
        response = requests.post(f"{BASE_URL}/api/v1/pipeline/upload-and-process", files=files, data=data)
        
        # 关闭文件
        files['file'][1].close()
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                project_id = result.get('project_id')
                print(f"✅ 上传成功! 项目ID: {project_id}")
                print(f"📋 项目信息: {result.get('project', {}).get('name')}")
                return project_id
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def test_get_progress(project_id):
    """测试获取进度API"""
    print(f"\n📊 测试获取进度API (项目ID: {project_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/pipeline/{project_id}/progress")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                progress = result.get('progress', {})
                stage = progress.get('current_stage', 'unknown')
                percent = progress.get('progress', 0)
                message = progress.get('message', '')
                
                print(f"✅ 进度查询成功!")
                print(f"   当前阶段: {stage}")
                print(f"   完成度: {percent}%")
                print(f"   消息: {message}")
                return True
            else:
                print(f"❌ 进度查询失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 进度查询异常: {e}")
        return False

def test_get_status(project_id):
    """测试获取状态API"""
    print(f"\n📋 测试获取状态API (项目ID: {project_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/pipeline/{project_id}/status")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                status = result.get('status', {})
                project_name = status.get('project_name', '')
                project_status = status.get('project_status', '')
                is_running = status.get('is_running', False)
                
                print(f"✅ 状态查询成功!")
                print(f"   项目名称: {project_name}")
                print(f"   项目状态: {project_status}")
                print(f"   是否运行中: {is_running}")
                return True
            else:
                print(f"❌ 状态查询失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")
        return False

def test_active_pipelines():
    """测试获取活跃管道API"""
    print(f"\n🔄 测试获取活跃管道API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/pipeline/active")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                pipelines = result.get('pipelines', [])
                count = result.get('count', 0)
                
                print(f"✅ 活跃管道查询成功!")
                print(f"   活跃管道数量: {count}")
                for i, pipeline in enumerate(pipelines):
                    print(f"   管道 {i+1}: {pipeline.get('project_name')} ({pipeline.get('project_id')})")
                return True
            else:
                print(f"❌ 活跃管道查询失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 活跃管道查询异常: {e}")
        return False

def test_cancel_pipeline(project_id):
    """测试取消管道API"""
    print(f"\n⏹️ 测试取消管道API (项目ID: {project_id})...")
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/pipeline/{project_id}/cancel")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 管道取消成功!")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"❌ 管道取消失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 管道取消异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 PPT Narrator 全流程API测试")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health():
        print("❌ 服务不可用，退出测试")
        sys.exit(1)
    
    # 等待服务完全启动
    print("\n⏳ 等待服务完全启动...")
    time.sleep(3)
    
    # 测试上传并处理
    project_id = test_upload_and_process()
    if not project_id:
        print("❌ 上传测试失败，退出测试")
        sys.exit(1)
    
    # 等待一下让处理开始
    time.sleep(2)
    
    # 测试获取进度
    test_get_progress(project_id)
    
    # 测试获取状态
    test_get_status(project_id)
    
    # 测试获取活跃管道
    test_active_pipelines()
    
    # 测试取消管道
    test_cancel_pipeline(project_id)
    
    print("\n🎉 测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
