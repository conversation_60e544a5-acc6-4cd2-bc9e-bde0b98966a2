#!/usr/bin/env python3
"""
简单测试修复后的全流程API
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_upload_api():
    """测试上传API"""
    print("\n🚀 测试上传API...")
    
    # 创建一个简单的测试文件
    test_content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00test"
    
    try:
        files = {
            'file': ('test.pptx', test_content, 'application/vnd.openxmlformats-officedocument.presentationml.presentation')
        }
        data = {
            'user_requirements': '测试需求',
            'project_name': '测试项目'
        }
        
        print("📤 发送上传请求...")
        response = requests.post(f"{BASE_URL}/api/v1/pipeline/upload-and-process", 
                               files=files, data=data, timeout=30)
        
        print(f"📋 响应状态码: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                project_id = result.get('project_id')
                print(f"✅ 上传成功! 项目ID: {project_id}")
                return project_id
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🧪 PPT Narrator API修复测试")
    print("=" * 40)
    
    # 测试健康检查
    if not test_health():
        print("❌ 服务不可用，退出测试")
        return
    
    # 等待服务完全启动
    print("\n⏳ 等待服务完全启动...")
    time.sleep(3)
    
    # 测试上传API
    project_id = test_upload_api()
    if project_id:
        print(f"\n🎉 测试成功! 项目ID: {project_id}")
        print("✅ 修复生效，API正常工作")
    else:
        print("\n❌ 测试失败")
    
    print("\n" + "=" * 40)

if __name__ == "__main__":
    main()
