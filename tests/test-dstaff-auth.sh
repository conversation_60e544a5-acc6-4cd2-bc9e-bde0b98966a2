#!/bin/bash

echo "=== DStaff Authentication Test ==="
echo

# 设置测试参数
MCP_SERVER_URL="http://localhost:48080"
TEST_TOKEN="your_test_token_here"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "Configuration:"
echo "  MCP Server URL: $MCP_SERVER_URL"
echo "  Test Token: $TEST_TOKEN"
echo

echo "Testing DStaff authentication..."
echo

echo "1. Testing without authentication (should fail if <PERSON>ta<PERSON> auth is enabled):"
response=$(curl -s -w "%{http_code}" $MCP_SERVER_URL -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ HTTP Status: $response (No auth required)${NC}"
elif [ "$response" = "401" ] || [ "$response" = "403" ]; then
    echo -e "${YELLOW}⚠ HTTP Status: $response (Auth required - expected if <PERSON>taff auth enabled)${NC}"
else
    echo -e "${RED}✗ HTTP Status: $response (Unexpected response)${NC}"
fi
echo

echo "2. Testing with Bearer Token:"
response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $TEST_TOKEN" \
    $MCP_SERVER_URL -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ HTTP Status: $response (Token accepted)${NC}"
elif [ "$response" = "401" ] || [ "$response" = "403" ]; then
    echo -e "${RED}✗ HTTP Status: $response (Token rejected - check token validity)${NC}"
else
    echo -e "${YELLOW}⚠ HTTP Status: $response (Unexpected response)${NC}"
fi
echo

echo "3. Testing MCP tools list:"
response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $TEST_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' \
    $MCP_SERVER_URL -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ HTTP Status: $response (MCP tools accessible)${NC}"
else
    echo -e "${RED}✗ HTTP Status: $response (MCP tools not accessible)${NC}"
fi
echo

echo "4. Testing file upload (multipart/form-data):"
echo "Creating test file..."
echo "This is a test file for DStaff integration" > test_upload.txt

response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $TEST_TOKEN" \
    -F "file=@test_upload.txt" \
    -F "project_name=test_project" \
    -F "task_id=test_task_123" \
    $MCP_SERVER_URL -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ HTTP Status: $response (File upload successful)${NC}"
else
    echo -e "${RED}✗ HTTP Status: $response (File upload failed)${NC}"
fi
echo

echo "5. Testing file download:"
response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $TEST_TOKEN" \
    $MCP_SERVER_URL/download/test_task_123/test_upload.txt -o /dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ HTTP Status: $response (File download successful)${NC}"
elif [ "$response" = "404" ]; then
    echo -e "${YELLOW}⚠ HTTP Status: $response (File not found - expected if upload failed)${NC}"
else
    echo -e "${RED}✗ HTTP Status: $response (File download failed)${NC}"
fi
echo

echo "=== Test Summary ==="
echo
echo "If DStaff authentication is enabled:"
echo "  - Test 1 should return 401/403 (Unauthorized/Forbidden)"
echo "  - Tests 2-5 should return 200 (OK) with valid token"
echo "  - Tests 2-5 should return 401/403 with invalid token"
echo
echo "If DStaff authentication is disabled:"
echo "  - All tests should return 200 (OK) regardless of token"
echo
echo "Check the MCP server logs for detailed request information."
echo

# 清理测试文件
if [ -f "test_upload.txt" ]; then
    rm test_upload.txt
    echo "Test file cleaned up."
fi
