#!/usr/bin/env python3
"""
测试下载API功能的脚本
"""

import requests
import json
import os
import time

BASE_URL = "http://localhost:8080"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_download_info(project_id):
    """测试获取下载信息API"""
    print(f"\n📋 测试获取下载信息 (项目ID: {project_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/download/{project_id}/info", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                downloads = data.get('downloads', {})
                
                print(f"✅ 下载信息获取成功!")
                print(f"   项目名称: {data.get('project_name')}")
                print(f"   项目状态: {data.get('status')}")
                
                # 显示各种下载选项
                for download_type, info in downloads.items():
                    if info.get('available'):
                        size_info = ""
                        if 'size' in info:
                            size_mb = info['size'] / (1024 * 1024)
                            size_info = f" ({size_mb:.1f}MB)"
                        elif 'count' in info:
                            size_info = f" ({info['count']}个文件)"
                        
                        print(f"   ✅ {download_type}: 可下载{size_info}")
                    else:
                        reason = info.get('reason', '未知原因')
                        print(f"   ❌ {download_type}: 不可下载 - {reason}")
                
                return data
            else:
                print(f"❌ 下载信息获取失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 下载信息获取异常: {e}")
        return None

def test_download_file(project_id, download_type, filename):
    """测试下载文件"""
    print(f"\n📥 测试下载{download_type}文件...")
    
    try:
        url = f"{BASE_URL}/api/v1/download/{project_id}/{download_type}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            # 保存文件
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"✅ {download_type}下载成功!")
            print(f"   文件名: {filename}")
            print(f"   文件大小: {file_size / (1024 * 1024):.1f}MB")
            
            # 验证文件存在
            if os.path.exists(filename):
                print(f"   ✅ 文件已保存到磁盘")
                return True
            else:
                print(f"   ❌ 文件保存失败")
                return False
        else:
            print(f"❌ {download_type}下载失败: {response.status_code}")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error')}")
                except:
                    pass
            return False
            
    except Exception as e:
        print(f"❌ {download_type}下载异常: {e}")
        return False

def test_download_narration_text(project_id):
    """测试下载讲稿文本"""
    print(f"\n📝 测试下载讲稿文本...")
    
    try:
        url = f"{BASE_URL}/api/v1/download/{project_id}/narration"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            filename = f"narration_{project_id}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 讲稿文本下载成功!")
            print(f"   文件名: {filename}")
            print(f"   内容长度: {len(content)}字符")
            print(f"   内容预览: {content[:100]}...")
            
            return True
        else:
            print(f"❌ 讲稿文本下载失败: {response.status_code}")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error')}")
                except:
                    pass
            return False
            
    except Exception as e:
        print(f"❌ 讲稿文本下载异常: {e}")
        return False

def cleanup_test_files(project_id):
    """清理测试文件"""
    print(f"\n🧹 清理测试文件...")
    
    test_files = [
        f"video_{project_id}.mp4",
        f"audio_{project_id}.mp3",
        f"screenshots_{project_id}.zip",
        f"narration_{project_id}.txt",
        f"complete_{project_id}.zip"
    ]
    
    cleaned = 0
    for filename in test_files:
        if os.path.exists(filename):
            try:
                os.remove(filename)
                cleaned += 1
                print(f"   ✅ 已删除: {filename}")
            except Exception as e:
                print(f"   ❌ 删除失败: {filename} - {e}")
    
    if cleaned > 0:
        print(f"✅ 清理完成，删除了{cleaned}个文件")
    else:
        print("ℹ️ 没有需要清理的文件")

def main():
    """主测试函数"""
    print("🧪 PPT Narrator 下载API测试")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health():
        print("❌ 服务不可用，退出测试")
        return
    
    # 获取项目ID（这里需要一个已完成的项目ID）
    project_id = input("\n请输入要测试的项目ID（必须是已完成处理的项目）: ").strip()
    if not project_id:
        print("❌ 项目ID不能为空")
        return
    
    print(f"\n🎯 开始测试项目: {project_id}")
    
    # 测试获取下载信息
    download_info = test_download_info(project_id)
    if not download_info:
        print("❌ 无法获取下载信息，退出测试")
        return
    
    downloads = download_info.get('downloads', {})
    
    # 测试各种下载功能
    success_count = 0
    total_tests = 0
    
    # 测试视频下载
    if downloads.get('video', {}).get('available'):
        total_tests += 1
        if test_download_file(project_id, 'video', f"video_{project_id}.mp4"):
            success_count += 1
    
    # 测试音频下载
    if downloads.get('audio', {}).get('available'):
        total_tests += 1
        if test_download_file(project_id, 'audio', f"audio_{project_id}.mp3"):
            success_count += 1
    
    # 测试截图下载
    if downloads.get('screenshots', {}).get('available'):
        total_tests += 1
        if test_download_file(project_id, 'screenshots', f"screenshots_{project_id}.zip"):
            success_count += 1
    
    # 测试讲稿下载
    if downloads.get('narration', {}).get('available'):
        total_tests += 1
        if test_download_narration_text(project_id):
            success_count += 1
    
    # 测试完整包下载
    if downloads.get('complete', {}).get('available'):
        total_tests += 1
        if test_download_file(project_id, 'all', f"complete_{project_id}.zip"):
            success_count += 1
    
    # 显示测试结果
    print(f"\n📊 测试结果统计")
    print("=" * 30)
    print(f"总测试数: {total_tests}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_tests - success_count}")
    print(f"成功率: {(success_count/total_tests*100) if total_tests > 0 else 0:.1f}%")
    
    if success_count == total_tests and total_tests > 0:
        print("\n🎉 所有测试通过！下载API工作正常")
    elif success_count > 0:
        print(f"\n⚠️ 部分测试通过，请检查失败的下载功能")
    else:
        print(f"\n❌ 所有测试失败，请检查服务状态和项目ID")
    
    # 询问是否清理测试文件
    if success_count > 0:
        cleanup = input(f"\n是否清理下载的测试文件？(y/N): ").strip().lower()
        if cleanup in ['y', 'yes']:
            cleanup_test_files(project_id)
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
