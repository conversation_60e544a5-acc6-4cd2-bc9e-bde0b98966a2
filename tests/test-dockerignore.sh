#!/bin/bash

# Test script to verify .dockerignore is working correctly
# This script simulates what Docker build context would include

echo "=== Testing .dockerignore effectiveness ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if a file/directory should be ignored
check_ignore() {
    local path="$1"
    if [ -e "$path" ]; then
        # Use git check-ignore to test .dockerignore patterns
        # Note: This is a simplified test, actual Docker behavior may differ slightly
        if grep -q "^${path}$\|^${path}/\|^$(basename "$path")$\|^\*\.$(echo "$path" | rev | cut -d. -f1 | rev)$" .dockerignore 2>/dev/null; then
            echo -e "${GREEN}✓ IGNORED:${NC} $path"
            return 0
        else
            echo -e "${RED}✗ INCLUDED:${NC} $path"
            return 1
        fi
    else
        echo -e "${YELLOW}? NOT FOUND:${NC} $path"
        return 2
    fi
}

echo "Checking data directories that should be ignored:"
echo "================================================"

# Check data directories
check_ignore "data"
check_ignore "uploads"
check_ignore "screenshots" 
check_ignore "videos"
check_ignore "temp"
check_ignore "logs"
check_ignore "audio"
check_ignore "work"

echo
echo "Checking database files that should be ignored:"
echo "=============================================="

# Check database files
check_ignore "ppt-narrator.db"
check_ignore "*.db"
check_ignore "data/ppt-narrator.db"

echo
echo "Checking build artifacts that should be ignored:"
echo "=============================================="

# Check build artifacts
check_ignore "main.exe"
check_ignore "*.tar"
check_ignore "1.tar"
check_ignore "2.tar"
check_ignore "3.tar"

echo
echo "Checking test files that should be ignored:"
echo "========================================="

# Check test files
check_ignore "test*.go"
check_ignore "test*.py"
check_ignore "test*.sh"
check_ignore "test*.bat"
check_ignore "audio_diagnostic_tool.py"

echo
echo "Checking documentation files that should be ignored:"
echo "================================================="

# Check documentation files
check_ignore "API_DOCUMENTATION.md"
check_ignore "DOCKER_MINIMAX_SETUP.md"
check_ignore "VIDEO_DURATION_FIX.md"

echo
echo "Checking MCP directory that should be ignored:"
echo "==========================================="

# Check MCP directory
check_ignore "mcp-pipeline-server"

echo
echo "Checking essential files that should be included:"
echo "=============================================="

# Check essential files that should NOT be ignored
essential_files=(
    "go.mod"
    "go.sum"
    "cmd"
    "internal"
    "scripts/docker-entrypoint.sh"
    "scripts/wait-for-db.sh"
    "Dockerfile"
)

for file in "${essential_files[@]}"; do
    if [ -e "$file" ]; then
        if grep -q "^${file}$\|^${file}/\|^$(basename "$file")$" .dockerignore 2>/dev/null; then
            echo -e "${RED}✗ WRONGLY IGNORED:${NC} $file"
        else
            echo -e "${GREEN}✓ CORRECTLY INCLUDED:${NC} $file"
        fi
    else
        echo -e "${YELLOW}? NOT FOUND:${NC} $file"
    fi
done

echo
echo "=== Summary ==="
echo "The .dockerignore file should exclude data directories, test files,"
echo "build artifacts, and documentation while keeping essential source code"
echo "and configuration files for the Docker build process."
echo
echo "To test the actual Docker build context size, run:"
echo "  docker build --no-cache -t ppt-narrator-test ."
echo
echo "To see what files are actually included in the build context:"
echo "  docker build --no-cache -t ppt-narrator-test . 2>&1 | grep 'COPY'"
