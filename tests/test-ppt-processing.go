package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	dbService, err := services.NewDatabaseService("./test.db")
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer dbService.Close()

	// Initialize services
	store := services.NewDatabaseStoreService(dbService.GetDB())
	pptProcessor := services.NewPPTProcessorService(cfg, store)

	// Create test project
	project, err := store.CreateProject("Test PPT", "Testing PPT processing")
	if err != nil {
		log.Fatal("Failed to create project:", err)
	}

	fmt.Printf("Created project: %s\n", project.ID)

	// Test PPT file path (you need to provide a real PPT file)
	testPPTPath := "./test.pptx"
	if _, err := os.Stat(testPPTPath); os.IsNotExist(err) {
		fmt.Printf("Test PPT file not found: %s\n", testPPTPath)
		fmt.Println("Please create a test.pptx file in the current directory")
		return
	}

	// Set up project file path
	project.FilePath = testPPTPath
	project.OriginalFileName = "test.pptx"
	if err := store.UpdateProject(project); err != nil {
		log.Fatal("Failed to update project:", err)
	}

	// Process screenshots
	fmt.Println("Processing screenshots...")
	err = pptProcessor.ProcessScreenshots(project.ID)
	if err != nil {
		log.Fatal("Failed to process screenshots:", err)
	}

	// Get updated project
	project, err = store.GetProject(project.ID)
	if err != nil {
		log.Fatal("Failed to get updated project:", err)
	}

	fmt.Printf("Project status: %s\n", project.Status)
	fmt.Printf("Slide count: %d\n", project.SlideCount)
	fmt.Printf("Screenshots path: %s\n", project.ScreenshotsPath)

	// List generated slides
	slides, err := store.GetSlidesByProject(project.ID)
	if err != nil {
		log.Fatal("Failed to get slides:", err)
	}

	fmt.Printf("Generated %d slides:\n", len(slides))
	for _, slide := range slides {
		fmt.Printf("  Slide %d: %s -> %s\n", slide.SlideNumber, slide.Title, slide.ScreenshotPath)
		
		// Check if screenshot file exists
		if _, err := os.Stat(slide.ScreenshotPath); os.IsNotExist(err) {
			fmt.Printf("    WARNING: Screenshot file not found: %s\n", slide.ScreenshotPath)
		} else {
			// Get file info
			info, _ := os.Stat(slide.ScreenshotPath)
			fmt.Printf("    Screenshot size: %d bytes\n", info.Size())
		}
	}

	// List all files in screenshots directory
	if project.ScreenshotsPath != "" {
		fmt.Printf("\nFiles in screenshots directory (%s):\n", project.ScreenshotsPath)
		err := filepath.Walk(project.ScreenshotsPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() {
				fmt.Printf("  %s (%d bytes)\n", path, info.Size())
			}
			return nil
		})
		if err != nil {
			fmt.Printf("Error walking directory: %v\n", err)
		}
	}

	fmt.Println("Test completed!")
}
