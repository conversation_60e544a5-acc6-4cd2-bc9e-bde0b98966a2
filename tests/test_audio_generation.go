package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
)

func main() {
	// Load configuration
	cfg := config.Load()
	
	// Test text with pause markers
	testText := `嘿，大家好，咱们今天呢要聊聊一个挺重要的话题 [停顿1秒]，叫做"SQL注入基础"。嗯，你可能会问，SQL注入是啥？[停顿2秒] 说白了呢，其实就是一种网络攻击技术，专门针对数据库的。

那为什么我们要关心这个呢？[停顿2秒] 这样想啊，咱们每天在网上各种操作，背后都是数据库在运作。如果这些数据库被攻击了，那可就麻烦了 [停顿1.5秒]。

打个比方吧 [停顿1秒]，就像你家的大门，要是锁没锁好，别人随时都可能进来。所以呢，理解SQL注入对保护数据安全是非常关键的。`

	fmt.Println("=== PPT Narrator 音频生成测试 ===")
	fmt.Printf("TTS Provider: %s\n", cfg.TTSProvider)
	
	// Create audio processor
	audioProcessor := services.NewAudioProcessor(cfg.FFmpegPath, cfg.TempDir)
	
	// Test 1: Parse text with pauses
	fmt.Println("\n1. 测试文本解析和停顿标记...")
	segments := audioProcessor.ParseTextWithPauses(testText)
	
	fmt.Printf("解析出 %d 个片段:\n", len(segments))
	for i, segment := range segments {
		if segment.Text != "" {
			fmt.Printf("  片段 %d: 文本 - %s\n", i+1, truncateText(segment.Text, 50))
		}
		if segment.PauseDuration > 0 {
			fmt.Printf("  片段 %d: 停顿 - %.1f秒\n", i+1, segment.PauseDuration)
		}
	}
	
	// Test 2: Test TTS client based on provider
	fmt.Printf("\n2. 测试 %s TTS 客户端...\n", cfg.TTSProvider)
	
	// Create output directory
	outputDir := filepath.Join(cfg.TempDir, "audio_test")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}
	
	switch cfg.TTSProvider {
	case "minimax":
		testMinimaxTTS(cfg, outputDir)
	case "openai":
		testOpenAITTS(cfg, outputDir)
	default:
		fmt.Printf("不支持的 TTS 提供商: %s\n", cfg.TTSProvider)
		return
	}
	
	// Test 3: Test silence generation
	fmt.Println("\n3. 测试静音音频生成...")
	silencePath := filepath.Join(outputDir, "test_silence.mp3")
	if err := audioProcessor.GenerateSilence(2.0, silencePath); err != nil {
		fmt.Printf("生成静音音频失败: %v\n", err)
	} else {
		fmt.Printf("成功生成 2 秒静音音频: %s\n", silencePath)
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Printf("输出目录: %s\n", outputDir)
}

func testMinimaxTTS(cfg *config.Config, outputDir string) {
	// Create MiniMax TTS client
	client := services.NewMinimaxTTSClient(cfg)
	
	// Validate configuration
	if err := client.ValidateConfig(); err != nil {
		fmt.Printf("MiniMax TTS 配置验证失败: %v\n", err)
		fmt.Println("请检查以下环境变量:")
		fmt.Println("  - MINIMAX_TTS_API_KEY")
		fmt.Println("  - MINIMAX_TTS_GROUP_ID")
		return
	}
	
	fmt.Println("MiniMax TTS 配置验证通过")
	
	// Test audio generation
	testText := "这是一个测试音频，用来验证MiniMax TTS功能是否正常工作。"
	fmt.Printf("正在生成测试音频: %s\n", testText)
	
	audioData, err := client.GenerateAudio(context.Background(), testText)
	if err != nil {
		fmt.Printf("生成音频失败: %v\n", err)
		return
	}
	
	// Save audio file
	outputPath := filepath.Join(outputDir, "minimax_test.mp3")
	if err := os.WriteFile(outputPath, audioData, 0644); err != nil {
		fmt.Printf("保存音频文件失败: %v\n", err)
		return
	}
	
	fmt.Printf("成功生成 MiniMax 测试音频: %s (大小: %d 字节)\n", outputPath, len(audioData))
}

func testOpenAITTS(cfg *config.Config, outputDir string) {
	fmt.Println("OpenAI TTS 测试...")
	
	if cfg.OpenAIAPIKey == "" {
		fmt.Println("OpenAI API Key 未配置，跳过测试")
		fmt.Println("请设置 OPENAI_API_KEY 环境变量")
		return
	}
	
	fmt.Println("OpenAI TTS 配置验证通过")
	fmt.Printf("使用语音: %s, 速度: %.1f\n", cfg.TTSVoice, cfg.TTSSpeed)
	
	// Note: 实际的 OpenAI TTS 测试需要完整的 TTS 服务实例
	// 这里只是显示配置信息
	fmt.Println("OpenAI TTS 测试需要完整的服务实例，此处仅验证配置")
}

func truncateText(text string, maxLen int) string {
	if len(text) <= maxLen {
		return text
	}
	return text[:maxLen] + "..."
}
