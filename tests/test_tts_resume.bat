@echo off
chcp 65001 >nul
echo === PPT Narrator TTS 断点续传测试 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080
set TEST_PROJECT_ID=

echo 测试目标: 验证TTS音频生成的断点续传功能
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问
    echo 请确保服务运行在 %BASE_URL%
    pause
    exit /b 1
)

REM 2. 获取现有项目列表
echo.
echo 2. 获取现有项目...
curl -s "%BASE_URL%/api/v1/projects" > temp_projects.json 2>nul

REM 检查是否有项目
findstr /C:"narration_ready" temp_projects.json >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到有讲稿的项目
    echo 请手动设置项目ID进行测试
    echo 或者先上传PPT并生成讲稿
) else (
    echo ⚠️  没有找到有讲稿的项目
    echo 请先：
    echo 1. 上传PPT文件
    echo 2. 生成截图
    echo 3. 生成讲稿
    echo 然后再运行此测试
)
del temp_projects.json >nul 2>&1

REM 3. 手动输入项目ID进行测试
echo.
set /p TEST_PROJECT_ID="请输入要测试的项目ID (或按回车跳过): "

if "%TEST_PROJECT_ID%"=="" (
    echo 跳过实际测试，显示API使用方法
    goto :show_usage
)

REM 4. 检查项目状态
echo.
echo 3. 检查项目状态...
curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%" > temp_project.json 2>nul

findstr /C:"narration_ready\|audio_failed" temp_project.json >nul
if %errorlevel% equ 0 (
    echo ✅ 项目状态适合进行音频生成
) else (
    echo ❌ 项目状态不适合音频生成
    echo 当前项目信息:
    type temp_project.json
    del temp_project.json >nul 2>&1
    goto :show_usage
)
del temp_project.json >nul 2>&1

REM 5. 获取音频生成进度
echo.
echo 4. 获取音频生成进度...
curl -s "%BASE_URL%/api/v1/audio/%TEST_PROJECT_ID%/progress" > temp_audio_progress.json 2>nul

if exist temp_audio_progress.json (
    echo 音频生成进度:
    type temp_audio_progress.json
    echo.
    
    REM 检查是否可以续传
    findstr /C:"can_resume.*true" temp_audio_progress.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 项目可以进行断点续传
        set CAN_RESUME=true
    ) else (
        echo ℹ️  项目当前不需要断点续传
        set CAN_RESUME=false
    )
) else (
    echo ❌ 无法获取音频进度信息
    set CAN_RESUME=false
)
del temp_audio_progress.json >nul 2>&1

REM 6. 测试音频生成或续传
echo.
echo 5. 测试音频生成...

if "%CAN_RESUME%"=="true" (
    echo 执行断点续传...
    curl -s -X POST "%BASE_URL%/api/v1/audio/%TEST_PROJECT_ID%/resume" ^
        -H "Content-Type: application/json" > temp_resume_response.json 2>nul
    
    if exist temp_resume_response.json (
        echo 续传响应:
        type temp_resume_response.json
        echo.
        
        findstr /C:"success.*true" temp_resume_response.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 断点续传启动成功
        ) else (
            echo ❌ 断点续传启动失败
        )
    )
    del temp_resume_response.json >nul 2>&1
) else (
    echo 执行正常音频生成...
    curl -s -X POST "%BASE_URL%/api/v1/audio/%TEST_PROJECT_ID%/generate" ^
        -H "Content-Type: application/json" > temp_generate_response.json 2>nul
    
    if exist temp_generate_response.json (
        echo 生成响应:
        type temp_generate_response.json
        echo.
        
        findstr /C:"success.*true" temp_generate_response.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 音频生成启动成功
        ) else (
            echo ❌ 音频生成启动失败
        )
    )
    del temp_generate_response.json >nul 2>&1
)

REM 7. 监控进度
echo.
echo 6. 监控生成进度...
echo 每5秒检查一次进度，按任意键停止监控

:monitor_loop
timeout /t 5 >nul 2>&1
curl -s "%BASE_URL%/api/v1/audio/%TEST_PROJECT_ID%/progress" > temp_monitor_progress.json 2>nul

if exist temp_monitor_progress.json (
    echo [%time%] 当前进度:
    
    REM 提取关键信息
    for /f "tokens=2 delims=:" %%a in ('findstr "completed_slides" temp_monitor_progress.json') do (
        set COMPLETED=%%a
    )
    for /f "tokens=2 delims=:" %%a in ('findstr "total_slides" temp_monitor_progress.json') do (
        set TOTAL=%%a
    )
    
    echo   已完成: %COMPLETED% / %TOTAL%
    
    REM 检查是否完成
    findstr /C:"completed_slides.*%TOTAL%" temp_monitor_progress.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 音频生成完成！
        del temp_monitor_progress.json >nul 2>&1
        goto :test_complete
    )
    
    REM 检查是否失败
    findstr /C:"audio_failed" temp_monitor_progress.json >nul
    if %errorlevel% equ 0 (
        echo ❌ 音频生成失败
        del temp_monitor_progress.json >nul 2>&1
        goto :test_complete
    )
)
del temp_monitor_progress.json >nul 2>&1

REM 检查用户是否想停止监控
echo 按任意键停止监控...
timeout /t 1 >nul 2>&1
if not errorlevel 1 goto :test_complete

goto :monitor_loop

:test_complete
echo.
echo 7. 最终状态检查...
curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%" > temp_final_status.json 2>nul

if exist temp_final_status.json (
    echo 项目最终状态:
    findstr /C:"status\|audio_path\|error_message" temp_final_status.json
    echo.
)
del temp_final_status.json >nul 2>&1

goto :end

:show_usage
echo.
echo === API 使用方法 ===
echo.
echo 1. 获取音频生成进度:
echo    GET %BASE_URL%/api/v1/audio/{projectId}/progress
echo.
echo 2. 开始音频生成:
echo    POST %BASE_URL%/api/v1/audio/{projectId}/generate
echo.
echo 3. 断点续传音频生成:
echo    POST %BASE_URL%/api/v1/audio/{projectId}/resume
echo.
echo 4. 检查项目状态:
echo    GET %BASE_URL%/api/v1/projects/{projectId}
echo.
echo === 断点续传工作流程 ===
echo.
echo 1. 音频生成过程中遇到错误（如速率限制）
echo 2. 项目状态变为 'audio_failed'
echo 3. 调用进度API检查 can_resume 状态
echo 4. 如果可以续传，调用 resume API
echo 5. 系统自动从中断点继续生成
echo.
echo === 进度响应示例 ===
echo {
echo   "success": true,
echo   "data": {
echo     "total_slides": 5,
echo     "completed_slides": 2,
echo     "current_slide": 3,
echo     "can_resume": true,
echo     "slide_details": [
echo       {
echo         "slide_number": 1,
echo         "title": "Introduction",
echo         "has_narration": true,
echo         "has_audio": true,
echo         "audio_path": "/path/to/slide_001.mp3",
echo         "audio_size": 1024000
echo       }
echo     ]
echo   }
echo }

:end
echo.
echo === 测试完成 ===
echo.
echo 功能特性:
echo ✅ 断点检测 - 自动识别已完成和未完成的音频
echo ✅ 续传逻辑 - 从正确位置继续生成
echo ✅ 进度监控 - 实时查看生成进度
echo ✅ 错误恢复 - 从失败点重新开始
echo ✅ 状态管理 - 正确维护项目状态
echo.
pause
