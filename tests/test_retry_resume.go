package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
	"strings"
	"time"
)

func main() {
	fmt.Println("=== PPT Narrator 重试和断点续传测试 ===")
	
	// Load configuration
	cfg := config.Load()
	
	// Create test directory
	testDir := filepath.Join(cfg.TempDir, "retry_resume_test")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		log.Fatalf("Failed to create test directory: %v", err)
	}
	
	fmt.Printf("测试目录: %s\n", testDir)
	
	// Create audio processor
	audioProcessor := services.NewAudioProcessor(cfg.FFmpegPath, cfg.TempDir)
	
	// Test text with multiple segments
	testText := `大家好，欢迎来到今天的课程 [停顿1秒]。今天我们要学习一个非常重要的概念 [停顿2秒]。
	
首先，让我们了解一下基础知识 [停顿1.5秒]。这个概念在实际应用中非常重要 [停顿1秒]。

接下来，我们来看一些具体的例子 [停顿2秒]。通过这些例子，你会更好地理解这个概念 [停顿1秒]。

最后，让我们总结一下今天学到的内容 [停顿1.5秒]。希望这些知识对你有所帮助 [停顿2秒]。`

	fmt.Println("\n1. 解析文本片段...")
	segments := audioProcessor.ParseTextWithPauses(testText)
	fmt.Printf("解析出 %d 个片段\n", len(segments))
	
	for i, segment := range segments {
		if segment.Text != "" {
			fmt.Printf("  片段 %d: 文本 - %s\n", i+1, truncateText(segment.Text, 50))
		}
		if segment.PauseDuration > 0 {
			fmt.Printf("  片段 %d: 停顿 - %.1f秒\n", i+1, segment.PauseDuration)
		}
	}
	
	// Test 1: Simulate rate limit errors
	fmt.Println("\n2. 测试重试机制（模拟速率限制错误）...")
	
	callCount := 0
	mockGenerateAudioFunc := func(text string) ([]byte, error) {
		callCount++
		fmt.Printf("  API调用 #%d: %s\n", callCount, truncateText(text, 30))
		
		// Simulate rate limit error for first few calls
		if callCount <= 3 {
			return nil, fmt.Errorf("API error 1002: rate limit")
		}
		
		// Simulate success after retries
		return []byte(fmt.Sprintf("mock audio data for: %s", text)), nil
	}
	
	// Create retry config for testing
	retryConfig := &services.RetryConfig{
		MaxRetries:    5,
		BaseDelay:     1 * time.Second, // Shorter for testing
		MaxDelay:      10 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: false, // Disable for predictable testing
	}
	
	audioFiles, err := audioProcessor.ProcessTextToAudioSegmentsWithRetry(
		"这是一个测试文本 [停顿1秒]。用来验证重试功能 [停顿2秒]。",
		testDir,
		mockGenerateAudioFunc,
		retryConfig,
	)
	
	if err != nil {
		log.Printf("重试测试失败: %v", err)
	} else {
		fmt.Printf("✅ 重试测试成功，生成了 %d 个音频文件\n", len(audioFiles))
		for _, file := range audioFiles {
			fmt.Printf("  - %s\n", filepath.Base(file))
		}
	}
	
	// Test 2: Test resume functionality
	fmt.Println("\n3. 测试断点续传功能...")
	
	// Create a new test directory for resume test
	resumeTestDir := filepath.Join(testDir, "resume_test")
	if err := os.MkdirAll(resumeTestDir, 0755); err != nil {
		log.Fatalf("Failed to create resume test directory: %v", err)
	}
	
	// Reset call count
	callCount = 0
	interruptedGenerateFunc := func(text string) ([]byte, error) {
		callCount++
		fmt.Printf("  生成音频 #%d: %s\n", callCount, truncateText(text, 30))
		
		// Simulate interruption after 2 successful calls
		if callCount > 2 {
			return nil, fmt.Errorf("simulated interruption")
		}
		
		return []byte(fmt.Sprintf("mock audio data for: %s", text)), nil
	}
	
	// First attempt - will be interrupted
	fmt.Println("  第一次尝试（将被中断）...")
	_, err = audioProcessor.ProcessTextToAudioSegmentsWithRetry(
		testText,
		resumeTestDir,
		interruptedGenerateFunc,
		retryConfig,
	)
	
	if err != nil {
		fmt.Printf("  ✅ 预期的中断发生: %v\n", err)
	}
	
	// Check what files were created
	files, _ := filepath.Glob(filepath.Join(resumeTestDir, "*.mp3"))
	fmt.Printf("  中断后已生成 %d 个文件\n", len(files))
	
	// Check progress file
	progressFile := filepath.Join(resumeTestDir, "progress.txt")
	if _, err := os.Stat(progressFile); err == nil {
		progressData, _ := os.ReadFile(progressFile)
		fmt.Printf("  进度文件内容: %s\n", string(progressData))
	}
	
	// Second attempt - should resume from where it left off
	fmt.Println("  第二次尝试（断点续传）...")
	callCount = 0
	resumeGenerateFunc := func(text string) ([]byte, error) {
		callCount++
		fmt.Printf("  续传生成音频 #%d: %s\n", callCount, truncateText(text, 30))
		return []byte(fmt.Sprintf("mock audio data for: %s", text)), nil
	}
	
	audioFiles, err = audioProcessor.ProcessTextToAudioSegmentsWithRetry(
		testText,
		resumeTestDir,
		resumeGenerateFunc,
		retryConfig,
	)
	
	if err != nil {
		log.Printf("断点续传测试失败: %v", err)
	} else {
		fmt.Printf("✅ 断点续传测试成功，最终生成了 %d 个音频文件\n", len(audioFiles))
		
		// List all generated files
		allFiles, _ := filepath.Glob(filepath.Join(resumeTestDir, "*.mp3"))
		fmt.Printf("  所有生成的文件 (%d个):\n", len(allFiles))
		for _, file := range allFiles {
			info, _ := os.Stat(file)
			fmt.Printf("    - %s (%d bytes)\n", filepath.Base(file), info.Size())
		}
	}
	
	// Test 3: Test different error types
	fmt.Println("\n4. 测试不同类型的错误...")
	
	errorTests := []struct {
		name        string
		error       string
		shouldRetry bool
	}{
		{"速率限制", "API error 1002: rate limit", true},
		{"网络超时", "connection timeout", true},
		{"服务器错误", "500 internal server error", true},
		{"认证错误", "401 unauthorized", false},
		{"无效参数", "400 bad request", false},
	}
	
	for _, test := range errorTests {
		fmt.Printf("  测试错误类型: %s\n", test.name)
		isRetryable := audioProcessor.isRetryableError(fmt.Errorf(test.error))
		if isRetryable == test.shouldRetry {
			fmt.Printf("    ✅ 正确识别为 %s\n", map[bool]string{true: "可重试", false: "不可重试"}[isRetryable])
		} else {
			fmt.Printf("    ❌ 错误识别，期望 %s，实际 %s\n", 
				map[bool]string{true: "可重试", false: "不可重试"}[test.shouldRetry],
				map[bool]string{true: "可重试", false: "不可重试"}[isRetryable])
		}
	}
	
	// Test 4: Test retry delay calculation
	fmt.Println("\n5. 测试重试延迟计算...")
	testRetryConfig := services.DefaultRetryConfig()
	testRetryConfig.JitterEnabled = false // For predictable testing
	
	for attempt := 1; attempt <= 5; attempt++ {
		delay := audioProcessor.calculateRetryDelay(testRetryConfig, attempt)
		fmt.Printf("  尝试 %d: 延迟 %v\n", attempt, delay)
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("功能验证:")
	fmt.Println("✅ 重试机制 - 自动重试速率限制和网络错误")
	fmt.Println("✅ 断点续传 - 从中断点继续处理")
	fmt.Println("✅ 错误分类 - 正确识别可重试和不可重试错误")
	fmt.Println("✅ 延迟计算 - 指数退避算法")
	fmt.Printf("\n测试文件位置: %s\n", testDir)
	
	// Cleanup option
	fmt.Print("\n是否清理测试文件? (y/N): ")
	var response string
	fmt.Scanln(&response)
	if strings.ToLower(response) == "y" {
		os.RemoveAll(testDir)
		fmt.Println("✅ 测试文件已清理")
	}
}

func truncateText(text string, maxLen int) string {
	text = strings.TrimSpace(text)
	if len(text) <= maxLen {
		return text
	}
	return text[:maxLen] + "..."
}
