#!/bin/bash

# TTS API 测试脚本
# 用于测试新增的 EasyVoice TTS 功能

BASE_URL="http://localhost:8080"
API_BASE="${BASE_URL}/api/v1"

echo "=== TTS API 测试脚本 ==="
echo "测试服务器: $BASE_URL"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "请求: $method $endpoint"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint")
    else
        response=$(curl -s -X $method "$API_BASE$endpoint")
    fi
    
    # 检查响应是否为有效的JSON
    if echo "$response" | jq . >/dev/null 2>&1; then
        success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            echo -e "${GREEN}✓ 成功${NC}"
            echo "$response" | jq .
        else
            echo -e "${RED}✗ 失败${NC}"
            echo "$response" | jq .
        fi
    else
        echo -e "${RED}✗ 无效响应${NC}"
        echo "$response"
    fi
    
    echo
}

# 检查服务器是否运行
echo -e "${YELLOW}检查服务器状态...${NC}"
if ! curl -s "$BASE_URL/health" >/dev/null; then
    echo -e "${RED}错误: 服务器未运行或无法访问 $BASE_URL${NC}"
    echo "请确保服务器正在运行，然后重新运行此脚本。"
    exit 1
fi
echo -e "${GREEN}✓ 服务器正在运行${NC}"
echo

# 1. 获取可用的 TTS 提供商
test_api "GET" "/tts/providers" "" "获取可用的 TTS 提供商"

# 2. 测试 EasyVoice 提供商
test_api "GET" "/tts/test/easyvoice" "" "测试 EasyVoice 提供商"

# 3. 测试 OpenAI 提供商
test_api "GET" "/tts/test/openai" "" "测试 OpenAI 提供商"

# 4. 测试 MiniMax 提供商
test_api "GET" "/tts/test/minimax" "" "测试 MiniMax 提供商"

# 5. 切换到 EasyVoice 提供商
test_api "POST" "/tts/provider" '{"provider": "easyvoice", "voice": "zh-CN-YunxiNeural"}' "切换到 EasyVoice 提供商"

# 6. 切换到 MiniMax 提供商
test_api "POST" "/tts/provider" '{"provider": "minimax", "voice": "male-qn-qingse"}' "切换到 MiniMax 提供商"

# 7. 切换到 OpenAI 提供商
test_api "POST" "/tts/provider" '{"provider": "openai", "voice": "alloy"}' "切换到 OpenAI 提供商"

# 8. 测试无效的提供商
test_api "POST" "/tts/provider" '{"provider": "invalid_provider"}' "测试无效的提供商（应该失败）"

# 9. 测试缺少必需参数
test_api "POST" "/tts/provider" '{}' "测试缺少必需参数（应该失败）"

echo -e "${YELLOW}=== 集成测试 ===${NC}"

# 如果有项目ID，可以测试完整的音频生成流程
if [ -n "$PROJECT_ID" ]; then
    echo "使用项目ID: $PROJECT_ID"
    
    # 切换到 EasyVoice
    test_api "POST" "/tts/provider" '{"provider": "easyvoice", "voice": "zh-CN-YunxiNeural"}' "切换到 EasyVoice 进行音频生成"
    
    # 生成音频
    test_api "POST" "/audio/$PROJECT_ID/generate" "" "使用 EasyVoice 生成音频"
    
    # 检查音频进度
    sleep 2
    test_api "GET" "/audio/$PROJECT_ID/progress" "" "检查音频生成进度"
    
else
    echo -e "${YELLOW}提示: 设置 PROJECT_ID 环境变量来测试完整的音频生成流程${NC}"
    echo "例如: PROJECT_ID=your-project-id $0"
fi

echo -e "${YELLOW}=== 测试完成 ===${NC}"
echo
echo -e "${BLUE}使用说明:${NC}"
echo "1. 确保在 .env 文件中配置了相应的 TTS 提供商凭据"
echo "2. EasyVoice 需要配置 EASYVOICE_USERNAME 和 EASYVOICE_PASSWORD"
echo "3. OpenAI 需要配置 OPENAI_API_KEY"
echo "4. MiniMax 需要配置 MINIMAX_TTS_API_KEY 和 MINIMAX_TTS_GROUP_ID"
echo
echo -e "${BLUE}可用的语音模型:${NC}"
echo "EasyVoice: zh-CN-YunxiNeural, zh-CN-XiaoxiaoNeural, zh-CN-YunyangNeural 等"
echo "OpenAI: alloy, echo, fable, onyx, nova, shimmer"
echo "MiniMax: male-qn-qingse, female-tianmei, male-qn-jingying 等"
