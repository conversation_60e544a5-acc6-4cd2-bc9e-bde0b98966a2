@echo off
chcp 65001 >nul
echo === PPT Narrator 提示词优化验证 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080

echo 测试目标: 验证优化后的提示词是否解决了重复开场白问题
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问
    echo 请先启动服务: docker-compose up -d
    pause
    exit /b 1
)

REM 2. 获取现有项目
echo.
echo 2. 获取现有项目列表...
curl -s "%BASE_URL%/api/v1/projects" > temp_projects.json 2>nul

if exist temp_projects.json (
    echo 当前项目:
    findstr /C:"name\|id\|status" temp_projects.json 2>nul
    echo.
) else (
    echo ⚠️  无法获取项目列表
)
del temp_projects.json >nul 2>&1

REM 3. 手动输入项目ID进行测试
echo 请选择一个已有项目进行测试，或创建新项目
set /p TEST_PROJECT_ID="请输入项目ID (或按回车跳过): "

if "%TEST_PROJECT_ID%"=="" (
    echo 跳过实际测试，显示优化说明
    goto :show_improvements
)

REM 4. 检查项目状态
echo.
echo 3. 检查项目状态...
curl -s "%BASE_URL%/api/v1/projects/%TEST_PROJECT_ID%" > temp_project.json 2>nul

if exist temp_project.json (
    echo 项目信息:
    findstr /C:"name\|status\|slide_count" temp_project.json
    echo.
    
    REM 检查是否可以生成讲稿
    findstr /C:"completed\|narration_failed" temp_project.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 项目状态适合生成讲稿
        set CAN_GENERATE=true
    ) else (
        echo ⚠️  项目状态可能不适合生成讲稿
        set CAN_GENERATE=false
    )
) else (
    echo ❌ 无法获取项目信息
    set CAN_GENERATE=false
)
del temp_project.json >nul 2>&1

REM 5. 生成优化后的讲稿
if "%CAN_GENERATE%"=="true" (
    echo.
    echo 4. 生成优化后的讲稿...
    echo 使用优化后的提示词生成讲稿，要求自然连贯...
    
    curl -s -X POST "%BASE_URL%/api/v1/narration/%TEST_PROJECT_ID%/generate" ^
        -H "Content-Type: application/json" ^
        -d "{\"user_requirements\":\"请生成自然连贯的讲稿，避免每页都用开场白，确保页面间自然衔接\"}" > temp_generate_response.json 2>nul
    
    if exist temp_generate_response.json (
        findstr /C:"success.*true" temp_generate_response.json >nul
        if %errorlevel% equ 0 (
            echo ✅ 讲稿生成启动成功
            echo 正在后台生成，请等待...
            
            REM 等待生成完成
            :wait_loop
            timeout /t 10 >nul 2>&1
            curl -s "%BASE_URL%/api/v1/narration/%TEST_PROJECT_ID%/progress" > temp_progress.json 2>nul
            
            if exist temp_progress.json (
                findstr /C:"progress.*100\|narration_ready" temp_progress.json >nul
                if %errorlevel% equ 0 (
                    echo ✅ 讲稿生成完成
                    del temp_progress.json >nul 2>&1
                    goto :check_result
                )
                
                findstr /C:"narration_failed\|failed" temp_progress.json >nul
                if %errorlevel% equ 0 (
                    echo ❌ 讲稿生成失败
                    type temp_progress.json
                    del temp_progress.json >nul 2>&1
                    goto :show_improvements
                )
                
                echo 生成中...
                del temp_progress.json >nul 2>&1
                goto :wait_loop
            )
        ) else (
            echo ❌ 讲稿生成启动失败
            type temp_generate_response.json
        )
    )
    del temp_generate_response.json >nul 2>&1
)

:check_result
REM 6. 检查生成结果
echo.
echo 5. 检查生成的讲稿质量...
curl -s "%BASE_URL%/api/v1/narration/%TEST_PROJECT_ID%" > temp_narration.json 2>nul

if exist temp_narration.json (
    echo 分析讲稿内容...
    
    REM 检查问题模式
    echo.
    echo 📊 质量分析:
    
    REM 检查是否有重复开场白
    findstr /C:"大家好\|嘿，大家好\|同学们好" temp_narration.json >nul
    if %errorlevel% equ 0 (
        echo ❌ 仍然包含重复开场白
        set QUALITY_ISSUE=true
    ) else (
        echo ✅ 没有重复开场白
        set QUALITY_ISSUE=false
    )
    
    REM 检查是否有课程化语言
    findstr /C:"今天我们来讲\|咱们今天继续聊聊\|让我们一起来学习" temp_narration.json >nul
    if %errorlevel% equ 0 (
        echo ❌ 仍然包含课程化语言
        set QUALITY_ISSUE=true
    ) else (
        echo ✅ 避免了课程化语言
    )
    
    REM 检查是否有停顿标记
    findstr /C:"[停顿" temp_narration.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 包含停顿标记
    ) else (
        echo ⚠️  缺少停顿标记
    )
    
    REM 检查是否有口语化表达
    findstr /C:"嗯\|呃\|那个\|怎么说呢" temp_narration.json >nul
    if %errorlevel% equ 0 (
        echo ✅ 包含口语化表达
    ) else (
        echo ⚠️  口语化程度可能不够
    )
    
    echo.
    echo 📝 讲稿预览 (前200字符):
    REM 提取并显示讲稿的前200个字符
    for /f "tokens=*" %%a in ('findstr /C:"narration_script" temp_narration.json') do (
        set NARRATION_LINE=%%a
    )
    echo %NARRATION_LINE:~0,200%...
    
) else (
    echo ❌ 无法获取生成的讲稿
)
del temp_narration.json >nul 2>&1

goto :end

:show_improvements
echo.
echo === 提示词优化说明 ===
echo.
echo 🎯 主要问题:
echo   ❌ 每页都用 "大家好"、"嘿，大家好" 开场
echo   ❌ 把每页当作独立课程: "今天我们来讲"
echo   ❌ 缺乏页面间的自然衔接
echo   ❌ 过度的总结语: "怎么样，是不是觉得..."
echo.
echo ✅ 优化方案:
echo   1. 强调这是连贯的PPT演示，不是独立课程
echo   2. 根据页面位置提供不同指导:
echo      - 第一页: 自然引入主题
echo      - 中间页: 承接上文，展开内容
echo      - 最后页: 自然总结
echo   3. 明确禁止重复开场白和课程化语言
echo   4. 提供具体的好坏示例
echo.
echo 📋 预期效果:
echo   优化前: "嘿，大家好，咱们今天继续聊聊SQL注入..."
echo   优化后: "刚才说到了基础概念，现在我们深入一点..."
echo.
echo 🔧 如何测试:
echo   1. 上传PPT并生成截图
echo   2. 使用优化后的系统生成讲稿
echo   3. 检查是否还有重复开场白
echo   4. 验证页面间的自然衔接
echo.

:end
echo.
echo === 测试完成 ===
echo.
echo 💡 使用建议:
echo 1. 在用户需求中明确要求: "请生成自然连贯的讲稿，避免重复开场"
echo 2. 对于技术类PPT，可以要求: "专业但易懂的技术分享风格"
echo 3. 如果仍有问题，可以在用户需求中具体说明避免的表达
echo.
echo 📚 相关文档:
echo - PROMPT_OPTIMIZATION_GUIDE.md: 详细的优化说明
echo - test_prompt_optimization.go: 代码级别的测试
echo.
pause
