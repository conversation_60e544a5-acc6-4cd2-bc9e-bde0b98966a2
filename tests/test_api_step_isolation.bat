@echo off
chcp 65001 >nul
echo === PPT Narrator API 步骤隔离测试 ===
echo.

REM 配置
set BASE_URL=http://localhost:8080
set PROJECT_ID=test-api-isolation-%RANDOM%

echo 测试目标: 验证每个步骤的失败不会影响前面已成功的步骤
echo.

REM 1. 检查服务状态
echo 1. 检查服务状态...
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/health" > temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ 服务运行正常
) else (
    echo ❌ 服务未运行或无法访问，请先启动服务
    echo 请确保服务运行在 %BASE_URL%
    pause
    exit /b 1
)

REM 2. 创建测试项目
echo.
echo 2. 创建测试项目...
echo ℹ️  模拟创建项目: %PROJECT_ID%
echo 注意: 实际环境中需要调用项目创建API

REM 3. 测试讲稿生成API
echo.
echo 3. 测试讲稿生成API...
echo ℹ️  测试正常状态下的讲稿生成
curl -s -X POST "%BASE_URL%/api/v1/narration/%PROJECT_ID%/generate" ^
    -H "Content-Type: application/json" ^
    -d "{\"user_requirements\":\"测试讲稿生成\"}" > temp_response.txt 2>nul

if %errorlevel% equ 0 (
    echo ✅ 讲稿生成API调用成功
) else (
    echo ⚠️  讲稿生成API调用失败（可能是项目不存在）
)
del temp_response.txt >nul 2>&1

REM 4. 测试音频生成API - 无讲稿状态
echo.
echo 4. 测试音频生成API - 无讲稿状态...
echo ℹ️  测试在没有讲稿的情况下生成音频
curl -s -X POST "%BASE_URL%/api/v1/audio/%PROJECT_ID%/generate" ^
    -H "Content-Type: application/json" > temp_audio_response.txt 2>nul

REM 检查响应是否包含预期的错误消息
findstr /C:"Narration not ready" temp_audio_response.txt >nul
if %errorlevel% equ 0 (
    echo ✅ 正确返回了 'Narration not ready' 错误
) else (
    echo ❌ 未返回预期的错误消息
    echo 实际响应:
    type temp_audio_response.txt
)
del temp_audio_response.txt >nul 2>&1

REM 5. 模拟讲稿生成成功
echo.
echo 5. 模拟讲稿生成成功...
echo ℹ️  模拟设置项目状态为 'narration_ready'
echo 注意: 实际环境中需要通过数据库或管理API来设置状态

REM 6. 测试音频生成API - 有讲稿状态
echo.
echo 6. 测试音频生成API - 有讲稿状态...
echo ℹ️  测试在有讲稿的情况下生成音频
curl -s -X POST "%BASE_URL%/api/v1/audio/%PROJECT_ID%/generate" ^
    -H "Content-Type: application/json" > temp_audio_success.txt 2>nul

findstr /C:"success.*true" temp_audio_success.txt >nul
if %errorlevel% equ 0 (
    echo ✅ 音频生成API调用成功
) else (
    echo ❌ 音频生成API调用失败
    echo 响应:
    type temp_audio_success.txt
)
del temp_audio_success.txt >nul 2>&1

REM 7. 模拟音频生成失败
echo.
echo 7. 模拟音频生成失败...
echo ℹ️  模拟设置项目状态为 'audio_failed'
echo 注意: 实际环境中需要通过数据库或管理API来设置状态

REM 8. 测试音频重试API
echo.
echo 8. 测试音频重试API...
echo ℹ️  测试从 'audio_failed' 状态重试音频生成
curl -s -X POST "%BASE_URL%/api/v1/audio/%PROJECT_ID%/generate" ^
    -H "Content-Type: application/json" > temp_retry.txt 2>nul

REM 应该允许重试，不应该返回 "Narration not ready" 错误
findstr /C:"Narration not ready" temp_retry.txt >nul
if %errorlevel% equ 0 (
    echo ❌ 不应该返回 'Narration not ready' 错误
) else (
    echo ✅ 允许从失败状态重试音频生成
)
del temp_retry.txt >nul 2>&1

REM 9. 测试视频生成API - 无音频状态
echo.
echo 9. 测试视频生成API - 无音频状态...
echo ℹ️  测试在没有音频的情况下生成视频
curl -s -X POST "%BASE_URL%/api/v1/video/%PROJECT_ID%/generate" ^
    -H "Content-Type: application/json" ^
    -d "{\"duration_per_slide\":5}" > temp_video.txt 2>nul

findstr /C:"Audio not ready" temp_video.txt >nul
if %errorlevel% equ 0 (
    echo ✅ 正确返回了 'Audio not ready' 错误
) else (
    echo ❌ 未返回预期的错误消息
    echo 实际响应:
    type temp_video.txt
)
del temp_video.txt >nul 2>&1

REM 10. 测试项目状态API
echo.
echo 10. 测试项目状态API...
echo ℹ️  获取项目状态
curl -s "%BASE_URL%/api/v1/projects/%PROJECT_ID%/status" > temp_status_response.txt 2>nul

if %errorlevel% equ 0 (
    echo ✅ 项目状态API调用成功
    echo 状态响应:
    type temp_status_response.txt
) else (
    echo ⚠️  项目状态API调用失败
)
del temp_status_response.txt >nul 2>&1

REM 11. 测试错误消息格式
echo.
echo 11. 测试错误消息格式...
echo ℹ️  验证错误响应格式

REM 测试不存在的项目
curl -s -X POST "%BASE_URL%/api/v1/audio/nonexistent-project/generate" ^
    -H "Content-Type: application/json" > temp_error_format.txt 2>nul

REM 检查响应格式
findstr /C:"success.*false" temp_error_format.txt >nul && findstr /C:"error" temp_error_format.txt >nul
if %errorlevel% equ 0 (
    echo ✅ 错误响应格式正确
) else (
    echo ❌ 错误响应格式不正确
    echo 实际响应:
    type temp_error_format.txt
)
del temp_error_format.txt >nul 2>&1

REM 测试总结
echo.
echo === 测试总结 ===
echo ✅ 测试完成
echo.
echo 预期行为:
echo 1. 无讲稿时音频生成应返回 'Narration not ready' 错误
echo 2. 音频失败后应允许重试，不影响讲稿状态
echo 3. 无音频时视频生成应返回 'Audio not ready' 错误
echo 4. 错误响应应包含 success:false 和具体错误信息
echo.
echo 注意事项:
echo - 此测试脚本需要服务运行在 %BASE_URL%
echo - 某些测试需要实际的项目数据，可能需要手动设置
echo - 完整测试需要配置有效的 MiniMax TTS API Key
echo.
echo 手动测试步骤:
echo 1. 启动服务: go run cmd/server/main.go
echo 2. 上传PPT文件并生成截图
echo 3. 生成讲稿
echo 4. 故意配置错误的TTS设置使音频生成失败
echo 5. 修复TTS配置并重试音频生成
echo 6. 验证讲稿内容未受影响
echo.
pause
