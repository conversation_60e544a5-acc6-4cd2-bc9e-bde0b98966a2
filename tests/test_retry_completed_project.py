#!/usr/bin/env python3
"""
测试已完成项目的重试功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def get_project_status(project_id):
    """获取项目状态"""
    print(f"\n📊 获取项目状态 (ID: {project_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/pipeline/{project_id}/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                status = result.get('status', {})
                print(f"✅ 项目状态获取成功!")
                print(f"   项目名称: {status.get('project_name')}")
                print(f"   项目状态: {status.get('project_status')}")
                print(f"   是否运行中: {status.get('is_running')}")
                print(f"   当前阶段: {status.get('progress', {}).get('current_stage')}")
                print(f"   进度: {status.get('progress', {}).get('progress')}%")
                return status
            else:
                print(f"❌ 获取项目状态失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取项目状态异常: {e}")
        return None

def get_retry_options(project_id):
    """获取重试选项"""
    print(f"\n🔄 获取重试选项 (ID: {project_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/pipeline/{project_id}/retry-options", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"✅ 重试选项获取成功!")
                print(f"   项目名称: {data.get('project_name')}")
                print(f"   项目状态: {data.get('project_status')}")
                print(f"   可以重试: {data.get('can_retry')}")
                print(f"   重试类型: {data.get('retry_type')}")
                print(f"   重试消息: {data.get('retry_message')}")
                print(f"   是否运行中: {data.get('is_running')}")
                
                if data.get('available_stages'):
                    print(f"   可用阶段:")
                    for stage in data.get('available_stages', []):
                        print(f"     - {stage['stage']}: {stage['name']} ({stage['description']})")
                
                return data
            else:
                print(f"❌ 获取重试选项失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取重试选项异常: {e}")
        return None

def retry_from_stage(project_id, stage, user_requirements=""):
    """从指定阶段重试"""
    print(f"\n🔄 从 {stage} 阶段重试 (ID: {project_id})...")
    
    try:
        payload = {
            "stage": stage,
            "user_requirements": user_requirements
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/pipeline/{project_id}/retry",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 重试启动成功!")
                print(f"   消息: {result.get('message')}")
                print(f"   项目状态: {result.get('project_status')}")
                print(f"   重试阶段: {result.get('retry_stage')}")
                return True
            else:
                print(f"❌ 重试启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error')}")
                except:
                    pass
            return False
            
    except Exception as e:
        print(f"❌ 重试启动异常: {e}")
        return False

def monitor_progress(project_id, max_wait_seconds=60):
    """监控进度"""
    print(f"\n👀 监控进度 (最多等待 {max_wait_seconds} 秒)...")
    
    start_time = time.time()
    last_stage = ""
    last_progress = -1
    
    while time.time() - start_time < max_wait_seconds:
        try:
            response = requests.get(f"{BASE_URL}/api/v1/pipeline/{project_id}/progress", timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    progress = result.get('progress', {})
                    current_stage = progress.get('current_stage')
                    current_progress = progress.get('progress', 0)
                    message = progress.get('message', '')
                    
                    # 只在状态变化时打印
                    if current_stage != last_stage or current_progress != last_progress:
                        print(f"   📊 {current_stage}: {current_progress}% - {message}")
                        last_stage = current_stage
                        last_progress = current_progress
                    
                    # 检查是否完成或失败
                    if current_stage in ['completed', 'failed']:
                        if current_stage == 'completed':
                            print(f"✅ 处理完成!")
                        else:
                            print(f"❌ 处理失败: {message}")
                        return current_stage == 'completed'
                        
            time.sleep(2)  # 每2秒检查一次
            
        except Exception as e:
            print(f"⚠️  监控异常: {e}")
            time.sleep(2)
    
    print(f"⏰ 监控超时 ({max_wait_seconds} 秒)")
    return False

def main():
    """主测试函数"""
    print("🧪 PPT Narrator 已完成项目重试功能测试")
    print("=" * 60)
    
    # 测试健康检查
    if not test_health():
        print("❌ 服务不可用，退出测试")
        return
    
    # 获取项目ID
    project_id = input("\n请输入要测试的项目ID（必须是已完成的项目）: ").strip()
    if not project_id:
        print("❌ 项目ID不能为空")
        return
    
    print(f"\n🎯 开始测试项目: {project_id}")
    
    # 1. 获取项目状态
    status = get_project_status(project_id)
    if not status:
        print("❌ 无法获取项目状态，退出测试")
        return
    
    # 2. 获取重试选项
    retry_options = get_retry_options(project_id)
    if not retry_options:
        print("❌ 无法获取重试选项，退出测试")
        return
    
    if not retry_options.get('can_retry'):
        print("❌ 项目当前不支持重试")
        print(f"   原因: {retry_options.get('retry_message')}")
        return
    
    # 3. 选择重试阶段
    available_stages = retry_options.get('available_stages', [])
    if not available_stages:
        print("❌ 没有可用的重试阶段")
        return
    
    print(f"\n📋 可用的重试阶段:")
    for i, stage in enumerate(available_stages):
        print(f"   {i+1}. {stage['stage']}: {stage['name']} - {stage['description']}")
    
    try:
        choice = int(input(f"\n请选择要重试的阶段 (1-{len(available_stages)}): ").strip())
        if choice < 1 or choice > len(available_stages):
            print("❌ 无效的选择")
            return
        
        selected_stage = available_stages[choice-1]['stage']
        print(f"✅ 选择了阶段: {selected_stage}")
        
    except ValueError:
        print("❌ 请输入有效的数字")
        return
    
    # 4. 询问用户需求（对于讲稿生成）
    user_requirements = ""
    if selected_stage == "narration":
        user_requirements = input("\n请输入用户需求（可选，用于讲稿生成）: ").strip()
    
    # 5. 确认重试
    confirm = input(f"\n确认要从 {selected_stage} 阶段重新开始吗？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return
    
    # 6. 执行重试
    if not retry_from_stage(project_id, selected_stage, user_requirements):
        print("❌ 重试启动失败")
        return
    
    # 7. 监控进度
    monitor_choice = input(f"\n是否监控处理进度？(Y/n): ").strip().lower()
    if monitor_choice not in ['n', 'no']:
        success = monitor_progress(project_id, max_wait_seconds=300)  # 5分钟
        
        if success:
            print(f"\n🎉 重试成功完成!")
        else:
            print(f"\n⚠️  重试可能仍在进行中，请稍后检查状态")
    
    # 8. 显示最终状态
    print(f"\n📊 最终状态:")
    final_status = get_project_status(project_id)
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
