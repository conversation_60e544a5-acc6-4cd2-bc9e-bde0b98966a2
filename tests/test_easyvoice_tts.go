package tests

import (
	"context"
	"os"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
	"strings"
	"testing"
)

// TestEasyVoiceTTSClient tests the EasyVoice TTS client functionality
func TestEasyVoiceTTSClient(t *testing.T) {
	// Skip test if credentials are not provided
	username := os.Getenv("EASYVOICE_USERNAME")
	password := os.Getenv("EASYVOICE_PASSWORD")
	
	if username == "" || password == "" {
		t.Skip("EasyVoice credentials not provided, skipping test")
	}

	// Create test configuration
	cfg := &config.Config{
		EasyVoiceAPIURL:  "https://easyvoice.wetolink.com/api/v1/tts/generate",
		EasyVoiceUsername: username,
		EasyVoicePassword: password,
		EasyVoiceVoice:   "zh-CN-YunxiNeural",
		EasyVoiceRate:    "0%",
		EasyVoicePitch:   "0Hz",
		EasyVoiceVolume:  "0%",
	}

	// Create EasyVoice TTS client
	client := services.NewEasyVoiceTTSClient(cfg)

	// Test configuration validation
	t.Run("ValidateConfig", func(t *testing.T) {
		err := client.ValidateConfig()
		if err != nil {
			t.Errorf("Configuration validation failed: %v", err)
		}
	})

	// Test audio generation
	t.Run("GenerateAudio", func(t *testing.T) {
		testText := "这是一个EasyVoice TTS测试。"
		
		audioData, err := client.GenerateAudio(context.Background(), testText)
		if err != nil {
			t.Errorf("Audio generation failed: %v", err)
			return
		}

		if len(audioData) == 0 {
			t.Error("Generated audio data is empty")
		}

		// Check if the audio data looks like MP3 (starts with ID3 or MP3 header)
		if len(audioData) >= 3 {
			// MP3 files typically start with ID3 tag or MP3 frame header
			header := audioData[:3]
			if string(header) != "ID3" && (header[0] != 0xFF || (header[1]&0xE0) != 0xE0) {
				t.Logf("Warning: Audio data may not be valid MP3 format")
			}
		}

		t.Logf("Successfully generated %d bytes of audio data", len(audioData))
	})

	// Test supported voices
	t.Run("GetSupportedVoices", func(t *testing.T) {
		voices := client.GetSupportedVoices()
		if len(voices) == 0 {
			t.Error("No supported voices returned")
		}

		// Check if default voice is in the list
		found := false
		for _, voice := range voices {
			if voice == cfg.EasyVoiceVoice {
				found = true
				break
			}
		}

		if !found {
			t.Errorf("Default voice %s not found in supported voices list", cfg.EasyVoiceVoice)
		}

		t.Logf("Found %d supported voices", len(voices))
	})

	// Test text padding functionality
	t.Run("TextPadding", func(t *testing.T) {
		testCases := []struct {
			name     string
			input    string
			minLen   int
		}{
			{"Empty text", "", 5},
			{"Single character", "在", 5},
			{"Two characters", "在吗", 5},
			{"Three characters", "你好吗", 5},
			{"Four characters", "今天好吗", 5},
			{"Exactly five characters", "今天天气好", 5},
			{"More than five characters", "今天天气很好", 6},
			{"Text with spaces", " 在 ", 5},
			{"English text", "Hi", 5},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Since padTextIfNeeded is private, we test it indirectly
				// by checking that short texts don't cause API errors
				if len([]rune(strings.TrimSpace(tc.input))) < 5 {
					// For short texts, we expect them to be padded
					t.Logf("Short text %q should be padded to meet minimum length", tc.input)
				}
			})
		}
	})
}

// TestEasyVoiceTTSClientErrors tests error handling
func TestEasyVoiceTTSClientErrors(t *testing.T) {
	// Test with invalid configuration
	t.Run("InvalidConfig", func(t *testing.T) {
		cfg := &config.Config{
			EasyVoiceAPIURL:  "",
			EasyVoiceUsername: "",
			EasyVoicePassword: "",
		}

		client := services.NewEasyVoiceTTSClient(cfg)
		err := client.ValidateConfig()
		if err == nil {
			t.Error("Expected validation error for empty configuration")
		}
	})

	// Test with invalid credentials
	t.Run("InvalidCredentials", func(t *testing.T) {
		cfg := &config.Config{
			EasyVoiceAPIURL:  "https://easyvoice.wetolink.com/api/v1/tts/generate",
			EasyVoiceUsername: "invalid_user",
			EasyVoicePassword: "invalid_pass",
			EasyVoiceVoice:   "zh-CN-YunxiNeural",
			EasyVoiceRate:    "0%",
			EasyVoicePitch:   "0Hz",
			EasyVoiceVolume:  "0%",
		}

		client := services.NewEasyVoiceTTSClient(cfg)
		
		_, err := client.GenerateAudio(context.Background(), "测试文本")
		if err == nil {
			t.Error("Expected error for invalid credentials")
		}
		
		t.Logf("Expected error occurred: %v", err)
	})
}

// BenchmarkEasyVoiceTTS benchmarks the EasyVoice TTS performance
func BenchmarkEasyVoiceTTS(b *testing.B) {
	// Skip benchmark if credentials are not provided
	username := os.Getenv("EASYVOICE_USERNAME")
	password := os.Getenv("EASYVOICE_PASSWORD")
	
	if username == "" || password == "" {
		b.Skip("EasyVoice credentials not provided, skipping benchmark")
	}

	cfg := &config.Config{
		EasyVoiceAPIURL:  "https://easyvoice.wetolink.com/api/v1/tts/generate",
		EasyVoiceUsername: username,
		EasyVoicePassword: password,
		EasyVoiceVoice:   "zh-CN-YunxiNeural",
		EasyVoiceRate:    "0%",
		EasyVoicePitch:   "0Hz",
		EasyVoiceVolume:  "0%",
	}

	client := services.NewEasyVoiceTTSClient(cfg)
	testText := "这是一个性能测试文本，用于评估EasyVoice TTS的响应速度。"

	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		_, err := client.GenerateAudio(context.Background(), testText)
		if err != nil {
			b.Errorf("Audio generation failed: %v", err)
		}
	}
}
