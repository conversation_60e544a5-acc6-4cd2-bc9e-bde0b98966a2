#!/bin/bash

# MiniMax TTS 功能测试脚本

echo "=== MiniMax TTS 功能测试 ==="

# 检查环境变量
echo "1. 检查环境变量配置..."

if [ -z "$MINIMAX_TTS_API_KEY" ]; then
    echo "❌ MINIMAX_TTS_API_KEY 未设置"
    echo "请设置环境变量: export MINIMAX_TTS_API_KEY=your_api_key"
    exit 1
else
    echo "✅ MINIMAX_TTS_API_KEY 已设置"
fi

if [ -z "$MINIMAX_TTS_GROUP_ID" ]; then
    echo "❌ MINIMAX_TTS_GROUP_ID 未设置"
    echo "请设置环境变量: export MINIMAX_TTS_GROUP_ID=your_group_id"
    exit 1
else
    echo "✅ MINIMAX_TTS_GROUP_ID 已设置"
fi

# 设置其他必要的环境变量
export TTS_PROVIDER=minimax
export MINIMAX_TTS_MODEL=speech-02-hd
export MINIMAX_TTS_VOICE_ID=male-qn-qingse
export MINIMAX_TTS_EMOTION=happy
export TTS_SPEED=1.0
export FFMPEG_PATH=ffmpeg

echo "✅ 环境变量配置完成"

# 检查 FFmpeg
echo ""
echo "2. 检查 FFmpeg 安装..."
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg 已安装: $(ffmpeg -version | head -n1)"
else
    echo "❌ FFmpeg 未安装，请先安装 FFmpeg"
    echo "Ubuntu/Debian: sudo apt install ffmpeg"
    echo "macOS: brew install ffmpeg"
    echo "Windows: 下载并安装 FFmpeg"
    exit 1
fi

# 运行测试
echo ""
echo "3. 运行音频生成测试..."

# 检查测试文件是否存在
if [ ! -f "test_audio_generation.go" ]; then
    echo "❌ 测试文件 test_audio_generation.go 不存在"
    exit 1
fi

# 运行 Go 测试
echo "正在运行测试..."
go run test_audio_generation.go

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 测试完成！"
    echo ""
    echo "生成的测试文件位置:"
    echo "- 测试音频: ./temp/audio_test/"
    echo "- 静音音频: ./temp/audio_test/test_silence.mp3"
    echo ""
    echo "如果使用 MiniMax TTS，还会生成:"
    echo "- MiniMax 测试音频: ./temp/audio_test/minimax_test.mp3"
else
    echo "❌ 测试失败，请检查配置和网络连接"
    exit 1
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "接下来可以："
echo "1. 启动服务: go run cmd/server/main.go"
echo "2. 或使用 Docker: docker-compose up"
echo "3. 访问 http://localhost:8080 使用完整功能"
