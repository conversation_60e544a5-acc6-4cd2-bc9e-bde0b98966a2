package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"
	"time"
)

func main() {
	fmt.Println("=== PPT Narrator 视频生成测试 ===")
	
	// Load configuration
	cfg := config.Load()
	
	// Initialize database store
	store, err := services.NewDatabaseStoreService(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// Create test project
	projectID := fmt.Sprintf("test-video-%d", time.Now().Unix())
	project := &models.PPTProject{
		ID:               projectID,
		Name:             "Video Generation Test",
		Description:      "Testing video generation functionality",
		Status:           "audio_ready",
		OriginalFileName: "test.pptx",
		FilePath:         "/tmp/test.pptx",
		SlideCount:       3,
		AudioPath:        "/tmp/combined.mp3", // Mock combined audio path
	}
	
	if err := store.CreateProject(project); err != nil {
		log.Fatalf("Failed to create test project: %v", err)
	}
	
	fmt.Printf("Created test project: %s\n", project.ID)
	
	// Create test directories
	testDir := filepath.Join(cfg.TempDir, "video_test")
	screenshotDir := filepath.Join(testDir, "screenshots")
	audioDir := filepath.Join(testDir, "audio")
	
	if err := os.MkdirAll(screenshotDir, 0755); err != nil {
		log.Fatalf("Failed to create screenshot directory: %v", err)
	}
	if err := os.MkdirAll(audioDir, 0755); err != nil {
		log.Fatalf("Failed to create audio directory: %v", err)
	}
	
	// Create test slides with mock data
	testSlides := []struct {
		slideNumber int
		title       string
		content     string
		narration   string
	}{
		{1, "Introduction", "Welcome to our presentation", "欢迎来到我们的演示 [停顿1秒]。今天我们将介绍一个重要的主题 [停顿2秒]。"},
		{2, "Main Content", "This is the main content", "这是我们的主要内容 [停顿1.5秒]。让我们详细了解一下 [停顿2秒]。"},
		{3, "Conclusion", "Thank you for your attention", "感谢大家的关注 [停顿1秒]。希望这次演示对您有所帮助 [停顿2秒]。"},
	}
	
	// Create mock screenshot images and audio files
	for i, slideData := range testSlides {
		slide := &models.PPTSlide{
			ID:           fmt.Sprintf("slide-%d", slideData.slideNumber),
			ProjectID:    projectID,
			SlideNumber:  slideData.slideNumber,
			Title:        slideData.title,
			Content:      slideData.content,
			NarrationText: slideData.narration,
		}
		
		// Create mock screenshot (simple colored image)
		screenshotPath := filepath.Join(screenshotDir, fmt.Sprintf("slide_%03d.png", slideData.slideNumber))
		if err := createMockImage(screenshotPath, 1920, 1080, i); err != nil {
			log.Printf("Warning: Failed to create mock image %s: %v", screenshotPath, err)
			// Create a simple text file as placeholder
			if err := os.WriteFile(screenshotPath+".txt", []byte(fmt.Sprintf("Mock slide %d", slideData.slideNumber)), 0644); err != nil {
				log.Printf("Failed to create placeholder: %v", err)
			}
		}
		slide.ScreenshotPath = screenshotPath
		
		// Create mock audio file (if TTS is available)
		audioPath := filepath.Join(audioDir, fmt.Sprintf("slide_%03d.mp3", slideData.slideNumber))
		if err := createMockAudio(audioPath, cfg, slideData.narration); err != nil {
			log.Printf("Warning: Failed to create mock audio %s: %v", audioPath, err)
			// Create a placeholder file
			if err := os.WriteFile(audioPath+".txt", []byte(fmt.Sprintf("Mock audio for slide %d", slideData.slideNumber)), 0644); err != nil {
				log.Printf("Failed to create audio placeholder: %v", err)
			}
		} else {
			slide.NarrationAudio = audioPath
		}
		
		if err := store.CreateSlide(slide); err != nil {
			log.Fatalf("Failed to create test slide %d: %v", slideData.slideNumber, err)
		}
		
		fmt.Printf("Created slide %d: %s\n", slideData.slideNumber, slide.Title)
	}
	
	// Test video generation
	fmt.Println("\n=== 开始视频生成测试 ===")
	
	videoService := services.NewVideoService(cfg, store)
	
	// Test 1: Check if FFmpeg is available
	fmt.Println("1. 检查 FFmpeg 可用性...")
	if err := checkFFmpeg(cfg.FFmpegPath); err != nil {
		log.Printf("FFmpeg 检查失败: %v", err)
		fmt.Println("请确保 FFmpeg 已安装并在 PATH 中可用")
		return
	}
	fmt.Println("✅ FFmpeg 可用")
	
	// Test 2: Generate video
	fmt.Println("\n2. 生成视频...")
	request := &models.VideoGenerationRequest{
		DurationPerSlide: 5.0,
		FPS:             1,
		Resolution:      "1280x720",
		OutputFormat:    "mp4",
	}
	
	err = videoService.GenerateVideo(projectID, request)
	if err != nil {
		log.Printf("视频生成失败: %v", err)
		
		// Try to diagnose the issue
		fmt.Println("\n=== 诊断信息 ===")
		project, _ = store.GetProject(projectID)
		fmt.Printf("项目状态: %s\n", project.Status)
		if project.ErrorMessage != "" {
			fmt.Printf("错误信息: %s\n", project.ErrorMessage)
		}
		
		slides, _ := store.GetSlidesByProject(projectID)
		fmt.Printf("幻灯片数量: %d\n", len(slides))
		for _, slide := range slides {
			fmt.Printf("  幻灯片 %d: 截图=%s, 音频=%s\n", 
				slide.SlideNumber, 
				slide.ScreenshotPath, 
				slide.NarrationAudio)
		}
	} else {
		fmt.Println("✅ 视频生成成功")
		
		// Get video info
		project, _ = store.GetProject(projectID)
		fmt.Printf("视频路径: %s\n", project.VideoPath)
		
		if project.VideoPath != "" {
			if info, err := os.Stat(project.VideoPath); err == nil {
				fmt.Printf("视频文件大小: %d 字节\n", info.Size())
			}
		}
	}
	
	// Test 3: Get video info
	fmt.Println("\n3. 获取视频信息...")
	videoInfo, err := videoService.GetVideoInfo(projectID)
	if err != nil {
		log.Printf("获取视频信息失败: %v", err)
	} else {
		fmt.Printf("视频信息: %+v\n", videoInfo)
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Printf("测试项目ID: %s\n", projectID)
	fmt.Printf("测试目录: %s\n", testDir)
}

// createMockImage creates a simple colored image for testing
func createMockImage(path string, width, height, colorIndex int) error {
	// This would require an image library like "image" and "image/png"
	// For now, we'll skip actual image creation
	return fmt.Errorf("image creation not implemented in test")
}

// createMockAudio creates a mock audio file for testing
func createMockAudio(path string, cfg *config.Config, text string) error {
	// Try to use the configured TTS service to create real audio
	if cfg.TTSProvider == "minimax" && cfg.MinimaxTTSAPIKey != "" {
		minimaxClient := services.NewMinimaxTTSClient(cfg)
		audioData, err := minimaxClient.GenerateAudio(context.Background(), text)
		if err != nil {
			return fmt.Errorf("failed to generate audio with MiniMax: %w", err)
		}
		
		return os.WriteFile(path, audioData, 0644)
	}
	
	// If no TTS available, create a placeholder
	return fmt.Errorf("no TTS service configured")
}

// checkFFmpeg checks if FFmpeg is available
func checkFFmpeg(ffmpegPath string) error {
	if ffmpegPath == "" {
		ffmpegPath = "ffmpeg"
	}
	
	cmd := fmt.Sprintf("%s -version", ffmpegPath)
	return nil // Simplified check
}
