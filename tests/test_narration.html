<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT讲稿生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        input[type="file"], input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffeaea;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        .progress {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            color: #856404;
        }
        .project-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .project-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        .project-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .project-card p {
            margin: 5px 0;
            color: #666;
        }
        .narration-content {
            max-height: 400px;
            overflow-y: auto;
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .slide-narration {
            border-left: 4px solid #007cba;
            padding-left: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 PPT讲稿生成器</h1>
        
        <!-- 上传PPT -->
        <div class="section">
            <h2>1. 上传PPT文件</h2>
            <input type="file" id="pptFile" accept=".ppt,.pptx" />
            <textarea id="uploadRequirements" placeholder="输入对PPT的要求或说明（可选）" rows="3"></textarea>
            <button onclick="uploadPPT()">上传PPT</button>
            <div id="uploadResult"></div>
        </div>

        <!-- 项目列表 -->
        <div class="section">
            <h2>2. 现有项目</h2>
            <button onclick="loadProjects()">刷新项目列表</button>
            <div id="projectsList"></div>
        </div>

        <!-- 生成讲稿 -->
        <div class="section">
            <h2>3. 生成讲稿</h2>
            <input type="text" id="projectId" placeholder="输入项目ID" />
            <textarea id="narrationRequirements" placeholder="输入讲稿要求，例如：请为这个PPT生成详细的讲稿，要求专业、生动，适合大学生听众" rows="3"></textarea>
            <button onclick="generateNarration()">生成讲稿</button>
            <button onclick="checkProgress()">查看进度</button>
            <div id="narrationResult"></div>
        </div>

        <!-- 查看讲稿 -->
        <div class="section">
            <h2>4. 查看讲稿</h2>
            <button onclick="getNarration()">获取完整讲稿</button>
            <button onclick="getSlideNarration()">获取单页讲稿</button>
            <input type="number" id="slideNumber" placeholder="页码" min="1" style="width: 100px;" />
            <div id="narrationContent"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        let currentProjectId = '';

        // 上传PPT
        async function uploadPPT() {
            const fileInput = document.getElementById('pptFile');
            const requirements = document.getElementById('uploadRequirements').value;
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择PPT文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            if (requirements) {
                formData.append('requirements', requirements);
            }

            try {
                showResult(resultDiv, '正在上传PPT文件...', 'progress');
                
                const response = await fetch(`${API_BASE}/projects/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentProjectId = result.data.project_id;
                    document.getElementById('projectId').value = currentProjectId;
                    showResult(resultDiv, `上传成功！项目ID: ${currentProjectId}`, 'result');
                    loadProjects(); // 刷新项目列表
                } else {
                    showResult(resultDiv, `上传失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `上传失败: ${error.message}`, 'error');
            }
        }

        // 加载项目列表
        async function loadProjects() {
            const listDiv = document.getElementById('projectsList');
            
            try {
                showResult(listDiv, '正在加载项目列表...', 'progress');
                
                const response = await fetch(`${API_BASE}/projects/`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    let html = '<div class="project-list">';
                    result.data.forEach(project => {
                        html += `
                            <div class="project-card">
                                <h3>${project.filename}</h3>
                                <p><strong>ID:</strong> ${project.id}</p>
                                <p><strong>状态:</strong> ${project.status}</p>
                                <p><strong>页数:</strong> ${project.slide_count}</p>
                                <p><strong>上传时间:</strong> ${new Date(project.created_at).toLocaleString()}</p>
                                <button onclick="selectProject('${project.id}')">选择此项目</button>
                                <button onclick="viewScreenshots('${project.id}')">查看截图</button>
                            </div>
                        `;
                    });
                    html += '</div>';
                    listDiv.innerHTML = html;
                } else {
                    showResult(listDiv, '暂无项目', 'result');
                }
            } catch (error) {
                showResult(listDiv, `加载失败: ${error.message}`, 'error');
            }
        }

        // 选择项目
        function selectProject(projectId) {
            currentProjectId = projectId;
            document.getElementById('projectId').value = projectId;
            alert(`已选择项目: ${projectId}`);
        }

        // 查看截图
        function viewScreenshots(projectId) {
            window.open(`${API_BASE}/projects/${projectId}/screenshots`, '_blank');
        }

        // 生成讲稿
        async function generateNarration() {
            const projectId = document.getElementById('projectId').value || currentProjectId;
            const requirements = document.getElementById('narrationRequirements').value;
            const resultDiv = document.getElementById('narrationResult');

            if (!projectId) {
                showResult(resultDiv, '请输入项目ID', 'error');
                return;
            }

            try {
                showResult(resultDiv, '正在生成讲稿...', 'progress');

                // 构建正确的请求体
                const requestBody = {
                    project_id: projectId,  // 添加project_id字段
                    user_requirements: requirements || '请为这个PPT生成详细的讲稿',
                    style: 'professional',  // 可选：专业风格
                    language: 'zh-CN'       // 可选：中文
                };

                console.log('发送请求:', requestBody); // 调试日志

                const response = await fetch(`${API_BASE}/narration/${projectId}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                console.log('响应结果:', result); // 调试日志

                if (result.success) {
                    showResult(resultDiv, '讲稿生成已开始，请稍后查看进度', 'result');
                } else {
                    showResult(resultDiv, `生成失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('请求错误:', error); // 调试日志
                showResult(resultDiv, `生成失败: ${error.message}`, 'error');
            }
        }

        // 查看进度
        async function checkProgress() {
            const projectId = document.getElementById('projectId').value || currentProjectId;
            const resultDiv = document.getElementById('narrationResult');
            
            if (!projectId) {
                showResult(resultDiv, '请输入项目ID', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/narration/${projectId}/progress`);
                const result = await response.json();
                
                if (result.success) {
                    const progress = result.data;
                    showResult(resultDiv, `进度: ${progress.completed_slides}/${progress.total_slides} 页完成`, 'progress');
                } else {
                    showResult(resultDiv, `查看进度失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `查看进度失败: ${error.message}`, 'error');
            }
        }

        // 获取完整讲稿
        async function getNarration() {
            const projectId = document.getElementById('projectId').value || currentProjectId;
            const contentDiv = document.getElementById('narrationContent');
            
            if (!projectId) {
                showResult(contentDiv, '请输入项目ID', 'error');
                return;
            }

            try {
                showResult(contentDiv, '正在获取讲稿...', 'progress');
                
                const response = await fetch(`${API_BASE}/narration/${projectId}`);
                const result = await response.json();
                
                if (result.success) {
                    let html = '<h3>完整讲稿</h3>';
                    html += `<div class="narration-content">${result.data.full_narration}</div>`;
                    
                    if (result.data.slides) {
                        html += '<h3>分页讲稿</h3>';
                        result.data.slides.forEach(slide => {
                            html += `
                                <div class="slide-narration">
                                    <h4>第 ${slide.slide_number} 页</h4>
                                    <p>${slide.narration}</p>
                                </div>
                            `;
                        });
                    }
                    
                    contentDiv.innerHTML = html;
                } else {
                    showResult(contentDiv, `获取讲稿失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(contentDiv, `获取讲稿失败: ${error.message}`, 'error');
            }
        }

        // 获取单页讲稿
        async function getSlideNarration() {
            const projectId = document.getElementById('projectId').value || currentProjectId;
            const slideNumber = document.getElementById('slideNumber').value;
            const contentDiv = document.getElementById('narrationContent');
            
            if (!projectId || !slideNumber) {
                showResult(contentDiv, '请输入项目ID和页码', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/narration/${projectId}/slides/${slideNumber}`);
                const result = await response.json();
                
                if (result.success) {
                    const slide = result.data;
                    const html = `
                        <h3>第 ${slide.slide_number} 页讲稿</h3>
                        <div class="narration-content">${slide.narration}</div>
                    `;
                    contentDiv.innerHTML = html;
                } else {
                    showResult(contentDiv, `获取单页讲稿失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(contentDiv, `获取单页讲稿失败: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(element, message, type) {
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // 页面加载时自动加载项目列表
        window.onload = function() {
            loadProjects();
        };
    </script>
</body>
</html>
