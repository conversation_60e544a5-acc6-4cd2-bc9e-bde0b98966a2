# PPT Narrator 字幕功能使用指南

## 功能概述

PPT Narrator 现已支持在生成的视频中嵌入字幕功能。用户可以选择是否在视频中包含字幕，并可以自定义字幕的样式。

## 功能特性

### ✅ 已实现功能

1. **字幕生成**
   - 从幻灯片的讲述文本自动生成SRT格式字幕文件
   - 智能解析停顿标记（如[停顿1秒]）
   - 根据音频时长自动计算字幕时间轴
   - 支持中英文文本的智能分割

2. **字幕样式自定义**
   - 字体大小调整
   - 字体颜色设置
   - 字体族选择
   - 轮廓宽度控制
   - 阴影效果开关
   - 背景颜色设置
   - 字幕位置调整（底部/顶部/中间）

3. **视频嵌入**
   - 使用FFmpeg将字幕烧录到视频中
   - 支持多种视频格式
   - 保持原有视频质量

4. **配置管理**
   - 全局默认字幕设置
   - 项目级别的字幕开关
   - 环境变量配置支持

## 使用方法

### 1. 环境变量配置

在 `.env` 文件中添加以下字幕相关配置：

```bash
# 字幕配置
SUBTITLE_ENABLED=false              # 全局默认是否启用字幕
SUBTITLE_FONT_SIZE=24              # 字幕字体大小（像素）
SUBTITLE_FONT_COLOR=#FFFFFF        # 字幕字体颜色（十六进制）
SUBTITLE_BACKGROUND_COLOR=         # 字幕背景颜色（留空表示无背景）
SUBTITLE_POSITION=bottom           # 字幕位置：bottom/top/center
SUBTITLE_FONT_FAMILY=Arial         # 字幕字体族
SUBTITLE_OUTLINE=2                 # 字幕轮廓宽度（像素）
SUBTITLE_SHADOW=true               # 是否添加字幕阴影
```

### 2. API 使用

#### 生成带字幕的视频

```http
POST /api/v1/projects/{projectId}/video/generate
Content-Type: application/json

{
  "output_format": "mp4",
  "quality": "high",
  "fps": 1,
  "resolution": "1920x1080",
  "enable_subtitles": true,
  "subtitle_style": {
    "font_size": 28,
    "font_color": "#FFFF00",
    "font_family": "Arial",
    "outline": 3,
    "shadow": true,
    "position": "bottom"
  }
}
```

#### 生成不带字幕的视频

```http
POST /api/v1/projects/{projectId}/video/generate
Content-Type: application/json

{
  "output_format": "mp4",
  "quality": "high",
  "fps": 1,
  "resolution": "1920x1080",
  "enable_subtitles": false
}
```

### 3. Docker 部署

字幕功能已集成到 Docker 配置中，使用以下命令启动：

```bash
docker compose up -d --build
```

服务将在 `http://localhost:38080` 上运行。

## 技术实现

### 核心组件

1. **SubtitleService** (`backend/internal/services/subtitle_service.go`)
   - 负责SRT字幕文件生成
   - 处理文本解析和时间轴计算
   - 支持停顿标记解析

2. **VideoService** 扩展
   - 集成字幕生成功能
   - FFmpeg字幕嵌入命令
   - 字幕样式处理

3. **配置系统** 扩展
   - 新增字幕相关配置项
   - 环境变量支持
   - 默认值管理

### 字幕生成流程

1. **文本解析**
   - 提取幻灯片讲述文本
   - 移除停顿标记
   - 按句子分割文本

2. **时间轴计算**
   - 根据音频文件时长
   - 平均分配字幕显示时间
   - 考虑阅读速度优化

3. **SRT文件生成**
   - 标准SRT格式输出
   - 时间戳格式化
   - 字幕索引管理

4. **视频嵌入**
   - FFmpeg subtitle filter
   - 样式参数转换
   - 视频重新编码

## 测试

### 单元测试

运行字幕功能的单元测试：

```bash
cd backend
go test ./internal/services -v -run TestSubtitleService
```

### 集成测试

运行完整的集成测试：

```bash
cd backend
go test ./tests -v -run TestSubtitleIntegration
```

## 故障排除

### 常见问题

1. **字幕不显示**
   - 检查 `enable_subtitles` 是否设置为 `true`
   - 确认讲述文本不为空
   - 验证FFmpeg是否正确安装

2. **字幕时间不准确**
   - 检查音频文件是否存在
   - 验证音频时长检测功能
   - 调整时间轴计算逻辑

3. **字幕样式问题**
   - 检查字体颜色格式（需要十六进制）
   - 确认字体族在系统中可用
   - 验证FFmpeg字幕filter参数

### 日志调试

查看服务日志：

```bash
docker compose logs ppt-narrator -f
```

## 未来扩展

### 计划中的功能

1. **多语言字幕支持**
   - 自动翻译功能
   - 多语言字幕文件生成

2. **字幕样式模板**
   - 预设样式模板
   - 用户自定义模板保存

3. **字幕编辑功能**
   - 在线字幕编辑器
   - 时间轴手动调整
   - 文本内容修改

4. **高级字幕效果**
   - 动画效果
   - 渐变色彩
   - 特殊字体效果

## 贡献指南

如需为字幕功能贡献代码：

1. Fork 项目仓库
2. 创建功能分支
3. 编写测试用例
4. 提交 Pull Request

## 许可证

本功能遵循项目的开源许可证。
