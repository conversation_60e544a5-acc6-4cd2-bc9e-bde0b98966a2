// Code generated by `go generate`. DO NOT EDIT.
// source: server/internal/gen/hooks.go.tmpl
package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
)

// OnRegisterSessionHookFunc is a hook that will be called when a new session is registered.
type OnRegisterSessionHookFunc func(ctx context.Context, session ClientSession)

// OnUnregisterSessionHookFunc is a hook that will be called when a session is being unregistered.
type OnUnregisterSessionHookFunc func(ctx context.Context, session ClientSession)

// BeforeAnyHookFunc is a function that is called after the request is
// parsed but before the method is called.
type BeforeAnyHookFunc func(ctx context.Context, id any, method mcp.MCPMethod, message any)

// OnSuccessHookFunc is a hook that will be called after the request
// successfully generates a result, but before the result is sent to the client.
type OnSuccessHookFunc func(ctx context.Context, id any, method mcp.MCPMethod, message any, result any)

// OnErrorHookFunc is a hook that will be called when an error occurs,
// either during the request parsing or the method execution.
// 
// Example usage:
// ```
// hooks.AddOnError(func(ctx context.Context, id any, method mcp.MCPMethod, message any, err error) {
//   // Check for specific error types using errors.Is
//   if errors.Is(err, ErrUnsupported) {
//     // Handle capability not supported errors
//     log.Printf("Capability not supported: %v", err)
//   }
//
//   // Use errors.As to get specific error types
//   var parseErr = &UnparsableMessageError{}
//   if errors.As(err, &parseErr) {
//     // Access specific methods/fields of the error type
//     log.Printf("Failed to parse message for method %s: %v", 
//                parseErr.GetMethod(), parseErr.Unwrap())
//     // Access the raw message that failed to parse
//     rawMsg := parseErr.GetMessage()
//   }
//
//   // Check for specific resource/prompt/tool errors
//   switch {
//   case errors.Is(err, ErrResourceNotFound):
//     log.Printf("Resource not found: %v", err)
//   case errors.Is(err, ErrPromptNotFound):
//     log.Printf("Prompt not found: %v", err)
//   case errors.Is(err, ErrToolNotFound):
//     log.Printf("Tool not found: %v", err)
//   }
// })
type OnErrorHookFunc func(ctx context.Context, id any, method mcp.MCPMethod, message any, err error)

// OnRequestInitializationFunc is a function that called before handle diff request method
// Should any errors arise during func execution, the service will promptly return the corresponding error message.
type OnRequestInitializationFunc func(ctx context.Context, id any, message any) error


{{range .}}
type OnBefore{{.HookName}}Func func(ctx context.Context, id any, message *mcp.{{.ParamType}})
type OnAfter{{.HookName}}Func func(ctx context.Context, id any, message *mcp.{{.ParamType}}, result *mcp.{{.ResultType}})
{{end}}

type Hooks struct {
    OnRegisterSession   []OnRegisterSessionHookFunc
	OnUnregisterSession   []OnUnregisterSessionHookFunc
	OnBeforeAny      []BeforeAnyHookFunc
	OnSuccess        []OnSuccessHookFunc
	OnError          []OnErrorHookFunc
	OnRequestInitialization       []OnRequestInitializationFunc
{{- range .}}
	OnBefore{{.HookName}} []OnBefore{{.HookName}}Func
	OnAfter{{.HookName}}  []OnAfter{{.HookName}}Func
{{- end}}
}

func (c *Hooks) AddBeforeAny(hook BeforeAnyHookFunc) {
	c.OnBeforeAny = append(c.OnBeforeAny, hook)
}

func (c *Hooks) AddOnSuccess(hook OnSuccessHookFunc) {
	c.OnSuccess = append(c.OnSuccess, hook)
}

// AddOnError registers a hook function that will be called when an error occurs.
// The error parameter contains the actual error object, which can be interrogated
// using Go's error handling patterns like errors.Is and errors.As.
//
// Example:
// ```
// // Create a channel to receive errors for testing
// errChan := make(chan error, 1)
// 
// // Register hook to capture and inspect errors
// hooks := &Hooks{}
// hooks.AddOnError(func(ctx context.Context, id any, method mcp.MCPMethod, message any, err error) {
//     // For capability-related errors
//     if errors.Is(err, ErrUnsupported) {
//         // Handle capability not supported
//         errChan <- err
//         return
//     }
//
//     // For parsing errors
//     var parseErr = &UnparsableMessageError{}
//     if errors.As(err, &parseErr) {
//         // Handle unparsable message errors
//         fmt.Printf("Failed to parse %s request: %v\n", 
//                    parseErr.GetMethod(), parseErr.Unwrap())
//         errChan <- parseErr
//         return
//     }
//
//     // For resource/prompt/tool not found errors
//     if errors.Is(err, ErrResourceNotFound) ||
//        errors.Is(err, ErrPromptNotFound) ||
//        errors.Is(err, ErrToolNotFound) {
//         // Handle not found errors
//         errChan <- err
//         return
//     }
//
//     // For other errors
//     errChan <- err
// })
//
// server := NewMCPServer("test-server", "1.0.0", WithHooks(hooks))
// ```
func (c *Hooks) AddOnError(hook OnErrorHookFunc) {
	c.OnError = append(c.OnError, hook)
}

func (c *Hooks) beforeAny(ctx context.Context, id any, method mcp.MCPMethod, message any) {
	if c == nil {
		return
	}
	for _, hook := range c.OnBeforeAny {
		hook(ctx, id, method, message)
	}
}

func (c *Hooks) onSuccess(ctx context.Context, id any, method mcp.MCPMethod, message any, result any) {
	if c == nil {
		return
	}
	for _, hook := range c.OnSuccess {
		hook(ctx, id, method, message, result)
	}
}

// onError calls all registered error hooks with the error object.
// The err parameter contains the actual error that occurred, which implements
// the standard error interface and may be a wrapped error or custom error type.
//
// This allows consumer code to use Go's error handling patterns:
// - errors.Is(err, ErrUnsupported) to check for specific sentinel errors
// - errors.As(err, &customErr) to extract custom error types
//
// Common error types include:
// - ErrUnsupported: When a capability is not enabled
// - UnparsableMessageError: When request parsing fails
// - ErrResourceNotFound: When a resource is not found
// - ErrPromptNotFound: When a prompt is not found
// - ErrToolNotFound: When a tool is not found
func (c *Hooks) onError(ctx context.Context, id any, method mcp.MCPMethod, message any, err error) {
	if c == nil {
		return
	}
	for _, hook := range c.OnError {
		hook(ctx, id, method, message, err)
	}
}

func (c *Hooks) AddOnRegisterSession(hook OnRegisterSessionHookFunc) {
    c.OnRegisterSession = append(c.OnRegisterSession, hook)
}

func (c *Hooks) RegisterSession(ctx context.Context, session ClientSession) {
    if c == nil {
        return
    }
    for _, hook := range c.OnRegisterSession {
        hook(ctx, session)
    }
}

func (c *Hooks) AddOnUnregisterSession(hook OnUnregisterSessionHookFunc) {
    c.OnUnregisterSession = append(c.OnUnregisterSession, hook)
}

func (c *Hooks) UnregisterSession(ctx context.Context, session ClientSession) {
    if c == nil {
        return
    }
    for _, hook := range c.OnUnregisterSession {
        hook(ctx, session)
    }
}

func (c *Hooks) AddOnRequestInitialization(hook OnRequestInitializationFunc) {
	c.OnRequestInitialization = append(c.OnRequestInitialization, hook)
}

func (c *Hooks) onRequestInitialization(ctx context.Context, id any, message any) error {
	if c == nil {
		return nil
	}
	for _, hook := range c.OnRequestInitialization {
		err := hook(ctx, id, message)
		if err != nil {
			return err
		}
	}
	return nil
}

{{- range .}}
func (c *Hooks) AddBefore{{.HookName}}(hook OnBefore{{.HookName}}Func) {
	c.OnBefore{{.HookName}} = append(c.OnBefore{{.HookName}}, hook)
}

func (c *Hooks) AddAfter{{.HookName}}(hook OnAfter{{.HookName}}Func) {
	c.OnAfter{{.HookName}} = append(c.OnAfter{{.HookName}}, hook)
}

func (c *Hooks) before{{.HookName}}(ctx context.Context, id any, message *mcp.{{.ParamType}}) {
	c.beforeAny(ctx, id, mcp.{{.MethodName}}, message)
	if c == nil {
		return
	}
	for _, hook := range c.OnBefore{{.HookName}} {
		hook(ctx, id, message)
	}
}

func (c *Hooks) after{{.HookName}}(ctx context.Context, id any, message *mcp.{{.ParamType}}, result *mcp.{{.ResultType}}) {
	c.onSuccess(ctx, id, mcp.{{.MethodName}}, message, result)
	if c == nil {
		return
	}
	for _, hook := range c.OnAfter{{.HookName}} {
		hook(ctx, id, message, result)
	}
}
{{- end -}}
