---
name: Bug report
about: Report an issue or unexpected behavior
title: 'bug: '
labels: bug
assignees: ''
---

## Description

A clear and concise description of the bug, including what happened and what you expected to happen.

## Code Sample

```go
// Minimum code snippet to reproduce the issue
// Remove if not applicable
```

## Logs or Error Messages

```text
If applicable, include any error messages, stack traces, or logs. Remove if not applicable.
```

## Environment

 - Go version (see `go.mod`): [e.g. 1.23]
 - mcp-go version (see `go.mod`): [e.g. 0.27.0]
 - Any other relevant environment details (OS, architecture, etc.)

## Additional Context

Add any other context about the problem here.

## Possible Solution

If you have a suggestion for fixing the issue, please describe it here.
