---
layout: landing
---

import { HomePage } from 'vocs/components'

<HomePage.Root>
  <HomePage.Logo />
  <HomePage.Tagline>MCP-Go</HomePage.Tagline>
  <HomePage.Description>
    A Go implementation of the Model Context Protocol (MCP), enabling seamless integration between LLM applications and external data sources and tools. Build powerful MCP servers with minimal boilerplate and focus on creating great tools.
  </HomePage.Description>
  <HomePage.Buttons>
    <HomePage.Button href="/getting-started" variant="accent">Get started</HomePage.Button>
    <HomePage.Button href="https://github.com/mark3labs/mcp-go">GitHub</HomePage.Button>
  </HomePage.Buttons>
</HomePage.Root>