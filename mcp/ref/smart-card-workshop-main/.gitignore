# Python相关忽略
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# uv相关文件
.uv/
.uvroot

# 项目输出文件
output/
# static/
# *.png
*.pdf
*.html
!templates/*.html

# IDE相关
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 日志文件
*.log
logs/

# 本地配置
tools/.env

# 测试相关
.coverage
htmlcov/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover

# 临时文件
temp/
tmp/
.temp/
.tmp/

# Chrome WebDriver
chromedriver*
