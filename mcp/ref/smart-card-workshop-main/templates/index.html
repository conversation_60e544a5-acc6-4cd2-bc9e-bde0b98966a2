<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能卡片工坊</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-green: #2ecc71;
            --dark-green: #27ae60;
            --light-green: #d5f5e3;
            --dark-text: #2c3e50;
            --light-text: #ecf0f1;
            --accent-blue: #3498db;
            --warning-red: #e74c3c;
            --secondary-bg: #f8f9fa;
            --border-color: #ddd;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, sans-serif;
            color: var(--dark-text);
            background-color: var(--secondary-bg);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .app-header {
            background-color: var(--dark-green);
            color: var(--light-text);
            padding: 1.5rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(39, 174, 96, 0.2);
            border-radius: 0 0 10px 10px;
        }

        .app-title {
            font-size: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .app-subtitle {
            font-size: 1rem;
            opacity: 0.85;
        }

        .preview-container {
            border: none;
            padding: 15px;
            margin-top: 20px;
            min-height: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        #htmlPreview {
            width: 100%;
            height: 500px;
            border: none;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.15);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading .spinner-border {
            color: var(--primary-green);
            width: 3rem;
            height: 3rem;
        }
        
        .btn-toolbar {
            margin-top: 15px;
        }

        #llmResponseContainer {
            display: none;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            padding: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
            white-space: pre-wrap;
            border-radius: 8px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--primary-green);
            color: white;
            font-weight: 500;
            border: none;
            padding: 0.75rem 1.25rem;
        }

        .card-header h5 {
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-control, .form-select {
            border-radius: 6px;
            padding: 0.625rem 0.75rem;
            border-color: #e0e0e0;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 0.25rem rgba(46, 204, 113, 0.25);
        }

        .nav-tabs {
            border-bottom: 2px solid #e0e0e0;
        }

        .nav-tabs .nav-link {
            border: none;
            color: var(--dark-text);
            position: relative;
            padding: 0.75rem 1rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-green);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-green);
            background-color: transparent;
            border: none;
        }

        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-green);
        }

        .btn-primary {
            background-color: var(--primary-green);
            border-color: var(--primary-green);
            transition: all 0.3s ease;
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--dark-green);
            border-color: var(--dark-green);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.25);
        }

        .btn-secondary {
            background-color: var(--accent-blue);
            border-color: var(--accent-blue);
        }

        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: var(--primary-green);
            border-color: var(--primary-green);
        }

        .btn-info {
            background-color: #3498db;
            border-color: #3498db;
            color: white;
        }

        .btn-info:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            color: white;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease;
        }
    </style>
    <!-- 引入html2canvas库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" 
            integrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoeqMV/TJlSKda6FXzoEyYGjTe+vXA==" 
            crossorigin="anonymous" 
            referrerpolicy="no-referrer"></script>
    <!-- 引入我们自己的html2image.js -->
    <script src="/static/html2image.js"></script>
</head>

<body>
    <header class="app-header">
        <div class="container">
            <h1 class="app-title text-center">
                <i class="fas fa-id-card"></i>
                智能卡片工坊
            </h1>
            <p class="app-subtitle text-center mb-0">AI内容转卡片专家</p>
        </div>
    </header>

    <div class="container mt-5">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-magic me-2"></i>生成HTML</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="genTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="prompt-tab" data-bs-toggle="tab" data-bs-target="#prompt"
                                    type="button" role="tab"><i class="fas fa-keyboard me-2"></i>需求生成</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary"
                                    type="button" role="tab"><i class="fas fa-file-alt me-2"></i>智能总结</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="web-summary-tab" data-bs-toggle="tab" data-bs-target="#web-summary"
                                    type="button" role="tab"><i class="fas fa-globe me-2"></i>总结网页</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="paste-tab" data-bs-toggle="tab" data-bs-target="#paste"
                                    type="button" role="tab"><i class="fas fa-paste me-2"></i>粘贴HTML</button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="genTabContent">
                            <div class="tab-pane fade show active" id="prompt" role="tabpanel">
                                <div class="mb-3">
                                    <label for="promptInput" class="form-label">输入您的需求:</label>
                                    <textarea class="form-control" id="promptInput" rows="5"
                                        placeholder="例如: 创建一个包含标题、图片和联系表单的响应式网页"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="modelSelect" class="form-label">选择模型:</label>
                                    <select class="form-select" id="modelSelect">
                                        <option value="deepseek-v3-250324">deepseek-v3-250324</option>
                                        <option value="deepseek-r1-250120">deepseek-r1-250120</option>
                                    </select>
                                </div>
                                <button id="promptGenerateBtn" class="btn btn-primary"><i class="fas fa-wand-magic-sparkles me-2"></i>根据需求生成</button>
                            </div>

                            <div class="tab-pane fade" id="summary" role="tabpanel">
                                <div class="mb-3">
                                    <label for="contentInput" class="form-label">输入内容:</label>
                                    <textarea class="form-control" id="contentInput" rows="5"
                                        placeholder="请输入您想要智能总结的内容..."></textarea>
                                </div>
                                <button id="summarizeBtn" class="btn btn-secondary mb-3"><i class="fas fa-compress-alt me-2"></i>智能总结</button>
                                
                                <div id="summaryContainer" class="mb-3" style="display: none;">
                                    <label for="summaryResult" class="form-label">总结结果:</label>
                                    <textarea class="form-control" id="summaryResult" rows="4"></textarea>
                                    <small class="form-text text-muted">您可以编辑上方的总结内容</small>
                                </div>
                                
                                <button id="summaryGenerateBtn" class="btn btn-primary" style="display: none;"><i class="fas fa-wand-magic-sparkles me-2"></i>根据需求生成</button>
                            </div>

                            <div class="tab-pane fade" id="web-summary" role="tabpanel">
                                <div class="mb-3">
                                    <label for="urlInput" class="form-label">输入网页URL:</label>
                                    <input type="url" class="form-control" id="urlInput" 
                                        placeholder="https://example.com">
                                </div>
                                <button id="fetchWebBtn" class="btn btn-secondary mb-3"><i class="fas fa-download me-2"></i>获取网页内容</button>
                                
                                <div id="webContentContainer" class="mb-3" style="display: none;">
                                    <label class="form-label">网页内容:</label>
                                    <div class="border p-2 mb-3 bg-light rounded" style="max-height: 200px; overflow-y: auto;" id="webContentPreview"></div>
                                    <button id="webSummarizeBtn" class="btn btn-secondary mb-3"><i class="fas fa-compress-alt me-2"></i>智能总结</button>
                                </div>
                                
                                <div id="webSummaryContainer" class="mb-3" style="display: none;">
                                    <label for="webSummaryResult" class="form-label">总结结果:</label>
                                    <textarea class="form-control" id="webSummaryResult" rows="4"></textarea>
                                    <small class="form-text text-muted">您可以编辑上方的总结内容</small>
                                </div>
                                
                                <button id="webSummaryGenerateBtn" class="btn btn-primary" style="display: none;"><i class="fas fa-wand-magic-sparkles me-2"></i>根据需求生成</button>
                            </div>

                            <div class="tab-pane fade" id="paste" role="tabpanel">
                                <div class="mb-3">
                                    <label for="htmlInput" class="form-label">粘贴您的HTML代码:</label>
                                    <textarea class="form-control" id="htmlInput" rows="10"
                                        placeholder="在此处粘贴完整的HTML代码..."></textarea>
                                </div>
                                <button id="pasteGenerateBtn" class="btn btn-primary"><i class="fas fa-code me-2"></i>使用粘贴的HTML</button>
                            </div>
                        </div>

                        <div id="loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">正在生成中，请稍候...</p>
                        </div>

                        <!-- LLM原始响应容器 -->
                        <div id="llmResponseContainer" class="mt-4 fade-in">
                            <h6><i class="fas fa-robot me-2"></i>LLM原始响应:</h6>
                            <div id="llmResponseContent" class="p-3"></div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4" id="downloadCard" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-download me-2"></i>下载选项</h5>
                    </div>
                    <div class="card-body">
                        <p>预览满意后，您可以选择以下格式下载：</p>
                        <div class="btn-toolbar">
                            <button id="downloadHtmlBtn" class="btn btn-success me-2"><i class="fas fa-file-code me-2"></i>下载HTML</button>
                            <button id="downloadImageBtn" class="btn btn-info"><i class="fas fa-file-image me-2"></i>下载卡片图片</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>卡片预览</h5>
                    </div>
                    <div class="card-body preview-container p-0">
                        <iframe id="htmlPreview" class="fade-in"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 存储当前生成的文件信息
        let currentFileId = null;
        let currentUrls = {
            html: null,
            image: null
        };

        // 显示生成的HTML预览
        async function showPreview(htmlUrl) {
            try {
                // 获取HTML内容用于预览
                const response = await fetch(htmlUrl);
                const htmlContent = await response.text();
                
                // 在iframe中显示预览
                document.getElementById('htmlPreview').srcdoc = htmlContent;
                
                // 显示下载选项
                document.getElementById('downloadCard').style.display = 'block';
            } catch (error) {
                console.error('Error loading preview:', error);
                alert('加载预览失败: ' + error.message);
            }
        }

        // 根据需求生成HTML
        document.getElementById('promptGenerateBtn').addEventListener('click', async function () {
            const prompt = document.getElementById('promptInput').value.trim();
            const model = document.getElementById('modelSelect').value;
            
            if (!prompt) {
                alert('请输入您的需求');
                return;
            }

            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // 隐藏之前的LLM响应
            document.getElementById('llmResponseContainer').style.display = 'none';

            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mode: 'prompt',
                        prompt: prompt,
                        model: model,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '服务器错误');
                }

                const data = await response.json();
                
                // 保存返回的信息
                currentFileId = data.file_id;
                currentUrls = {
                    html: data.html_path,
                    image: data.image_path
                };
                
                // 显示预览
                await showPreview(data.html_path);
                
                // 显示LLM原始响应（如果有）
                if (data.raw_llm_response) {
                    document.getElementById('llmResponseContent').textContent = data.raw_llm_response;
                    document.getElementById('llmResponseContainer').style.display = 'block';
                }
                
            } catch (error) {
                console.error('Error:', error);
                alert('请求失败: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        });

        // 使用粘贴的HTML
        document.getElementById('pasteGenerateBtn').addEventListener('click', async function () {
            const htmlInput = document.getElementById('htmlInput').value.trim();
            if (!htmlInput) {
                alert('请粘贴HTML代码');
                return;
            }

            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // 隐藏之前的LLM响应
            document.getElementById('llmResponseContainer').style.display = 'none';

            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mode: 'paste',
                        html_input: htmlInput
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '服务器错误');
                }

                const data = await response.json();
                
                // 保存返回的信息
                currentFileId = data.file_id;
                currentUrls = {
                    html: data.html_path,
                    image: data.image_path
                };
                
                // 显示预览
                await showPreview(data.html_path);
                
            } catch (error) {
                console.error('Error:', error);
                alert('请求失败: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        });

        // 智能总结功能
        document.getElementById('summarizeBtn').addEventListener('click', async function () {
            const content = document.getElementById('contentInput').value.trim();
            
            if (!content) {
                alert('请输入需要总结的内容');
                return;
            }

            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                const response = await fetch('/api/summarize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '服务器错误');
                }

                const data = await response.json();
                
                // 显示总结结果
                document.getElementById('summaryResult').value = data.summary;
                document.getElementById('summaryContainer').style.display = 'block';
                document.getElementById('summaryGenerateBtn').style.display = 'block';
                
            } catch (error) {
                console.error('Error:', error);
                alert('总结请求失败: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        });

        // 从总结结果生成卡片
        document.getElementById('summaryGenerateBtn').addEventListener('click', function() {
            const summary = document.getElementById('summaryResult').value.trim();
            
            if (summary) {
                // 将总结内容复制到需求输入框
                document.getElementById('promptInput').value = summary;
                
                // 切换到需求生成标签页
                document.querySelector('#prompt-tab').click();
                
                // 可选：自动点击生成按钮
                // document.getElementById('promptGenerateBtn').click();
            } else {
                alert('请先生成总结内容');
            }
        });

        // 获取网页内容
        document.getElementById('fetchWebBtn').addEventListener('click', async function() {
            const url = document.getElementById('urlInput').value.trim();
            
            if (!url) {
                alert('请输入有效的网页URL');
                return;
            }

            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                const response = await fetch('/api/fetch-web', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '服务器错误');
                }

                const data = await response.json();
                
                // 显示网页内容预览
                document.getElementById('webContentPreview').textContent = data.content.substring(0, 1000) + 
                    (data.content.length > 1000 ? '...' : '');
                document.getElementById('webContentContainer').style.display = 'block';
                
                // 存储完整内容供后续使用
                document.getElementById('webContentPreview').dataset.fullContent = data.content;
                
            } catch (error) {
                console.error('Error:', error);
                alert('获取网页内容失败: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        });

        // 总结网页内容
        document.getElementById('webSummarizeBtn').addEventListener('click', async function() {
            const content = document.getElementById('webContentPreview').dataset.fullContent;
            
            if (!content) {
                alert('请先获取网页内容');
                return;
            }

            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                const response = await fetch('/api/summarize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '服务器错误');
                }

                const data = await response.json();
                
                // 显示总结结果
                document.getElementById('webSummaryResult').value = data.summary;
                document.getElementById('webSummaryContainer').style.display = 'block';
                document.getElementById('webSummaryGenerateBtn').style.display = 'block';
                
            } catch (error) {
                console.error('Error:', error);
                alert('总结请求失败: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        });

        // 从网页总结结果生成卡片
        document.getElementById('webSummaryGenerateBtn').addEventListener('click', function() {
            const summary = document.getElementById('webSummaryResult').value.trim();
            
            if (summary) {
                // 将总结内容复制到需求输入框
                document.getElementById('promptInput').value = summary;
                
                // 切换到需求生成标签页
                document.querySelector('#prompt-tab').click();
                
                // 可选：自动点击生成按钮
                // document.getElementById('promptGenerateBtn').click();
            } else {
                alert('请先生成总结内容');
            }
        });

        // 下载HTML按钮
        document.getElementById('downloadHtmlBtn').addEventListener('click', function () {
            if (currentUrls.html) {
                window.open(currentUrls.html, '_blank');
            }
        });

        // 下载图片按钮
        document.getElementById('downloadImageBtn').addEventListener('click', function () {
            // 获取预览iframe中的卡片内容
            const iframe = document.getElementById('htmlPreview');
            if (!iframe || !iframe.contentWindow) {
                alert('卡片预览不可用，无法导出图片');
                return;
            }

            // 显示加载中提示
            const loading = document.getElementById('loading');
            if (loading) loading.style.display = 'block';

            try {
                // 检查html2canvas是否已加载
                if (typeof html2canvas === 'undefined') {
                    const script = document.createElement('script');
                    script.src = "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js";
                    script.onload = function() {
                        // 库加载后执行导出
                        exportCardAsImage(iframe);
                    };
                    script.onerror = function() {
                        if (loading) loading.style.display = 'none';
                        alert('无法加载图片导出工具，请检查网络连接');
                    };
                    document.head.appendChild(script);
                } else {
                    // 如果已加载，直接导出
                    exportCardAsImage(iframe);
                }
            } catch (error) {
                console.error('导出图片时出错:', error);
                if (loading) loading.style.display = 'none';
                alert('导出图片失败: ' + error.message);
            }
        });

        // 导出卡片为图片的函数
        function exportCardAsImage(iframe) {
            // 获取iframe中的卡片元素（通常是第一个主要容器）
            const cardElement = iframe.contentWindow.document.querySelector('.card') || 
                               iframe.contentWindow.document.body;
            
            // 使用html2canvas捕获元素
            html2canvas(cardElement, {
                allowTaint: true,
                useCORS: true,
                scale: 2, // 提高分辨率
                backgroundColor: null, // 保持背景透明
                logging: false
            }).then(canvas => {
                // 转换为数据URL
                const imageData = canvas.toDataURL('image/png');
                
                // 创建下载链接
                const link = document.createElement('a');
                link.download = '智能卡片_' + new Date().toISOString().slice(0, 10) + '.png';
                link.href = imageData;
                
                // 模拟点击下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 隐藏加载提示
                const loading = document.getElementById('loading');
                if (loading) loading.style.display = 'none';
            }).catch(error => {
                console.error('导出图片时出错:', error);
                // 隐藏加载提示
                const loading = document.getElementById('loading');
                if (loading) loading.style.display = 'none';
                alert('导出图片失败: ' + error.message);
            });
        }
    </script>
</body>

</html>