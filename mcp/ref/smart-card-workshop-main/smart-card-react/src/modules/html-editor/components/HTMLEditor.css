/* HTML编辑器基础样式 */
.html-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  border-radius: 8px;
  overflow: hidden;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
  gap: 8px;
}

.toolbar-group {
  display: flex;
  gap: 4px;
  margin-right: 12px;
}

.toolbar-button {
  color: #666;
  background: transparent;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background-color: #f0f0f0;
}

.toolbar-button.active {
  color: #fff;
  background-color: #e0e0e0;
}

.color-picker {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 编辑区域样式 */
.editor-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 300px;
  margin-bottom: 16px;
}

.editable-area {
  min-height: 300px;
  padding: 16px;
  outline: none;
}

.preview-area {
  min-height: 300px;
  padding: 16px;
  background-color: #fafafa;
}

/* HTML代码预览区域样式 */
.html-preview {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.html-preview h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.html-code {
  width: 100%;
  min-height: 150px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
}

/* 代码块样式 */
.editable-area code, .preview-area code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

/* 引用块样式 */
.editable-area blockquote, .preview-area blockquote {
  border-left: 2px solid #ddd;
  margin-left: 0;
  margin-right: 0;
  padding-left: 10px;
  color: #666;
  font-style: italic;
}

/* 图片样式 */
.editable-area img, .preview-area img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
  }
  
  .toolbar-group {
    margin-bottom: 8px;
  }
} 