#!/bin/bash

# ChatPPT MCP Server Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your configuration before running again."
            exit 1
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
}

# Check if API_PPT_KEY is set
check_api_key() {
    source .env
    if [ -z "$API_PPT_KEY" ] || [ "$API_PPT_KEY" = "your_api_key_here" ]; then
        print_error "API_PPT_KEY is not set in .env file. Please set your API key from www.yoo-ai.com"
        exit 1
    fi
}

# Build and start services
deploy_production() {
    print_status "Deploying ChatPPT MCP Server in production mode..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose down || true
    
    # Build and start services
    print_status "Building and starting services..."
    docker-compose up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "ChatPPT MCP Server deployed successfully!"
        print_status "Services are running:"
        docker-compose ps
        
        # Show logs
        print_status "Recent logs:"
        docker-compose logs --tail=20
        
        print_success "Deployment complete!"
        print_status "SSE endpoint: http://localhost:${MCP_PORT:-8000}/sse"
        print_status "Messages endpoint: http://localhost:${MCP_PORT:-8000}/messages"
    else
        print_error "Deployment failed. Check logs:"
        docker-compose logs
        exit 1
    fi
}

# Deploy development environment
deploy_development() {
    print_status "Deploying ChatPPT MCP Server in development mode..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose -f docker-compose.dev.yml down || true
    
    # Build and start services
    print_status "Building and starting development services..."
    docker-compose -f docker-compose.dev.yml up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 5
    
    # Check if services are running
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        print_success "ChatPPT MCP Server (development) deployed successfully!"
        print_status "Services are running:"
        docker-compose -f docker-compose.dev.yml ps
        
        print_success "Development deployment complete!"
        print_status "SSE endpoint: http://localhost:${MCP_PORT:-8000}/sse"
        print_status "Messages endpoint: http://localhost:${MCP_PORT:-8000}/messages"
        print_status "Logs: docker-compose -f docker-compose.dev.yml logs -f"
    else
        print_error "Development deployment failed. Check logs:"
        docker-compose -f docker-compose.dev.yml logs
        exit 1
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [production|development|stop|logs|status]"
    echo ""
    echo "Commands:"
    echo "  production   - Deploy in production mode"
    echo "  development  - Deploy in development mode"
    echo "  stop         - Stop all services"
    echo "  logs         - Show logs"
    echo "  status       - Show service status"
    echo ""
}

# Main script
case "$1" in
    "production")
        check_env_file
        check_api_key
        deploy_production
        ;;
    "development")
        check_env_file
        check_api_key
        deploy_development
        ;;
    "stop")
        print_status "Stopping all services..."
        docker-compose down || true
        docker-compose -f docker-compose.dev.yml down || true
        print_success "All services stopped."
        ;;
    "logs")
        if docker-compose ps | grep -q "Up"; then
            docker-compose logs -f
        elif docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
            docker-compose -f docker-compose.dev.yml logs -f
        else
            print_warning "No services are running."
        fi
        ;;
    "status")
        print_status "Production services:"
        docker-compose ps || print_warning "No production services running."
        echo ""
        print_status "Development services:"
        docker-compose -f docker-compose.dev.yml ps || print_warning "No development services running."
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
