package auth

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"chatppt-mcp-go/internal/config"
)

// DStaffAuthContext holds authentication context for DStaff requests
type DStaffAuthContext struct {
	Token  string
	TaskID string
}

// FileUploadResponse represents the response format for dstaff file uploads
type FileUploadResponse struct {
	WorkType    string       `json:"work_type"`
	Compression bool         `json:"compression"`
	Status      string       `json:"status"`
	Message     string       `json:"message"`
	Attachments []Attachment `json:"attachments"`
}

// Attachment represents a file attachment in the response
type Attachment struct {
	Path          string `json:"path"`
	Filename      string `json:"filename"`
	Type          string `json:"type"`
	ContentType   string `json:"content_type"`
	ContentLength int64  `json:"content_length"`
}

// ValidateTokenWithDStaff validates token using dstaff official auth service
func ValidateTokenWithDStaff(config *config.DStaffConfig, token string) bool {
	log.Printf("=== DStaff Token Validation ===")
	log.Printf("Config enabled: %v, UseOfficialAuth: %v", config.Enabled, config.UseOfficialAuth)
	log.Printf("Token to validate: '%s' (length: %d)", token, len(token))
	log.Printf("Auth service URL: %s", config.AuthServiceURL)

	if !config.Enabled || !config.UseOfficialAuth {
		log.Printf("DStaff validation skipped: config not enabled or not using official auth")
		return false
	}

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", config.AuthServiceURL, nil)
	if err != nil {
		log.Printf("Failed to create auth request to %s: %v", config.AuthServiceURL, err)
		return false
	}

	authHeaderValue := "Bearer " + token
	req.Header.Set("Authorization", authHeaderValue)
	log.Printf("Sending validation request with Authorization header: '%s'", authHeaderValue)

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to validate token with %s: %v", config.AuthServiceURL, err)
		return false
	}
	defer resp.Body.Close()

	// Read response body for logging
	respBody, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("Failed to read validation response body: %v", readErr)
	} else {
		log.Printf("Validation response (HTTP %d): %s", resp.StatusCode, string(respBody))
	}

	if resp.StatusCode == 200 {
		var result map[string]interface{}
		if err := json.Unmarshal(respBody, &result); err == nil {
			log.Printf("Parsed validation response: %+v", result)
			if data, ok := result["data"].(bool); ok && data {
				log.Printf("Token validation successful for token: '%s'", token)
				return true
			} else {
				log.Printf("Token validation failed: data field is %v (type: %T)", result["data"], result["data"])
			}
		} else {
			log.Printf("Failed to parse validation response JSON: %v", err)
		}
	}

	log.Printf("Token validation failed for token '%s': HTTP %d", token, resp.StatusCode)
	log.Printf("===============================")
	return false
}

// ExtractDStaffAuthContext extracts authentication context from HTTP request
func ExtractDStaffAuthContext(r *http.Request) *DStaffAuthContext {
	// Extract token from Authorization header
	var token string
	authHeader := r.Header.Get("Authorization")
	if strings.HasPrefix(authHeader, "Bearer ") {
		token = strings.TrimPrefix(authHeader, "Bearer ")
	}

	// Try to extract task_id from various sources
	var taskID string

	// First try query parameters
	taskID = r.URL.Query().Get("task_id")

	// Then try form data if it's a POST request
	if taskID == "" && r.Method == "POST" {
		if err := r.ParseForm(); err == nil {
			taskID = r.FormValue("task_id")
		}
	}

	// Fallback to environment variable
	if taskID == "" {
		taskID = os.Getenv("DSTAFF_TASK_ID")
	}

	return &DStaffAuthContext{
		Token:  token,
		TaskID: taskID,
	}
}

// ExtractTokenFromContext extracts DStaff token from Go context (passed from HTTP Authorization header)
func ExtractTokenFromContext(ctx context.Context) string {
	if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
		if tokenStr, ok := tokenValue.(string); ok {
			return tokenStr
		}
	}
	return ""
}

// ExtractTaskIDFromContext extracts task ID from various sources in context and request
func ExtractTaskIDFromContext(ctx context.Context, r *http.Request) string {
	// Try to get from query parameters first
	if r != nil {
		if taskID := r.URL.Query().Get("task_id"); taskID != "" {
			return taskID
		}
	}

	// Try to get from context
	if taskIDValue := ctx.Value("dstaff_task_id"); taskIDValue != nil {
		if taskIDStr, ok := taskIDValue.(string); ok {
			return taskIDStr
		}
	}

	// Fallback to environment variable
	return os.Getenv("DSTAFF_TASK_ID")
}

// UploadFileToDStaff uploads a file to dstaff platform
func UploadFileToDStaff(config *config.DStaffConfig, authCtx *DStaffAuthContext, filePath string, targetPath string) (*FileUploadResponse, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// Read file content
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Encode file content to base64
	fileBase64 := base64.StdEncoding.EncodeToString(fileContent)

	// Prepare request payload
	payload := map[string]interface{}{
		"file":     fileBase64,
		"taskId":   authCtx.TaskID,
		"filePath": targetPath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileUploadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Determine content type
	contentType := "application/octet-stream"
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".py":
		contentType = "text/x-python"
	case ".txt":
		contentType = "text/plain"
	case ".sh":
		contentType = "text/x-shellscript"
	case ".json":
		contentType = "application/json"
	case ".html":
		contentType = "text/html"
	case ".pptx":
		contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
	case ".mp4":
		contentType = "video/mp4"
	case ".zip":
		contentType = "application/zip"
	}

	// Create successful response
	response := &FileUploadResponse{
		WorkType:    "mcp_tool",
		Compression: false,
		Status:      "success",
		Message:     fmt.Sprintf("文件上传成功！上传的文件路径：%s", targetPath),
		Attachments: []Attachment{
			{
				Path:          targetPath,
				Filename:      filepath.Base(targetPath),
				Type:          "file",
				ContentType:   contentType,
				ContentLength: int64(len(fileContent)),
			},
		},
	}

	log.Printf("File uploaded successfully to dstaff: %s", targetPath)
	return response, nil
}

// DownloadFileFromDStaff downloads a file from dstaff platform
func DownloadFileFromDStaff(config *config.DStaffConfig, authCtx *DStaffAuthContext, filePath string) (string, error) {
	if !config.Enabled {
		return "", fmt.Errorf("dstaff integration not enabled")
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   authCtx.TaskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	log.Printf("Downloading file from DStaff: taskId=%s, filePath=%s", authCtx.TaskID, filePath)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return "", fmt.Errorf("download failed: %v", errorResp)
		}
		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	filename := filepath.Base(filePath)
	if filename == "." || filename == "/" {
		filename = "downloaded_file"
	}

	// Generate unique filename to avoid conflicts
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("%s_%s_%s", authCtx.TaskID, timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("File downloaded successfully from DStaff: %s -> %s (size: %d bytes)", filePath, localFilePath, len(fileContent))
	return localFilePath, nil
}


