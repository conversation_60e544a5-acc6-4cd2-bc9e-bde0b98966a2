package tools

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"chatppt-mcp-go/internal/auth"
	"chatppt-mcp-go/internal/binding"
	"chatppt-mcp-go/internal/config"
	"github.com/mark3labs/mcp-go/mcp"
)

// HTTPClient interface for making HTTP requests (for testing)
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// ToolHandler handles tool execution
type ToolHandler struct {
	client         HTTPClient
	apiBase        string
	dstaffConfig   *config.DStaffConfig
	bindingManager *binding.TaskBindingManager
}

// NewToolHandler creates a new tool handler
func NewToolHandler(apiBase string, dstaffConfig *config.DStaffConfig, bindingManager *binding.TaskBindingManager) *ToolHandler {
	return &ToolHandler{
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
		apiBase:        apiBase,
		dstaffConfig:   dstaffConfig,
		bindingManager: bindingManager,
	}
}

// HandleToolCall handles all tool calls with detailed logging like the reference implementation
func (h *ToolHandler) HandleToolCall(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	toolName := request.Params.Name
	arguments := request.Params.Arguments

	// Log detailed request information (similar to ref implementation)
	log.Printf("🔧 Tool call received: %s", toolName)
	log.Printf("📝 Arguments: %+v", arguments)

	// Output detailed request body information for debugging
	requestJSON, _ := json.MarshalIndent(request, "", "  ")
	log.Printf("=== MCP Tool Call: %s ===", toolName)
	log.Printf("Request Body: %s", string(requestJSON))
	log.Printf("=======================================")

	// Convert arguments to map[string]interface{}
	argsMap, ok := arguments.(map[string]interface{})
	if !ok {
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Invalid arguments type: expected map[string]interface{}, got %T", arguments),
				},
			},
			IsError: true,
		}

		// Log error response
		resultJSON, _ := json.MarshalIndent(errorResult, "", "  ")
		log.Printf("=== MCP Tool Error Response: %s ===", toolName)
		log.Printf("Error Response Body: %s", string(resultJSON))
		log.Printf("=====================================")

		return errorResult, nil
	}

	// Extract DStaff authentication context if available
	var dstaffAuth *auth.DStaffAuthContext
	if token := auth.ExtractTokenFromContext(ctx); token != "" {
		// Extract task_id from request arguments
		taskID := h.extractTaskIDFromRequest(argsMap)
		dstaffAuth = &auth.DStaffAuthContext{
			Token:  token,
			TaskID: taskID,
		}
		log.Printf("🔐 DStaff auth context: task_id=%s, token_length=%d", taskID, len(token))
	}

	// Route to appropriate handler based on tool name
	var result *mcp.CallToolResult
	var err error

	switch toolName {
	case "query_ppt":
		result, err = h.handleQueryPPT(ctx, argsMap)
	case "build_ppt":
		result, err = h.handleBuildPPT(ctx, argsMap)
	case "text_build_ppt":
		result, err = h.handleTextBuildPPT(ctx, argsMap)
	case "build_ppt_by_file":
		result, err = h.handleBuildPPTByFile(ctx, argsMap)
	case "build_thesis_ppt":
		result, err = h.handleBuildThesisPPT(ctx, argsMap)
	case "download_ppt":
		result, err = h.handleDownloadPPTWithDStaff(ctx, argsMap, dstaffAuth)
	case "editor_ppt":
		result, err = h.handleEditorPPT(ctx, argsMap)
	case "replace_ppt":
		result, err = h.handleReplacePPT(ctx, argsMap)
	case "set_font_ppt":
		result, err = h.handleSetFontPPT(ctx, argsMap)
	case "set_anim_ppt":
		result, err = h.handleSetAnimPPT(ctx, argsMap)
	case "ppt_create_note":
		result, err = h.handlePPTCreateNote(ctx, argsMap)
	case "ppt_add_slides":
		result, err = h.handlePPTAddSlides(ctx, argsMap)
	case "ppt_create_outline_text":
		result, err = h.handlePPTCreateOutlineText(ctx, argsMap)
	case "ppt_create_template_cover_image":
		result, err = h.handlePPTCreateTemplateCoverImage(ctx, argsMap)
	case "ppt_replace_user_select_template":
		result, err = h.handlePPTReplaceUserSelectTemplate(ctx, argsMap)
	default:
		log.Printf("❌ Unknown tool requested: %s", toolName)
		result = &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Unknown tool: %s", toolName),
				},
			},
			IsError: true,
		}
	}

	// Handle errors by converting them to error results
	if err != nil {
		log.Printf("❌ Tool execution error for %s: %v", toolName, err)
		result = &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Tool execution failed: %v", err),
				},
			},
			IsError: true,
		}
	}

	// Log the response for debugging (similar to ref implementation)
	if result != nil {
		resultJSON, _ := json.MarshalIndent(result, "", "  ")
		if result.IsError {
			log.Printf("=== MCP Tool Error Response: %s ===", toolName)
		} else {
			log.Printf("=== MCP Tool Response: %s ===", toolName)
		}
		log.Printf("Response Body: %s", string(resultJSON))
		log.Printf("=====================================")
	}

	return result, nil
}

// extractTaskIDFromRequest extracts task_id from request arguments (similar to ref implementation)
func (h *ToolHandler) extractTaskIDFromRequest(argsMap map[string]interface{}) string {
	// Try to extract from Context parameter
	if contextParam, ok := argsMap["Context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract from context parameter (lowercase)
	if contextParam, ok := argsMap["context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract directly from arguments
	if taskID, ok := argsMap["task_id"].(string); ok {
		return taskID
	}

	return ""
}

// handleQueryPPT handles the query_ppt tool
func (h *ToolHandler) handleQueryPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("🔍 Executing 'query_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	params := url.Values{}
	params.Set("id", pptID)

	return h.makeGetRequest(ctx, "/mcp/ppt/ppt-result", params, apiKey)
}

// handleBuildPPT handles the build_ppt tool
func (h *ToolHandler) handleBuildPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	theme, ok := arguments["theme"].(string)
	if !ok || theme == "" {
		return nil, fmt.Errorf("theme is required and must be a string")
	}

	log.Printf("🏗️ Executing 'build_ppt' tool with theme: %s", theme)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Prepare form data
	data := url.Values{}
	data.Set("text", theme)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleTextBuildPPT handles the text_build_ppt tool
func (h *ToolHandler) handleTextBuildPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	text, ok := arguments["text"].(string)
	if !ok || text == "" {
		return nil, fmt.Errorf("text is required and must be a string")
	}

	log.Printf("📄 Executing 'text_build_ppt' tool with text length: %d characters", len(text))

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Prepare form data
	data := url.Values{}
	data.Set("text", text)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// makeGetRequest is a helper function for making GET requests
func (h *ToolHandler) makeGetRequest(ctx context.Context, endpoint string, params url.Values, apiKey string) (*mcp.CallToolResult, error) {
	reqURL := h.apiBase + endpoint
	if len(params) > 0 {
		reqURL += "?" + params.Encode()
	}

	log.Printf("🌐 Making GET request to: %s", reqURL)

	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", auth.CreateAuthHeader(apiKey))
	req.Header.Set("User-Agent", "ChatPPT-MCP-Go/1.0")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("📊 Response status: %d", resp.StatusCode)
	log.Printf("📝 Response body: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("✅ API request completed successfully")
	return mcp.NewToolResultText(string(body)), nil
}

// makePostRequest is a helper function for making POST requests
func (h *ToolHandler) makePostRequest(ctx context.Context, endpoint string, data url.Values, apiKey string) (*mcp.CallToolResult, error) {
	reqURL := h.apiBase + endpoint
	log.Printf("🌐 Making API request to: %s", reqURL)

	req, err := http.NewRequestWithContext(ctx, "POST", reqURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Authorization", auth.CreateAuthHeader(apiKey))

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("📡 API response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("✅ API request completed successfully")
	return mcp.NewToolResultText(string(body)), nil
}

// handleBuildPPTByFile handles the build_ppt_by_file tool
func (h *ToolHandler) handleBuildPPTByFile(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	fileURL, ok := arguments["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url is required and must be a string")
	}

	log.Printf("📁 Executing 'build_ppt_by_file' tool with file URL: %s", fileURL)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("file_url", fileURL)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create-file", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleBuildThesisPPT handles the build_thesis_ppt tool
func (h *ToolHandler) handleBuildThesisPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	fileURL, ok := arguments["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url is required and must be a string")
	}

	log.Printf("🎓 Executing 'build_thesis_ppt' tool with file URL: %s", fileURL)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("file_key", fileURL)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create-thesis", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleDownloadPPT handles the download_ppt tool
func (h *ToolHandler) handleDownloadPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("⬇️ Executing 'download_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	params := url.Values{}
	params.Set("id", pptID)

	// Get download URL from API
	result, err := h.makeGetRequest(ctx, "/mcp/ppt/ppt-download", params, apiKey)
	if err != nil {
		return result, err
	}

	// If DStaff is enabled, try to upload the file to DStaff platform
	if h.dstaffConfig.Enabled {
		log.Printf("🔗 DStaff enabled, attempting to upload PPT file to DStaff platform")

		// Get token from context and task_id from arguments
		token := auth.ExtractTokenFromContext(ctx)
		taskID := h.extractTaskIDFromRequest(arguments)

		if token != "" && taskID != "" {
			// Try to extract download URL from the result
			authCtx := &auth.DStaffAuthContext{
				Token:  token,
				TaskID: taskID,
			}
			if err := h.uploadPPTToDStaff(ctx, pptID, result, authCtx); err != nil {
				log.Printf("⚠️ Failed to upload PPT to DStaff: %v", err)
				// Don't fail the entire operation, just log the error
			}
		} else {
			log.Printf("⚠️ DStaff auth context not available (token: %s, task_id: %s), skipping upload",
				func() string { if token != "" { return "present" } else { return "missing" } }(),
				func() string { if taskID != "" { return "present" } else { return "missing" } }())
		}
	}

	return result, nil
}

// handleDownloadPPTWithDStaff handles the download_ppt tool with DStaff integration
func (h *ToolHandler) handleDownloadPPTWithDStaff(ctx context.Context, arguments map[string]interface{}, dstaffAuth *auth.DStaffAuthContext) (*mcp.CallToolResult, error) {
	pptID, ok := arguments["ppt_id"].(string)
	if !ok || pptID == "" {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: "ppt_id is required and must be a string",
				},
			},
			IsError: true,
		}, nil
	}

	log.Printf("⬇️ Executing 'download_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get API key: %v", err),
				},
			},
			IsError: true,
		}, nil
	}

	params := url.Values{}
	params.Set("id", pptID)

	// Get download URL from API
	result, err := h.makeGetRequest(ctx, "/mcp/ppt/ppt-download", params, apiKey)
	if err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get download URL: %v", err),
				},
			},
			IsError: true,
		}, nil
	}

	// If DStaff is enabled and auth context is available, upload the file to DStaff platform
	if h.dstaffConfig.Enabled && dstaffAuth != nil && dstaffAuth.Token != "" && dstaffAuth.TaskID != "" {
		log.Printf("🔗 DStaff enabled, attempting to upload PPT file to DStaff platform")

		fileSize, err := h.uploadPPTToDStaffWithSize(ctx, pptID, result, dstaffAuth)
		if err != nil {
			log.Printf("⚠️ Failed to upload PPT to DStaff: %v", err)
			// Don't fail the entire operation, just add error info to the response
			return h.createDStaffUploadResponseWithSize(result, pptID, dstaffAuth, false, err.Error(), 0), nil
		} else {
			log.Printf("✅ Successfully uploaded PPT to DStaff platform")
			// Create success response with attachment info
			return h.createDStaffUploadResponseWithSize(result, pptID, dstaffAuth, true, "", fileSize), nil
		}
	} else {
		log.Printf("⚠️ DStaff upload skipped - either disabled or missing auth context")
	}

	return result, nil
}

// handleEditorPPT handles the editor_ppt tool
func (h *ToolHandler) handleEditorPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("✏️ Executing 'editor_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-editor", data, apiKey)
}

// handleReplacePPT handles the replace_ppt tool
func (h *ToolHandler) handleReplacePPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	newContent, ok := arguments["new_content"].(string)
	if !ok || newContent == "" {
		return nil, fmt.Errorf("new_content is required and must be a string")
	}

	log.Printf("🔄 Executing 'replace_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("content", newContent)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-replace", data, apiKey)
}

// handleSetFontPPT handles the set_font_ppt tool
func (h *ToolHandler) handleSetFontPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	fontName, ok := arguments["font_name"].(string)
	if !ok || fontName == "" {
		return nil, fmt.Errorf("font_name is required and must be a string")
	}

	log.Printf("🔤 Executing 'set_font_ppt' tool for PPT ID: %s, font: %s", pptID, fontName)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("font_name", fontName)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handleSetAnimPPT handles the set_anim_ppt tool
func (h *ToolHandler) handleSetAnimPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	setAnim, ok := arguments["set_anim"].(string)
	if !ok || setAnim == "" {
		setAnim = "1" // Default to enable animation
	}

	log.Printf("🎬 Executing 'set_anim_ppt' tool for PPT ID: %s, set_anim: %s", pptID, setAnim)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("transition", setAnim)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handlePPTCreateNote handles the ppt_create_note tool
func (h *ToolHandler) handlePPTCreateNote(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("📝 Executing 'ppt_create_note' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("note", "1")

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handlePPTAddSlides handles the ppt_add_slides tool
func (h *ToolHandler) handlePPTAddSlides(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	slideText, ok := arguments["slide_text"].(string)
	if !ok || slideText == "" {
		return nil, fmt.Errorf("slide_text is required and must be a string")
	}

	slideType, _ := arguments["slide_type"].(string)
	if slideType == "" {
		slideType = "内容页"
	}

	log.Printf("➕ Executing 'ppt_add_slides' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("slide_text", slideText)
	data.Set("slide_type", slideType)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-page", data, apiKey)
}

// handlePPTCreateOutlineText handles the ppt_create_outline_text tool
func (h *ToolHandler) handlePPTCreateOutlineText(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	pptText, ok := arguments["ppt_text"].(string)
	if !ok || pptText == "" {
		return nil, fmt.Errorf("ppt_text is required and must be a string")
	}

	log.Printf("📋 Executing 'ppt_create_outline_text' tool")

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("text", pptText)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-structure", data, apiKey)
}

// handlePPTCreateTemplateCoverImage handles the ppt_create_template_cover_image tool
func (h *ToolHandler) handlePPTCreateTemplateCoverImage(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	pptText, ok := arguments["ppt_text"].(string)
	if !ok || pptText == "" {
		return nil, fmt.Errorf("ppt_text is required and must be a string")
	}

	log.Printf("🎨 Executing 'ppt_create_template_cover_image' tool")

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("title", pptText)

	// Handle optional parameters
	if pptColor, ok := arguments["ppt_color"].(string); ok && pptColor != "" {
		data.Set("color", pptColor)
	}

	if pptStyle, ok := arguments["ppt_style"].(string); ok && pptStyle != "" {
		data.Set("style", pptStyle)
	} else {
		data.Set("style", "科技风")
	}

	if pptNum, ok := arguments["ppt_num"].(float64); ok {
		data.Set("count", strconv.Itoa(int(pptNum)))
	} else {
		data.Set("count", "4")
	}

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-cover", data, apiKey)
}

// handlePPTReplaceUserSelectTemplate handles the ppt_replace_user_select_template tool
func (h *ToolHandler) handlePPTReplaceUserSelectTemplate(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	coverID, ok := arguments["cover_id"].(string)
	if !ok || coverID == "" {
		return nil, fmt.Errorf("cover_id is required and must be a string")
	}

	log.Printf("🔄 Executing 'ppt_replace_user_select_template' tool for PPT ID: %s, Cover ID: %s", pptID, coverID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("cover_id", coverID)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}





// uploadPPTToDStaff uploads a PPT file to DStaff platform after downloading
func (h *ToolHandler) uploadPPTToDStaff(ctx context.Context, pptID string, downloadResult *mcp.CallToolResult, dstaffAuth *auth.DStaffAuthContext) error {
	log.Printf("🔄 Processing PPT download result for DStaff upload (PPT ID: %s)", pptID)

	// Extract download URL from the result
	downloadURL, err := h.extractDownloadURL(downloadResult)
	if err != nil {
		return fmt.Errorf("failed to extract download URL: %w", err)
	}

	if downloadURL == "" {
		return fmt.Errorf("no download URL found in result")
	}

	log.Printf("📥 Downloading PPT from URL: %s", downloadURL)

	// Download the PPT file
	localFilePath, err := h.downloadFileToTemp(downloadURL, pptID+".pptx")
	if err != nil {
		return fmt.Errorf("failed to download PPT file: %w", err)
	}
	defer func() {
		// Clean up temporary file
		if err := os.Remove(localFilePath); err != nil {
			log.Printf("⚠️ Failed to clean up temporary file %s: %v", localFilePath, err)
		}
	}()

	log.Printf("📁 PPT downloaded to temporary file: %s", localFilePath)

	// Get file size before upload
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}
	fileSize := fileInfo.Size()

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, localFilePath, targetPath)
	if err != nil {
		return fmt.Errorf("failed to upload PPT to DStaff: %w", err)
	}

	log.Printf("📤 PPT uploaded successfully to DStaff path: %s (task_id: %s)", targetPath, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📊 File size: %d bytes", fileSize)

	return nil
}

// uploadPPTToDStaffWithSize uploads a PPT file to DStaff platform and returns the file size
func (h *ToolHandler) uploadPPTToDStaffWithSize(ctx context.Context, pptID string, downloadResult *mcp.CallToolResult, dstaffAuth *auth.DStaffAuthContext) (int64, error) {
	log.Printf("🔄 Processing PPT download result for DStaff upload (PPT ID: %s)", pptID)

	// Extract download URL from the result
	downloadURL, err := h.extractDownloadURL(downloadResult)
	if err != nil {
		return 0, fmt.Errorf("failed to extract download URL: %w", err)
	}

	if downloadURL == "" {
		return 0, fmt.Errorf("no download URL found in result")
	}

	log.Printf("📥 Downloading PPT from URL: %s", downloadURL)

	// Download the PPT file
	localFilePath, err := h.downloadFileToTemp(downloadURL, pptID+".pptx")
	if err != nil {
		return 0, fmt.Errorf("failed to download PPT file: %w", err)
	}
	defer func() {
		// Clean up temporary file
		if err := os.Remove(localFilePath); err != nil {
			log.Printf("⚠️ Failed to clean up temporary file %s: %v", localFilePath, err)
		}
	}()

	log.Printf("📁 PPT downloaded to temporary file: %s", localFilePath)

	// Get file size before upload
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file info: %w", err)
	}
	fileSize := fileInfo.Size()

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, localFilePath, targetPath)
	if err != nil {
		return 0, fmt.Errorf("failed to upload PPT to DStaff: %w", err)
	}

	log.Printf("📤 PPT uploaded successfully to DStaff path: %s (task_id: %s)", targetPath, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📊 File size: %d bytes", fileSize)

	return fileSize, nil
}

// createDStaffUploadResponseWithSize creates a proper MCP response for DStaff upload operations with file size
func (h *ToolHandler) createDStaffUploadResponseWithSize(originalResult *mcp.CallToolResult, pptID string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string, fileSize int64) *mcp.CallToolResult {
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)
	filename := fmt.Sprintf("%s.pptx", pptID)

	var response *auth.FileUploadResponse

	if success {
		// Create successful response using the same format as the reference
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "success",
			Message:     fmt.Sprintf("PPT文件上传成功！上传的文件路径：%s", targetPath),
			Attachments: []auth.Attachment{
				{
					Path:          targetPath,
					Filename:      filename,
					Type:          "file",
					ContentType:   "application/vnd.openxmlformats-officedocument.presentationml.presentation",
					ContentLength: fileSize,
				},
			},
		}
	} else {
		// Create error response
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "error",
			Message:     fmt.Sprintf("PPT文件上传失败：%s", errorMsg),
			Attachments: []auth.Attachment{},
		}
	}

	// Convert to JSON
	responseJSON, _ := json.Marshal(response)

	// Return the JSON response
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(responseJSON),
			},
		},
		IsError: !success,
	}
}

// createDStaffUploadResponse creates a proper MCP response for DStaff upload operations (legacy function)
func (h *ToolHandler) createDStaffUploadResponse(originalResult *mcp.CallToolResult, pptID string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string) *mcp.CallToolResult {
	return h.createDStaffUploadResponseWithSize(originalResult, pptID, dstaffAuth, success, errorMsg, 0)
}

// extractDownloadURL extracts the download URL from a CallToolResult
func (h *ToolHandler) extractDownloadURL(result *mcp.CallToolResult) (string, error) {
	if result == nil || len(result.Content) == 0 {
		return "", fmt.Errorf("empty result")
	}

	// Look for text content that contains JSON response
	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			// Try to parse as JSON to extract download URL
			var response map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &response); err != nil {
				// If not JSON, check if it's a direct URL
				text := strings.TrimSpace(textContent.Text)
				if strings.HasPrefix(text, "http://") || strings.HasPrefix(text, "https://") {
					return text, nil
				}
				continue
			}

			// Look for download URL in various possible locations in the JSON response
			downloadURL := h.findDownloadURLInResponse(response)
			if downloadURL != "" {
				return downloadURL, nil
			}
		}
	}

	return "", fmt.Errorf("no download URL found in result content")
}

// findDownloadURLInResponse searches for download URLs in the JSON response
func (h *ToolHandler) findDownloadURLInResponse(response map[string]interface{}) string {
	// Check common patterns for download URLs based on the ChatPPT API structure

	// Pattern 1: data.download_url (ChatPPT API format)
	if data, ok := response["data"].(map[string]interface{}); ok {
		if url, ok := data["download_url"].(string); ok && url != "" {
			return url
		}
	}

	// Pattern 2: data.downloads.video.url (for video downloads)
	if data, ok := response["data"].(map[string]interface{}); ok {
		if downloads, ok := data["downloads"].(map[string]interface{}); ok {
			if video, ok := downloads["video"].(map[string]interface{}); ok {
				if url, ok := video["url"].(string); ok {
					return url
				}
			}
		}
	}

	// Pattern 3: Direct URL field
	if url, ok := response["url"].(string); ok {
		return url
	}

	// Pattern 4: download_url field (root level)
	if url, ok := response["download_url"].(string); ok {
		return url
	}

	// Pattern 5: Look for any field containing "url" and starting with http
	for key, value := range response {
		if strings.Contains(strings.ToLower(key), "url") {
			if url, ok := value.(string); ok && (strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")) {
				return url
			}
		}
	}

	return ""
}

// downloadFileToTemp downloads a file from URL to a temporary location
func (h *ToolHandler) downloadFileToTemp(fileURL, filename string) (string, error) {
	// Create HTTP request
	req, err := http.NewRequest("GET", fileURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Send request
	resp, err := h.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("download failed with status %d", resp.StatusCode)
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("ppt_%s_%s", timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("📁 File downloaded successfully: %s (%d bytes)", localFilePath, len(fileContent))
	return localFilePath, nil
}

// extractProjectIDFromResult extracts project_id (ppt_id) from API result
func (h *ToolHandler) extractProjectIDFromResult(result *mcp.CallToolResult) string {
	if result == nil || result.IsError || len(result.Content) == 0 {
		return ""
	}

	// Try to extract from text content
	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			// Try to parse as JSON
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonData); err == nil {
				// Look for common project ID fields
				if pptID, ok := jsonData["ppt_id"].(string); ok && pptID != "" {
					return pptID
				}
				if id, ok := jsonData["id"].(string); ok && id != "" {
					return id
				}
				if projectID, ok := jsonData["project_id"].(string); ok && projectID != "" {
					return projectID
				}
			}
		}
	}

	return ""
}

// resolveProjectID resolves project_id from task_id if not directly provided
func (h *ToolHandler) resolveProjectID(arguments map[string]interface{}) (string, error) {
	// First try to get ppt_id directly from arguments
	if pptID, ok := arguments["ppt_id"].(string); ok && pptID != "" {
		return pptID, nil
	}

	// If no direct ppt_id, try to resolve from task_id
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID == "" {
		return "", fmt.Errorf("neither ppt_id nor task_id provided")
	}

	if projectID, found := h.bindingManager.GetProjectID(taskID); found {
		log.Printf("🔗 Resolved project_id=%s from task_id=%s", projectID, taskID)
		return projectID, nil
	}

	return "", fmt.Errorf("no project_id found for task_id=%s", taskID)
}
