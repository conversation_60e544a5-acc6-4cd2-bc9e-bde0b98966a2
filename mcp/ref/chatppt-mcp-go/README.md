# ChatPPT MCP Server (Go)

A Go implementation of the ChatPPT MCP (Model Context Protocol) server, providing PPT generation and management capabilities through various transport protocols.

## Features

- 🚀 **Multiple Transport Modes**: stdio, SSE (Server-Sent Events), and Streamable HTTP
- 🔐 **Authentication**: API key authentication and SSE access key validation
- 🐳 **Docker Support**: Complete containerization with development and production configurations
- 📊 **Comprehensive Logging**: Configurable log levels with structured logging
- 🛠️ **PPT Tools**: Complete set of PPT creation, editing, and management tools
- 🌐 **Web-Ready**: HTTP-based transports for web integration

## Quick Start

### Prerequisites

- Go 1.21 or later
- Docker and Docker Compose (for containerized deployment)
- ChatPPT API key from [www.yoo-ai.com](https://www.yoo-ai.com)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatppt-mcp-go
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Set your API key**
   ```bash
   # In .env file
   API_PPT_KEY=your_api_key_from_yoo_ai_com
   ```

### Running the Server

#### Method 1: Direct Go Run

```bash
# stdio mode (default)
go run . -t stdio

# SSE mode
go run . -t sse

# Streamable HTTP mode
go run . -t streamable_http
```

#### Method 2: Docker (Recommended)

```bash
# Production deployment
./deploy.sh production

# Development deployment
./deploy.sh development

# Stop services
./deploy.sh stop

# View logs
./deploy.sh logs

# Check status
./deploy.sh status
```

#### Method 3: Docker Compose

```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.dev.yml up -d
```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `API_PPT_KEY` | ChatPPT API key from www.yoo-ai.com | - | ✅ |
| `LOG_LEVEL` | Log level (DEBUG, INFO, WARNING, ERROR) | INFO | ❌ |
| `SSE_ACCESS_KEY` | SSE access key for authentication | - | ❌ |
| `MCP_PORT` | Server port | 8000 | ❌ |
| `API_BASE` | ChatPPT API base URL | https://saas.api.yoo-ai.com | ❌ |
| `MCP_TRANSPORT` | Transport mode (stdio, sse, streamable_http) | stdio | ❌ |

### Transport Modes

#### 1. stdio Mode
- **Use case**: Command-line MCP clients, development
- **Endpoint**: Standard input/output
- **Authentication**: Environment variables

#### 2. SSE Mode
- **Use case**: Web applications, real-time communication
- **Endpoints**: 
  - SSE: `http://localhost:8000/sse`
  - Messages: `http://localhost:8000/messages`
- **Authentication**: Query parameter `?key=your_sse_access_key`

#### 3. Streamable HTTP Mode
- **Use case**: HTTP-based clients with streaming support
- **Endpoint**: `http://localhost:8000/`
- **Authentication**: HTTP headers

## Available Tools

The server provides the following PPT management tools:

### Core Tools
- `query_ppt` - Query PPT generation progress
- `build_ppt` - Generate PPT from theme
- `text_build_ppt` - Generate PPT from long text
- `build_ppt_by_file` - Generate PPT from file URL
- `build_thesis_ppt` - Generate thesis defense PPT

### Management Tools
- `download_ppt` - Download generated PPT
- `editor_ppt` - Get online editor URL
- `replace_ppt` - Replace PPT content
- `set_font_ppt` - Set PPT font
- `set_anim_ppt` - Set PPT animations

### Advanced Tools
- `ppt_create_note` - Generate presentation notes
- `ppt_add_slides` - Add slides to existing PPT
- `ppt_create_outline_text` - Generate outline from text
- `ppt_create_template_cover_image` - Generate template covers
- `ppt_replace_user_select_template` - Replace with selected template

## API Usage Examples

### Using with MCP Client

```javascript
// Connect to SSE endpoint
const client = new MCPClient('http://localhost:8000/sse?key=your_access_key');

// Generate PPT
const result = await client.callTool('build_ppt', {
  theme: 'Introduction to AI and Machine Learning'
});

// Query progress
const progress = await client.callTool('query_ppt', {
  ppt_id: result.ppt_id
});
```

### Using with curl

```bash
# Generate PPT
curl -X POST http://localhost:8000/messages \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "build_ppt",
      "arguments": {
        "theme": "Introduction to AI"
      }
    }
  }'
```

## Development

### Project Structure

```
chatppt-mcp-go/
├── cmd/server/          # Server entry point
├── internal/
│   ├── auth/           # Authentication logic
│   ├── config/         # Configuration management
│   ├── tools/          # Tool definitions and handlers
│   └── transport/      # Transport layer
├── docker-compose.yml  # Production Docker setup
├── docker-compose.dev.yml # Development Docker setup
├── Dockerfile          # Production Docker image
├── Dockerfile.dev      # Development Docker image
└── deploy.sh          # Deployment script
```

### Building

```bash
# Build binary
go build -o chatppt-mcp-server .

# Build for different platforms
GOOS=linux GOARCH=amd64 go build -o chatppt-mcp-server-linux .
GOOS=windows GOARCH=amd64 go build -o chatppt-mcp-server.exe .
```

### Testing

```bash
# Run tests
go test ./...

# Run with coverage
go test -cover ./...

# Run specific test
go test ./internal/tools -v
```

## Deployment

### Production Deployment

1. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

2. **Deploy with Docker**
   ```bash
   ./deploy.sh production
   ```

3. **Verify deployment**
   ```bash
   curl http://localhost:8000/health
   ```

### SSL/HTTPS Setup

For production use with HTTPS:

1. **Obtain SSL certificates**
2. **Update nginx.conf** with your domain and certificate paths
3. **Enable nginx service** in docker-compose.yml
4. **Deploy with SSL profile**
   ```bash
   docker-compose --profile production up -d
   ```

## Monitoring and Logs

### View Logs
```bash
# All logs
./deploy.sh logs

# Specific service
docker-compose logs -f chatppt-mcp-server

# Development logs
docker-compose -f docker-compose.dev.yml logs -f
```

### Health Checks
```bash
# Check service status
./deploy.sh status

# Health endpoint
curl http://localhost:8000/health
```

## Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   Error: API_PPT_KEY environment variable is required
   ```
   Solution: Set your API key in the `.env` file

2. **Port Already in Use**
   ```
   Error: bind: address already in use
   ```
   Solution: Change `MCP_PORT` in `.env` or stop conflicting services

3. **SSE Access Denied**
   ```
   Error: 401 Unauthorized
   ```
   Solution: Include access key in URL: `?key=your_access_key`

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
go run . -t sse
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Documentation: [Wiki](https://github.com/your-repo/wiki)
