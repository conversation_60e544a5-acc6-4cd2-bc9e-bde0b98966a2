package main

import (
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"

	"ppt-narrator-mcp/internal/config"
	"ppt-narrator-mcp/internal/transport"
)

func main() {
	// Parse command line flags
	var transportMode string
	flag.StringVar(&transportMode, "t", "", "Transport type (stdio, sse, streamable_http)")
	flag.StringVar(&transportMode, "transport", "", "Transport type (stdio, sse, streamable_http)")
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ Failed to load configuration: %v", err)
	}

	// Override transport mode if specified via command line
	if transportMode != "" {
		cfg.Transport = transportMode
	}

	// Log configuration
	cfg.LogConfig()

	// Create MCP server wrapper
	serverWrapper := transport.NewMCPServerWrapper(cfg)

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	// Start server based on transport mode
	errChan := make(chan error, 1)

	go func() {
		switch cfg.Transport {
		case "stdio":
			errChan <- serverWrapper.ServeStdio()
		case "sse":
			errChan <- serverWrapper.ServeSSE()
		case "streamable_http":
			errChan <- serverWrapper.ServeStreamableHTTP()
		case "mixed":
			errChan <- serverWrapper.ServeMixed()
		default:
			errChan <- serverWrapper.ServeStdio() // Default to stdio
		}
	}()

	// Wait for either an error or a signal
	select {
	case err := <-errChan:
		if err != nil {
			log.Fatalf("❌ Server error: %v", err)
		}
	case sig := <-sigChan:
		log.Printf("🛑 Received signal %v, shutting down gracefully...", sig)
	}

	log.Println("👋 Smart Card MCP Server stopped")
}
