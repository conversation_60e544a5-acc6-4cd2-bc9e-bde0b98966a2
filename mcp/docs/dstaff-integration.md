# DStaff Integration Guide

本文档介绍如何在 Smart Card MCP 服务器中配置和使用 DStaff 平台集成功能。

## 概述

DStaff 集成允许 Smart Card MCP 服务器自动将生成的智能卡片上传到 DStaff 平台，实现与 DStaff 工作流的无缝集成。

## 功能特性

- **自动文件上传**: 生成的卡片图像自动上传到 DStaff 平台
- **Token 验证**: 支持 DStaff 官方认证服务进行 token 验证
- **任务关联**: 支持将上传的文件与特定的 DStaff 任务关联
- **错误处理**: 上传失败不会影响卡片生成的主要功能
- **灵活配置**: 可以启用/禁用集成功能

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 启用 DStaff 集成
DSTAFF_ENABLED=true

# DStaff 服务器端点 URL
DSTAFF_ENDPOINT_URL=http://*********:8800

# 启用官方认证服务
DSTAFF_USE_OFFICIAL_AUTH=true
```

### 配置参数说明

| 参数 | 说明 | 默认值 | 必需 |
|------|------|--------|------|
| `DSTAFF_ENABLED` | 是否启用 DStaff 集成 | `false` | 是 |
| `DSTAFF_ENDPOINT_URL` | DStaff 服务器端点 URL | `http://*********:8800` | 否 |
| `DSTAFF_USE_OFFICIAL_AUTH` | 是否使用官方认证服务 | `true` | 否 |

## 使用方法

### 1. 基本使用

当 DStaff 集成启用后，所有卡片生成请求都会自动尝试上传到 DStaff 平台。

### 2. API 调用示例

#### 使用 Bearer Token 认证

```bash
curl -X POST http://localhost:48084/api/v1/tools/generate_card_from_text \
  -H "Authorization: Bearer your_dstaff_token" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是要生成卡片的内容",
    "task_id": "your_task_id"
  }'
```

#### 使用查询参数传递 task_id

```bash
curl -X POST "http://localhost:48084/api/v1/tools/generate_card_from_text?task_id=your_task_id" \
  -H "Authorization: Bearer your_dstaff_token" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是要生成卡片的内容"
  }'
```

#### 使用 Header 传递 task_id

```bash
curl -X POST http://localhost:48084/api/v1/tools/generate_card_from_text \
  -H "Authorization: Bearer your_dstaff_token" \
  -H "X-Task-ID: your_task_id" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是要生成卡片的内容"
  }'
```

### 3. MCP 客户端使用

当使用 MCP 客户端时，需要在请求上下文中提供认证信息：

```json
{
  "method": "tools/call",
  "params": {
    "name": "generate_card_from_text",
    "arguments": {
      "text": "这是要生成卡片的内容"
    }
  },
  "headers": {
    "Authorization": "Bearer your_dstaff_token"
  },
  "query": {
    "task_id": "your_task_id"
  }
}
```

## 认证机制

### Token 验证流程

1. 客户端在请求头中提供 `Authorization: Bearer <token>`
2. 服务器提取 token 并发送到 DStaff 认证服务进行验证
3. 验证通过后，允许请求继续处理
4. 生成卡片后，使用相同的 token 上传文件到 DStaff

### Task ID 提取

服务器会按以下优先级提取 task_id：

1. URL 查询参数: `?task_id=xxx`
2. HTTP 头部: `X-Task-ID: xxx`
3. 请求上下文中的值

## 文件上传

### 上传路径

生成的卡片会上传到 DStaff 平台的以下路径：

```
mcps_upload/smart_cards/{filename}
```

其中 `{filename}` 格式为：`smart_card_{timestamp}.png`

### 上传响应

成功上传后，DStaff 会返回以下格式的响应：

```json
{
  "work_type": "mcp_tool",
  "compression": false,
  "status": "success",
  "message": "文件上传成功！上传的文件路径：mcps_upload/smart_cards/smart_card_20240329_143022.png",
  "attachments": [
    {
      "path": "mcps_upload/smart_cards/smart_card_20240329_143022.png",
      "filename": "smart_card_20240329_143022.png",
      "type": "file",
      "content_type": "image/png",
      "content_length": 12345
    }
  ]
}
```

## 错误处理

### 常见错误

1. **认证失败**: Token 无效或过期
2. **缺少 task_id**: 未提供必需的任务 ID
3. **网络错误**: 无法连接到 DStaff 服务器
4. **上传失败**: 文件上传过程中出现错误

### 错误处理策略

- DStaff 集成错误不会影响卡片生成的主要功能
- 上传失败时会记录错误日志，但仍返回生成的卡片
- 认证失败时会拒绝整个请求

## 日志记录

启用 DStaff 集成后，服务器会记录以下日志：

```
📤 DStaff integration enabled, uploading generated card...
✅ Successfully uploaded card to DStaff platform
📤 Card uploaded successfully to DStaff path: mcps_upload/smart_cards/smart_card_20240329_143022.png (task_id: your_task_id)
```

## 故障排除

### 1. 集成未启用

确保在 `.env` 文件中设置了 `DSTAFF_ENABLED=true`

### 2. 认证失败

- 检查 Bearer token 是否正确
- 确认 DStaff 认证服务是否可访问
- 验证 `DSTAFF_ENDPOINT_URL` 配置是否正确

### 3. 上传失败

- 检查网络连接
- 确认 DStaff 服务器状态
- 查看服务器日志获取详细错误信息

### 4. 缺少 task_id

确保在请求中提供了 task_id，可以通过以下方式之一：
- URL 查询参数: `?task_id=xxx`
- HTTP 头部: `X-Task-ID: xxx`

## 安全注意事项

1. **Token 安全**: 确保 Bearer token 的安全传输和存储
2. **网络安全**: 建议在生产环境中使用 HTTPS
3. **访问控制**: 确保只有授权的客户端可以访问服务
4. **日志安全**: 避免在日志中记录敏感的认证信息

## 开发和测试

### 本地测试

1. 启动 Smart Card MCP 服务器
2. 配置 DStaff 集成参数
3. 使用 curl 或其他 HTTP 客户端发送测试请求
4. 检查日志确认集成是否正常工作

### 集成测试

建议创建自动化测试来验证 DStaff 集成功能：

1. 测试认证流程
2. 测试文件上传
3. 测试错误处理
4. 测试不同的 task_id 传递方式
