# MCP 进度报告功能

## 概述

Smart Card MCP 服务器现在支持 MCP 协议的进度报告功能，可以在工具执行过程中实时向客户端发送进度更新。

## 功能特性

### ✅ 已实现的功能

1. **标准 MCP 进度通知**
   - 支持 `notifications/progress` 标准格式
   - 包含 `progressToken`、`progress`、`total`、`message` 字段
   - 符合 MCP 协议规范

2. **智能进度报告**
   - 自动检测客户端是否支持进度报告
   - 如果没有进度令牌，优雅降级为日志输出
   - 支持长时间操作的定期更新

3. **多步骤进度跟踪**
   - 文本卡片生成：3个步骤（分析总结 → HTML生成 → 图片转换）
   - URL卡片生成：4个步骤（获取内容 → 分析总结 → HTML生成 → 图片转换）
   - 每个步骤都有详细的进度描述

4. **错误处理**
   - 进度报告失败时不影响主要功能
   - 详细的错误日志记录
   - 优雅的错误恢复机制

## 使用方法

### 客户端请求格式

要启用进度报告，客户端需要在工具调用请求中包含进度令牌：

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "generate_card_from_text",
    "arguments": {
      "text": "这是要生成卡片的文本内容"
    },
    "_meta": {
      "progressToken": "unique-progress-token-123"
    }
  }
}
```

### 服务器进度通知格式

服务器会发送标准的 MCP 进度通知：

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/progress",
  "params": {
    "progressToken": "unique-progress-token-123",
    "progress": 1,
    "total": 3,
    "message": "正在分析和总结文本内容..."
  }
}
```

### 进度步骤说明

#### 文本卡片生成 (3步骤)
1. **步骤 1/3**: 正在分析和总结文本内容...
2. **步骤 2/3**: 正在生成HTML卡片设计...
3. **步骤 3/3**: 正在转换为高清图片...

#### URL卡片生成 (4步骤)
1. **步骤 1/4**: 正在获取网页内容 (可能需要较长时间)
2. **步骤 2/4**: 正在分析和总结网页内容...
3. **步骤 3/4**: 正在生成HTML卡片设计...
4. **步骤 4/4**: 正在转换为高清图片...

## 技术实现

### 核心组件

1. **Progress Reporter** (`internal/progress/reporter.go`)
   - 负责进度报告的核心逻辑
   - 支持步骤跟踪、错误报告、完成通知
   - 包含防抖机制，避免过于频繁的更新

2. **Transport Integration** (`internal/transport/server.go`)
   - 集成到 MCP 服务器传输层
   - 支持 SSE 和其他传输模式
   - 自动处理通知发送

3. **Tool Handlers** (`internal/tools/handlers.go`)
   - 工具处理器集成进度报告
   - 每个工具都有对应的带进度版本
   - 向后兼容不支持进度的客户端

### 关键特性

- **自动检测**: 自动检测客户端是否提供进度令牌
- **优雅降级**: 没有进度令牌时仍然正常工作
- **防抖机制**: 避免过于频繁的进度更新
- **长时间操作支持**: 对于耗时操作定期发送"正在执行"更新
- **错误处理**: 进度报告错误不影响主要功能

## 配置选项

目前进度报告功能是自动启用的，无需额外配置。服务器会：

1. 自动检测客户端请求中的进度令牌
2. 如果有进度令牌，启用进度报告
3. 如果没有进度令牌，使用标准日志输出

## 兼容性

- ✅ 向后兼容：不支持进度报告的客户端仍然正常工作
- ✅ 标准兼容：完全符合 MCP 协议规范
- ✅ 传输兼容：支持 SSE、WebSocket 等传输模式

## 示例日志输出

### 带进度令牌的请求
```
📊 Sending progress: 1/3 - 正在分析和总结文本内容...
📊 Sending progress: 2/3 - 正在生成HTML卡片设计...
📊 Sending progress: 3/3 - 正在转换为高清图片...
📊 Sending progress: 3/3 - 智能卡片生成完成！
```

### 不带进度令牌的请求
```
📊 Step 1/3: 正在分析和总结文本内容...
📊 Step 2/3: 正在生成HTML卡片设计...
📊 Step 3/3: 正在转换为高清图片...
```

## 未来扩展

计划中的功能增强：

1. **日志消息支持**: 实现 `notifications/message` 日志通知
2. **更细粒度进度**: 支持子步骤进度报告
3. **进度取消**: 支持客户端取消长时间运行的操作
4. **自定义进度格式**: 支持客户端自定义进度消息格式

## 故障排除

### 常见问题

1. **进度通知没有收到**
   - 检查客户端是否在请求中包含了 `progressToken`
   - 确认传输连接正常工作
   - 查看服务器日志中的进度发送记录

2. **进度更新太频繁**
   - 进度报告器内置了防抖机制（500ms间隔）
   - 可以通过修改 `minInterval` 调整更新频率

3. **进度报告影响性能**
   - 进度报告是异步的，不应影响主要功能
   - 如果出现问题，进度报告会自动降级为日志输出

### 调试建议

1. 启用详细日志记录
2. 检查 MCP 连接状态
3. 验证进度令牌格式
4. 监控网络传输状态
