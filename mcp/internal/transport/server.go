package transport

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"

	"ppt-narrator-mcp/internal/auth"
	"ppt-narrator-mcp/internal/config"
	"ppt-narrator-mcp/internal/tasks"
	"ppt-narrator-mcp/internal/tools"
)

// SimplePPTClient is a simple implementation of PPTNarratorClient
type SimplePPTClient struct {
	config *config.Config
}

// UploadFileFromURL uploads a file from URL to PPT narrator
func (c *SimplePPTClient) UploadFileFromURL(fileURL, name, userRequirements string, enableSubtitles bool, subtitleStyleTemplate string) ([]byte, error) {
	// This would normally make HTTP calls to the PPT narrator backend
	// For now, return a mock response
	response := map[string]interface{}{
		"success":    true,
		"project_id": "mock-project-id",
		"message":    "Mock upload successful",
	}

	return json.Marshal(response)
}

// GetProgress gets the progress of a project
func (c *SimplePPTClient) GetProgress(projectID string) ([]byte, error) {
	// Mock progress response
	response := map[string]interface{}{
		"success":  true,
		"progress": 50.0,
		"stage":    "processing",
		"message":  "Mock progress",
	}

	return json.Marshal(response)
}

// GetDownloadURL gets the download URL for a project
func (c *SimplePPTClient) GetDownloadURL(projectID string) ([]byte, error) {
	// Mock download URL response
	response := map[string]interface{}{
		"success":      true,
		"download_url": "https://example.com/download/" + projectID,
		"message":      "Mock download URL",
	}

	return json.Marshal(response)
}

// RetryProject retries a failed project
func (c *SimplePPTClient) RetryProject(projectID string) ([]byte, error) {
	// Mock retry response
	response := map[string]interface{}{
		"success": true,
		"message": "Mock retry successful",
	}

	return json.Marshal(response)
}

// MCPServerWrapper wraps the MCP server with different transport modes
type MCPServerWrapper struct {
	config       *config.Config
	mcpServer    *server.MCPServer
	toolHandlers *tools.ToolHandlers
	taskManager  *tasks.TaskManager
	// 用于发送通知的函数，在不同传输模式下会有不同的实现
	notificationSender func(notification interface{}) error
}

// NewMCPServerWrapper creates a new MCP server wrapper
func NewMCPServerWrapper(cfg *config.Config) *MCPServerWrapper {
	// Create PPT client and task binding manager
	pptClient := &SimplePPTClient{config: cfg}
	taskBindingManager := tools.NewSimpleTaskBindingManager()

	toolHandlers := tools.NewToolHandlers(cfg, pptClient, taskBindingManager)

	// Create task manager (max 10 concurrent tasks, 1 hour TTL)
	taskManager := tasks.NewTaskManager(10, time.Hour)

	// Create task executor
	taskExecutor := tasks.NewTaskExecutor(taskManager, toolHandlers)

	// Set task manager and executor in tool handlers for async operations
	toolHandlers.SetTaskManager(taskManager)
	toolHandlers.SetTaskExecutor(taskExecutor)

	// Create MCP server with necessary capabilities
	mcpServer := server.NewMCPServer(
		"ppt-narrator-mcp",
		"1.0.0",
		server.WithToolCapabilities(true),
		server.WithLogging(),
	)

	// Server info will be set automatically by the MCP library

	wrapper := &MCPServerWrapper{
		config:       cfg,
		mcpServer:    mcpServer,
		toolHandlers: toolHandlers,
		taskManager:  taskManager,
	}

	// Register tools
	wrapper.registerTools()

	// 设置进度发送函数（在注册工具之后）
	wrapper.setupProgressSending()

	return wrapper
}

// setupProgressSending sets up progress notification sending
func (w *MCPServerWrapper) setupProgressSending() {
	// 设置工具处理器的进度发送函数
	// 这里只是设置占位符，实际的发送逻辑在工具处理函数中
	w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
		log.Printf("📊 Progress notification prepared: %.0f/%.0f - %s",
			notification.Params.Progress,
			notification.Params.Total,
			notification.Params.Message)
		// 实际发送逻辑在工具处理函数中处理
		return nil
	})

	// 设置工具处理器的日志发送函数
	w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
		log.Printf("📝 Log notification prepared: [%s] %s",
			string(notification.Params.Level),
			notification.Params.Data)
		// 实际发送逻辑在工具处理函数中处理
		return nil
	})
}

// registerTools registers all available tools with the MCP server
func (w *MCPServerWrapper) registerTools() {
	allTools := tools.GetAllTools(w.config)

	for _, tool := range allTools {
		w.mcpServer.AddTool(tool, w.createToolHandler(tool.Name))
	}

	log.Printf("✅ Registered %d tools", len(allTools))
}

// createToolHandler creates a handler function for a specific tool
func (w *MCPServerWrapper) createToolHandler(toolName string) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		log.Printf("🔧 Executing tool: %s", toolName)

		// Add DStaff auth context if available
		ctx = w.addDStaffAuthContext(ctx, request)

		// 设置当前请求的进度发送函数，使用当前的 context
		w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
			log.Printf("📊 Sending progress notification: %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)

			// 如果有进度令牌，更新任务管理器中的进度
			if notification.Params.ProgressToken != nil {
				if taskID, ok := notification.Params.ProgressToken.(string); ok && taskID != "" {
					if w.taskManager != nil {
						current := int(notification.Params.Progress)
						total := int(notification.Params.Total)
						message := notification.Params.Message

						log.Printf("🔍 Debug: Updating task progress - taskID: %s, current: %d, total: %d, message: %s", taskID, current, total, message)
						err := w.taskManager.UpdateTaskProgress(taskID, current, total, message)
						if err != nil {
							log.Printf("❌ Failed to update task progress: %v", err)
						}
					}
				}
			}

			// 检查客户端会话是否已初始化
			session := server.ClientSessionFromContext(ctx)
			if session == nil {
				log.Printf("⚠️ No client session in context, using fallback logging")
				log.Printf("📊 Progress: %.0f/%.0f - %s",
					notification.Params.Progress,
					notification.Params.Total,
					notification.Params.Message)
				return nil
			}

			if !session.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				session.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.mcpServer.SendNotificationToClient(ctx, "notifications/progress", map[string]any{
				"progress":      notification.Params.Progress,
				"total":         notification.Params.Total,
				"progressToken": notification.Params.ProgressToken,
				"message":       notification.Params.Message,
			})

			if err != nil {
				log.Printf("❌ Failed to send progress notification: %v", err)
				// 使用日志作为后备
				log.Printf("📊 Progress (fallback): %.0f/%.0f - %s",
					notification.Params.Progress,
					notification.Params.Total,
					notification.Params.Message)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Progress notification sent to current client")
			return nil
		})

		// 设置当前请求的日志发送函数
		w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Sending log notification: [%s] %s",
				string(notification.Params.Level),
				notification.Params.Data)

			// 检查客户端会话是否已初始化
			session := server.ClientSessionFromContext(ctx)
			if session == nil {
				log.Printf("⚠️ No client session in context, using fallback logging")
				log.Printf("📝 Log [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil
			}

			if !session.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				session.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.mcpServer.SendNotificationToClient(ctx, "notifications/message", map[string]any{
				"level":  string(notification.Params.Level),
				"logger": notification.Params.Logger,
				"data":   notification.Params.Data,
			})

			if err != nil {
				log.Printf("❌ Failed to send log notification: %v", err)
				// 使用日志作为后备
				log.Printf("📝 Log (fallback) [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Log notification sent to current client")
			return nil
		})

		var result map[string]interface{}
		var err error

		// Convert arguments to map[string]interface{}
		args, ok := request.Params.Arguments.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid arguments format")
		}

		// 路由到相应的工具处理器
		switch toolName {
		case "upload_file":
			// Handle DStaff file download if needed
			result, err = w.handleUploadFileWithDStaff(ctx, request, args)
		case "get_progress":
			result, err = w.toolHandlers.GetProgress(args)
		case "get_download_url":
			result, err = w.handleGetDownloadURLWithDStaff(ctx, request, args)
		case "retry_project":
			result, err = w.toolHandlers.RetryProject(args)
		case "upload_and_monitor":
			// Handle DStaff file download if needed
			result, err = w.handleUploadAndMonitorWithDStaff(ctx, request, args)
		default:
			return nil, fmt.Errorf("unknown tool: %s", toolName)
		}

		if err != nil {
			log.Printf("❌ Tool execution failed: %v", err)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.NewTextContent(fmt.Sprintf("Error: %v", err)),
				},
				IsError: true,
			}, nil
		}

		log.Printf("✅ Tool executed successfully: %s", toolName)

		// For get_download_url and upload_and_monitor, return result directly without conversion
		if toolName == "get_download_url" || toolName == "upload_and_monitor" {
			log.Printf("🎯 Returning direct result for tool: %s", toolName)

			// Convert result to JSON string for MCP content
			jsonBytes, err := json.Marshal(result)
			if err != nil {
				log.Printf("❌ Failed to marshal result to JSON: %v", err)
				return &mcp.CallToolResult{
					Content: []mcp.Content{
						mcp.NewTextContent(fmt.Sprintf("Error marshaling result: %v", err)),
					},
					IsError: true,
				}, nil
			}

			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.NewTextContent(string(jsonBytes)),
				},
				IsError: false,
			}, nil
		}

		// Convert result to MCP content for other tools
		content := w.convertResultToContent(result)

		return &mcp.CallToolResult{
			Content: content,
			IsError: false,
		}, nil
	}
}

// convertResultToContent converts tool result to MCP content
func (w *MCPServerWrapper) convertResultToContent(result map[string]interface{}) []mcp.Content {
	var content []mcp.Content

	// Check if result already contains properly formatted content
	if contentArray, exists := result["content"]; exists {
		if contentSlice, ok := contentArray.([]map[string]interface{}); ok {
			// Convert each content item to mcp.Content
			for _, item := range contentSlice {
				if itemType, hasType := item["type"]; hasType {
					switch itemType {
					case "text":
						if text, hasText := item["text"]; hasText {
							if textStr, isString := text.(string); isString {
								content = append(content, mcp.NewTextContent(textStr))
							}
						}
					case "image":
						if data, hasData := item["data"]; hasData {
							if dataStr, isString := data.(string); isString {
								if mimeType, hasMime := item["mimeType"]; hasMime {
									if mimeStr, isMimeString := mimeType.(string); isMimeString {
										content = append(content, mcp.NewImageContent(dataStr, mimeStr))
									}
								}
							}
						}
					}
				}
			}
			return content
		}
	}

	// Fallback: Convert result to JSON-like text representation
	var parts []string
	for key, value := range result {
		switch v := value.(type) {
		case string:
			parts = append(parts, fmt.Sprintf("%s: %s", key, v))
		case []string:
			parts = append(parts, fmt.Sprintf("%s: [%s]", key, strings.Join(v, ", ")))
		case int:
			parts = append(parts, fmt.Sprintf("%s: %d", key, v))
		case bool:
			parts = append(parts, fmt.Sprintf("%s: %t", key, v))
		default:
			// Skip the content field to avoid double processing
			if key != "content" {
				parts = append(parts, fmt.Sprintf("%s: %v", key, v))
			}
		}
	}

	content = append(content, mcp.NewTextContent(strings.Join(parts, "\n")))

	return content
}

// ServeStdio starts the server in stdio mode
func (w *MCPServerWrapper) ServeStdio() error {
	log.Println("🚀 Starting Smart Card MCP Server in stdio mode...")
	return server.ServeStdio(w.mcpServer)
}

// ServeSSE starts the server in SSE mode with authentication
func (w *MCPServerWrapper) ServeSSE() error {
	if len(w.config.Ports) == 0 {
		return fmt.Errorf("no ports configured for SSE mode")
	}

	port := w.config.Ports[0]
	log.Printf("🚀 Starting Smart Card MCP Server in SSE mode on port %s...", port)

	// Create authenticated SSE server
	sseServer := w.createAuthenticatedSSEServer(port)
	return sseServer.Start(":" + port)
}

// ServeStreamableHTTP starts the server in streamable HTTP mode with authentication
func (w *MCPServerWrapper) ServeStreamableHTTP() error {
	if len(w.config.Ports) < 2 {
		return fmt.Errorf("need at least 2 ports configured for streamable HTTP mode")
	}

	port := w.config.Ports[1]
	log.Printf("🚀 Starting Smart Card MCP Server in streamable HTTP mode on port %s...", port)

	// Create authenticated HTTP server with API routes
	httpServer := w.createAuthenticatedHTTPServerWithAPI(port)
	return httpServer.ListenAndServe()
}

// ServeMixed starts the server in mixed mode (multiple transports) with authentication
func (w *MCPServerWrapper) ServeMixed() error {
	log.Println("🚀 Starting Smart Card MCP Server in mixed mode...")

	// Start SSE server in a goroutine
	if len(w.config.Ports) > 0 {
		go func() {
			port := w.config.Ports[0]
			log.Printf("📡 SSE server starting on port %s", port)
			sseServer := w.createAuthenticatedSSEServer(port)
			if err := sseServer.Start(":" + port); err != nil {
				log.Printf("❌ SSE server error: %v", err)
			}
		}()
	}

	// Start HTTP server with API routes in a goroutine
	if len(w.config.Ports) > 1 {
		go func() {
			port := w.config.Ports[1]
			log.Printf("🌐 HTTP server starting on port %s", port)
			httpServer := w.createAuthenticatedHTTPServerWithAPI(port)
			if err := httpServer.ListenAndServe(); err != nil {
				log.Printf("❌ HTTP server error: %v", err)
			}
		}()
	}

	// Add health check endpoint
	w.setupHealthCheck()

	// Keep the main goroutine alive
	select {}
}

// setupHealthCheck sets up health check endpoints
func (w *MCPServerWrapper) setupHealthCheck() {
	http.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"ppt-narrator-mcp","version":"1.0.0"}`))
	})

	// Start health check server on the first available port
	if len(w.config.Ports) > 0 {
		port := w.config.Ports[0]
		portInt, _ := strconv.Atoi(port)
		healthPort := portInt + 1000 // Use a different port for health check

		go func() {
			log.Printf("🏥 Health check server starting on port %d", healthPort)
			if err := http.ListenAndServe(fmt.Sprintf(":%d", healthPort), nil); err != nil {
				log.Printf("❌ Health check server error: %v", err)
			}
		}()
	}
}

// createAuthenticatedSSEServer creates an SSE server with authentication middleware (with DStaff support)
func (w *MCPServerWrapper) createAuthenticatedSSEServer(port string) *server.SSEServer {
	return w.createAuthenticatedSSEServerWithDStaff(port)
}

// createAuthenticatedHTTPServer creates an HTTP server with authentication middleware
func (w *MCPServerWrapper) createAuthenticatedHTTPServer(port string) *http.Server {
	mux := http.NewServeMux()

	// Add health check endpoint
	mux.HandleFunc("/health", w.healthCheckHandler)

	// Add main MCP endpoint with authentication middleware
	mux.HandleFunc("/mcp", w.authenticatedMCPHandler)

	return &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}
}

// healthCheckHandler handles health check requests
func (w *MCPServerWrapper) healthCheckHandler(rw http.ResponseWriter, r *http.Request) {
	rw.Header().Set("Content-Type", "application/json; charset=utf-8")
	rw.WriteHeader(http.StatusOK)
	rw.Write([]byte(`{"status":"healthy","service":"ppt-narrator-mcp","version":"1.0.0"}`))
}

// authenticatedMCPHandler handles MCP requests with authentication (with DStaff support)
func (w *MCPServerWrapper) authenticatedMCPHandler(rw http.ResponseWriter, r *http.Request) {
	// Validate authentication
	if !w.validateAuthentication(r) {
		log.Printf("❌ MCP connection rejected due to authentication failure from %s", r.RemoteAddr)
		http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Add DStaff auth context if enabled
	ctx := r.Context()
	if w.config.DStaff.Enabled {
		log.Printf("🌐 === DStaff MCP Request Processing ===")
		log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		log.Printf("🔍 Request Headers:")

		// Log relevant headers
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
		} else {
			log.Printf("   - Authorization: Not provided")
		}

		taskIDHeader := r.Header.Get("X-Task-ID")
		if taskIDHeader != "" {
			log.Printf("   - X-Task-ID: %s", taskIDHeader)
		} else {
			log.Printf("   - X-Task-ID: Not provided")
		}

		contentType := r.Header.Get("Content-Type")
		if contentType != "" {
			log.Printf("   - Content-Type: %s", contentType)
		}

		// Log query parameters
		log.Printf("🔍 Query Parameters:")
		if len(r.URL.Query()) > 0 {
			for key, values := range r.URL.Query() {
				log.Printf("   - %s: %v", key, values)
			}
		} else {
			log.Printf("   - No query parameters")
		}

		// Extract token from Authorization header and add to context
		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			ctx = context.WithValue(ctx, "authorization_token", token)
			log.Printf("🔐 Bearer token extracted and added to context")
		}

		// Extract task_id and add to context
		taskID := auth.ExtractTaskIDFromContext(ctx, r)
		if taskID != "" {
			ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
			log.Printf("📋 Task ID extracted and added to context: %s", taskID)
		} else {
			log.Printf("⚠️ No task ID found in request")
		}

		log.Printf("✅ DStaff MCP request processing completed")
	}

	// Update request with new context
	r = r.WithContext(ctx)

	// Create streamable HTTP server and handle the request
	httpServer := server.NewStreamableHTTPServer(w.mcpServer)
	httpServer.ServeHTTP(rw, r)
}

// validateAuthentication validates authentication for SSE and MCP endpoints (supports both key-based and DStaff auth)
func (w *MCPServerWrapper) validateAuthentication(r *http.Request) bool {
	// Check if DStaff official auth is enabled (highest priority)
	if w.config.DStaff.Enabled && w.config.DStaff.UseOfficialAuth {
		// Extract Bearer token from Authorization header
		authHeader := r.Header.Get("Authorization")
		log.Printf("DStaff Auth Check: %s %s from %s, Auth header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)

		if authHeader == "" {
			log.Printf("Access denied: No Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		// Extract token from Bearer header
		if !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("Access denied: Invalid Authorization header format for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			log.Printf("Access denied: Empty token in Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		// Validate token with DStaff service
		if auth.ValidateTokenWithDStaff(w.config.DStaff, token) {
			log.Printf("Access granted (DStaff auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return true
		}

		log.Printf("Access denied: DStaff token validation failed for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return false
	}

	// Fallback to key-based authentication if DStaff is not enabled or not using official auth
	if w.config.SSEAccessKey == "" {
		log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// Check for key-based authentication on /sse and /mcp endpoints
	if r.URL.Path == "/sse" || r.URL.Path == "/mcp" {
		providedKey := r.URL.Query().Get("key")
		log.Printf("Key Auth Check: %s %s from %s, query key: '%s', expected key: '%s'", r.Method, r.URL.Path, r.RemoteAddr, providedKey, w.config.SSEAccessKey)

		if providedKey == "" {
			log.Printf("Access denied: No key provided for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		if providedKey != w.config.SSEAccessKey {
			log.Printf("Access denied: Invalid key '%s' (expected: '%s') for %s %s from %s", providedKey, w.config.SSEAccessKey, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted (key auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// No authentication required for other endpoints (like /health)
	log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	return true
}

// createAuthenticatedHTTPServerWithAPI creates an HTTP server with API routes (with DStaff support)
func (w *MCPServerWrapper) createAuthenticatedHTTPServerWithAPI(port string) *http.Server {
	// Create a new HTTP mux
	mux := http.NewServeMux()

	// Add MCP streamable HTTP handler with DStaff auth context support
	mux.HandleFunc("/mcp", func(rw http.ResponseWriter, r *http.Request) {
		// Add DStaff auth context if enabled
		ctx := r.Context()
		if w.config.DStaff.Enabled {
			log.Printf("🌐 === DStaff HTTP API Request Processing ===")
			log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			log.Printf("🔍 Request Headers:")

			// Log relevant headers
			authHeader := r.Header.Get("Authorization")
			if authHeader != "" {
				log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
			} else {
				log.Printf("   - Authorization: Not provided")
			}

			taskIDHeader := r.Header.Get("X-Task-ID")
			if taskIDHeader != "" {
				log.Printf("   - X-Task-ID: %s", taskIDHeader)
			} else {
				log.Printf("   - X-Task-ID: Not provided")
			}

			contentType := r.Header.Get("Content-Type")
			if contentType != "" {
				log.Printf("   - Content-Type: %s", contentType)
			}

			// Log query parameters
			log.Printf("🔍 Query Parameters:")
			if len(r.URL.Query()) > 0 {
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}
			} else {
				log.Printf("   - No query parameters")
			}

			// Extract token from Authorization header and add to context
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				ctx = context.WithValue(ctx, "authorization_token", token)
				log.Printf("🔐 Bearer token extracted and added to context")
			}

			// Extract task_id and add to context
			taskID := auth.ExtractTaskIDFromContext(ctx, r)
			if taskID != "" {
				ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
				log.Printf("📋 Task ID extracted and added to context: %s", taskID)
			} else {
				log.Printf("⚠️ No task ID found in request")
			}

			log.Printf("✅ DStaff HTTP API request processing completed")
		}

		// Update request with new context
		r = r.WithContext(ctx)

		// Handle with streamable server
		streamableServer := server.NewStreamableHTTPServer(w.mcpServer)
		streamableServer.ServeHTTP(rw, r)
	})

	// API routes removed - MCP server only uses MCP protocol

	// Add health check
	mux.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"ppt-narrator-mcp","version":"1.0.0"}`))
	})

	// Add root redirect to docs
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/api/v1/docs", http.StatusFound)
			return
		}
		http.NotFound(w, r)
	})

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	log.Printf("🌐 HTTP API server will be available at http://localhost:%s", port)
	log.Printf("📚 API documentation: http://localhost:%s/api/v1/docs", port)
	log.Printf("🔗 MCP endpoint: http://localhost:%s/mcp", port)

	return server
}

// addDStaffAuthContext adds DStaff authentication context to the request context
func (w *MCPServerWrapper) addDStaffAuthContext(ctx context.Context, request mcp.CallToolRequest) context.Context {
	log.Printf("🔍 === DStaff Auth Context Setup ===")
	log.Printf("🔧 DStaff Config Enabled: %v", w.config.DStaff.Enabled)
	log.Printf("🛠️ Tool Name: %s", request.Params.Name)

	if !w.config.DStaff.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping auth context setup")
		return ctx
	}

	// Log current context values
	log.Printf("🔍 Current Context Values:")
	if authToken := ctx.Value("authorization_token"); authToken != nil {
		if tokenStr, ok := authToken.(string); ok {
			log.Printf("   - Authorization Token: %s", maskTokenForLog(tokenStr))
		}
	} else {
		log.Printf("   - Authorization Token: Not found")
	}

	if taskID := ctx.Value("dstaff_task_id"); taskID != nil {
		if taskIDStr, ok := taskID.(string); ok {
			log.Printf("   - DStaff Task ID: %s", taskIDStr)
		}
	} else {
		log.Printf("   - DStaff Task ID: Not found")
	}

	// Log request parameters for debugging
	log.Printf("🔍 Request Parameters:")
	if args, ok := request.Params.Arguments.(map[string]interface{}); ok {
		for key, value := range args {
			if key == "task_id" {
				log.Printf("   - %s: %v", key, value)
			} else {
				// Truncate long values for readability
				valueStr := fmt.Sprintf("%v", value)
				if len(valueStr) > 100 {
					valueStr = valueStr[:100] + "..."
				}
				log.Printf("   - %s: %s", key, valueStr)
			}
		}
	}

	log.Printf("✅ DStaff auth context setup completed")
	return ctx
}

// createAuthenticatedSSEServerWithDStaff creates an SSE server with DStaff authentication support
func (w *MCPServerWrapper) createAuthenticatedSSEServerWithDStaff(port string) *server.SSEServer {
	baseURL := w.config.BaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("http://localhost:%s", port)
	}

	// Create authentication middleware with DStaff support
	authMiddleware := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(rw http.ResponseWriter, r *http.Request) {
			// Skip authentication for health check endpoint
			if r.URL.Path == "/health" {
				w.healthCheckHandler(rw, r)
				return
			}

			// Validate authentication (supports both key-based and DStaff)
			if !w.validateAuthentication(r) {
				log.Printf("❌ SSE connection rejected due to authentication failure from %s", r.RemoteAddr)
				http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
				return
			}

			// Add DStaff auth context if enabled
			ctx := r.Context()
			if w.config.DStaff.Enabled {
				log.Printf("🌐 === DStaff SSE Request Processing ===")
				log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
				log.Printf("🔍 Request Headers:")

				// Log relevant headers
				authHeader := r.Header.Get("Authorization")
				if authHeader != "" {
					log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
				} else {
					log.Printf("   - Authorization: Not provided")
				}

				taskIDHeader := r.Header.Get("X-Task-ID")
				if taskIDHeader != "" {
					log.Printf("   - X-Task-ID: %s", taskIDHeader)
				} else {
					log.Printf("   - X-Task-ID: Not provided")
				}

				// Log query parameters
				log.Printf("🔍 Query Parameters:")
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}

				// Extract token from Authorization header and add to context
				if strings.HasPrefix(authHeader, "Bearer ") {
					token := strings.TrimPrefix(authHeader, "Bearer ")
					ctx = context.WithValue(ctx, "authorization_token", token)
					log.Printf("🔐 Bearer token extracted and added to context")
				}

				// Extract task_id and add to context
				taskID := auth.ExtractTaskIDFromContext(ctx, r)
				if taskID != "" {
					ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
					log.Printf("📋 Task ID extracted and added to context: %s", taskID)
				} else {
					log.Printf("⚠️ No task ID found in request")
				}

				log.Printf("✅ DStaff SSE request processing completed")
			}

			// Update request with new context
			r = r.WithContext(ctx)

			// Authentication passed, continue to SSE server
			next.ServeHTTP(rw, r)
		})
	}

	// Create SSE server
	sseServer := server.NewSSEServer(w.mcpServer)

	// Create a custom HTTP server with authentication middleware
	customServer := &http.Server{
		Handler: authMiddleware(sseServer),
	}

	// Return SSE server with custom HTTP server
	return server.NewSSEServer(
		w.mcpServer,
		server.WithHTTPServer(customServer),
	)
}

// maskTokenForLog masks a token for safe logging
func maskTokenForLog(token string) string {
	if token == "" {
		return "Not set"
	}
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskAuthHeaderForLog masks an Authorization header for safe logging
func maskAuthHeaderForLog(authHeader string) string {
	if authHeader == "" {
		return "Not provided"
	}

	if strings.HasPrefix(authHeader, "Bearer ") {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		return "Bearer " + maskTokenForLog(token)
	}

	// For other auth types, just show the type
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) == 2 {
		return parts[0] + " ***"
	}

	return "***"
}

// handleUploadFileWithDStaff handles upload_file tool with DStaff file download support
func (w *MCPServerWrapper) handleUploadFileWithDStaff(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	// 输出完整的请求信息用于调试
	log.Printf("🔍 === UPLOAD_FILE DEBUG INFO ===")
	log.Printf("🔍 Tool Name: %s", request.Params.Name)
	log.Printf("🔍 Method: %s", request.Method)

	// 输出完整的参数结构
	log.Printf("🔍 Raw Arguments Type: %T", request.Params.Arguments)
	log.Printf("🔍 Raw Arguments Value: %+v", request.Params.Arguments)

	// 输出解析后的 args
	log.Printf("🔍 Parsed Args Type: %T", args)
	log.Printf("🔍 Parsed Args Content:")
	for key, value := range args {
		log.Printf("🔍   %s: %v (type: %T)", key, value, value)
	}

	// 输出上下文信息
	log.Printf("🔍 Context Values:")
	if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
		log.Printf("🔍   authorization_token: [REDACTED] (type: %T)", tokenValue)
	} else {
		log.Printf("🔍   authorization_token: <nil>")
	}

	// 检查其他可能的上下文键
	contextKeys := []string{"task_id", "taskId", "user_id", "userId", "auth_token", "bearer_token"}
	for _, key := range contextKeys {
		if value := ctx.Value(key); value != nil {
			log.Printf("🔍   %s: %v (type: %T)", key, value, value)
		}
	}

	log.Printf("🔍 === END DEBUG INFO ===")

	// Check if file_url is a DStaff file path
	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	// If it's a regular HTTP URL, use the normal handler
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		log.Printf("🔍 Regular HTTP URL detected, using normal handler: %s", fileURL)
		return w.toolHandlers.UploadFile(args)
	}

	// This is a DStaff file path, we need to download it first
	log.Printf("🔍 DStaff file path detected: %s", fileURL)

	// Extract authentication context
	taskID, token, err := w.extractDStaffAuthFromContext(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to extract DStaff authentication: %w", err)
	}

	if taskID == "" || token == "" {
		return nil, fmt.Errorf("DStaff file path requires task_id and authentication token")
	}

	// Download file from DStaff
	localFilePath, err := w.downloadFileFromDStaff(fileURL, taskID, token)
	if err != nil {
		return nil, fmt.Errorf("failed to download file from DStaff: %w", err)
	}

	// Update args with local file path
	args["file_url"] = "file://" + localFilePath
	log.Printf("✅ DStaff file downloaded, using local path: %s", args["file_url"])

	// Call the normal handler with the local file path
	return w.toolHandlers.UploadFile(args)
}

// handleUploadAndMonitorWithDStaff handles upload_and_monitor tool with DStaff file download support
func (w *MCPServerWrapper) handleUploadAndMonitorWithDStaff(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	// Check if file_url is a DStaff file path
	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	// If it's a regular HTTP URL, check if DStaff integration is enabled for output upload
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// Call the normal handler first
		response, err := w.toolHandlers.UploadAndMonitor(args)
		if err != nil {
			return nil, err
		}

		// If DStaff is enabled, upload the result files to DStaff
		if w.config.DStaff.Enabled {
			return w.handleUploadAndMonitorDStaffUpload(ctx, request, response, args)
		}

		return response, nil
	}

	// This is a DStaff file path, we need to download it first
	log.Printf("🔍 DStaff file path detected: %s", fileURL)

	// Extract authentication context
	taskID, token, err := w.extractDStaffAuthFromContext(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to extract DStaff authentication: %w", err)
	}

	if taskID == "" || token == "" {
		return nil, fmt.Errorf("DStaff file path requires task_id and authentication token")
	}

	// Download file from DStaff
	localFilePath, err := w.downloadFileFromDStaff(fileURL, taskID, token)
	if err != nil {
		return nil, fmt.Errorf("failed to download file from DStaff: %w", err)
	}

	// Update args with local file path
	args["file_url"] = "file://" + localFilePath
	log.Printf("✅ DStaff file downloaded, using local path: %s", args["file_url"])

	// Call the normal handler with the local file path
	response, err := w.toolHandlers.UploadAndMonitor(args)
	if err != nil {
		return nil, err
	}

	// If DStaff is enabled, upload the result files to DStaff
	if w.config.DStaff.Enabled {
		return w.handleUploadAndMonitorDStaffUpload(ctx, request, response, args)
	}

	return response, nil
}

// extractDStaffAuthFromContext extracts task_id and token from context and request
func (w *MCPServerWrapper) extractDStaffAuthFromContext(ctx context.Context, request mcp.CallToolRequest) (taskID, token string, err error) {
	// Debug: Log the entire request structure
	log.Printf("🔍 === EXTRACT_DSTAFF_AUTH DEBUG INFO ===")
	log.Printf("🔍 Request Method: %s", request.Method)
	log.Printf("🔍 Tool Name: %s", request.Params.Name)
	log.Printf("🔍 Arguments Type: %T", request.Params.Arguments)

	// 尝试将参数转换为 JSON 字符串以便更好地查看结构
	if argsBytes, err := json.Marshal(request.Params.Arguments); err == nil {
		log.Printf("🔍 Arguments JSON: %s", string(argsBytes))
	} else {
		log.Printf("🔍 Arguments (failed to marshal): %+v", request.Params.Arguments)
	}

	// Extract task_id from Context parameter in arguments
	if args, ok := request.Params.Arguments.(map[string]interface{}); ok {
		log.Printf("🔍 Arguments successfully cast to map[string]interface{}")
		log.Printf("🔍 Available argument keys: %v", func() []string {
			keys := make([]string, 0, len(args))
			for k := range args {
				keys = append(keys, k)
			}
			return keys
		}())

		// Try multiple context parameter names (case variations)
		contextKeys := []string{"Context", "context", "CONTEXT"}
		var contextMap map[string]interface{}
		var contextFound bool

		for _, key := range contextKeys {
			if contextParam, exists := args[key]; exists {
				log.Printf("🔍 Found context parameter with key: %s", key)
				if cm, ok := contextParam.(map[string]interface{}); ok {
					contextMap = cm
					contextFound = true
					break
				} else {
					log.Printf("⚠️ Context parameter %s is not a map: %T", key, contextParam)
				}
			}
		}

		if contextFound {
			log.Printf("🔍 Context map contents: %v", contextMap)

			// Try multiple task_id parameter names
			taskIDKeys := []string{"task_id", "taskId", "task-id", "TASK_ID"}
			for _, key := range taskIDKeys {
				if taskIDValue, exists := contextMap[key]; exists {
					log.Printf("🔍 Found task_id with key: %s, value: %v", key, taskIDValue)
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
						log.Printf("✅ Extracted task_id: %s", taskID)
						break
					} else {
						log.Printf("⚠️ task_id value is not a string: %T", taskIDValue)
					}
				}
			}
		} else {
			log.Printf("⚠️ No context parameter found in arguments")
		}

		// Also check for task_id directly in args (fallback)
		if taskID == "" {
			taskIDKeys := []string{"task_id", "taskId", "task-id", "TASK_ID"}
			for _, key := range taskIDKeys {
				if taskIDValue, exists := args[key]; exists {
					log.Printf("🔍 Found task_id directly in args with key: %s, value: %v", key, taskIDValue)
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
						log.Printf("✅ Extracted task_id from args: %s", taskID)
						break
					}
				}
			}
		}
	} else {
		log.Printf("⚠️ Request arguments is not a map: %T", request.Params.Arguments)
	}

	// Extract token from context (set by authentication middleware)
	if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
		if tokenStr, ok := tokenValue.(string); ok {
			token = tokenStr
			log.Printf("✅ Extracted token from context")
		}
	} else {
		log.Printf("⚠️ No authorization_token found in context")
	}

	// Also try to extract token from request headers or other sources
	if token == "" {
		// For now, we'll use a placeholder token for testing
		// In production, this should come from proper authentication
		token = "placeholder_token"
		log.Printf("⚠️ Using placeholder token for testing")
	}

	if taskID == "" {
		log.Printf("❌ task_id extraction failed. Available args: %v", request.Params.Arguments)
		return "", "", fmt.Errorf("task_id is required for DStaff file operations. Please provide task_id in Context parameter")
	}

	if token == "" {
		return "", "", fmt.Errorf("authentication token is required for DStaff file operations")
	}

	log.Printf("✅ Successfully extracted DStaff auth - task_id: %s, token: [REDACTED]", taskID)
	return taskID, token, nil
}

// downloadFileFromDStaff downloads a file from DStaff platform
func (w *MCPServerWrapper) downloadFileFromDStaff(filePath, taskID, token string) (string, error) {
	if !w.config.DStaff.Enabled {
		return "", fmt.Errorf("DStaff integration is not enabled")
	}

	// Use the tool handler's download method
	return w.toolHandlers.DownloadFileFromDStaff(filePath, taskID, token)
}

// handleGetDownloadURLWithDStaff handles get_download_url with DStaff integration
func (w *MCPServerWrapper) handleGetDownloadURLWithDStaff(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	// First get the basic download information
	response, err := w.toolHandlers.GetDownloadURL(args)
	if err != nil {
		return nil, err
	}

	// Check if DStaff is enabled and we have Context with task_id
	if w.config.DStaff != nil && w.config.DStaff.Enabled {
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskID, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskID.(string); ok {
						log.Printf("🔄 DStaff enabled, downloading and uploading files for task: %s", taskIDStr)

						// Extract token from context with detailed debugging
						token := ""
						log.Printf("🔍 Checking context for authorization token...")

						// Check all possible context keys
						contextKeys := []string{"authorization_token", "Authorization", "token", "auth_token"}
						for _, key := range contextKeys {
							if authToken := ctx.Value(key); authToken != nil {
								if tokenStr, ok := authToken.(string); ok && tokenStr != "" {
									token = tokenStr
									log.Printf("🔐 Found token with key '%s': FULL_TOKEN=%s (length: %d)", key, token, len(token))
									break
								} else {
									log.Printf("🔍 Found value for key '%s' but not a valid string: %T", key, authToken)
								}
							}
						}

						if token == "" {
							log.Printf("⚠️ No authorization token found in context with any of the keys: %v", contextKeys)
							log.Printf("⚠️ Available context keys:")
							// This is a debug approach - in production you wouldn't iterate over context like this
							log.Printf("⚠️ Context type: %T", ctx)
							log.Printf("⚠️ Skipping DStaff upload due to missing token")
							return response, nil
						}

						// Validate token format
						if !strings.HasPrefix(token, "Bearer ") && !strings.Contains(token, "eyJ") {
							log.Printf("⚠️ Token format seems invalid (doesn't start with 'Bearer ' or contain JWT pattern): FULL_TOKEN=%s", token)
						} else {
							log.Printf("✅ Token format looks valid: starts_with_Bearer=%v, contains_JWT_pattern=%v", strings.HasPrefix(token, "Bearer "), strings.Contains(token, "eyJ"))
						}

						// Get ppt_id from args or task binding
						pptID, _ := args["ppt_id"].(string)
						if pptID == "" {
							if w.toolHandlers.GetTaskBindingManager() != nil {
								if boundProjectID, err := w.toolHandlers.GetTaskBindingManager().GetProjectID(taskIDStr); err == nil {
									pptID = boundProjectID
								}
							}
						}

						if pptID == "" {
							log.Printf("⚠️ No ppt_id found, skipping DStaff upload")
							return response, nil
						}

						// Download and upload files to DStaff
						dstaffResponse, err := w.downloadAndUploadToDStaffWithAuth(response, taskIDStr, pptID, token)
						if err != nil {
							log.Printf("⚠️ Failed to upload to DStaff: %v", err)
							// Return original response if DStaff upload fails
							return response, nil
						}

						log.Printf("✅ Successfully uploaded files to DStaff")
						return dstaffResponse, nil
					}
				}
			}
		}
	}

	return response, nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// downloadAndUploadToDStaffWithAuth downloads files from PPT Narrator and uploads them to DStaff with authentication
func (w *MCPServerWrapper) downloadAndUploadToDStaffWithAuth(downloadInfo map[string]interface{}, taskID, pptID, token string) (map[string]interface{}, error) {
	log.Printf("🔄 Starting download and upload to DStaff for task: %s, project: %s", taskID, pptID)
	log.Printf("🔐 Received token parameter: FULL_RECEIVED_TOKEN=%s (length: %d)", token, len(token))

	// Extract download information
	data, ok := downloadInfo["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid download info format")
	}

	downloads, ok := data["downloads"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("no downloads available")
	}

	projectName, _ := data["project_name"].(string)
	if projectName == "" {
		projectName = pptID
	}

	// Create DStaff auth context with the extracted token
	authCtx := &auth.DStaffAuthContext{
		TaskID: taskID,
		Token:  token,
	}

	log.Printf("🔐 Created auth context with token: FULL_AUTH_CTX_TOKEN=%s (length: %d)", authCtx.Token, len(authCtx.Token))

	// Download and upload only video file as requested by user
	fileTypes := []string{"video"}

	for _, fileType := range fileTypes {
		if fileInfo, exists := downloads[fileType]; exists {
			if fileMap, ok := fileInfo.(map[string]interface{}); ok {
				if available, ok := fileMap["available"].(bool); ok && available {
					if downloadURL, ok := fileMap["url"].(string); ok {
						log.Printf("📥 Downloading %s file from: %s", fileType, downloadURL)

						// Download file from PPT Narrator
						localFilePath, err := w.downloadFileFromPPTNarrator(downloadURL, fileType, projectName)
						if err != nil {
							log.Printf("❌ Failed to download %s file: %v", fileType, err)
							continue
						}

						// Upload to DStaff (path must start with mcps_upload/)
						// Use filename directly since it already contains project name
						targetPath := fmt.Sprintf("mcps_upload/%s", filepath.Base(localFilePath))
						uploadResp, err := auth.UploadFileToDStaff(w.config.DStaff, authCtx, localFilePath, targetPath)
						if err != nil {
							log.Printf("❌ Failed to upload %s file to DStaff: %v", fileType, err)
							// Clean up local file
							os.Remove(localFilePath)
							continue
						}

						log.Printf("✅ Successfully uploaded %s file to DStaff: %s", fileType, targetPath)

						// Clean up local file
						os.Remove(localFilePath)

						// Convert DStaff upload response to map[string]interface{}
						log.Printf("🎯 Converting DStaff upload response to map format")

						// Convert struct to map using JSON marshaling/unmarshaling
						responseBytes, err := json.Marshal(uploadResp)
						if err != nil {
							log.Printf("❌ Failed to marshal upload response: %v", err)
							return nil, fmt.Errorf("failed to marshal upload response: %w", err)
						}

						var responseMap map[string]interface{}
						err = json.Unmarshal(responseBytes, &responseMap)
						if err != nil {
							log.Printf("❌ Failed to unmarshal upload response: %v", err)
							return nil, fmt.Errorf("failed to unmarshal upload response: %w", err)
						}

						log.Printf("🎯 Returning DStaff upload response directly")
						return responseMap, nil
					}
				}
			}
		}
	}

	// If we reach here, no files were successfully uploaded
	return nil, fmt.Errorf("no files were successfully uploaded to DStaff")
}

// downloadFileFromPPTNarrator downloads a file from PPT Narrator backend
func (w *MCPServerWrapper) downloadFileFromPPTNarrator(downloadURL, fileType, projectName string) (string, error) {
	log.Printf("📥 Downloading %s file from PPT Narrator: %s", fileType, downloadURL)

	// Create full URL
	pptNarratorURL := "http://ppt-narrator:8080" // Default PPT Narrator URL
	if w.config.PPTNarratorURL != "" {
		pptNarratorURL = w.config.PPTNarratorURL
	}
	fullURL := pptNarratorURL + downloadURL

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 5 * time.Minute, // Files might be large
	}

	// Create request
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("download failed with status %d", resp.StatusCode)
	}

	// Create temporary file
	tempDir := os.TempDir()

	// Determine file extension based on type
	var ext string
	switch fileType {
	case "video":
		ext = ".mp4"
	case "audio":
		ext = ".mp3"
	case "screenshots":
		ext = ".zip"
	case "narration":
		ext = ".txt"
	case "complete":
		ext = ".zip" // Complete package is usually a zip file
	default:
		ext = ".bin"
	}

	// Generate unique filename
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s_%s%s", projectName, fileType, timestamp, ext)
	localFilePath := filepath.Join(tempDir, filename)

	// Create local file
	localFile, err := os.Create(localFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()

	// Copy response body to local file
	_, err = io.Copy(localFile, resp.Body)
	if err != nil {
		os.Remove(localFilePath) // Clean up on error
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("✅ Successfully downloaded %s file to: %s", fileType, localFilePath)
	return localFilePath, nil
}

// handleUploadAndMonitorDStaffUpload handles uploading result files to DStaff after upload_and_monitor completes
func (w *MCPServerWrapper) handleUploadAndMonitorDStaffUpload(ctx context.Context, request mcp.CallToolRequest, response map[string]interface{}, args map[string]interface{}) (map[string]interface{}, error) {
	log.Printf("🔄 Starting DStaff upload for upload_and_monitor result")

	// Check if the response indicates completion with download info
	if success, ok := response["success"].(bool); !ok || !success {
		log.Printf("⚠️ Response not successful, skipping DStaff upload")
		return response, nil
	}

	// Check if download info is available
	downloadInfo, hasDownload := response["download"]
	if !hasDownload {
		log.Printf("⚠️ No download info in response, skipping DStaff upload")
		return response, nil
	}

	// Extract authentication context
	taskID, token, err := w.extractDStaffAuthFromContext(ctx, request)
	if err != nil {
		log.Printf("⚠️ Failed to extract DStaff authentication: %v", err)
		return response, nil
	}

	if taskID == "" || token == "" {
		log.Printf("⚠️ No DStaff authentication available, skipping upload")
		return response, nil
	}

	// Get project ID from response
	pptID, _ := response["ppt_id"].(string)
	if pptID == "" {
		log.Printf("⚠️ No ppt_id in response, skipping DStaff upload")
		return response, nil
	}

	// Create a download info structure compatible with downloadAndUploadToDStaffWithAuth
	downloadInfoMap := map[string]interface{}{
		"data": downloadInfo,
	}

	// Upload files to DStaff
	dstaffResponse, err := w.downloadAndUploadToDStaffWithAuth(downloadInfoMap, taskID, pptID, token)
	if err != nil {
		log.Printf("⚠️ Failed to upload to DStaff: %v", err)
		// Return original response if DStaff upload fails
		return response, nil
	}

	log.Printf("✅ Successfully uploaded upload_and_monitor result files to DStaff")
	return dstaffResponse, nil
}
