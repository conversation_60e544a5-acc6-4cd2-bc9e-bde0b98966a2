package auth

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"ppt-narrator-mcp/internal/config"
)

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// DStaffAuthContext holds authentication context for DStaff requests
type DStaffAuthContext struct {
	Token  string
	TaskID string
}

// FileUploadResponse represents the response format for dstaff file uploads
type FileUploadResponse struct {
	WorkType    string       `json:"work_type"`
	Compression bool         `json:"compression"`
	Status      string       `json:"status"`
	Message     string       `json:"message"`
	Attachments []Attachment `json:"attachments"`
}

// Attachment represents a file attachment in the response
type Attachment struct {
	Path          string `json:"path"`
	Filename      string `json:"filename"`
	Type          string `json:"type"`
	ContentType   string `json:"content_type"`
	ContentLength int64  `json:"content_length"`
}

// ValidateTokenWithDStaff validates token using dstaff official auth service
func ValidateTokenWithDStaff(config *config.DStaffConfig, token string) bool {
	log.Printf("=== DStaff Token Validation ===")
	log.Printf("Config enabled: %v, UseOfficialAuth: %v", config.Enabled, config.UseOfficialAuth)
	log.Printf("Token to validate: '%s' (length: %d)", token, len(token))
	log.Printf("Auth service URL: %s", config.AuthServiceURL)

	if !config.Enabled || !config.UseOfficialAuth {
		log.Printf("DStaff validation skipped: config not enabled or not using official auth")
		return false
	}

	if token == "" {
		log.Printf("DStaff validation failed: empty token")
		return false
	}

	// Create HTTP request (GET method with Authorization header)
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", config.AuthServiceURL, nil)
	if err != nil {
		log.Printf("DStaff validation failed: failed to create request: %v", err)
		return false
	}

	// Set Authorization header with Bearer token
	authHeaderValue := "Bearer " + token
	req.Header.Set("Authorization", authHeaderValue)

	log.Printf("Sending validation request with Authorization header: '%s'", authHeaderValue)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("DStaff validation failed: request error: %v", err)
		return false
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("DStaff validation failed: failed to read response: %v", err)
		return false
	}

	log.Printf("Validation response (HTTP %d): %s", resp.StatusCode, string(respBody))

	if resp.StatusCode == 200 {
		var result map[string]interface{}
		if err := json.Unmarshal(respBody, &result); err == nil {
			log.Printf("Parsed validation response: %+v", result)
			if data, ok := result["data"].(bool); ok && data {
				log.Printf("✅ Token validation successful for token: '%s'", token)
				return true
			} else {
				log.Printf("❌ Token validation failed: data field is %v (type: %T)", result["data"], result["data"])
			}
		} else {
			log.Printf("❌ Failed to parse validation response JSON: %v", err)
		}
	}

	log.Printf("❌ Token validation failed for token '%s': HTTP %d", token, resp.StatusCode)
	return false
}

// ExtractTaskIDFromContext extracts task ID from HTTP request context
func ExtractTaskIDFromContext(ctx context.Context, r *http.Request) string {
	// Try to extract from query parameters first
	if taskID := r.URL.Query().Get("task_id"); taskID != "" {
		log.Printf("🔍 Task ID extracted from query: %s", taskID)
		return taskID
	}

	// Try to extract from headers
	if taskID := r.Header.Get("X-Task-ID"); taskID != "" {
		log.Printf("🔍 Task ID extracted from header: %s", taskID)
		return taskID
	}

	// Try to extract from context
	if taskID := ctx.Value("dstaff_task_id"); taskID != nil {
		if taskIDStr, ok := taskID.(string); ok {
			log.Printf("🔍 Task ID extracted from context: %s", taskIDStr)
			return taskIDStr
		}
	}

	log.Printf("🔍 No task ID found in request")
	return ""
}

// UploadFileToDStaff uploads a file to dstaff platform
func UploadFileToDStaff(config *config.DStaffConfig, authCtx *DStaffAuthContext, filePath string, targetPath string) (*FileUploadResponse, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// Read file content
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Encode file content to base64
	fileBase64 := base64.StdEncoding.EncodeToString(fileContent)

	// Prepare request payload
	payload := map[string]interface{}{
		"file":     fileBase64,
		"taskId":   authCtx.TaskID,
		"filePath": targetPath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileUploadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.Printf("=== DStaff File Upload Request ===")
	log.Printf("📍 Request URL: %s", config.FileUploadURL)
	log.Printf("🔑 Request Headers:")
	log.Printf("   Authorization: Bearer FULL_SEND_TOKEN=%s", authCtx.Token)
	log.Printf("   Content-Type: application/json")
	log.Printf("📦 Request Body (first 500 chars): %s", string(payloadJSON)[:min(len(payloadJSON), 500)])
	if len(payloadJSON) > 500 {
		log.Printf("   ... (truncated, total size: %d bytes)", len(payloadJSON))
	}

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ Request failed: %v", err)
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("⚠️ Failed to read response body: %v", readErr)
		respBody = []byte("failed to read response")
	}

	// Log response details
	log.Printf("📥 Response Status: %d %s", resp.StatusCode, resp.Status)
	log.Printf("📥 Response Headers:")
	for key, values := range resp.Header {
		for _, value := range values {
			log.Printf("   %s: %s", key, value)
		}
	}
	log.Printf("📥 Response Body: %s", string(respBody))

	if resp.StatusCode != 200 {
		log.Printf("❌ Upload failed with status %d", resp.StatusCode)
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	log.Printf("✅ Upload request completed successfully")

	// Try to parse DStaff response to get actual file information
	var dstaffResponse map[string]interface{}
	actualFilePath := targetPath
	actualFilename := filepath.Base(targetPath)

	if json.Unmarshal(respBody, &dstaffResponse) == nil {
		log.Printf("📋 Parsed DStaff response successfully")

		// Check if DStaff returned actual file path/name
		if filePath, exists := dstaffResponse["filePath"]; exists {
			if filePathStr, ok := filePath.(string); ok && filePathStr != "" {
				actualFilePath = filePathStr
				actualFilename = filepath.Base(filePathStr)
				log.Printf("📁 Using actual file path from DStaff: %s", actualFilePath)
			}
		}

		// Check for filename field
		if filename, exists := dstaffResponse["filename"]; exists {
			if filenameStr, ok := filename.(string); ok && filenameStr != "" {
				actualFilename = filenameStr
				log.Printf("📄 Using actual filename from DStaff: %s", actualFilename)
			}
		}
	} else {
		log.Printf("⚠️ Could not parse DStaff response as JSON, using original path")
	}

	// Determine content type
	contentType := "application/octet-stream"
	ext := strings.ToLower(filepath.Ext(actualFilePath))
	switch ext {
	case ".png":
		contentType = "image/png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".gif":
		contentType = "image/gif"
	case ".pdf":
		contentType = "application/pdf"
	case ".txt":
		contentType = "text/plain"
	case ".html":
		contentType = "text/html"
	case ".json":
		contentType = "application/json"
	}

	// Create successful response with actual file information
	response := &FileUploadResponse{
		WorkType:    "mcp_tool",
		Compression: false,
		Status:      "success",
		Message:     fmt.Sprintf("文件上传成功！上传的文件路径：%s", actualFilePath),
		Attachments: []Attachment{
			{
				Path:          actualFilePath,
				Filename:      actualFilename,
				Type:          "file",
				ContentType:   contentType,
				ContentLength: int64(len(fileContent)),
			},
		},
	}

	// Log final response
	responseJSON, _ := json.MarshalIndent(response, "", "  ")
	log.Printf("📤 Final Upload Response:")
	log.Printf("%s", string(responseJSON))
	log.Printf("=== DStaff File Upload Complete ===")

	log.Printf("File uploaded successfully to dstaff: %s", targetPath)
	return response, nil
}

// DownloadFileFromDStaff downloads a file from dstaff platform
func DownloadFileFromDStaff(config *config.DStaffConfig, authCtx *DStaffAuthContext, filePath string) (string, error) {
	if !config.Enabled {
		return "", fmt.Errorf("dstaff integration not enabled")
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   authCtx.TaskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.Printf("=== DStaff File Download Request ===")
	log.Printf("📍 Request URL: %s", config.FileDownloadURL)
	log.Printf("🔑 Request Headers:")
	log.Printf("   Authorization: Bearer %s", authCtx.Token[:min(len(authCtx.Token), 20)]+"...")
	log.Printf("   Content-Type: application/json")
	log.Printf("📦 Request Body: %s", string(payloadJSON))
	log.Printf("Downloading file from DStaff: taskId=%s, filePath=%s", authCtx.TaskID, filePath)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("❌ Download request failed: %v", err)
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("⚠️ Failed to read download response body: %v", readErr)
		return "", fmt.Errorf("failed to read response: %w", readErr)
	}

	// Log response details
	log.Printf("📥 Download Response Status: %d %s", resp.StatusCode, resp.Status)
	log.Printf("📥 Download Response Headers:")
	for key, values := range resp.Header {
		for _, value := range values {
			log.Printf("   %s: %s", key, value)
		}
	}
	log.Printf("📥 Download Response Body (first 200 chars): %s", string(respBody)[:min(len(respBody), 200)])
	if len(respBody) > 200 {
		log.Printf("   ... (truncated, total size: %d bytes)", len(respBody))
	}

	if resp.StatusCode != 200 {
		log.Printf("❌ Download failed with status %d", resp.StatusCode)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return "", fmt.Errorf("download failed: %v", errorResp)
		}
		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	log.Printf("✅ Download request completed successfully")

	// Try to parse as JSON response first
	var downloadResp map[string]interface{}
	if json.Unmarshal(respBody, &downloadResp) == nil {
		if fileContent, ok := downloadResp["file"].(string); ok {
			log.Printf("📤 Downloaded file content (base64, first 100 chars): %s", fileContent[:min(len(fileContent), 100)])
			if len(fileContent) > 100 {
				log.Printf("   ... (truncated, total size: %d chars)", len(fileContent))
			}
			log.Printf("=== DStaff File Download Complete ===")
			return fileContent, nil
		}
	}

	// If not JSON, assume the response body is the base64 encoded file content
	fileContent := string(respBody)
	log.Printf("📤 Downloaded file content (base64, first 100 chars): %s", fileContent[:min(len(fileContent), 100)])
	if len(fileContent) > 100 {
		log.Printf("   ... (truncated, total size: %d chars)", len(fileContent))
	}
	log.Printf("=== DStaff File Download Complete ===")
	return fileContent, nil
}
