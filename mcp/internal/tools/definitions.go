package tools

import (
	"github.com/mark3labs/mcp-go/mcp"
	"ppt-narrator-mcp/internal/config"
)

// GetAllTools returns all available tools for the PPT Narrator MCP server
func GetAllTools(cfg *config.Config) []mcp.Tool {
	// Base tools without Context parameter
	baseTools := []mcp.Tool{
		// Upload file and start pipeline tool
		mcp.NewTool("upload_file",
			mcp.WithDescription("上传PPT文件并开始生成解说视频"),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("PPT文件名称"),
			),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("PPT文件的下载URL地址"),
			),
			mcp.WithString("user_requirements",
				mcp.Description("用户需求描述（可选）"),
			),
			mcp.WithBoolean("enable_subtitles",
				mcp.Description("是否启用字幕（可选，默认为false）"),
			),
			mcp.WithString("subtitle_style_template",
				mcp.Description("字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal"),
			),
		),

		// Get progress tool
		mcp.NewTool("get_progress",
			mcp.WithDescription("查询PPT解说视频生成进度"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
		),

		// Get download URL tool
		mcp.NewTool("get_download_url",
			mcp.WithDescription("获取生成的PPT解说视频下载链接"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
		),

		// Retry project tool
		mcp.NewTool("retry_project",
			mcp.WithDescription("重试失败的PPT解说视频生成项目"),
			mcp.WithString("stage",
				mcp.Required(),
				mcp.Description("重试的阶段：screenshot, narration, audio, video"),
			),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
		),

		// Upload and monitor tool
		mcp.NewTool("upload_and_monitor",
			mcp.WithDescription("上传PPT文件并监控整个生成过程直到完成"),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("PPT文件名称"),
			),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("PPT文件的下载URL地址"),
			),
			mcp.WithString("user_requirements",
				mcp.Description("用户需求描述（可选）"),
			),
			mcp.WithBoolean("enable_subtitles",
				mcp.Description("是否启用字幕（可选，默认为false）"),
			),
			mcp.WithString("subtitle_style_template",
				mcp.Description("字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal"),
			),
		),
	}

	// If DStaff is enabled, add Context parameter to all tools
	if cfg.DStaff != nil && cfg.DStaff.Enabled {
		tools := make([]mcp.Tool, len(baseTools))
		for i, baseTool := range baseTools {
			// Create new tool with Context parameter
			tools[i] = addContextParameterToTool(baseTool)
		}
		return tools
	}

	return baseTools
}

// addContextParameterToTool adds a Context parameter to an existing tool for DStaff integration
func addContextParameterToTool(baseTool mcp.Tool) mcp.Tool {
	// We need to recreate each tool with the Context parameter
	// Since we can't modify the existing tool, we'll recreate them based on their names

	switch baseTool.Name {
	case "upload_file":
		return mcp.NewTool("upload_file",
			mcp.WithDescription("上传PPT文件并开始生成解说视频"),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("PPT文件名称"),
			),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("PPT文件的下载URL地址"),
			),
			mcp.WithString("user_requirements",
				mcp.Description("用户需求描述（可选）"),
			),
			mcp.WithBoolean("enable_subtitles",
				mcp.Description("是否启用字幕（可选，默认为false）"),
			),
			mcp.WithString("subtitle_style_template",
				mcp.Description("字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)

	case "get_progress":
		return mcp.NewTool("get_progress",
			mcp.WithDescription("查询PPT解说视频生成进度"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)

	case "get_download_url":
		return mcp.NewTool("get_download_url",
			mcp.WithDescription("获取生成的PPT解说视频下载链接"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)

	case "retry_project":
		return mcp.NewTool("retry_project",
			mcp.WithDescription("重试失败的PPT解说视频生成项目"),
			mcp.WithString("stage",
				mcp.Required(),
				mcp.Description("重试的阶段：screenshot, narration, audio, video"),
			),
			mcp.WithString("ppt_id",
				mcp.Description("PPT项目ID（可选，如果未提供将从上下文获取）"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)

	case "upload_and_monitor":
		return mcp.NewTool("upload_and_monitor",
			mcp.WithDescription("上传PPT文件并监控整个生成过程直到完成"),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("PPT文件名称"),
			),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("PPT文件的下载URL地址"),
			),
			mcp.WithString("user_requirements",
				mcp.Description("用户需求描述（可选）"),
			),
			mcp.WithBoolean("enable_subtitles",
				mcp.Description("是否启用字幕（可选，默认为false）"),
			),
			mcp.WithString("subtitle_style_template",
				mcp.Description("字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)

	default:
		// Fallback: return the original tool
		return baseTool
	}
}
