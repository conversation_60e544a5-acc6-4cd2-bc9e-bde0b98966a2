package tools

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"ppt-narrator-mcp/internal/config"
	"ppt-narrator-mcp/internal/progress"
)

// TaskBindingManager interface for managing task-project bindings
type TaskBindingManager interface {
	SaveBinding(taskID, projectID string) error
	GetProjectID(taskID string) (string, error)
	DeleteBinding(taskID string) error
}

// PPTNarratorClient interface for PPT narrator operations
type PPTNarratorClient interface {
	UploadFileFromURL(fileURL, name, userRequirements string, enableSubtitles bool, subtitleStyleTemplate string) ([]byte, error)
	GetProgress(projectID string) ([]byte, error)
	GetDownloadURL(projectID string) ([]byte, error)
	RetryProject(projectID string) ([]byte, error)
}

// ToolHandlers contains all tool handler functions for PPT Narrator
type ToolHandlers struct {
	config             *config.Config
	progressSendFunc   func(notification *mcp.ProgressNotification) error
	logSendFunc        func(notification *mcp.LoggingMessageNotification) error
	taskManager        TaskManager          // Interface for task management
	taskExecutor       TaskExecutor         // Interface for task execution
	taskBindingManager TaskBindingManager   // Interface for task binding management
	pptClient          PPTNarratorClient    // PPT Narrator client
	dstaffConfig       *config.DStaffConfig // DStaff configuration
	pptNarratorURL     string               // PPT Narrator backend URL
	httpClient         *http.Client         // HTTP client for API calls
	logMessages        []string             // Log messages collection
	logMutex           sync.RWMutex         // Mutex for log messages
}

// TaskManager interface for task management operations
type TaskManager interface {
	// Task lifecycle methods
	CreateTask(toolName string, arguments map[string]interface{}, callbackURL string) (interface{}, error)
	RegisterExternalTask(taskID, toolName string, arguments map[string]interface{}) error
	GetTask(taskID string) (interface{}, error)
	GetTaskResult(taskID string) (interface{}, error)

	// Task status and progress methods
	UpdateTaskProgress(taskID string, current, total int, message string) error
	UpdateTaskStatus(taskID string, status interface{}) error
	SetTaskResult(taskID string, result interface{}) error
	SetTaskError(taskID string, err error) error
}

// TaskExecutor interface for task execution operations
type TaskExecutor interface {
	ExecuteTask(taskID string) error
}

// PPTNarratorResponse represents a standard response from PPT Narrator API
type PPTNarratorResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// NewToolHandlers creates a new ToolHandlers instance
func NewToolHandlers(cfg *config.Config, pptClient PPTNarratorClient, taskBindingManager TaskBindingManager) *ToolHandlers {
	// Get PPT Narrator URL from config or use default
	pptNarratorURL := "http://localhost:8080" // Default PPT Narrator URL
	if cfg.PPTNarratorURL != "" {
		pptNarratorURL = cfg.PPTNarratorURL
	}

	return &ToolHandlers{
		config:             cfg,
		pptClient:          pptClient,
		taskBindingManager: taskBindingManager,
		dstaffConfig:       cfg.DStaff, // Use config.DStaffConfig directly
		pptNarratorURL:     pptNarratorURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logMessages: make([]string, 0),
	}
}

// SetTaskManager sets the task manager for async operations
func (h *ToolHandlers) SetTaskManager(tm TaskManager) {
	h.taskManager = tm
}

// SetTaskExecutor sets the task executor for async operations
func (h *ToolHandlers) SetTaskExecutor(te TaskExecutor) {
	h.taskExecutor = te
}

// SetProgressSendFunc sets the function to send progress notifications
func (h *ToolHandlers) SetProgressSendFunc(sendFunc func(notification *mcp.ProgressNotification) error) {
	h.progressSendFunc = sendFunc
}

// SetLogSendFunc sets the function to send log notifications
func (h *ToolHandlers) SetLogSendFunc(sendFunc func(notification *mcp.LoggingMessageNotification) error) {
	h.logSendFunc = sendFunc
}

// AddLogMessage adds a log message to the collection
func (h *ToolHandlers) AddLogMessage(level, message string) {
	h.logMutex.Lock()
	defer h.logMutex.Unlock()

	logEntry := fmt.Sprintf("[%s] %s", strings.ToUpper(level), message)
	h.logMessages = append(h.logMessages, logEntry)

	// Keep only the last 100 messages to prevent memory issues
	if len(h.logMessages) > 100 {
		h.logMessages = h.logMessages[len(h.logMessages)-100:]
	}
}

// GetAndClearLogMessages returns all log messages and clears the collection
func (h *ToolHandlers) GetAndClearLogMessages() []string {
	h.logMutex.Lock()
	defer h.logMutex.Unlock()

	messages := make([]string, len(h.logMessages))
	copy(messages, h.logMessages)
	h.logMessages = h.logMessages[:0] // Clear the slice
	return messages
}

// GetPPTClient returns the PPT narrator client
func (h *ToolHandlers) GetPPTClient() PPTNarratorClient {
	return h.pptClient
}

// GetDStaffConfig returns the DStaff configuration
func (h *ToolHandlers) GetDStaffConfig() *config.DStaffConfig {
	return h.dstaffConfig
}

// GetTaskBindingManager returns the task binding manager
func (h *ToolHandlers) GetTaskBindingManager() TaskBindingManager {
	return h.taskBindingManager
}

// GetProgressSendFunc returns the progress send function
func (h *ToolHandlers) GetProgressSendFunc() func(notification *mcp.ProgressNotification) error {
	return h.progressSendFunc
}

// GetLogSendFunc returns the log send function
func (h *ToolHandlers) GetLogSendFunc() func(notification *mcp.LoggingMessageNotification) error {
	return h.logSendFunc
}

// UploadFile handles PPT file upload and starts pipeline
func (h *ToolHandlers) UploadFile(args map[string]interface{}) (map[string]interface{}, error) {
	name, ok := args["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("name parameter is required")
	}

	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	userRequirements, _ := args["user_requirements"].(string)
	enableSubtitles, _ := args["enable_subtitles"].(bool)
	subtitleStyleTemplate, _ := args["subtitle_style_template"].(string)

	log.Printf("upload_file: Starting upload for file '%s', URL: %s", name, fileURL)

	// Check if fileURL is a relative path (DStaff file path)
	var actualFileURL string
	var relativeFilePath string

	// Check if it's a full HTTP/HTTPS URL
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// This is a regular HTTP URL
		actualFileURL = fileURL
		log.Printf("upload_file: Using HTTP URL: %s", actualFileURL)
	} else if strings.HasPrefix(fileURL, "file://") {
		// Extract relative path from file:// URL
		relativeFilePath = strings.TrimPrefix(fileURL, "file://")
		// Remove leading slash if present
		relativeFilePath = strings.TrimPrefix(relativeFilePath, "/")
		log.Printf("upload_file: Extracted relative path from file:// URL: %s", relativeFilePath)
	} else {
		// This is a relative path without protocol
		relativeFilePath = fileURL
		log.Printf("upload_file: Treating as relative path: %s", relativeFilePath)
	}

	// Handle relative paths (download from DStaff platform)
	if relativeFilePath != "" {
		// Extract task_id from Context parameter
		var taskID string
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}

		if taskID == "" {
			log.Printf("upload_file: task_id is required for downloading files from DStaff platform")
			return nil, fmt.Errorf("task_id is required for downloading files from DStaff platform")
		}

		// For DStaff file paths, we need to download the file first
		log.Printf("upload_file: DStaff file path detected, attempting download: %s", relativeFilePath)

		// Note: This method doesn't have access to authentication context
		// The DStaff file download should be handled by the transport layer
		// For now, we'll try to use the file path directly and let the API handle it
		actualFileURL = fileURL
		log.Printf("upload_file: Using DStaff file path directly: %s", actualFileURL)
	}

	// Prepare request data
	requestData := map[string]interface{}{
		"name":     name,
		"file_url": actualFileURL,
	}

	if userRequirements != "" {
		requestData["user_requirements"] = userRequirements
	}
	if enableSubtitles {
		requestData["enable_subtitles"] = enableSubtitles
	}
	if subtitleStyleTemplate != "" {
		requestData["subtitle_style_template"] = subtitleStyleTemplate
	}

	// Call PPT Narrator API to upload file and start pipeline
	response, err := h.callPPTNarratorAPI("POST", "/api/v1/pipeline/upload-and-process", requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Save task binding if Context with task_id is provided
	if contextParam, exists := args["Context"]; exists {
		if contextMap, ok := contextParam.(map[string]interface{}); ok {
			if taskID, exists := contextMap["task_id"]; exists {
				if taskIDStr, ok := taskID.(string); ok {
					// Extract project ID from response for binding
					if data, ok := response["data"].(map[string]interface{}); ok {
						if projectID, ok := data["ppt_id"].(string); ok && h.taskBindingManager != nil {
							if err := h.taskBindingManager.SaveBinding(taskIDStr, projectID); err != nil {
								log.Printf("Failed to save task binding: %v", err)
							} else {
								log.Printf("upload_file: Task binding saved: %s -> %s", taskIDStr, projectID)
							}
						}
					}
				}
			}
		}
	}

	log.Printf("upload_file: Upload completed successfully")
	return response, nil
}

// UploadPPT handles PPT file upload and creates a project (legacy method)
func (h *ToolHandlers) UploadPPT(args map[string]interface{}) (map[string]interface{}, error) {
	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	projectName, ok := args["project_name"].(string)
	if !ok || projectName == "" {
		return nil, fmt.Errorf("project_name parameter is required")
	}

	description, _ := args["description"].(string)

	// Prepare request data for project upload
	requestData := map[string]interface{}{
		"name":        projectName,
		"description": description,
		"file_url":    fileURL,
	}

	// Call PPT Narrator API to upload PPT
	response, err := h.callPPTNarratorAPI("POST", "/api/v1/projects/upload", requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to upload PPT: %w", err)
	}

	// Extract project ID from response
	if data, ok := response["data"].(map[string]interface{}); ok {
		if projectID, ok := data["id"].(string); ok {
			// Save task binding if Context with task_id is provided
			if contextParam, exists := args["Context"]; exists {
				if contextMap, ok := contextParam.(map[string]interface{}); ok {
					if taskID, exists := contextMap["task_id"]; exists {
						if taskIDStr, ok := taskID.(string); ok && h.taskBindingManager != nil {
							if err := h.taskBindingManager.SaveBinding(taskIDStr, projectID); err != nil {
								log.Printf("Failed to save task binding: %v", err)
							}
						}
					}
				}
			}

			return map[string]interface{}{
				"success":    true,
				"project_id": projectID,
				"message":    "PPT文件上传成功，项目已创建",
				"data":       data,
			}, nil
		}
	}

	return response, nil
}

// GenerateNarration handles narration generation for a project
func (h *ToolHandlers) GenerateNarration(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, ok := args["project_id"].(string)
	if !ok || projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required")
	}

	userRequirements, _ := args["user_requirements"].(string)
	style, _ := args["style"].(string)
	language, _ := args["language"].(string)

	// Prepare request data for narration generation
	requestData := map[string]interface{}{
		"project_id": projectID,
	}

	if userRequirements != "" {
		requestData["user_requirements"] = userRequirements
	}
	if style != "" {
		requestData["style"] = style
	}
	if language != "" {
		requestData["language"] = language
	}

	// Call PPT Narrator API to generate narration
	response, err := h.callPPTNarratorAPI("POST", fmt.Sprintf("/api/v1/narration/%s/generate", projectID), requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to generate narration: %w", err)
	}

	return response, nil
}

// GenerateAudio handles audio generation for a project
func (h *ToolHandlers) GenerateAudio(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, ok := args["project_id"].(string)
	if !ok || projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required")
	}

	ttsProvider, _ := args["tts_provider"].(string)
	voice, _ := args["voice"].(string)
	speed, _ := args["speed"].(float64)

	// Prepare request data for audio generation
	requestData := map[string]interface{}{
		"project_id": projectID,
	}

	if ttsProvider != "" {
		requestData["tts_provider"] = ttsProvider
	}
	if voice != "" {
		requestData["voice"] = voice
	}
	if speed > 0 {
		requestData["speed"] = speed
	}

	// Call PPT Narrator API to generate audio
	response, err := h.callPPTNarratorAPI("POST", fmt.Sprintf("/api/v1/audio/%s/generate", projectID), requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to generate audio: %w", err)
	}

	return response, nil
}

// GenerateVideo handles video generation for a project
func (h *ToolHandlers) GenerateVideo(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, ok := args["project_id"].(string)
	if !ok || projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required")
	}

	outputFormat, _ := args["output_format"].(string)
	quality, _ := args["quality"].(string)
	fps, _ := args["fps"].(float64)
	resolution, _ := args["resolution"].(string)
	enableSubtitles, _ := args["enable_subtitles"].(bool)

	// Prepare request data for video generation
	requestData := map[string]interface{}{
		"project_id": projectID,
	}

	if outputFormat != "" {
		requestData["output_format"] = outputFormat
	}
	if quality != "" {
		requestData["quality"] = quality
	}
	if fps > 0 {
		requestData["fps"] = int(fps)
	}
	if resolution != "" {
		requestData["resolution"] = resolution
	}
	requestData["enable_subtitles"] = enableSubtitles

	// Call PPT Narrator API to generate video
	response, err := h.callPPTNarratorAPI("POST", fmt.Sprintf("/api/v1/video/%s/generate", projectID), requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to generate video: %w", err)
	}

	return response, nil
}

// GetProgress handles getting project progress
func (h *ToolHandlers) GetProgress(args map[string]interface{}) (map[string]interface{}, error) {
	// Try both ppt_id and project_id parameter names for compatibility
	projectID, _ := args["ppt_id"].(string)
	if projectID == "" {
		projectID, _ = args["project_id"].(string)
	}

	log.Printf("🔍 GetProgress: Received projectID: %s", projectID)
	log.Printf("🔍 GetProgress: Available args: %v", args)

	// If project_id is not provided, try to get it from task binding using Context
	if projectID == "" {
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskID, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskID.(string); ok && h.taskBindingManager != nil {
						log.Printf("🔍 GetProgress: Trying to get project ID from task binding for task: %s", taskIDStr)
						if boundProjectID, err := h.taskBindingManager.GetProjectID(taskIDStr); err == nil {
							projectID = boundProjectID
							log.Printf("✅ GetProgress: Found bound project ID: %s", projectID)
						} else {
							log.Printf("⚠️ GetProgress: No binding found for task: %s, error: %v", taskIDStr, err)
						}
					}
				}
			}
		}
	}

	if projectID == "" {
		return nil, fmt.Errorf("ppt_id parameter is required or must be available through task binding")
	}

	log.Printf("🚀 GetProgress: Calling API with project ID: %s", projectID)

	// Call PPT Narrator API to get pipeline progress
	response, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/pipeline/%s/progress", projectID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get progress: %w", err)
	}

	return response, nil
}

// DownloadVideo handles getting download URL for generated video
func (h *ToolHandlers) DownloadVideo(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, _ := args["project_id"].(string)

	// If project_id is not provided, try to get it from task binding using Context
	if projectID == "" {
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskID, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskID.(string); ok && h.taskBindingManager != nil {
						if boundProjectID, err := h.taskBindingManager.GetProjectID(taskIDStr); err == nil {
							projectID = boundProjectID
						}
					}
				}
			}
		}
	}

	if projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required or must be available through task binding")
	}

	// Call PPT Narrator API to get download info
	response, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/download/%s/info", projectID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get download URL: %w", err)
	}

	return response, nil
}

// callPPTNarratorAPI makes HTTP requests to PPT Narrator backend
func (h *ToolHandlers) callPPTNarratorAPI(method, endpoint string, data interface{}) (map[string]interface{}, error) {
	url := h.pptNarratorURL + endpoint

	var reqBody io.Reader
	var contentType string

	if data != nil {
		switch v := data.(type) {
		case map[string]string:
			// Form data
			formData := make(map[string]interface{})
			for k, val := range v {
				formData[k] = val
			}
			jsonData, err := json.Marshal(formData)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal form data: %w", err)
			}
			reqBody = bytes.NewBuffer(jsonData)
			contentType = "application/json"
		case map[string]interface{}:
			// Check if this is a file upload request (contains file_url)
			if fileURL, hasFileURL := v["file_url"].(string); hasFileURL && endpoint == "/api/v1/pipeline/upload-and-process" {
				// Handle file upload with multipart/form-data
				return h.callPPTNarratorAPIWithFileUpload(method, endpoint, v, fileURL, "", "")
			}

			// Regular JSON data
			jsonData, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal JSON data: %w", err)
			}
			reqBody = bytes.NewBuffer(jsonData)
			contentType = "application/json"
		}
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	resp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response body for debugging
	log.Printf("Response body: %s", string(body))

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if resp.StatusCode >= 400 {
		if errorMsg, ok := result["error"].(string); ok {
			return nil, fmt.Errorf("API error: %s", errorMsg)
		}
		return nil, fmt.Errorf("API error: status %d", resp.StatusCode)
	}

	return result, nil
}

// callPPTNarratorAPIWithAuth makes HTTP requests to PPT Narrator backend with authentication context
func (h *ToolHandlers) callPPTNarratorAPIWithAuth(method, endpoint string, data interface{}, taskID, token string) (map[string]interface{}, error) {
	url := h.pptNarratorURL + endpoint

	var reqBody io.Reader
	var contentType string

	if data != nil {
		switch v := data.(type) {
		case map[string]interface{}:
			// Check if this is a file upload request (contains file_url)
			if fileURL, hasFileURL := v["file_url"].(string); hasFileURL && endpoint == "/api/v1/pipeline/upload-and-process" {
				// Handle file upload with multipart/form-data and authentication context
				return h.callPPTNarratorAPIWithFileUpload(method, endpoint, v, fileURL, taskID, token)
			}
			// Regular JSON request
			jsonData, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal JSON data: %w", err)
			}
			reqBody = bytes.NewBuffer(jsonData)
			contentType = "application/json"
		default:
			return nil, fmt.Errorf("unsupported data type: %T", data)
		}
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	resp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response body for debugging
	log.Printf("Response body: %s", string(body))

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if resp.StatusCode >= 400 {
		if errorMsg, ok := result["error"].(string); ok {
			return nil, fmt.Errorf("API error: %s", errorMsg)
		}
		return nil, fmt.Errorf("API error: status %d", resp.StatusCode)
	}

	return result, nil
}

// callPPTNarratorAPIWithFileUpload handles file upload requests with multipart/form-data
func (h *ToolHandlers) callPPTNarratorAPIWithFileUpload(method, endpoint string, data map[string]interface{}, fileURL, taskID, token string) (map[string]interface{}, error) {
	var fileContent []byte
	var err error

	// Check if this is a DStaff file path or HTTP URL
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// Download file from HTTP URL
		resp, err := h.httpClient.Get(fileURL)
		if err != nil {
			return nil, fmt.Errorf("failed to download file from URL: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("failed to download file: HTTP %d", resp.StatusCode)
		}

		// Read file content
		fileContent, err = io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read file content: %w", err)
		}
	} else if strings.HasPrefix(fileURL, "file://") {
		// Handle local file:// URLs
		localPath := strings.TrimPrefix(fileURL, "file://")
		fileContent, err = os.ReadFile(localPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read local file '%s': %w", localPath, err)
		}
		log.Printf("Read local file: %s (%d bytes)", localPath, len(fileContent))
	} else {
		// This is a DStaff file path, use DStaff download
		if h.dstaffConfig == nil || !h.dstaffConfig.Enabled {
			return nil, fmt.Errorf("DStaff integration is not enabled. File path '%s' appears to be a DStaff file path, but DStaff integration is disabled. Please enable DStaff integration or provide an HTTP URL", fileURL)
		}

		if taskID == "" || token == "" {
			return nil, fmt.Errorf("DStaff file path '%s' requires task_id and authentication token", fileURL)
		}

		// Download file from DStaff platform
		localFilePath, err := h.downloadFileFromDStaff(fileURL, taskID, token)
		if err != nil {
			return nil, fmt.Errorf("failed to download file from DStaff: %w", err)
		}

		// Read the downloaded file
		fileContent, err = os.ReadFile(localFilePath)
		if err != nil {
			return nil, fmt.Errorf("failed to read downloaded file: %w", err)
		}

		log.Printf("Downloaded and read DStaff file: %s (%d bytes)", fileURL, len(fileContent))
	}

	// Extract filename from URL or use default
	filename := "presentation.pptx"
	if name, ok := data["name"].(string); ok && name != "" {
		filename = name + ".pptx"
	}

	// Create multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add file part
	fileWriter, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := fileWriter.Write(fileContent); err != nil {
		return nil, fmt.Errorf("failed to write file content: %w", err)
	}

	// Add other form fields
	if name, ok := data["name"].(string); ok {
		writer.WriteField("project_name", name)
	}
	if userReq, ok := data["user_requirements"].(string); ok {
		writer.WriteField("user_requirements", userReq)
	}
	if enableSubs, ok := data["enable_subtitles"].(bool); ok {
		writer.WriteField("enable_subtitles", fmt.Sprintf("%t", enableSubs))
	}
	if subStyle, ok := data["subtitle_style_template"].(string); ok {
		writer.WriteField("subtitle_style_template", subStyle)
	}

	writer.Close()

	// Make request
	url := h.pptNarratorURL + endpoint
	req, err := http.NewRequest(method, url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	httpResp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer httpResp.Body.Close()

	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response body for debugging
	log.Printf("Upload response body: %s", string(body))

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if httpResp.StatusCode >= 400 {
		if errorMsg, ok := result["error"].(string); ok {
			return nil, fmt.Errorf("API error: %s", errorMsg)
		}
		return nil, fmt.Errorf("API error: status %d", httpResp.StatusCode)
	}

	return result, nil
}

// monitorProgressUntilComplete polls the progress API until the project is complete
func (h *ToolHandlers) monitorProgressUntilComplete(pptID string, reporter *progress.Reporter, taskID ...string) (map[string]interface{}, error) {
	maxAttempts := 240 // Maximum 20 minutes (240 * 5 seconds)
	pollInterval := 5 * time.Second
	lastProgress := -1.0
	lastStage := ""

	log.Printf("🔍 Starting progress monitoring for project %s (max %d attempts, %v interval)", pptID, maxAttempts, pollInterval)

	// 阶段名称中文映射
	stageNameMap := map[string]string{
		"screenshot": "智能阅读中",
		"narration":  "生成解说中",
		"audio":      "合成音频中",
		"video":      "制作视频中",
		"completed":  "处理完成",
		"failed":     "处理失败",
	}

	// 使用 Reporter 上报开始监控的日志
	if reporter != nil {
		reporter.SendLogMessage("info", fmt.Sprintf("开始监控项目进度: %s", pptID))
	}

	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Get current progress
		progressResponse, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/pipeline/%s/progress", pptID), nil)
		if err != nil {
			log.Printf("❌ Failed to get progress (attempt %d/%d): %v", attempt+1, maxAttempts, err)
			// Don't fail immediately, continue monitoring
			time.Sleep(pollInterval)
			continue
		}

		// Check if we have progress data
		if progressData, ok := progressResponse["progress"].(map[string]interface{}); ok {
			// Extract status from current_stage (PPT Narrator uses current_stage instead of status)
			stage, _ := progressData["current_stage"].(string)
			progress, _ := progressData["progress"].(float64)
			message, _ := progressData["message"].(string)

			// Determine status based on stage
			var status string
			switch stage {
			case "completed":
				status = "completed"
			case "failed", "error":
				status = "failed"
			default:
				status = "processing"
			}

			// Log progress changes
			if progress != lastProgress || stage != lastStage {
				log.Printf("📊 Progress update: %s - %.1f%% (%s)", stage, progress, status)
				lastProgress = progress
				lastStage = stage
			}

			// Check if completed successfully
			if status == "completed" || status == "success" {
				log.Printf("✅ Project completed successfully: %s", pptID)

				// Get download info
				downloadResponse, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/download/%s/info", pptID), nil)
				if err != nil {
					log.Printf("⚠️ Project completed but failed to get download info: %v", err)
					// Return success without download info
					return map[string]interface{}{
						"success":  true,
						"status":   "completed",
						"ppt_id":   pptID,
						"progress": 100.0,
						"stage":    "completed",
						"message":  "PPT解说视频生成完成，但获取下载链接失败",
					}, nil
				}

				// 使用 Reporter 上报完成状态
				if reporter != nil {
					reporter.Complete("处理完成")
					reporter.SendLogMessage("info", "PPT解说视频生成完成，已获取下载信息")
				}

				// Send final progress notification
				if h.progressSendFunc != nil {
					total := 100.0
					message := "PPT解说视频生成完成"

					// Use task_id if provided, otherwise use project_id
					progressToken := pptID
					if len(taskID) > 0 && taskID[0] != "" {
						progressToken = taskID[0]
					}

					progressNotification := mcp.NewProgressNotification(
						progressToken,
						100.0,
						&total,
						&message,
					)
					h.progressSendFunc(&progressNotification)
				}

				// Return final result with download info
				return map[string]interface{}{
					"success":     true,
					"status":      "completed",
					"ppt_id":      pptID,
					"progress":    100.0,
					"stage":       "completed",
					"download":    downloadResponse["data"],
					"message":     "PPT解说视频生成完成",
					"final_stage": stage,
				}, nil
			}

			// Check if failed
			if status == "failed" || status == "error" {
				errorMsg, _ := progressData["error"].(string)
				if errorMsg == "" {
					errorMsg = message // Use message as fallback
				}
				if errorMsg == "" {
					errorMsg = "项目处理失败"
				}
				log.Printf("❌ Project failed: %s - %s", pptID, errorMsg)

				// 使用 Reporter 上报错误
				if reporter != nil {
					// 获取中文阶段名称
					chineseStage := stageNameMap[stage]
					if chineseStage == "" {
						chineseStage = stage
					}
					reporter.Error(fmt.Sprintf("处理失败: %s", errorMsg))
					reporter.SendLogMessage("error", fmt.Sprintf("项目在 %s 阶段失败: %s", chineseStage, errorMsg))
				}

				return map[string]interface{}{
					"success": false,
					"status":  "failed",
					"ppt_id":  pptID,
					"error":   errorMsg,
					"stage":   stage,
					"message": fmt.Sprintf("处理失败: %s", errorMsg),
				}, nil
			}

			// 使用 Reporter 上报进度和日志
			if reporter != nil {
				// 获取中文阶段名称
				chineseStage := stageNameMap[stage]
				if chineseStage == "" {
					chineseStage = stage // 如果没有映射，使用原始名称
				}

				progressMessage := chineseStage
				// 将进度百分比转换为步骤数（假设100步）
				currentStep := int(progress)
				if currentStep > 100 {
					currentStep = 100
				}
				reporter.ReportStep(currentStep, progressMessage)
				reporter.SendLogMessage("info", fmt.Sprintf("处理进度: %s - %.1f%%", chineseStage, progress))
			}

			// 保持原有的进度发送函数兼容性
			if h.progressSendFunc != nil {
				total := 100.0
				message := fmt.Sprintf("正在处理阶段: %s (%.1f%%)", stage, progress)

				// Use task_id if provided, otherwise use project_id
				progressToken := pptID
				if len(taskID) > 0 && taskID[0] != "" {
					progressToken = taskID[0]
				}

				progressNotification := mcp.NewProgressNotification(
					progressToken,
					progress,
					&total,
					&message,
				)
				h.progressSendFunc(&progressNotification)
			}

			// Add log message for current progress
			h.AddLogMessage("info", fmt.Sprintf("处理进度: %s - %.1f%%", stage, progress))
		} else {
			log.Printf("⚠️ No progress data in response (attempt %d/%d)", attempt+1, maxAttempts)
		}

		// Wait before next poll
		time.Sleep(pollInterval)
	}

	// Timeout reached
	log.Printf("⏰ Progress monitoring timeout for project: %s", pptID)

	// 使用 Reporter 上报超时错误
	if reporter != nil {
		reporter.Error("监控超时，项目可能仍在处理中")
		reporter.SendLogMessage("warning", fmt.Sprintf("项目 %s 监控超时，已达到最大等待时间", pptID))
	}

	return map[string]interface{}{
		"success": false,
		"status":  "timeout",
		"ppt_id":  pptID,
		"error":   "处理超时，请稍后查询进度",
		"message": "监控超时，项目可能仍在处理中",
	}, nil
}

// downloadFileFromDStaff downloads a file from DStaff platform
func (h *ToolHandlers) downloadFileFromDStaff(filePath, taskID, token string) (string, error) {
	if h.dstaffConfig == nil || !h.dstaffConfig.Enabled {
		return "", fmt.Errorf("dstaff integration not enabled")
	}

	// Construct the file download URL
	fileDownloadURL := h.dstaffConfig.FileDownloadURL
	if fileDownloadURL == "" {
		fileDownloadURL = h.dstaffConfig.EndpointURL + "/api/v1/old-mcp/file/download"
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   taskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", fileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	log.Printf("Downloading file from DStaff: taskId=%s, filePath=%s, url=%s", taskID, filePath, fileDownloadURL)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return "", fmt.Errorf("download failed: %v", errorResp)
		}
		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	filename := filepath.Base(filePath)
	if filename == "." || filename == "/" {
		filename = "downloaded_file"
	}

	// Generate unique filename to avoid conflicts
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("%s_%s_%s", taskID, timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("File downloaded successfully from DStaff: %s -> %s (size: %d bytes)", filePath, localFilePath, len(fileContent))
	return localFilePath, nil
}

// SimpleTaskBindingManager is a simple in-memory implementation of TaskBindingManager
type SimpleTaskBindingManager struct {
	bindings map[string]string // taskID -> projectID
	mu       sync.RWMutex
}

// NewSimpleTaskBindingManager creates a new simple task binding manager
func NewSimpleTaskBindingManager() *SimpleTaskBindingManager {
	return &SimpleTaskBindingManager{
		bindings: make(map[string]string),
	}
}

// SaveBinding saves a task-project binding
func (m *SimpleTaskBindingManager) SaveBinding(taskID, projectID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.bindings[taskID] = projectID
	log.Printf("📋 Task binding saved: %s -> %s", taskID, projectID)
	return nil
}

// GetProjectID gets the project ID for a task ID
func (m *SimpleTaskBindingManager) GetProjectID(taskID string) (string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	projectID, exists := m.bindings[taskID]
	if !exists {
		return "", fmt.Errorf("no project binding found for task %s", taskID)
	}

	return projectID, nil
}

// DeleteBinding deletes a task-project binding
func (m *SimpleTaskBindingManager) DeleteBinding(taskID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.bindings, taskID)
	log.Printf("📋 Task binding deleted: %s", taskID)
	return nil
}

// DownloadFileFromDStaff downloads a file from DStaff platform (public method for transport layer)
func (h *ToolHandlers) DownloadFileFromDStaff(filePath, taskID, token string) (string, error) {
	return h.downloadFileFromDStaff(filePath, taskID, token)
}

// UploadFileWithProgress handles file upload with progress reporting
func (h *ToolHandlers) UploadFileWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	name, err := request.RequireString("name")
	if err != nil {
		log.Printf("upload_file: Missing 'name' parameter: %v", err)
		return nil, err
	}

	fileURL, err := request.RequireString("file_url")
	if err != nil {
		log.Printf("upload_file: Missing 'file_url' parameter: %v", err)
		return nil, err
	}

	userRequirements := request.GetString("user_requirements", "")
	enableSubtitles := request.GetBool("enable_subtitles", false)
	subtitleStyleTemplate := request.GetString("subtitle_style_template", "")

	// Get task_id from context parameter (dictionary)
	var taskID string
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	log.Printf("upload_file: 开始为PPT文件 '%s' 生成解说视频，URL: %s, task_id: %s, 字幕: %t, 样式: %s", name, fileURL, taskID, enableSubtitles, subtitleStyleTemplate)

	// Check if fileURL is a relative path (not HTTP URL)
	var actualFileURL string
	var relativeFilePath string

	// Check if it's a full HTTP/HTTPS URL
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// This is a regular HTTP URL
		actualFileURL = fileURL
		log.Printf("upload_file: Using HTTP URL: %s", actualFileURL)
	} else if strings.HasPrefix(fileURL, "file://") {
		// Extract relative path from file:// URL
		relativeFilePath = strings.TrimPrefix(fileURL, "file://")
		// Remove leading slash if present
		relativeFilePath = strings.TrimPrefix(relativeFilePath, "/")
		log.Printf("upload_file: Extracted relative path from file:// URL: %s", relativeFilePath)
	} else {
		// This is a relative path without protocol
		relativeFilePath = fileURL
		log.Printf("upload_file: Treating as relative path: %s", relativeFilePath)
	}

	// Handle relative paths (download from DStaff platform)
	if relativeFilePath != "" {
		if taskID == "" {
			log.Printf("upload_file: task_id is required for downloading files from DStaff platform")
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: "task_id is required for downloading files from DStaff platform",
					},
				},
				IsError: true,
			}, nil
		}

		// Get token from context
		token := ""
		if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
			if tokenStr, ok := tokenValue.(string); ok {
				token = tokenStr
			}
		}

		if token == "" {
			log.Printf("upload_file: Authorization token is required for downloading files from DStaff platform")
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: "Authorization token is required for downloading files from DStaff platform",
					},
				},
				IsError: true,
			}, nil
		}

		// Download file from DStaff platform
		localFilePath, err := h.downloadFileFromDStaff(relativeFilePath, taskID, token)
		if err != nil {
			log.Printf("upload_file: Failed to download file from DStaff: %v", err)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: fmt.Sprintf("Failed to download file from DStaff platform: %v", err),
					},
				},
				IsError: true,
			}, nil
		}

		// Convert local file path to file:// URL for upload
		actualFileURL = "file://" + localFilePath
		log.Printf("upload_file: File downloaded from DStaff, using local path: %s", actualFileURL)
	}

	// Download file from URL and upload to ppt-narrator
	respBody, err := h.pptClient.UploadFileFromURL(actualFileURL, name, userRequirements, enableSubtitles, subtitleStyleTemplate)
	if err != nil {
		log.Printf("upload_file: Failed to start pipeline: %v", err)
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to start pipeline: %v", err),
				},
			},
			IsError: true,
		}

		// Add error to log messages
		h.AddLogMessage("error", fmt.Sprintf("Failed to start pipeline: %v", err))
		return errorResult, nil
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Pipeline request sent. Response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Extract project_id from response and save task binding
	if projectIDValue, exists := response["project_id"]; exists {
		if projectID, ok := projectIDValue.(string); ok && taskID != "" {
			// Save task binding for future reference
			if h.taskBindingManager != nil {
				if err := h.taskBindingManager.SaveBinding(taskID, projectID); err != nil {
					log.Printf("upload_file: Failed to save task binding: %v", err)
				} else {
					log.Printf("upload_file: Task binding saved successfully (task_id=%s -> project_id=%s)", taskID, projectID)
				}
			}
		}
	}

	result := &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: fmt.Sprintf("Pipeline started successfully. Project ID: %s", response["project_id"]),
			},
		},
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Pipeline started successfully. Project ID: %s", response["project_id"]))
	return result, nil
}

// uploadFileToDStaffFromContent uploads file content to DStaff platform
func (h *ToolHandlers) uploadFileToDStaffFromContent(content []byte, targetPath, taskID, token, contentType string) (map[string]interface{}, error) {
	if h.dstaffConfig == nil || !h.dstaffConfig.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// 构建上传URL
	uploadURL := h.dstaffConfig.FileUploadURL
	if uploadURL == "" {
		uploadURL = h.dstaffConfig.EndpointURL + "/api/v1/old-mcp/file/upload"
	}

	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件内容
	part, err := writer.CreateFormFile("file", filepath.Base(targetPath))
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(content); err != nil {
		return nil, fmt.Errorf("failed to write file content: %w", err)
	}

	// 添加其他字段
	if err := writer.WriteField("targetPath", targetPath); err != nil {
		return nil, fmt.Errorf("failed to write targetPath field: %w", err)
	}

	if err := writer.WriteField("taskId", taskID); err != nil {
		return nil, fmt.Errorf("failed to write taskId field: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建HTTP请求
	client := &http.Client{Timeout: 60 * time.Second}
	req, err := http.NewRequest("POST", uploadURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	log.Printf("Uploading file to DStaff: taskId=%s, targetPath=%s, size=%d bytes", taskID, targetPath, len(content))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != 200 {
		// 尝试解析错误响应
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return nil, fmt.Errorf("upload failed: %v", errorResp)
		}
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析成功响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.Printf("File uploaded successfully to DStaff: %s", targetPath)
	return result, nil
}

// GetDownloadURL handles getting video download URL and uploading to DStaff if enabled
func (h *ToolHandlers) GetDownloadURL(args map[string]interface{}) (map[string]interface{}, error) {
	pptID, _ := args["ppt_id"].(string)

	log.Printf("🔍 GetDownloadURL: Received ppt_id: %s", pptID)
	log.Printf("🔍 GetDownloadURL: Available args: %v", args)

	// If ppt_id is not provided, try to get it from task binding using Context
	if pptID == "" {
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskID, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskID.(string); ok && h.taskBindingManager != nil {
						log.Printf("🔍 GetDownloadURL: Trying to get project ID from task binding for task: %s", taskIDStr)
						if boundProjectID, err := h.taskBindingManager.GetProjectID(taskIDStr); err == nil {
							pptID = boundProjectID
							log.Printf("✅ GetDownloadURL: Found bound project ID: %s", pptID)
						} else {
							log.Printf("⚠️ GetDownloadURL: No binding found for task: %s, error: %v", taskIDStr, err)
						}
					}
				}
			}
		}
	}

	if pptID == "" {
		return nil, fmt.Errorf("ppt_id parameter is required or must be available through task binding")
	}

	log.Printf("🚀 GetDownloadURL: Calling API with ppt_id: %s", pptID)

	// Call PPT Narrator API to get download info
	response, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/download/%s/info", pptID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get download URL: %w", err)
	}

	// Note: DStaff integration is now handled in the transport layer (server.go)
	// to have access to the HTTP context and authentication token

	return response, nil
}

// RetryProject handles retrying a failed project
func (h *ToolHandlers) RetryProject(args map[string]interface{}) (map[string]interface{}, error) {
	stage, ok := args["stage"].(string)
	if !ok || stage == "" {
		return nil, fmt.Errorf("stage parameter is required")
	}

	pptID, _ := args["ppt_id"].(string)

	// If ppt_id is not provided, try to get it from task binding using Context
	if pptID == "" {
		if contextParam, exists := args["Context"]; exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskID, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskID.(string); ok && h.taskBindingManager != nil {
						if boundProjectID, err := h.taskBindingManager.GetProjectID(taskIDStr); err == nil {
							pptID = boundProjectID
						}
					}
				}
			}
		}
	}

	if pptID == "" {
		return nil, fmt.Errorf("ppt_id parameter is required or must be available through task binding")
	}

	// Prepare request data
	requestData := map[string]interface{}{
		"stage": stage,
	}

	// Call PPT Narrator API to retry project (correct endpoint)
	response, err := h.callPPTNarratorAPI("POST", fmt.Sprintf("/api/v1/pipeline/%s/retry", pptID), requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to retry project: %w", err)
	}

	return response, nil
}

// UploadAndMonitor handles uploading PPT file and monitoring the entire process
func (h *ToolHandlers) UploadAndMonitor(args map[string]interface{}) (map[string]interface{}, error) {
	name, ok := args["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("name parameter is required")
	}

	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	userRequirements, _ := args["user_requirements"].(string)
	enableSubtitles, _ := args["enable_subtitles"].(bool)
	subtitleStyleTemplate, _ := args["subtitle_style_template"].(string)

	// Prepare request data
	requestData := map[string]interface{}{
		"name":     name,
		"file_url": fileURL,
	}

	if userRequirements != "" {
		requestData["user_requirements"] = userRequirements
	}
	if enableSubtitles {
		requestData["enable_subtitles"] = enableSubtitles
	}
	if subtitleStyleTemplate != "" {
		requestData["subtitle_style_template"] = subtitleStyleTemplate
	}

	// Step 1: Upload file and start pipeline
	log.Printf("🚀 Starting upload and monitor for file: %s", name)
	response, err := h.callPPTNarratorAPI("POST", "/api/v1/pipeline/upload-and-process", requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Extract project ID from response
	var projectID string

	// Try to get project_id from root level first
	if id, ok := response["project_id"].(string); ok {
		projectID = id
	} else if project, ok := response["project"].(map[string]interface{}); ok {
		// Try to get id from project object
		if id, ok := project["id"].(string); ok {
			projectID = id
		}
	} else if data, ok := response["data"].(map[string]interface{}); ok {
		// Fallback: try to get from data object (legacy format)
		if pptID, ok := data["ppt_id"].(string); ok {
			projectID = pptID
		} else if id, ok := data["project_id"].(string); ok {
			projectID = id
		}
	}

	if projectID == "" {
		return nil, fmt.Errorf("failed to extract project ID from upload response")
	}

	log.Printf("📋 Project created with ID: %s", projectID)

	// Save task binding if Context with task_id is provided and extract task_id
	var taskIDStr string
	if contextParam, exists := args["Context"]; exists {
		if contextMap, ok := contextParam.(map[string]interface{}); ok {
			if taskID, exists := contextMap["task_id"]; exists {
				if taskIDStr, ok = taskID.(string); ok {
					// Register the external task in task manager for progress tracking
					if h.taskManager != nil {
						err := h.taskManager.RegisterExternalTask(taskIDStr, "upload_and_monitor", args)
						if err != nil {
							log.Printf("Failed to register external task: %v", err)
						} else {
							log.Printf("✅ External task registered: %s", taskIDStr)
						}
					}

					// Save task binding
					if h.taskBindingManager != nil {
						if err := h.taskBindingManager.SaveBinding(taskIDStr, projectID); err != nil {
							log.Printf("Failed to save task binding: %v", err)
						} else {
							log.Printf("✅ Task binding saved: %s -> %s", taskIDStr, projectID)
						}
					}
				}
			}
		}
	}

	// Step 2: Monitor progress until completion
	log.Printf("🔍 Starting progress monitoring for project: %s", projectID)

	// 创建 Reporter 用于统一的进度和日志上报
	var reporter *progress.Reporter
	if taskIDStr != "" {
		// 使用 task_id 作为进度令牌，总步数设为100（对应百分比）
		reporter = progress.NewReporter(taskIDStr, 100, h.progressSendFunc)
		reporter.SetLogSendFunc(h.logSendFunc)
		reporter.SendLogMessage("info", fmt.Sprintf("开始处理项目: %s", name))
	}

	finalResult, err := h.monitorProgressUntilComplete(projectID, reporter, taskIDStr)
	if err != nil {
		if reporter != nil {
			reporter.Error(fmt.Sprintf("监控进度失败: %v", err))
		}
		return nil, fmt.Errorf("failed to monitor progress: %w", err)
	}

	return finalResult, nil
}

// GetNarration handles getting narration content for a project
func (h *ToolHandlers) GetNarration(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, ok := args["project_id"].(string)
	if !ok || projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required")
	}

	// Call PPT Narrator API to get narration
	response, err := h.callPPTNarratorAPI("GET", fmt.Sprintf("/api/v1/narration/%s", projectID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get narration: %w", err)
	}

	return response, nil
}

// ListProjects handles listing all projects
func (h *ToolHandlers) ListProjects(args map[string]interface{}) (map[string]interface{}, error) {
	// Call PPT Narrator API to list projects
	response, err := h.callPPTNarratorAPI("GET", "/api/v1/projects", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list projects: %w", err)
	}

	return response, nil
}

// DeleteProject handles deleting a project
func (h *ToolHandlers) DeleteProject(args map[string]interface{}) (map[string]interface{}, error) {
	projectID, ok := args["project_id"].(string)
	if !ok || projectID == "" {
		return nil, fmt.Errorf("project_id parameter is required")
	}

	// Call PPT Narrator API to delete project
	response, err := h.callPPTNarratorAPI("DELETE", fmt.Sprintf("/api/v1/projects/%s", projectID), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to delete project: %w", err)
	}

	return response, nil
}

// UploadAndProcess handles uploading PPT file and starting full pipeline
func (h *ToolHandlers) UploadAndProcess(args map[string]interface{}) (map[string]interface{}, error) {
	fileURL, ok := args["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url parameter is required")
	}

	projectName, ok := args["project_name"].(string)
	if !ok || projectName == "" {
		return nil, fmt.Errorf("project_name parameter is required")
	}

	userRequirements, _ := args["user_requirements"].(string)
	ttsProvider, _ := args["tts_provider"].(string)
	enableSubtitles, _ := args["enable_subtitles"].(bool)

	// Prepare request data
	requestData := map[string]interface{}{
		"file_url":     fileURL,
		"project_name": projectName,
	}

	if userRequirements != "" {
		requestData["user_requirements"] = userRequirements
	}
	if ttsProvider != "" {
		requestData["tts_provider"] = ttsProvider
	}
	if enableSubtitles {
		requestData["enable_subtitles"] = enableSubtitles
	}

	// Call PPT Narrator API to upload and process
	response, err := h.callPPTNarratorAPI("POST", "/api/v1/pipeline/upload-and-process", requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to upload and process: %w", err)
	}

	// Save task binding if Context with task_id is provided
	if contextParam, exists := args["Context"]; exists {
		if contextMap, ok := contextParam.(map[string]interface{}); ok {
			if taskID, exists := contextMap["task_id"]; exists {
				if taskIDStr, ok := taskID.(string); ok {
					// Extract project ID from response for binding
					if data, ok := response["data"].(map[string]interface{}); ok {
						if projectID, ok := data["project_id"].(string); ok && h.taskBindingManager != nil {
							if err := h.taskBindingManager.SaveBinding(taskIDStr, projectID); err != nil {
								log.Printf("Failed to save task binding: %v", err)
							}
						}
					}
				}
			}
		}
	}

	return response, nil
}
