package tasks

import (
	"context"
	"fmt"
	"log"

	"github.com/mark3labs/mcp-go/mcp"
)

// ToolExecutor interface for executing tools
type ToolExecutor interface {
	UploadFileWithProgress(ctx context.Context, req mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error)
}

// TaskExecutorImpl implements task execution logic
type TaskExecutorImpl struct {
	taskManager  *TaskManager
	toolExecutor ToolExecutor
}

// NewTaskExecutor creates a new task executor
func NewTaskExecutor(taskManager *TaskManager, toolExecutor ToolExecutor) *TaskExecutorImpl {
	return &TaskExecutorImpl{
		taskManager:  taskManager,
		toolExecutor: toolExecutor,
	}
}

// ExecuteTask executes a task asynchronously
func (e *TaskExecutorImpl) ExecuteTask(taskID string) error {
	// Get the actual task
	task, err := e.taskManager.GetTaskByID(taskID)
	if err != nil {
		log.Printf("❌ Failed to get task %s: %v", taskID, err)
		return err
	}

	// Update task status to running
	e.taskManager.UpdateTaskStatus(task.ID, TaskStatusRunning)

	// Get task context
	ctx, err := e.taskManager.GetTaskContext(task.ID)
	if err != nil {
		log.Printf("❌ Failed to get task context for %s: %v", task.ID, err)
		e.taskManager.SetTaskError(task.ID, err)
		e.taskManager.UpdateTaskStatus(task.ID, TaskStatusFailed)
		return err
	}

	// Execute the tool
	var result map[string]interface{}

	switch task.ToolName {
	case "upload_file":
		mockReq := createMockMCPRequest(task.ID).(mcp.CallToolRequest)
		mcpResult, err := e.toolExecutor.UploadFileWithProgress(ctx, mockReq, task.Arguments)
		if err == nil && mcpResult != nil {
			// Convert MCP result to map for compatibility
			result = map[string]interface{}{
				"success": !mcpResult.IsError,
				"content": mcpResult.Content,
			}
		}
	default:
		err = fmt.Errorf("unknown tool: %s", task.ToolName)
	}

	if err != nil {
		log.Printf("❌ Task %s execution failed: %v", task.ID, err)
		e.taskManager.SetTaskError(task.ID, err)
		e.taskManager.UpdateTaskStatus(task.ID, TaskStatusFailed)
		return err
	}

	// Set result and mark as completed
	e.taskManager.SetTaskResult(task.ID, result)
	e.taskManager.UpdateTaskStatus(task.ID, TaskStatusCompleted)

	log.Printf("✅ Task %s completed successfully", task.ID)

	return nil
}

// createMockMCPRequest creates a mock MCP request for task execution
func createMockMCPRequest(taskID string) interface{} {
	// Create a mock MCP request with progress token for task tracking
	return mcp.CallToolRequest{
		Request: mcp.Request{
			Method: "tools/call",
		},
		Params: mcp.CallToolParams{
			Name: "mock_tool",
			Meta: &mcp.Meta{
				ProgressToken: taskID,
			},
		},
	}
}
