package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"ppt-narrator-mcp/internal/config"
	"ppt-narrator-mcp/internal/tools"
)

// TestCase represents a test case for MCP tools
type TestCase struct {
	Name        string                 `json:"name"`
	Tool        string                 `json:"tool"`
	Arguments   map[string]interface{} `json:"arguments"`
	Description string                 `json:"description"`
}

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create tool handlers
	handlers := tools.NewToolHandlers(cfg)

	// Define test cases
	testCases := []TestCase{
		{
			Name: "文本总结测试",
			Tool: "summarize_text",
			Arguments: map[string]interface{}{
				"text":       "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。",
				"style":      "简洁",
				"max_length": "100",
			},
			Description: "测试文本总结功能，使用简洁风格，限制100字符",
		},
		{
			Name: "网页抓取总结测试",
			Tool: "fetch_and_summarize_url",
			Arguments: map[string]interface{}{
				"url":         "https://www.example.com",
				"style":       "详细",
				"max_length":  "200",
				"include_url": true,
			},
			Description: "测试网页抓取和总结功能（注意：需要有效的Jina API密钥）",
		},
		{
			Name: "卡片生成测试",
			Tool: "generate_card",
			Arguments: map[string]interface{}{
				"content":               "人工智能是未来科技发展的重要方向，它将改变我们的生活方式和工作模式。",
				"title":                 "AI技术发展趋势",
				"card_style":            "tech",
				"color_theme":           "blue",
				"include_keywords":      true,
				"include_summary_stats": true,
			},
			Description: "测试HTML卡片生成功能，使用科技风格和蓝色主题",
		},
		{
			Name: "关键词提取测试",
			Tool: "extract_keywords",
			Arguments: map[string]interface{}{
				"text":         "机器学习是人工智能的一个重要分支，通过算法让计算机从数据中学习模式。深度学习作为机器学习的子集，使用神经网络来模拟人脑的学习过程。",
				"max_keywords": "5",
				"language":     "zh",
			},
			Description: "测试关键词提取功能，提取5个中文关键词",
		},
		{
			Name: "标题生成测试",
			Tool: "generate_title",
			Arguments: map[string]interface{}{
				"content":    "随着5G技术的普及和物联网设备的增加，边缘计算正在成为云计算的重要补充。边缘计算将数据处理能力推向网络边缘，减少延迟，提高响应速度。",
				"style":      "吸引人",
				"max_length": "30",
				"count":      "3",
			},
			Description: "测试标题生成功能，生成3个吸引人的标题",
		},
	}

	fmt.Println("🧪 Smart Card MCP Tools 测试开始")
	fmt.Println(repeatString("=", 50))

	// Run test cases
	for i, testCase := range testCases {
		fmt.Printf("\n📋 测试 %d: %s\n", i+1, testCase.Name)
		fmt.Printf("📝 描述: %s\n", testCase.Description)
		fmt.Printf("🔧 工具: %s\n", testCase.Tool)

		// Print arguments
		argsJSON, _ := json.MarshalIndent(testCase.Arguments, "", "  ")
		fmt.Printf("📊 参数:\n%s\n", string(argsJSON))

		// Execute test
		var result map[string]interface{}
		var err error

		switch testCase.Tool {
		case "summarize_text":
			result, err = handlers.SummarizeText(testCase.Arguments)
		case "fetch_and_summarize_url":
			result, err = handlers.FetchAndSummarizeURL(testCase.Arguments)
		case "generate_card":
			result, err = handlers.GenerateCard(testCase.Arguments)
		case "extract_keywords":
			result, err = handlers.ExtractKeywords(testCase.Arguments)
		case "generate_title":
			result, err = handlers.GenerateTitle(testCase.Arguments)
		default:
			err = fmt.Errorf("unknown tool: %s", testCase.Tool)
		}

		if err != nil {
			fmt.Printf("❌ 测试失败: %v\n", err)
		} else {
			fmt.Printf("✅ 测试成功\n")
			resultJSON, _ := json.MarshalIndent(result, "", "  ")
			fmt.Printf("📤 结果:\n%s\n", string(resultJSON))
		}

		fmt.Println(repeatString("-", 50))
	}

	fmt.Println("\n🎉 测试完成！")
	fmt.Println("\n💡 提示:")
	fmt.Println("- 如果网页抓取测试失败，请检查JINA_API_KEY环境变量")
	fmt.Println("- 如果AI相关测试失败，请检查OPENAI_API_KEY环境变量")
	fmt.Println("- 生成的HTML卡片可以保存为.html文件在浏览器中查看")
}

// Helper function to repeat strings (Go doesn't have built-in string multiplication)
func repeatString(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
