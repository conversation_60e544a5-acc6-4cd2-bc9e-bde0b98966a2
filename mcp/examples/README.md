# Smart Card MCP 异步API客户端示例

本目录包含了使用HTTP API异步调用Smart Card MCP服务的完整示例代码。

## 🚀 快速开始

### 1. 启动服务

确保Smart Card MCP服务正在运行：
```bash
# 使用Docker运行
docker run -p 48089:48089 smart-card-mcp:latest

# 或者直接运行二进制文件
./bin/smart-card-mcp-linux-latest
```

### 2. 配置API密钥

在所有示例中，将 `smart-card-mcp-your-secure-key-here` 替换为你的实际API密钥。

## 📁 文件说明

### Python示例
- **文件**: `async_client_python.py`
- **依赖**: `pip install -r requirements.txt`
- **运行**: `python async_client_python.py`

**特性**:
- 完整的异步流程实现
- 进度监控和状态显示
- 错误处理和超时机制
- 图片自动保存功能
- 可配置的轮询间隔

### JavaScript示例
- **文件**: `async_client_javascript.js`
- **依赖**: `npm install`
- **运行**: `node async_client_javascript.js`

**特性**:
- 基于Promise的异步处理
- 详细的进度显示
- 完整的错误处理
- 图片保存功能
- 可配置的超时设置

### Bash示例
- **文件**: `async_client_bash.sh`
- **依赖**: `curl`, `jq`
- **运行**: `chmod +x async_client_bash.sh && ./async_client_bash.sh`

**特性**:
- 纯Shell脚本实现
- 彩色日志输出
- JSON数据处理
- 图片base64解码保存
- 完整的错误处理

## 🔧 自定义配置

### Python配置示例
```python
config = SmartCardConfig(
    base_url="http://localhost:48089/api/v1",
    api_key="your-api-key-here",
    timeout=300,  # 5分钟超时
    poll_interval=2  # 2秒轮询间隔
)
```

### JavaScript配置示例
```javascript
const config = {
    baseUrl: 'http://localhost:48089/api/v1',
    apiKey: 'your-api-key-here',
    timeout: 300000, // 5分钟超时
    pollInterval: 2000, // 2秒轮询间隔
};
```

### Bash配置示例
```bash
BASE_URL="http://localhost:48089/api/v1"
API_KEY="your-api-key-here"
TIMEOUT=300  # 5分钟超时
POLL_INTERVAL=2  # 2秒轮询间隔
```

## 📊 使用示例

### 基本用法
```python
# Python
client = SmartCardAsyncClient(config)
result = client.generate_card_from_url(
    url="https://example.com/article",
    save_path="card.png"
)
```

```javascript
// JavaScript
const client = new SmartCardAsyncClient(config);
const result = await client.generateCardFromUrl(
    'https://example.com/article',
    null, null,
    'card.png'
);
```

```bash
# Bash
./async_client_bash.sh
```

### 高级用法
```python
# 带提示词的生成
result = client.generate_card_from_url(
    url="https://tech-blog.com/ai-article",
    summary_prompt="重点关注技术细节和实现方案",
    style_prompt="科技感十足的深蓝色主题",
    save_path="tech_card.png"
)
```

## 🔍 故障排查

### 常见问题

1. **连接被拒绝**
   - 检查服务是否启动：`curl http://localhost:48089/api/v1/health`
   - 确认端口号是否正确

2. **401 Unauthorized**
   - 检查API密钥是否正确
   - 确认请求头格式：`X-API-Key: your-key`

3. **任务超时**
   - 增加超时时间配置
   - 检查网络连接和服务器负载

4. **图片保存失败**
   - 检查文件写入权限
   - 确认目录是否存在

### 调试技巧

1. **启用详细日志**（Python）:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **检查API响应**（curl）:
```bash
curl -v -H "X-API-Key: your-key" http://localhost:48089/api/v1/health
```

3. **验证JSON格式**:
```bash
echo '{"arguments":{"url":"https://example.com"}}' | jq '.'
```

## 📚 更多资源

- [完整API文档](../docs/async-api-guide.md)
- [服务器配置指南](../README.md)
- [MCP协议文档](../docs/mcp-integration.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这些示例代码！

## 📄 许可证

MIT License
