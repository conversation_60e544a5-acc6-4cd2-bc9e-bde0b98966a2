# Smart Card MCP Server 使用示例

本文档提供了Smart Card MCP Server的详细使用示例，包括各种工具的调用方法和实际应用场景。

## 目录
- [基础使用](#基础使用)
- [工具详细示例](#工具详细示例)
- [实际应用场景](#实际应用场景)
- [集成示例](#集成示例)

## 基础使用

### 启动服务器

```bash
# 使用Docker Compose（推荐）
docker-compose up -d

# 或者本地运行
go run main.go -transport mixed
```

### 检查服务状态

```bash
# 健康检查
curl http://localhost:49083/health

# 预期响应
{
  "status": "healthy",
  "service": "smart-card-mcp",
  "version": "1.0.0"
}
```

## 工具详细示例

### 1. 文本总结 (summarize_text)

#### 基础总结
```json
{
  "tool": "summarize_text",
  "arguments": {
    "text": "人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。AI包括机器学习、深度学习、自然语言处理、计算机视觉等多个子领域。近年来，随着计算能力的提升和大数据的普及，AI技术取得了突破性进展，在医疗、金融、交通、教育等各个领域都有广泛应用。",
    "style": "简洁",
    "max_length": "100"
  }
}
```

#### 不同风格的总结
```json
{
  "tool": "summarize_text",
  "arguments": {
    "text": "区块链是一种分布式账本技术，通过密码学方法将数据块按时间顺序链接起来，形成不可篡改的数据链。每个区块包含前一个区块的哈希值、时间戳和交易数据。区块链的去中心化特性使其不依赖于单一的中央机构，而是通过网络中的多个节点共同维护账本的完整性和一致性。",
    "style": "专业",
    "max_length": "150"
  }
}
```

### 2. 网页抓取总结 (fetch_and_summarize_url)

#### 抓取新闻文章
```json
{
  "tool": "fetch_and_summarize_url",
  "arguments": {
    "url": "https://example-news-site.com/tech-article",
    "style": "要点",
    "max_length": "200",
    "include_url": true
  }
}
```

#### 抓取技术博客
```json
{
  "tool": "fetch_and_summarize_url",
  "arguments": {
    "url": "https://tech-blog.example.com/ai-trends-2024",
    "style": "详细",
    "max_length": "300",
    "include_url": true
  }
}
```

### 3. 卡片生成 (generate_card)

#### 基础卡片
```json
{
  "tool": "generate_card",
  "arguments": {
    "content": "机器学习是人工智能的核心技术之一，通过算法让计算机从数据中自动学习模式和规律，无需明确编程。主要包括监督学习、无监督学习和强化学习三大类型。",
    "title": "机器学习基础概念",
    "card_style": "default",
    "color_theme": "blue"
  }
}
```

#### 科技风格卡片
```json
{
  "tool": "generate_card",
  "arguments": {
    "content": "5G技术作为第五代移动通信技术，具有高速率、低延迟、大连接的特点。相比4G，5G的峰值速率可达20Gbps，延迟低至1毫秒，能够支持每平方公里100万个设备连接。",
    "title": "5G技术特性解析",
    "source_url": "https://example.com/5g-tech",
    "card_style": "tech",
    "color_theme": "cyan",
    "include_keywords": true,
    "include_summary_stats": true
  }
}
```

#### 多彩主题卡片
```json
{
  "tool": "generate_card",
  "arguments": {
    "content": "可持续发展是指在满足当代人需求的同时，不损害后代人满足其需求的能力。它涉及经济、社会和环境三个维度的协调发展，追求经济增长、社会公平和环境保护的平衡。",
    "title": "可持续发展理念",
    "card_style": "colorful",
    "color_theme": "green",
    "include_keywords": true
  }
}
```

### 4. 关键词提取 (extract_keywords)

#### 中文关键词提取
```json
{
  "tool": "extract_keywords",
  "arguments": {
    "text": "云计算是一种基于互联网的计算方式，通过网络以按需、易扩展的方式获得所需的计算资源。云计算包括基础设施即服务(IaaS)、平台即服务(PaaS)和软件即服务(SaaS)三种服务模式。",
    "max_keywords": "8",
    "language": "zh"
  }
}
```

#### 英文关键词提取
```json
{
  "tool": "extract_keywords",
  "arguments": {
    "text": "Machine learning algorithms can be broadly categorized into supervised learning, unsupervised learning, and reinforcement learning. Supervised learning uses labeled data to train models, while unsupervised learning finds patterns in unlabeled data.",
    "max_keywords": "10",
    "language": "en"
  }
}
```

### 5. 标题生成 (generate_title)

#### 生成多个标题选项
```json
{
  "tool": "generate_title",
  "arguments": {
    "content": "物联网(IoT)设备的快速增长带来了前所未有的数据量，这些数据需要实时处理和分析。边缘计算通过在数据源附近进行计算，减少了数据传输延迟，提高了响应速度，同时降低了带宽成本。",
    "style": "吸引人",
    "max_length": "25",
    "count": "5"
  }
}
```

#### 专业风格标题
```json
{
  "tool": "generate_title",
  "arguments": {
    "content": "深度学习模型的训练需要大量的计算资源和时间。GPU并行计算、分布式训练和模型压缩技术的发展，使得大规模深度学习模型的训练变得更加高效和可行。",
    "style": "专业",
    "max_length": "30",
    "count": "3"
  }
}
```

## 实际应用场景

### 场景1：新闻摘要卡片生成

```bash
# 1. 抓取新闻内容并总结
curl -X POST http://localhost:48084/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "fetch_and_summarize_url",
    "arguments": {
      "url": "https://news.example.com/tech-news",
      "style": "要点",
      "max_length": "200"
    }
  }'

# 2. 生成新闻卡片
curl -X POST http://localhost:48084/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "generate_card",
    "arguments": {
      "content": "总结的新闻内容...",
      "title": "今日科技要闻",
      "source_url": "https://news.example.com/tech-news",
      "card_style": "default",
      "color_theme": "blue"
    }
  }'
```

### 场景2：学术论文摘要

```json
{
  "workflow": [
    {
      "step": 1,
      "tool": "summarize_text",
      "arguments": {
        "text": "完整的学术论文内容...",
        "style": "专业",
        "max_length": "300"
      }
    },
    {
      "step": 2,
      "tool": "extract_keywords",
      "arguments": {
        "text": "论文内容...",
        "max_keywords": "10"
      }
    },
    {
      "step": 3,
      "tool": "generate_card",
      "arguments": {
        "content": "步骤1的总结结果",
        "title": "论文标题",
        "card_style": "minimal",
        "color_theme": "purple",
        "include_keywords": true
      }
    }
  ]
}
```

### 场景3：产品介绍卡片

```json
{
  "tool": "generate_card",
  "arguments": {
    "content": "我们的新产品采用最新的AI技术，能够自动分析用户行为，提供个性化推荐。产品具有高性能、易使用、安全可靠的特点，适用于各种商业场景。",
    "title": "AI智能推荐系统",
    "card_style": "colorful",
    "color_theme": "orange",
    "include_keywords": true,
    "include_summary_stats": true
  }
}
```

## 集成示例

### Python集成示例

```python
import requests
import json

class SmartCardMCP:
    def __init__(self, base_url="http://localhost:48084"):
        self.base_url = base_url
    
    def call_tool(self, tool_name, arguments):
        response = requests.post(
            f"{self.base_url}/mcp",
            headers={"Content-Type": "application/json"},
            json={
                "tool": tool_name,
                "arguments": arguments
            }
        )
        return response.json()
    
    def summarize_text(self, text, style="简洁", max_length=200):
        return self.call_tool("summarize_text", {
            "text": text,
            "style": style,
            "max_length": str(max_length)
        })
    
    def generate_card(self, content, title=None, **kwargs):
        args = {"content": content}
        if title:
            args["title"] = title
        args.update(kwargs)
        return self.call_tool("generate_card", args)

# 使用示例
mcp = SmartCardMCP()

# 总结文本
summary = mcp.summarize_text("长文本内容...")
print(summary)

# 生成卡片
card = mcp.generate_card(
    content="卡片内容",
    title="卡片标题",
    card_style="tech",
    color_theme="blue"
)
print(card)
```

### JavaScript集成示例

```javascript
class SmartCardMCP {
    constructor(baseUrl = 'http://localhost:48084') {
        this.baseUrl = baseUrl;
    }
    
    async callTool(toolName, arguments) {
        const response = await fetch(`${this.baseUrl}/mcp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                tool: toolName,
                arguments: arguments
            })
        });
        return await response.json();
    }
    
    async summarizeText(text, style = '简洁', maxLength = 200) {
        return await this.callTool('summarize_text', {
            text: text,
            style: style,
            max_length: maxLength.toString()
        });
    }
    
    async generateCard(content, options = {}) {
        const args = { content, ...options };
        return await this.callTool('generate_card', args);
    }
}

// 使用示例
const mcp = new SmartCardMCP();

// 总结文本
mcp.summarizeText('长文本内容...')
    .then(result => console.log(result));

// 生成卡片
mcp.generateCard('卡片内容', {
    title: '卡片标题',
    card_style: 'tech',
    color_theme: 'blue'
}).then(result => console.log(result));
```

## 测试工具

项目包含了一个测试工具，可以快速验证所有功能：

```bash
# 运行测试
go run examples/test_tools.go

# 或者编译后运行
go build -o test_tools examples/test_tools.go
./test_tools
```

测试工具会依次测试所有MCP工具，并输出详细的结果信息。

## 注意事项

1. **API密钥配置**：确保正确配置了`OPENAI_API_KEY`和`JINA_API_KEY`
2. **网络访问**：网页抓取功能需要服务器能够访问目标网站
3. **资源限制**：AI API调用可能有速率限制，请合理控制调用频率
4. **HTML输出**：生成的HTML卡片可以直接保存为文件在浏览器中查看

## 更多信息

- [项目README](../README.md)
- [配置说明](../README.md#配置说明)
- [故障排除](../README.md#故障排除)
