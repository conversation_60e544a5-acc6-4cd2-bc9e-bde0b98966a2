#!/usr/bin/env python3
"""
Smart Card MCP 异步HTTP API客户端示例 (Python)
支持从URL生成卡片的完整异步流程
"""

import requests
import time
import base64
import json
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class SmartCardConfig:
    """配置类"""
    base_url: str = "http://localhost:48089/api/v1"
    api_key: str = "smart-card-mcp-your-secure-key-here"
    timeout: int = 300  # 5分钟超时
    poll_interval: int = 2  # 2秒轮询间隔


class SmartCardAsyncClient:
    """Smart Card MCP 异步客户端"""
    
    def __init__(self, config: SmartCardConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "X-API-Key": config.api_key
        })
    
    def create_task_from_url(self, url: str, summary_prompt: str = None, style_prompt: str = None) -> str:
        """
        从URL创建异步卡片生成任务
        
        Args:
            url: 网页URL
            summary_prompt: 可选的总结提示词
            style_prompt: 可选的风格提示词
            
        Returns:
            task_id: 任务ID
        """
        endpoint = f"{self.config.base_url}/tools/create_task_from_url/async"
        
        payload = {
            "arguments": {
                "url": url
            }
        }
        
        # 添加可选参数
        if summary_prompt:
            payload["arguments"]["summary_prompt"] = summary_prompt
        if style_prompt:
            payload["arguments"]["style_prompt"] = style_prompt
        
        print(f"🚀 发起异步任务: {url}")
        print(f"📝 总结提示词: {summary_prompt or '默认'}")
        print(f"🎨 风格提示词: {style_prompt or '默认'}")
        
        try:
            response = self.session.post(endpoint, json=payload)
            response.raise_for_status()  # 成功时返回200状态码

            result = response.json()
            task_id = result["data"]["task_id"]

            print(f"✅ 任务创建成功: {task_id}")
            return task_id

        except requests.exceptions.RequestException as e:
            print(f"❌ 任务创建失败: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        endpoint = f"{self.config.base_url}/tasks/{task_id}/status"
        
        try:
            response = self.session.get(endpoint)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取任务状态失败: {e}")
            raise
    
    def get_task_result(self, task_id: str, public_url: bool = False) -> Dict[str, Any]:
        """
        获取任务结果

        Args:
            task_id: 任务ID
            public_url: 是否返回公共URL而不是base64数据

        Returns:
            任务结果
        """
        endpoint = f"{self.config.base_url}/tasks/{task_id}/result"
        params = {}
        if public_url:
            params["public_url"] = "1"

        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"❌ 获取任务结果失败: {e}")
            raise
    
    def wait_for_completion(self, task_id: str) -> Dict[str, Any]:
        """
        等待任务完成并返回结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务结果
        """
        start_time = time.time()
        
        print(f"⏳ 等待任务完成: {task_id}")
        
        while True:
            # 检查超时
            if time.time() - start_time > self.config.timeout:
                raise TimeoutError(f"任务超时 ({self.config.timeout}秒)")
            
            # 获取任务状态
            status_info = self.get_task_status(task_id)
            status = status_info.get("status")
            progress = status_info.get("progress", {})
            
            # 显示进度
            if progress:
                current_step = progress.get("current_step", 0)
                total_steps = progress.get("total_steps", 0)
                message = progress.get("message", "")
                print(f"📊 进度: {current_step}/{total_steps} - {message}")
            
            # 检查任务状态
            if status == "completed":
                print("✅ 任务完成!")
                return self.get_task_result(task_id, public_url=False)
            elif status == "failed":
                error_msg = status_info.get("error", "未知错误")
                raise Exception(f"任务失败: {error_msg}")
            elif status == "cancelled":
                raise Exception("任务已取消")
            
            # 等待下次轮询
            time.sleep(self.config.poll_interval)

    def wait_for_completion_with_public_url(self, task_id: str) -> Dict[str, Any]:
        """
        等待任务完成并返回公共URL结果

        Args:
            task_id: 任务ID

        Returns:
            任务结果（包含公共URL）
        """
        start_time = time.time()

        print(f"⏳ 等待任务完成: {task_id}")

        while True:
            # 检查超时
            if time.time() - start_time > self.config.timeout:
                raise TimeoutError(f"任务超时 ({self.config.timeout}秒)")

            # 获取任务状态
            status_info = self.get_task_status(task_id)
            status = status_info.get("status")
            progress = status_info.get("progress", {})

            # 显示进度
            if progress:
                current_step = progress.get("current_step", 0)
                total_steps = progress.get("total_steps", 0)
                message = progress.get("message", "")
                print(f"📊 进度: {current_step}/{total_steps} - {message}")

            # 检查任务状态
            if status == "completed":
                print("✅ 任务完成!")
                return self.get_task_result(task_id, public_url=True)
            elif status == "failed":
                error_msg = status_info.get("error", "未知错误")
                raise Exception(f"任务失败: {error_msg}")
            elif status == "cancelled":
                raise Exception("任务已取消")

            # 等待下次轮询
            time.sleep(self.config.poll_interval)

    def save_image(self, image_data: str, filename: str) -> None:
        """
        保存base64编码的图片
        
        Args:
            image_data: base64编码的图片数据
            filename: 保存的文件名
        """
        try:
            # 解码base64数据
            image_bytes = base64.b64decode(image_data)
            
            # 保存到文件
            with open(filename, 'wb') as f:
                f.write(image_bytes)
            
            print(f"💾 图片已保存: {filename}")
            
        except Exception as e:
            print(f"❌ 保存图片失败: {e}")
            raise
    
    def generate_card_from_url(self, url: str, summary_prompt: str = None,
                              style_prompt: str = None, save_path: str = None,
                              public_url: bool = False) -> Dict[str, Any]:
        """
        完整的异步卡片生成流程

        Args:
            url: 网页URL
            summary_prompt: 可选的总结提示词
            style_prompt: 可选的风格提示词
            save_path: 可选的图片保存路径
            public_url: 是否返回公共URL而不是base64数据

        Returns:
            完整的任务结果
        """
        # 1. 创建异步任务
        task_id = self.create_task_from_url(url, summary_prompt, style_prompt)

        # 2. 等待任务完成
        if public_url:
            # 使用公共URL模式获取结果
            result = self.wait_for_completion_with_public_url(task_id)
        else:
            result = self.wait_for_completion(task_id)
        
        # 3. 处理结果
        if result.get("success"):
            data = result.get("data", {})
            content = data.get("content", [])

            # 查找图片内容
            for item in content:
                if item.get("type") == "image":
                    image_data = item.get("data")
                    is_public_url = item.get("public_url", False)

                    if image_data and save_path:
                        if is_public_url:
                            print(f"🌐 图片公共URL: {image_data}")
                        else:
                            self.save_image(image_data, save_path)
                    break

            print(f"🎉 卡片生成成功! 执行时间: {data.get('execution_time', 0):.2f}秒")
            return result
        else:
            raise Exception("任务执行失败")


def main():
    """示例用法"""
    # 配置
    config = SmartCardConfig(
        base_url="http://localhost:48089/api/v1",
        api_key="smart-card-mcp-your-secure-key-here",
        timeout=300,
        poll_interval=2
    )
    
    # 创建客户端
    client = SmartCardAsyncClient(config)
    
    # 示例1：从URL生成卡片（base64模式）
    try:
        print("=== Base64模式示例 ===")
        result = client.generate_card_from_url(
            url="https://example.com/article",
            summary_prompt="重点关注技术细节",
            style_prompt="简约现代风格",
            save_path="generated_card.png",
            public_url=False
        )

        print("🎉 任务完成!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

    except Exception as e:
        print(f"❌ 错误: {e}")

    # 示例2：从URL生成卡片（公共URL模式）
    try:
        print("\n=== 公共URL模式示例 ===")
        result = client.generate_card_from_url(
            url="https://example.com/article",
            summary_prompt="重点关注技术细节",
            style_prompt="简约现代风格",
            public_url=True
        )

        print("🎉 任务完成!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()
