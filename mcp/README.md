# Smart Card MCP Server

一个基于Go实现的MCP（Model Context Protocol）服务器，专门用于智能卡片生成。支持文本总结、网页内容抓取和精美HTML卡片生成功能。

![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)

## 功能特点

### 🎯 核心功能
- **文本总结** - 使用AI对长文本进行智能总结
- **网页抓取总结** - 自动抓取网页内容并生成总结
- **智能卡片生成** - 生成精美的HTML卡片，支持多种样式和主题
- **📝 总结方向指导** - 支持自定义总结提示词，指导AI的总结方向
- **🎨 风格自定义** - 支持自定义风格提示词，指导卡片设计风格
- **💾 内容自动保存** - 自动保存生成的总结、HTML和图片到本地
- **关键词提取** - 从文本中提取重要关键词
- **智能标题生成** - 根据内容类型自动生成合适风格的标题

### 🚀 技术特性
- **多传输模式** - 支持stdio、SSE、HTTP等多种传输方式
- **容器化部署** - 完整的Docker支持
- **响应式设计** - 生成的卡片适配各种设备
- **API兼容** - 支持OpenAI及兼容API
- **健康检查** - 内置健康检查端点

## 快速开始

### 使用Docker Compose（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd smart-card-mcp
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **检查服务状态**
```bash
docker-compose ps
curl http://localhost:49083/health
```

### 本地开发

1. **安装依赖**
```bash
go mod download
```

2. **配置环境变量**
```bash
# 复制配置文件
cp .env.example .env

# 或者直接设置环境变量
export OPENAI_API_KEY="your_openai_api_key"
export OPENAI_BASE_URL="https://api.openai.com/v1"
export OPENAI_MODEL="gpt-4o-mini"
export JINA_API_KEY="your_jina_api_key"
```

3. **运行服务**
```bash
# stdio模式（默认）
go run main.go

# SSE模式
go run main.go -transport sse

# HTTP模式
go run main.go -transport streamable_http

# 混合模式
go run main.go -transport mixed
```

## 配置说明

### 必需配置

| 环境变量 | 说明 | 示例 |
|---------|------|------|
| `OPENAI_API_KEY` | OpenAI API密钥 | `sk-...` |

### OpenAI API 配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `OPENAI_BASE_URL` | `https://api.openai.com/v1` | OpenAI API基础URL |
| `OPENAI_MODEL` | `gpt-4o-mini` | 使用的AI模型 |

**支持的API提供商：**
- **OpenAI官方**: `https://api.openai.com/v1`
- **Azure OpenAI**: `https://your-resource.openai.azure.com/openai/deployments/your-deployment-name`
- **本地API** (如Ollama): `http://localhost:11434/v1`
- **其他兼容API**: 任何OpenAI兼容的API端点

**推荐模型：**
- `gpt-4o-mini` - 性价比最高，推荐使用
- `gpt-4o` - 最强能力
- `gpt-4-turbo` - 平衡性能和成本
- `llama2`, `mistral` - 本地模型选项

### 其他配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `JINA_API_KEY` | - | Jina API密钥（用于网页抓取，可选） |
| `MCP_TRANSPORT` | `stdio` | 传输模式 |
| `MCP_PORTS` | `48083,48084` | 服务端口 |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `AUTO_CLEANUP_FILES` | `false` | 是否自动清理output目录中的文件 |

### 配置示例

**标准OpenAI配置：**
```bash
OPENAI_API_KEY=sk-your-openai-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
```

**Azure OpenAI配置：**
```bash
OPENAI_API_KEY=your-azure-key
OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/gpt-4
OPENAI_MODEL=gpt-4
```

**本地Ollama配置：**
```bash
OPENAI_API_KEY=not-needed
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=llama2
```

**文件管理配置：**
```bash
# 保留生成的文件（默认）
AUTO_CLEANUP_FILES=false

# 自动清理文件
AUTO_CLEANUP_FILES=true

# 保存生成的内容到 output/saved 目录（默认）
SAVE_GENERATED_CONTENT=true

# 公共URL基础地址（默认）
PUBLIC_URL_BASE=http://localhost:48089
```

> **注意**: `AUTO_CLEANUP_FILES` 只影响保存到 output 目录的文件，系统临时文件始终会被清理。

## 🌐 公共URL功能

系统支持将生成的图片保存为公共可访问的URL，方便直接在网页中使用或分享。

### 📋 使用方法

在获取任务结果时添加 `?public_url=1` 参数：

```bash
curl -H "X-API-Key: your-api-key" \
  "http://localhost:48089/api/v1/tasks/{task_id}/result?public_url=1"
```

### 🔄 功能对比

**默认模式（返回base64）**：
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "iVBORw0KGgoAAAANSUhEUgAA...",
        "mimeType": "image/png"
      }
    ]
  }
}
```

**公共URL模式（返回链接）**：
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "http://localhost:48089/api/v1/public/output/task_abc123_0.png",
        "mimeType": "image/png",
        "public_url": true
      }
    ]
  }
}
```

### ⚙️ 配置选项

通过 `PUBLIC_URL_BASE` 环境变量配置基础URL：
```bash
# 本地开发
PUBLIC_URL_BASE=http://localhost:48089

# 生产环境
PUBLIC_URL_BASE=https://your-domain.com
```

### 📁 文件存储

- **存储位置**: `output/public/` 目录
- **访问路径**: `/api/v1/public/output/{filename}`
- **文件命名**: `{task_id}_{index}.png`
- **权限**: 公开可访问，无需API密钥

### 🎯 使用场景

- **网页集成**: 直接在HTML中使用图片URL
- **移动应用**: 避免处理大量base64数据
- **分享链接**: 生成可分享的图片链接
- **缓存优化**: 浏览器可以缓存图片资源

## 💾 内容自动保存功能

系统会自动将所有生成的内容保存到 `output/saved` 目录下，便于后续查看和管理。

### 📁 保存目录结构

```
output/saved/
├── 2025-08-30/           # 按日期分组
│   ├── task_abc123/      # 按任务ID分组
│   │   ├── summary.md    # AI生成的总结内容
│   │   ├── card.html     # HTML卡片代码
│   │   ├── card.png      # 生成的高清图片
│   │   └── metadata.json # 任务元数据信息
│   └── task_def456/
│       ├── summary.md
│       ├── card.html
│       ├── card.png
│       └── metadata.json
```

### 📋 元数据信息

每个任务的 `metadata.json` 包含：
- 任务ID和创建时间
- 原始输入内容（文本或URL）
- 使用的总结提示词和风格提示词
- 各文件的大小信息
- 处理状态和结果

### ⚙️ 配置选项

通过 `SAVE_GENERATED_CONTENT` 环境变量控制：
- `true`（默认）：启用内容保存功能
- `false`：禁用内容保存功能

## 🎨 自定义提示词功能

所有卡片生成工具（`generate_card_from_text`、`generate_card_from_url`、`create_task_from_text`、`create_task_from_url`）都支持两个可选的自定义参数：

### 📝 总结提示词 (`summary_prompt`)
用于指导AI的总结方向和重点，让AI按照特定视角或需求进行内容总结。

### 🎨 风格提示词 (`style_prompt`)
用于自定义卡片的设计风格，指导HTML卡片的视觉呈现。

### 总结提示词示例

**视角导向**：
- `"重点关注技术细节"` - 从技术角度总结内容
- `"突出商业价值"` - 强调商业意义和价值点
- `"强调实用性"` - 侧重实际应用和操作指导
- `"侧重数据分析"` - 重点关注数据和统计信息

**内容侧重**：
- `"突出创新点和亮点"` - 强调新颖性和独特性
- `"关注风险和挑战"` - 重点分析潜在问题
- `"强调用户体验"` - 从用户角度进行总结
- `"侧重成本效益分析"` - 关注经济性和效率

### 风格提示词示例

**基础风格**：
- `"简约现代风格"` - 简洁、现代的设计
- `"商务专业"` - 正式、专业的商务风格
- `"温馨可爱"` - 温暖、友好的设计
- `"科技感十足"` - 未来感、科技感的设计

**具体要求**：
- `"使用深蓝色主题，简洁大方"` - 指定颜色和整体感觉
- `"卡通风格，色彩鲜艳"` - 指定设计风格和色彩特点
- `"极简黑白风格，突出文字"` - 指定配色方案和重点

### 智能融合机制

**总结方向融合**：
1. **内容分析**：首先分析原始内容的主题和结构
2. **方向指导**：根据用户的总结提示词调整分析重点
3. **智能平衡**：在保持内容完整性的基础上突出指定方向

**风格设计融合**：
1. **主题识别**：分析内容主题（商业、医疗、科技等）
2. **风格融合**：将用户风格要求与主题配色协调融合
3. **创意实现**：在保持功能性的前提下实现风格需求

## MCP工具说明

### 1. summarize_text
总结文本内容

**参数：**
- `text` (必需) - 需要总结的文本
- `style` (可选) - 总结风格：'简洁'、'详细'、'要点'、'专业'
- `max_length` (可选) - 最大长度，默认200字符

**示例：**
```json
{
  "text": "这是一篇很长的文章内容...",
  "style": "简洁",
  "max_length": "150"
}
```

### 2. fetch_and_summarize_url
抓取网页并总结

**参数：**
- `url` (必需) - 网页URL
- `style` (可选) - 总结风格
- `max_length` (可选) - 最大长度，默认300字符
- `include_url` (可选) - 是否包含原始URL

**示例：**
```json
{
  "url": "https://example.com/article",
  "style": "详细",
  "include_url": true
}
```

### 3. generate_card
生成智能卡片图片（返回base64编码的PNG图片）

**参数：**
- `content` (必需) - 卡片内容
- `title` (可选) - 卡片标题，不提供时自动生成
- `source_url` (可选) - 来源URL
- `color_theme` (可选) - 颜色主题：'blue'、'green'、'purple'、'orange'、'red'、'cyan'，默认'blue'
- `include_keywords` (可选) - 是否包含关键词，默认true
- `include_summary_stats` (可选) - 是否包含统计信息，默认true

**返回：**
- `image_base64` - base64编码的PNG图片数据
- `image_format` - 图片格式 (png)
- `title` - 卡片标题
- `keywords` - 提取的关键词列表
- `color_theme` - 使用的颜色主题
- `word_count` - 字数统计
- `char_count` - 字符数统计
- `read_time` - 预计阅读时间（分钟）

**示例：**
```json
{
  "content": "人工智能技术正在快速发展...",
  "title": "AI发展趋势",
  "color_theme": "blue",
  "include_keywords": true
}
```

**使用生成的图片：**
```html
<!-- 在HTML中直接展示 -->
<img src="data:image/png;base64,{image_base64}" alt="Smart Card" />
```

```python
# 保存为图片文件
import base64
with open('card.png', 'wb') as f:
    f.write(base64.b64decode(image_base64))
```

## API端点

### HTTP API (新功能)

Smart Card MCP 现在提供完整的 RESTful HTTP API，支持同步和异步调用：

- **API Base URL**: `http://localhost:48089/api/v1`
- **API 文档**: `http://localhost:48089/api/v1/docs` (Swagger UI)
- **OpenAPI 规范**: `http://localhost:48089/api/v1/docs/openapi.json`

#### API认证配置

生成安全的API密钥：
```bash
go run scripts/generate-api-key.go
```

或手动在.env文件中配置：
```bash
API_KEY=smart-card-mcp-your-secure-key-here
```

#### 主要功能

1. **同步工具调用**
   ```bash
   curl -X POST http://localhost:48089/api/v1/tools/generate_card_from_text/sync \
     -H "Content-Type: application/json" \
     -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     -d '{"arguments": {"text": "这是要生成卡片的文本内容", "summary_prompt": "重点关注技术细节", "style_prompt": "简约现代风格"}}'
   ```

2. **异步工具调用**
   ```bash
   curl -X POST http://localhost:48089/api/v1/tools/generate_card_from_text/async \
     -H "Content-Type: application/json" \
     -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     -d '{"arguments": {"text": "这是要生成卡片的文本内容", "summary_prompt": "突出商业价值", "style_prompt": "科技感十足"}}'
   ```

3. **任务管理**
   ```bash
   # 查询任务状态
   curl -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     http://localhost:48089/api/v1/tasks/{task_id}/status

   # 获取任务结果（返回base64图片数据）
   curl -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     http://localhost:48089/api/v1/tasks/{task_id}/result

   # 获取任务结果（返回公共URL链接）
   curl -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     "http://localhost:48089/api/v1/tasks/{task_id}/result?public_url=1"

   # 实时进度 (SSE)
   curl -H "Accept: text/event-stream" \
     -H "X-API-Key: smart-card-mcp-your-secure-key-here" \
     http://localhost:48089/api/v1/tasks/{task_id}/progress
   ```

## 🚀 异步API完整使用流程

### 📋 基本流程

1. **发起异步任务** → 获取任务ID
2. **轮询任务状态** → 监控执行进度
3. **获取最终结果** → 下载生成的图片

### 🐍 Python示例

```python
import requests
import time
import base64

class SmartCardClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Key": api_key
        }

    def create_task(self, url, summary_prompt=None, style_prompt=None):
        """创建异步任务"""
        payload = {"arguments": {"url": url}}
        if summary_prompt:
            payload["arguments"]["summary_prompt"] = summary_prompt
        if style_prompt:
            payload["arguments"]["style_prompt"] = style_prompt

        response = requests.post(
            f"{self.base_url}/tools/create_task_from_url/async",
            json=payload,
            headers=self.headers
        )
        return response.json()["data"]["task_id"]

    def wait_for_completion(self, task_id, timeout=300):
        """等待任务完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 查询状态
            response = requests.get(
                f"{self.base_url}/tasks/{task_id}/status",
                headers=self.headers
            )
            status_data = response.json()

            status = status_data.get("status")
            if status == "completed":
                # 获取结果
                result_response = requests.get(
                    f"{self.base_url}/tasks/{task_id}/result",
                    headers=self.headers
                )
                return result_response.json()
            elif status == "failed":
                raise Exception(f"任务失败: {status_data.get('error')}")

            time.sleep(2)  # 2秒轮询间隔

        raise TimeoutError("任务超时")

# 使用示例
client = SmartCardClient(
    "http://localhost:48089/api/v1",
    "smart-card-mcp-your-secure-key-here"
)

# 生成卡片
task_id = client.create_task(
    "https://example.com/article",
    summary_prompt="重点关注技术细节",
    style_prompt="简约现代风格"
)

result = client.wait_for_completion(task_id)

# 保存图片
for item in result["data"]["content"]:
    if item["type"] == "image":
        image_data = base64.b64decode(item["data"])
        with open("card.png", "wb") as f:
            f.write(image_data)
        break
```

### 🟨 JavaScript示例

```javascript
const axios = require('axios');

class SmartCardClient {
    constructor(baseUrl, apiKey) {
        this.client = axios.create({
            baseURL: baseUrl,
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': apiKey
            }
        });
    }

    async createTask(url, summaryPrompt, stylePrompt) {
        const payload = { arguments: { url } };
        if (summaryPrompt) payload.arguments.summary_prompt = summaryPrompt;
        if (stylePrompt) payload.arguments.style_prompt = stylePrompt;

        const response = await this.client.post('/tools/create_task_from_url/async', payload);
        return response.data.data.task_id;
    }

    async waitForCompletion(taskId, timeout = 300000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const statusResponse = await this.client.get(`/tasks/${taskId}/status`);
            const status = statusResponse.data.status;

            if (status === 'completed') {
                const resultResponse = await this.client.get(`/tasks/${taskId}/result`);
                return resultResponse.data;
            } else if (status === 'failed') {
                throw new Error(`任务失败: ${statusResponse.data.error}`);
            }

            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        throw new Error('任务超时');
    }
}

// 使用示例
(async () => {
    const client = new SmartCardClient(
        'http://localhost:48089/api/v1',
        'smart-card-mcp-your-secure-key-here'
    );

    const taskId = await client.createTask(
        'https://example.com/article',
        '重点关注技术细节',
        '简约现代风格'
    );

    const result = await client.waitForCompletion(taskId);
    console.log('任务完成!', result);
})();
```

### 📚 更多示例

查看 `examples/` 目录获取完整的客户端实现：
- `async_client_python.py` - Python完整实现
- `async_client_javascript.js` - JavaScript完整实现
- `async_client_bash.sh` - Bash/curl完整实现

详细的API文档请参考：`docs/async-api-guide.md`

4. **系统信息**
   ```bash
   # 健康检查
   curl http://localhost:48089/api/v1/health

   # 系统统计
   curl http://localhost:48089/api/v1/stats
   ```

### 传统MCP端点
- **SSE模式**: `http://localhost:48088`
- **HTTP模式**: `http://localhost:48089/mcp`

### 健康检查
```
GET /health
GET /api/v1/health
```

返回服务健康状态。

## 开发指南

### 项目结构
```
smart-card-mcp/
├── main.go                 # 主程序入口
├── go.mod                  # Go模块定义
├── internal/
│   ├── config/            # 配置管理
│   ├── tools/             # MCP工具实现
│   └── transport/         # 传输层实现
├── templates/             # HTML模板
├── output/               # 输出目录
├── logs/                 # 日志目录
├── Dockerfile            # Docker镜像构建
├── docker-compose.yml    # Docker Compose配置
└── README.md            # 项目文档
```

### 添加新工具

1. 在 `internal/tools/definitions.go` 中定义工具
2. 在 `internal/tools/handlers.go` 中实现处理逻辑
3. 在 `internal/transport/server.go` 中注册工具

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的API密钥是否正确
   - 确认API密钥有足够的权限

2. **端口冲突**
   - 修改 `docker-compose.yml` 中的端口映射
   - 或设置 `MCP_PORTS` 环境变量

3. **网页抓取失败**
   - 检查 `JINA_API_KEY` 是否配置
   - 确认目标网站可以访问

### 日志查看

```bash
# Docker Compose日志
docker-compose logs -f smart-card-mcp

# 容器内日志
docker exec -it smart-card-mcp-server tail -f /app/logs/app.log
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题或建议，请通过GitHub Issues联系我们。
