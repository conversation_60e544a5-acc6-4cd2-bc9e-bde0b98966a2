# DStaff Response Test Script
Write-Host "Testing DStaff upload response format..." -ForegroundColor Cyan

$BaseURL = "http://localhost:48089"
$MCPEndpoint = "$BaseURL/mcp"

$headers = @{
    "Authorization" = "Bearer test_dstaff_response_token_123"
    "Content-Type" = "application/json"
}

# Create task
Write-Host "Creating async task..." -ForegroundColor Yellow
$createBody = @"
{
    "method": "tools/call",
    "params": {
        "name": "create_task_from_text",
        "arguments": {
            "text": "Test DStaff response format - this should return upload response instead of image",
            "Context": {
                "task_id": "dstaff_response_test_123"
            }
        }
    }
}
"@

try {
    $createResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $createBody -TimeoutSec 10
    $createResult = $createResponse.Content | ConvertFrom-Json
    $taskId = $createResult.result.task_id
    Write-Host "Task created: $taskId" -ForegroundColor Green
} catch {
    Write-Host "Task creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait for completion
Write-Host "Waiting for task completion..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Get result - this should return DStaff upload response, not image
Write-Host "Getting task result (should return DStaff upload response)..." -ForegroundColor Yellow
$resultBody = @"
{
    "method": "tools/call",
    "params": {
        "name": "get_task_result",
        "arguments": {
            "task_id": "$taskId",
            "Context": {
                "task_id": "dstaff_response_test_123"
            }
        }
    }
}
"@

try {
    $resultResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $resultBody -TimeoutSec 15
    Write-Host "Result retrieved successfully!" -ForegroundColor Green
    
    $result = $resultResponse.Content | ConvertFrom-Json
    
    # Check if we got DStaff upload response instead of image
    if ($result.result.content -and $result.result.content[0].type -eq "text") {
        Write-Host "✅ Got text response (DStaff upload response)" -ForegroundColor Green
        
        # Try to parse the JSON content
        try {
            $uploadResponse = $result.result.content[0].text | ConvertFrom-Json
            Write-Host "✅ Upload response parsed successfully:" -ForegroundColor Green
            Write-Host "   Status: $($uploadResponse.status)" -ForegroundColor White
            Write-Host "   Message: $($uploadResponse.message)" -ForegroundColor White
            Write-Host "   WorkType: $($uploadResponse.workType)" -ForegroundColor White
            
            if ($uploadResponse.attachments -and $uploadResponse.attachments.Count -gt 0) {
                Write-Host "   Attachment:" -ForegroundColor White
                Write-Host "     Path: $($uploadResponse.attachments[0].path)" -ForegroundColor Gray
                Write-Host "     Filename: $($uploadResponse.attachments[0].filename)" -ForegroundColor Gray
                Write-Host "     Type: $($uploadResponse.attachments[0].type)" -ForegroundColor Gray
                Write-Host "     ContentType: $($uploadResponse.attachments[0].contentType)" -ForegroundColor Gray
                Write-Host "     ContentLength: $($uploadResponse.attachments[0].contentLength)" -ForegroundColor Gray
            }
        } catch {
            Write-Host "⚠️ Could not parse upload response JSON: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "Raw text: $($result.result.content[0].text)" -ForegroundColor Gray
        }
        
    } elseif ($result.result.content -and $result.result.content[0].type -eq "image") {
        Write-Host "❌ Still got image response (DStaff upload may have failed)" -ForegroundColor Red
        Write-Host "   This means DStaff upload was not successful or not attempted" -ForegroundColor Yellow
        
    } else {
        Write-Host "⚠️ Unexpected response format" -ForegroundColor Yellow
        Write-Host "Response: $($result | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "Result retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Expected behavior:" -ForegroundColor Cyan
Write-Host "✅ When DStaff upload is successful:" -ForegroundColor Green
Write-Host "   - Response should be text type (not image)" -ForegroundColor White
Write-Host "   - Content should be JSON with upload response" -ForegroundColor White
Write-Host "   - Should include status, message, and attachments" -ForegroundColor White
Write-Host ""
Write-Host "❌ When DStaff upload fails or is not attempted:" -ForegroundColor Red
Write-Host "   - Response falls back to image type" -ForegroundColor White
Write-Host "   - Content contains the actual image data" -ForegroundColor White

Write-Host ""
Write-Host "Check server logs for detailed upload process:" -ForegroundColor Cyan
Write-Host "docker compose logs smart-card-mcp --tail=50" -ForegroundColor Gray
