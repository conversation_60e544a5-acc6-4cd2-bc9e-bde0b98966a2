# Smart Card MCP 二进制更新脚本
# 用于从 Docker 容器中提取最新的二进制文件

param(
    [string]$ContainerName = "smart-card-mcp-server",
    [string]$BinaryPath = "/app/smart-card-mcp",
    [string]$OutputDir = "bin"
)

Write-Host "🔄 开始更新 Smart Card MCP 二进制文件..." -ForegroundColor Green

# 检查容器是否运行
$containerCheck = docker ps --filter "name=$ContainerName" --format "{{.Names}}" 2>$null
if ([string]::IsNullOrEmpty($containerCheck)) {
    Write-Host "❌ 容器 '$ContainerName' 未运行，请先启动容器" -ForegroundColor Red
    Write-Host "💡 运行: docker compose up -d" -ForegroundColor Yellow
    exit 1
}

$containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"

Write-Host "✅ 容器状态: $containerStatus" -ForegroundColor Green

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "📁 创建目录: $OutputDir" -ForegroundColor Blue
}

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$timestampedFile = "$OutputDir/smart-card-mcp-linux-$timestamp"
$latestFile = "$OutputDir/smart-card-mcp-linux-latest"

# 从容器拷贝二进制文件
Write-Host "📦 从容器拷贝二进制文件..." -ForegroundColor Blue
$copyResult = docker cp "${ContainerName}:${BinaryPath}" $timestampedFile 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 拷贝失败: $copyResult" -ForegroundColor Red
    exit 1
}

# 创建最新版本的符号链接/拷贝
if (Test-Path $latestFile) {
    Remove-Item $latestFile -Force
}
Copy-Item $timestampedFile $latestFile

# 获取文件大小
$fileSize = (Get-Item $timestampedFile).Length
$fileSizeMB = [math]::Round($fileSize / 1MB, 2)

Write-Host "✅ 二进制文件更新完成!" -ForegroundColor Green
Write-Host "📄 时间戳版本: $timestampedFile ($fileSizeMB MB)" -ForegroundColor Cyan
Write-Host "🔗 最新版本: $latestFile ($fileSizeMB MB)" -ForegroundColor Cyan

# 显示目录内容
Write-Host "`n📋 $OutputDir 目录内容:" -ForegroundColor Yellow
Get-ChildItem $OutputDir | Format-Table Name, Length, LastWriteTime -AutoSize

Write-Host "`n💡 使用方法:" -ForegroundColor Yellow
Write-Host "   - 直接运行: ./$latestFile" -ForegroundColor White
Write-Host "   - 或使用时间戳版本: ./$timestampedFile" -ForegroundColor White
Write-Host "   - 查看版本: ./$latestFile --version" -ForegroundColor White

Write-Host "`n🎉 更新完成!" -ForegroundColor Green
