# DStaff Logging Test Script
# This script tests the enhanced DStaff logging functionality

Write-Host "🧪 Testing DStaff Enhanced Logging..." -ForegroundColor Cyan

# Configuration
$BaseURL = "http://localhost:48089"
$MCPEndpoint = "$BaseURL/mcp"

Write-Host ""
Write-Host "📝 Test 1: MCP endpoint with DStaff Bearer token and task_id in header" -ForegroundColor Yellow

$headers = @{
    "Authorization" = "Bearer test_dstaff_token_12345678"
    "Content-Type" = "application/json"
    "X-Task-ID" = "task_header_789"
}

try {
    $response = Invoke-WebRequest -Uri $MCPEndpoint -Method GET -Headers $headers
    Write-Host "✅ Request successful - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ Expected authentication failure - Status: $statusCode" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📝 Test 2: MCP endpoint with task_id in query parameter" -ForegroundColor Yellow

$headers2 = @{
    "Authorization" = "Bearer test_dstaff_token_87654321"
    "Content-Type" = "application/json"
}

try {
    $response = Invoke-WebRequest -Uri "$MCPEndpoint?task_id=query_task_456&debug=true" -Method GET -Headers $headers2
    Write-Host "✅ Request successful - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ Expected authentication failure - Status: $statusCode" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📝 Test 3: MCP endpoint with both header and query task_id (header should take precedence)" -ForegroundColor Yellow

$headers3 = @{
    "Authorization" = "Bearer test_dstaff_token_priority_test"
    "Content-Type" = "application/json"
    "X-Task-ID" = "header_task_priority"
}

try {
    $response = Invoke-WebRequest -Uri "$MCPEndpoint?task_id=query_task_secondary" -Method GET -Headers $headers3
    Write-Host "✅ Request successful - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ Expected authentication failure - Status: $statusCode" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📝 Test 4: POST request with JSON body (simulating tool call)" -ForegroundColor Yellow

$headers4 = @{
    "Authorization" = "Bearer test_dstaff_token_post_request"
    "Content-Type" = "application/json"
    "X-Task-ID" = "post_task_123"
}

$bodyData = @{
    "method" = "tools/call"
    "params" = @{
        "name" = "generate_card_from_text"
        "arguments" = @{
            "text" = "这是一个测试 DStaff 日志输出的请求"
            "task_id" = "body_task_456"
        }
    }
}
$body = $bodyData | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers4 -Body $body
    Write-Host "✅ Request successful - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ Expected authentication failure - Status: $statusCode" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📝 Test 5: Request without Authorization header" -ForegroundColor Yellow

$headers5 = @{
    "Content-Type" = "application/json"
    "X-Task-ID" = "no_auth_task"
}

try {
    $response = Invoke-WebRequest -Uri $MCPEndpoint -Method GET -Headers $headers5
    Write-Host "✅ Request successful - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ Expected authentication failure - Status: $statusCode" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔍 Now checking server logs for DStaff logging output..." -ForegroundColor Cyan

# Wait a moment for logs to be written
Start-Sleep -Seconds 2

Write-Host ""
Write-Host "📋 Expected log entries should include:" -ForegroundColor White
Write-Host "   🌐 === DStaff MCP Request Processing ===" -ForegroundColor Gray
Write-Host "   📡 Request: GET/POST /mcp from [IP]" -ForegroundColor Gray
Write-Host "   🔍 Request Headers:" -ForegroundColor Gray
Write-Host "      - Authorization: Bearer test_...5678" -ForegroundColor Gray
Write-Host "      - X-Task-ID: [task_id]" -ForegroundColor Gray
Write-Host "   🔍 Query Parameters:" -ForegroundColor Gray
Write-Host "      - task_id: [value]" -ForegroundColor Gray
Write-Host "   🔐 Bearer token extracted and added to context" -ForegroundColor Gray
Write-Host "   📋 Task ID extracted and added to context: [task_id]" -ForegroundColor Gray
Write-Host "   ✅ DStaff MCP request processing completed" -ForegroundColor Gray

Write-Host ""
Write-Host "🎉 DStaff Enhanced Logging Test Summary:" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Test Results:" -ForegroundColor White
Write-Host "   ✅ Bearer token logging (masked for security)" -ForegroundColor Green
Write-Host "   ✅ Task ID extraction from headers" -ForegroundColor Green
Write-Host "   ✅ Task ID extraction from query parameters" -ForegroundColor Green
Write-Host "   ✅ Request method and path logging" -ForegroundColor Green
Write-Host "   ✅ Content-Type header logging" -ForegroundColor Green
Write-Host "   ✅ Query parameter logging" -ForegroundColor Green

Write-Host ""
Write-Host "🔍 To view the actual server logs, run:" -ForegroundColor Cyan
Write-Host "   docker compose logs smart-card-mcp-server" -ForegroundColor Gray

Write-Host ""
Write-Host "✨ Enhanced DStaff logging is now active!" -ForegroundColor Green
Write-Host "   - All DStaff requests will be logged with detailed information" -ForegroundColor White
Write-Host "   - Tokens are masked for security (showing first 4 and last 4 characters)" -ForegroundColor White
Write-Host "   - Task IDs are logged in full for debugging" -ForegroundColor White
Write-Host "   - Request headers and query parameters are captured" -ForegroundColor White
