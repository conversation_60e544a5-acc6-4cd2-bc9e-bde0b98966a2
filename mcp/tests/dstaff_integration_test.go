package tests

import (
	"context"
	"os"
	"testing"

	"ppt-narrator-mcp/internal/auth"
	"ppt-narrator-mcp/internal/config"
)

// TestDStaffConfigLoading tests DStaff configuration loading
func TestDStaffConfigLoading(t *testing.T) {
	// Set test environment variables
	os.Setenv("DSTAFF_ENABLED", "true")
	os.Setenv("DSTAFF_ENDPOINT_URL", "http://test.example.com")
	os.Setenv("DSTAFF_USE_OFFICIAL_AUTH", "true")
	defer func() {
		os.Unsetenv("DSTAFF_ENABLED")
		os.Unsetenv("DSTAFF_ENDPOINT_URL")
		os.Unsetenv("DSTAFF_USE_OFFICIAL_AUTH")
	}()

	cfg, err := config.Load()
	if err != nil {
		t.Fatalf("Failed to load configuration: %v", err)
	}

	if !cfg.DStaff.Enabled {
		t.Error("Expected DStaff to be enabled")
	}

	if cfg.DStaff.EndpointURL != "http://test.example.com" {
		t.Errorf("Expected endpoint URL to be 'http://test.example.com', got '%s'", cfg.DStaff.EndpointURL)
	}

	if !cfg.DStaff.UseOfficialAuth {
		t.Error("Expected UseOfficialAuth to be true")
	}

	expectedAuthURL := "http://test.example.com/api/v1/mcp/validateToken"
	if cfg.DStaff.AuthServiceURL != expectedAuthURL {
		t.Errorf("Expected auth service URL to be '%s', got '%s'", expectedAuthURL, cfg.DStaff.AuthServiceURL)
	}
}

// TestDStaffAuthContext tests DStaff authentication context
func TestDStaffAuthContext(t *testing.T) {
	authCtx := &auth.DStaffAuthContext{
		Token:  "test_token_123",
		TaskID: "test_task_456",
	}

	if authCtx.Token != "test_token_123" {
		t.Errorf("Expected token to be 'test_token_123', got '%s'", authCtx.Token)
	}

	if authCtx.TaskID != "test_task_456" {
		t.Errorf("Expected task ID to be 'test_task_456', got '%s'", authCtx.TaskID)
	}
}

// TestExtractTaskIDFromContext tests task ID extraction from context
func TestExtractTaskIDFromContext(t *testing.T) {
	// Test with context value
	ctx := context.Background()
	ctx = context.WithValue(ctx, "dstaff_task_id", "context_task_id")

	// Note: This test would require a mock HTTP request to fully test
	// For now, we just test that the context value is set correctly
	if taskID := ctx.Value("dstaff_task_id"); taskID != "context_task_id" {
		t.Errorf("Expected task ID to be 'context_task_id', got '%v'", taskID)
	}
}

// TestDStaffConfigDisabled tests behavior when DStaff is disabled
func TestDStaffConfigDisabled(t *testing.T) {
	// Ensure DStaff is disabled
	os.Setenv("DSTAFF_ENABLED", "false")
	defer os.Unsetenv("DSTAFF_ENABLED")

	cfg, err := config.Load()
	if err != nil {
		t.Fatalf("Failed to load configuration: %v", err)
	}

	if cfg.DStaff.Enabled {
		t.Error("Expected DStaff to be disabled")
	}
}

// TestTokenMasking tests token masking for logging
func TestTokenMasking(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"", "***"},
		{"abc", "***"},
		{"abcdefgh", "***"},
		{"abcdefghi", "abcd...fghi"},
		{"very_long_token_12345", "very...2345"},
	}

	for _, test := range tests {
		result := maskToken(test.input)
		if result != test.expected {
			t.Errorf("For input '%s', expected '%s', got '%s'", test.input, test.expected, result)
		}
	}
}

// maskToken masks a token for safe logging (helper function for testing)
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "..." + token[len(token)-4:]
}
