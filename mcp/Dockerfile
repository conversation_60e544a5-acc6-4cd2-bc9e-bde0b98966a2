# Build stage
FROM golang:1.23-alpine AS builder

# Install git and ca-certificates (needed for fetching dependencies and HTTPS)
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ppt-narrator-mcp .

# Final stage
FROM alpine:latest

# Install ca-certificates, tzdata, and wget for health checks
RUN apk --no-cache add ca-certificates tzdata wget

# PPT Narrator MCP doesn't need heavy dependencies like Chromium or Puppeteer
# since it delegates processing to the PPT Narrator backend

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/ppt-narrator-mcp .

# Create necessary directories
RUN mkdir -p output templates logs data && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 48088 48089 49083

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:49083/health || exit 1

# Default command
CMD ["./ppt-narrator-mcp"]
