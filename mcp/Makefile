# Smart Card MCP Server Makefile

# Variables
BINARY_NAME=smart-card-mcp
DOCKER_IMAGE=smart-card-mcp
DOCKER_TAG=latest
GO_VERSION=1.21

# Default target
.PHONY: help
help: ## Show this help message
	@echo "Smart Card MCP Server - Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Development
.PHONY: dev
dev: ## Run the server in development mode
	@echo "🚀 Starting Smart Card MCP Server in development mode..."
	go run main.go -transport mixed

.PHONY: build
build: ## Build the binary
	@echo "🔨 Building $(BINARY_NAME)..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o $(BINARY_NAME) .
	@echo "✅ Build complete: $(BINARY_NAME)"

.PHONY: clean
clean: ## Clean build artifacts
	@echo "🧹 Cleaning build artifacts..."
	rm -f $(BINARY_NAME)
	rm -rf output/*
	rm -rf logs/*
	@echo "✅ Clean complete"

.PHONY: test
test: ## Run tests
	@echo "🧪 Running tests..."
	go test -v ./...
	@echo "✅ Tests complete"

.PHONY: test-tools
test-tools: ## Run tool integration tests
	@echo "🔧 Running tool integration tests..."
	go run examples/test_tools.go
	@echo "✅ Tool tests complete"

# Dependencies
.PHONY: deps
deps: ## Download dependencies
	@echo "📦 Downloading dependencies..."
	go mod download
	go mod tidy
	@echo "✅ Dependencies updated"

.PHONY: deps-update
deps-update: ## Update dependencies
	@echo "🔄 Updating dependencies..."
	go get -u ./...
	go mod tidy
	@echo "✅ Dependencies updated"

# Docker
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "🐳 Building Docker image $(DOCKER_IMAGE):$(DOCKER_TAG)..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	@echo "✅ Docker image built: $(DOCKER_IMAGE):$(DOCKER_TAG)"

.PHONY: docker-run
docker-run: ## Run Docker container
	@echo "🐳 Running Docker container..."
	docker run --rm -p 48083:48083 -p 48084:48084 -p 49083:49083 \
		--env-file .env \
		$(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-compose-up
docker-compose-up: ## Start services with Docker Compose
	@echo "🐳 Starting services with Docker Compose..."
	docker-compose up -d
	@echo "✅ Services started"

.PHONY: docker-compose-down
docker-compose-down: ## Stop services with Docker Compose
	@echo "🐳 Stopping services with Docker Compose..."
	docker-compose down
	@echo "✅ Services stopped"

.PHONY: docker-compose-logs
docker-compose-logs: ## Show Docker Compose logs
	docker-compose logs -f

.PHONY: docker-compose-build
docker-compose-build: ## Build and start services with Docker Compose
	@echo "🐳 Building and starting services..."
	docker-compose up --build -d
	@echo "✅ Services built and started"

# Linting and formatting
.PHONY: fmt
fmt: ## Format Go code
	@echo "🎨 Formatting Go code..."
	go fmt ./...
	@echo "✅ Code formatted"

.PHONY: lint
lint: ## Run linter
	@echo "🔍 Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

.PHONY: vet
vet: ## Run go vet
	@echo "🔍 Running go vet..."
	go vet ./...
	@echo "✅ Vet complete"

# Setup and installation
.PHONY: setup
setup: ## Setup development environment
	@echo "⚙️  Setting up development environment..."
	@if [ ! -f .env ]; then \
		echo "📝 Creating .env file from .env.example..."; \
		cp .env.example .env; \
		echo "⚠️  Please edit .env file with your API keys"; \
	fi
	@echo "📦 Installing dependencies..."
	go mod download
	@echo "📁 Creating directories..."
	mkdir -p output logs data templates
	@echo "✅ Setup complete"

.PHONY: install-tools
install-tools: ## Install development tools
	@echo "🔧 Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "✅ Tools installed"

# Health checks
.PHONY: health
health: ## Check service health
	@echo "🏥 Checking service health..."
	@curl -f http://localhost:49083/health || echo "❌ Service not healthy"

.PHONY: status
status: ## Show service status
	@echo "📊 Service status:"
	@docker-compose ps 2>/dev/null || echo "Docker Compose not running"
	@echo ""
	@echo "Health check:"
	@curl -s http://localhost:49083/health 2>/dev/null | jq . || echo "Service not responding"

# Utility
.PHONY: logs
logs: ## Show application logs
	@echo "📋 Application logs:"
	@if [ -f logs/app.log ]; then \
		tail -f logs/app.log; \
	else \
		echo "No log file found. Try: make docker-compose-logs"; \
	fi

.PHONY: env-check
env-check: ## Check environment variables
	@echo "🔍 Checking environment variables..."
	@echo "OPENAI_API_KEY: $${OPENAI_API_KEY:+✅ Set}$${OPENAI_API_KEY:-❌ Not set}"
	@echo "JINA_API_KEY: $${JINA_API_KEY:+✅ Set}$${JINA_API_KEY:-❌ Not set}"
	@echo "MCP_TRANSPORT: $${MCP_TRANSPORT:-stdio (default)}"
	@echo "LOG_LEVEL: $${LOG_LEVEL:-INFO (default)}"

# Release
.PHONY: release
release: clean build docker-build ## Build release artifacts
	@echo "🚀 Release build complete"
	@echo "Binary: $(BINARY_NAME)"
	@echo "Docker image: $(DOCKER_IMAGE):$(DOCKER_TAG)"

# All-in-one commands
.PHONY: start
start: docker-compose-up ## Start all services
	@echo "🎉 Smart Card MCP Server is starting..."
	@echo "📡 SSE endpoint: http://localhost:48083"
	@echo "🌐 HTTP endpoint: http://localhost:48084"
	@echo "🏥 Health check: http://localhost:49083/health"

.PHONY: stop
stop: docker-compose-down ## Stop all services

.PHONY: restart
restart: stop start ## Restart all services

.PHONY: full-setup
full-setup: setup install-tools deps docker-build ## Complete setup for new development environment
	@echo "🎉 Full setup complete!"
	@echo "Next steps:"
	@echo "1. Edit .env file with your API keys"
	@echo "2. Run 'make start' to start the services"
	@echo "3. Run 'make test-tools' to verify everything works"
