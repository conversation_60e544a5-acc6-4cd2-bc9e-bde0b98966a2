package main

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

func main() {
	if len(os.Args) > 1 && (os.Args[1] == "-h" || os.Args[1] == "--help") {
		printHelp()
		return
	}

	// Generate a secure API key
	apiKey := generateAPIKey()

	fmt.Printf("🔑 Generated API Key: %s\n\n", apiKey)

	// Try to update .env file
	if err := updateEnvFile(apiKey); err != nil {
		fmt.Printf("⚠️  Could not update .env file: %v\n", err)
		fmt.Printf("Please manually add this line to your .env file:\n")
		fmt.Printf("API_KEY=%s\n", apiKey)
	} else {
		fmt.Printf("✅ Updated .env file with new API key\n")
	}

	fmt.Printf("\n📋 Usage examples:\n")
	fmt.Printf("curl -H \"X-API-Key: %s\" http://localhost:48089/api/v1/tools\n", apiKey)
	fmt.Printf("curl -H \"Authorization: Bearer %s\" http://localhost:48089/api/v1/health\n", apiKey)
}

func printHelp() {
	fmt.Printf(`Smart Card MCP API Key Generator

Usage: go run scripts/generate-api-key.go

This tool generates a secure API key for the Smart Card MCP HTTP API
and optionally updates your .env file.

The generated key will be a 32-character hexadecimal string that provides
sufficient entropy for secure API authentication.

Options:
  -h, --help    Show this help message

Examples:
  go run scripts/generate-api-key.go
`)
}

func generateAPIKey() string {
	// Generate 16 random bytes (128 bits of entropy)
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		log.Fatalf("Failed to generate random bytes: %v", err)
	}

	// Convert to hexadecimal string
	return "ppt-narrator-mcp-" + hex.EncodeToString(bytes)
}

func updateEnvFile(apiKey string) error {
	envPath := ".env"

	// Check if .env file exists
	if _, err := os.Stat(envPath); os.IsNotExist(err) {
		// Create new .env file
		return createEnvFile(envPath, apiKey)
	}

	// Read existing .env file
	content, err := os.ReadFile(envPath)
	if err != nil {
		return fmt.Errorf("failed to read .env file: %w", err)
	}

	lines := strings.Split(string(content), "\n")
	updated := false

	// Update existing API_KEY line or add new one
	for i, line := range lines {
		if strings.HasPrefix(line, "API_KEY=") {
			lines[i] = fmt.Sprintf("API_KEY=%s", apiKey)
			updated = true
			break
		}
	}

	// If no API_KEY line found, add it
	if !updated {
		// Find a good place to insert it (after authentication section if exists)
		insertIndex := len(lines)
		for i, line := range lines {
			if strings.Contains(line, "Authentication") || strings.HasPrefix(line, "SSE_ACCESS_KEY=") {
				insertIndex = i + 1
				break
			}
		}

		// Insert the new API_KEY line
		newLines := make([]string, 0, len(lines)+1)
		newLines = append(newLines, lines[:insertIndex]...)
		newLines = append(newLines, fmt.Sprintf("API_KEY=%s", apiKey))
		newLines = append(newLines, lines[insertIndex:]...)
		lines = newLines
	}

	// Write back to file
	newContent := strings.Join(lines, "\n")
	return os.WriteFile(envPath, []byte(newContent), 0644)
}

func createEnvFile(envPath, apiKey string) error {
	content := fmt.Sprintf(`# Smart Card MCP Configuration

# Authentication Configuration
API_KEY=%s

# Transport Configuration
MCP_TRANSPORT=mixed
MCP_PORTS=48088,48089

# API Configuration (choose one)
# ARK API (recommended)
ARK_API_KEY=your_ark_api_key_here
ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
ARK_MODEL=deepseek-v3-250324

# OpenAI API (alternative)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Jina API for web content fetching
JINA_API_KEY=your_jina_api_key_here

# Server Configuration
LOG_LEVEL=INFO
OUTPUT_DIR=output
TEMPLATES_DIR=templates
`, apiKey)

	return os.WriteFile(envPath, []byte(content), 0644)
}
