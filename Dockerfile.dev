# Development Dockerfile with hot reload support
FROM golang:1.23-bullseye

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=UTC \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    GO111MODULE=on \
    CGO_ENABLED=0

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libreoffice \
    libreoffice-writer \
    libreoffice-impress \
    libreoffice-calc \
    ffmpeg \
    curl \
    ca-certificates \
    fonts-liberation \
    fonts-dejavu-core \
    fontconfig \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install development tools
RUN go install github.com/cosmtrek/air@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest && \
    go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Set working directory
WORKDIR /app

# Copy go mod files
COPY backend/go.mod backend/go.sum ./

# Download dependencies
RUN go mod download

# Create necessary directories
RUN mkdir -p uploads screenshots videos temp logs

# Expose port
EXPOSE 8080

# Default command for development (can be overridden)
CMD ["air", "-c", ".air.toml"]
