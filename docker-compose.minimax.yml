version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=ppt_narrator
      - POSTGRES_USER=ppt_narrator
      - POSTGRES_PASSWORD=ppt_narrator123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - LC_ALL=C.UTF-8
      - LANG=C.UTF-8
    volumes:
      - postgres_data_minimax:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ppt_narrator -d ppt_narrator"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  mcp-pipeline-server:
    build: ./mcp
    ports:
      - "48080:48080"
      - "48081:48081"
    environment:
      - PORT=48080
      - PPT_NARRATOR_URL=http://ppt-narrator:8080
      - MCP_ACCESS_KEY=test123
      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-false}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-http://*********:8800}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-false}
      - MCP_DEBUG=${MCP_DEBUG:-false}
    restart: unless-stopped
    depends_on:
      - ppt-narrator

  ppt-narrator:
    build: .
    ports:
      - "38080:8080"
    environment:
      - PORT=8080
      - DATABASE_URL=*****************************************************/ppt_narrator?sslmode=disable&client_encoding=UTF8
      - UPLOAD_DIR=/app/uploads
      - SCREENSHOT_DIR=/app/screenshots
      - VIDEO_DIR=/app/videos
      - TEMP_DIR=/app/temp
      # MiniMax AI Configuration
      - AI_PROVIDER=minimax
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - MINIMAX_GROUP_ID=${MINIMAX_GROUP_ID}
      - MINIMAX_MODEL=${MINIMAX_MODEL:-abab6.5s-chat}
      - MINIMAX_BASE_URL=${MINIMAX_BASE_URL:-https://api.minimaxi.com/v1/text/chatcompletion_v2}
      # MiniMax TTS Configuration
      - TTS_PROVIDER=minimax
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID}
      - MINIMAX_TTS_MODEL=${MINIMAX_TTS_MODEL:-speech-02-hd}
      - MINIMAX_TTS_VOICE_ID=${MINIMAX_TTS_VOICE_ID:-male-qn-qingse}
      - MINIMAX_TTS_EMOTION=${MINIMAX_TTS_EMOTION:-happy}
      # EasyVoice TTS 配置
      - EASYVOICE_API_URL=${EASYVOICE_API_URL:-https://easyvoice.wetolink.com/api/v1/tts/generate}
      - EASYVOICE_USERNAME=${EASYVOICE_USERNAME:-}
      - EASYVOICE_PASSWORD=${EASYVOICE_PASSWORD:-}
      - EASYVOICE_VOICE=${EASYVOICE_VOICE:-zh-CN-YunxiNeural}
      - EASYVOICE_RATE=${EASYVOICE_RATE:-0%}
      - EASYVOICE_PITCH=${EASYVOICE_PITCH:-0Hz}
      - EASYVOICE_VOLUME=${EASYVOICE_VOLUME:-0%}
      # System Configuration
      - LIBREOFFICE_PATH=libreoffice
      - FFMPEG_PATH=ffmpeg
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-}
      - NARRATOR_ROLE=${NARRATOR_ROLE:-资深教授}
      - NARRATOR_STYLE=${NARRATOR_STYLE:-亲切自然}
      - TARGET_AUDIENCE=${TARGET_AUDIENCE:-大学生}
      - SPEAKING_TONE=${SPEAKING_TONE:-轻松友好}
      - SPEECH_NATURALNESS=${SPEECH_NATURALNESS:-高度口语化}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - TEMPERATURE=${TEMPERATURE:-0.7}
    volumes:
      - ppt_uploads_minimax:/app/uploads
      - ppt_screenshots_minimax:/app/screenshots
      - ppt_videos_minimax:/app/videos
      - ppt_temp_minimax:/app/temp
      - ppt_audio_minimax:/app/audio
      - ppt_work_minimax:/app/work
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data_minimax:
  ppt_uploads_minimax:
  ppt_screenshots_minimax:
  ppt_videos_minimax:
  ppt_temp_minimax:
  ppt_audio_minimax:
  ppt_work_minimax:
