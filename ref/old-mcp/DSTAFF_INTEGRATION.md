# DStaff平台集成说明

本文档说明如何配置MCP Pipeline Server以集成DStaff平台的鉴权和文件上传下载功能。

## 功能特性

- **双重鉴权支持**：支持传统的访问密钥鉴权和DStaff官方Bearer Token鉴权
- **文件下载集成**：upload_file工具支持从DStaff平台下载文件
- **详细日志记录**：所有MCP工具调用和文件操作都输出详细日志用于调试
- **无缝切换**：通过环境变量控制鉴权方式，保持向后兼容

## 配置方式

### 1. 环境变量配置

```bash
# 启用DStaff集成
DSTAFF_ENABLED=true

# DStaff平台端点URL
DSTAFF_ENDPOINT_URL=http://*********:8800

# 使用DStaff官方鉴权
DSTAFF_USE_OFFICIAL_AUTH=true

# 传统鉴权（可选，作为备用）
MCP_ACCESS_KEY=your_secret_key_here
```

### 2. Docker Compose配置

在`docker-compose.yml`中的`mcp-pipeline-server`服务中添加：

```yaml
environment:
  - DSTAFF_ENABLED=true
  - DSTAFF_ENDPOINT_URL=http://*********:8800
  - DSTAFF_USE_OFFICIAL_AUTH=true
  - MCP_ACCESS_KEY=your_backup_key
```

## 鉴权模式

### 模式1：DStaff官方鉴权（推荐）

```bash
DSTAFF_ENABLED=true
DSTAFF_USE_OFFICIAL_AUTH=true
```

- 所有请求都需要在`Authorization`头中提供Bearer Token
- Token通过DStaff的`/api/v1/mcp/validateToken`接口验证
- 支持文件上传下载功能

### 模式2：传统鉴权

```bash
DSTAFF_ENABLED=false
MCP_ACCESS_KEY=your_secret_key
```

- 只有`/sse`端点需要验证
- 使用查询参数`?key=YOUR_ACCESS_KEY`进行验证
- 不支持DStaff文件上传下载功能

### 模式3：混合模式

```bash
DSTAFF_ENABLED=true
DSTAFF_USE_OFFICIAL_AUTH=false
MCP_ACCESS_KEY=your_secret_key
```

- 启用DStaff文件上传下载功能
- 使用传统的访问密钥鉴权
- 适用于内部使用或小范围部署

## DStaff透明文件处理

### 透明文件上传

当启用DStaff集成时，系统会自动识别包含`multipart/form-data`的POST请求并透明地上传到DStaff平台。

**触发条件：**
- HTTP方法：POST
- Content-Type：包含`multipart/form-data`
- Authorization头：包含有效的Bearer Token

**支持的表单字段：**
- `file`: 要上传的文件
- `project_name`: 项目名称（可选，用于组织文件路径）
- `task_id`: 任务ID（可选，默认为"default_task"）

**自动生成的目标路径：**
- 无项目名称：`mcps_upload/{filename}`
- 有项目名称：`mcps_upload/{project_name}/{filename}`

### 透明文件下载

当启用DStaff集成时，系统会自动处理`/download/`路径的GET请求并从DStaff平台下载文件。

**URL格式：**
```
GET /download/{task_id}/{file_path}
```

**示例：**
```
GET /download/task_123/videos/presentation.mp4
Authorization: Bearer your_token_here
```

## 文件路径规范

### 上传文件路径

上传到DStaff平台的文件路径必须以`mcps_upload/`开头：

- ✅ `mcps_upload/test.txt`
- ✅ `mcps_upload/videos/output.mp4`
- ✅ `mcps_upload/documents/report.pdf`
- ❌ `uploads/test.txt` (不以mcps_upload/开头)

### 支持的文件类型

系统会自动识别文件类型并设置正确的Content-Type：

- `.py` → `text/x-python`
- `.txt` → `text/plain`
- `.sh` → `text/x-shellscript`
- `.json` → `application/json`
- `.html` → `text/html`
- `.pptx` → `application/vnd.openxmlformats-officedocument.presentationml.presentation`
- `.mp4` → `video/mp4`
- `.zip` → `application/zip`

## 使用示例

### 1. 启动服务

```bash
# 使用Docker Compose
docker-compose up old-mcp-pipeline-server

# 或直接运行
cd ppt-narrator/old-mcp-pipeline-server
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export DSTAFF_ENDPOINT_URL=http://*********:8800
go run main.go
```

### 2. 使用Bearer Token访问

```bash
# 所有请求都需要Bearer Token
curl -H "Authorization: Bearer your_token_here" \
     http://localhost:48080/
```

### 3. 透明文件上传示例

直接通过HTTP POST请求上传文件：

```bash
curl -X POST \
  -H "Authorization: Bearer your_token_here" \
  -F "file=@/path/to/your/file.mp4" \
  -F "project_name=my_project" \
  -F "task_id=task_123" \
  http://localhost:48080/
```

### 4. 透明文件下载示例

直接通过HTTP GET请求下载文件：

```bash
curl -H "Authorization: Bearer your_token_here" \
  http://localhost:48080/download/task_123/videos/presentation.mp4 \
  -o downloaded_file.mp4
```

## 故障排除

### 1. Token验证失败

检查：
- `DSTAFF_ENDPOINT_URL`是否正确
- Token是否有效
- 网络连接是否正常

### 2. 文件上传失败

检查：
- 文件路径是否存在
- `target_path`是否以`mcps_upload/`开头
- Token和TaskID是否正确

### 3. 服务无法启动

检查：
- 环境变量配置是否正确
- ppt-narrator服务是否正常运行
- 端口48080是否被占用

## 最佳实践

1. **生产环境**：使用DStaff官方鉴权（`DSTAFF_USE_OFFICIAL_AUTH=true`）
2. **开发环境**：可以使用传统鉴权或禁用鉴权
3. **文件管理**：定期清理上传的临时文件
4. **监控**：关注日志中的鉴权和文件操作记录
5. **安全**：确保Token的安全存储和传输

## 兼容性

- 保持与现有MCP工具的完全兼容
- 传统鉴权方式继续有效
- 可以在不同鉴权模式间无缝切换
- 支持渐进式迁移到DStaff平台
