.PHONY: build run clean test deps help

# Variables
BINARY_NAME=mcp-pipeline-server
BUILD_DIR=build
WORK_DIR=work

# Default target
help:
	@echo "Available targets:"
	@echo "  deps    - Download dependencies"
	@echo "  build   - Build the application"
	@echo "  run     - Run the application"
	@echo "  clean   - Clean build artifacts and work directory"
	@echo "  test    - Run tests"
	@echo "  dev     - Run in development mode with auto-reload"
	@echo "  help    - Show this help message"

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# Build the application
build: deps
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) main.go

# Run the application
run: build
	@echo "Starting $(BINARY_NAME)..."
	@mkdir -p $(WORK_DIR)/uploads $(WORK_DIR)/results
	./$(BUILD_DIR)/$(BINARY_NAME)

# Run in development mode
dev:
	@echo "Starting in development mode..."
	@mkdir -p $(WORK_DIR)/uploads $(WORK_DIR)/results
	go run main.go

# Clean build artifacts and work directory
clean:
	@echo "Cleaning up..."
	rm -rf $(BUILD_DIR)
	rm -rf $(WORK_DIR)
	rm -f pipeline.db

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/cosmtrek/air@latest

# Run with hot reload (requires air)
watch:
	@echo "Starting with hot reload..."
	@mkdir -p $(WORK_DIR)/uploads $(WORK_DIR)/results
	air

# Docker targets
docker-build:
	@echo "Building Docker image..."
	docker build -t $(BINARY_NAME) .

docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 -v $(PWD)/work:/app/work $(BINARY_NAME)

# Check if all required tools are installed
check-deps:
	@echo "Checking dependencies..."
	@which go > /dev/null || (echo "Go is not installed" && exit 1)
	@echo "All dependencies are satisfied"
