<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Pipeline Server - Client Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status.pending { background-color: #ffc107; color: #212529; }
        .status.processing { background-color: #17a2b8; color: white; }
        .status.completed { background-color: #28a745; color: white; }
        .status.failed { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>MCP Pipeline Server - Client Example</h1>
    
    <div class="container">
        <h2>Upload File and Start Pipeline</h2>
        <div class="form-group">
            <label for="jobName">Job Name:</label>
            <input type="text" id="jobName" placeholder="Enter job name" value="Test Job">
        </div>
        <div class="form-group">
            <label for="fileUrl">File URL:</label>
            <input type="url" id="fileUrl" placeholder="https://example.com/file.pdf" 
                   value="https://httpbin.org/json">
        </div>
        <button onclick="uploadFile()">Start Pipeline</button>
    </div>

    <div class="container" id="progressContainer" style="display: none;">
        <h2>Pipeline Progress</h2>
        <div>Job ID: <span id="jobId"></span></div>
        <div>Status: <span id="jobStatus" class="status"></span></div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        <div>Progress: <span id="progressText">0%</span></div>
        <div>Message: <span id="progressMessage"></span></div>
        <div id="downloadSection" style="display: none;">
            <button onclick="downloadFile()">Download Result</button>
        </div>
        <button onclick="cancelJob()" id="cancelButton">Cancel Job</button>
    </div>

    <div class="container">
        <h2>Real-time Updates (SSE)</h2>
        <div>Connection Status: <span id="sseStatus">Disconnected</span></div>
        <button onclick="connectSSE()" id="sseButton">Connect to SSE</button>
        <div class="log" id="sseLog"></div>
    </div>

    <script>
        let currentJobId = null;
        let eventSource = null;

        const API_BASE = 'http://localhost:8080/api';

        function log(message) {
            const logElement = document.getElementById('sseLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function uploadFile() {
            const name = document.getElementById('jobName').value;
            const fileUrl = document.getElementById('fileUrl').value;

            if (!name || !fileUrl) {
                alert('Please fill in all fields');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name,
                        file_url: fileUrl
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    currentJobId = result.job_id;
                    document.getElementById('jobId').textContent = currentJobId;
                    document.getElementById('progressContainer').style.display = 'block';
                    log(`Pipeline started: ${currentJobId}`);
                    
                    // Start polling for progress
                    pollProgress();
                } else {
                    alert('Failed to start pipeline: ' + result.message);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function pollProgress() {
            if (!currentJobId) return;

            try {
                const response = await fetch(`${API_BASE}/progress/${currentJobId}`);
                const result = await response.json();

                if (result.success) {
                    updateProgress(result.job);
                    
                    // Continue polling if job is still running
                    if (result.job.status === 'pending' || result.job.status === 'processing') {
                        setTimeout(pollProgress, 2000);
                    }
                }
            } catch (error) {
                log('Error polling progress: ' + error.message);
            }
        }

        function updateProgress(job) {
            document.getElementById('jobStatus').textContent = job.status;
            document.getElementById('jobStatus').className = `status ${job.status}`;
            document.getElementById('progressFill').style.width = `${job.progress}%`;
            document.getElementById('progressText').textContent = `${job.progress.toFixed(1)}%`;
            document.getElementById('progressMessage').textContent = job.message;

            if (job.status === 'completed') {
                document.getElementById('downloadSection').style.display = 'block';
                document.getElementById('cancelButton').style.display = 'none';
            } else if (job.status === 'failed') {
                document.getElementById('cancelButton').style.display = 'none';
            }
        }

        async function downloadFile() {
            if (!currentJobId) return;

            try {
                const response = await fetch(`${API_BASE}/download/${currentJobId}`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `result_${currentJobId}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    log('File downloaded successfully');
                } else {
                    alert('Failed to download file');
                }
            } catch (error) {
                alert('Error downloading file: ' + error.message);
            }
        }

        async function cancelJob() {
            if (!currentJobId) return;

            try {
                const response = await fetch(`${API_BASE}/cancel/${currentJobId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                if (result.success) {
                    log('Job cancelled successfully');
                } else {
                    alert('Failed to cancel job: ' + result.message);
                }
            } catch (error) {
                alert('Error cancelling job: ' + error.message);
            }
        }

        function connectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                document.getElementById('sseStatus').textContent = 'Disconnected';
                document.getElementById('sseButton').textContent = 'Connect to SSE';
                return;
            }

            const url = currentJobId ? 
                `${API_BASE}/sse?job_id=${currentJobId}` : 
                `${API_BASE}/sse`;

            eventSource = new EventSource(url);

            eventSource.onopen = function() {
                document.getElementById('sseStatus').textContent = 'Connected';
                document.getElementById('sseButton').textContent = 'Disconnect SSE';
                log('SSE connection established');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`SSE: ${data.type} - ${JSON.stringify(data.data)}`);
                    
                    if (data.type === 'progress' && data.data.job_id === currentJobId) {
                        updateProgress(data.data);
                    }
                } catch (error) {
                    log(`SSE message: ${event.data}`);
                }
            };

            eventSource.onerror = function() {
                document.getElementById('sseStatus').textContent = 'Error';
                log('SSE connection error');
            };
        }

        // Initialize
        log('Client initialized. Ready to start pipeline.');
    </script>
</body>
</html>
