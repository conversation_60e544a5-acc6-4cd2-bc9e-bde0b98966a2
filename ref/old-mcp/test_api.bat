@echo off
setlocal enabledelayedexpansion

echo Testing MCP Pipeline Server API
echo ==================================

set BASE_URL=http://localhost:8080
set API_URL=%BASE_URL%/api
set TEST_FILE_URL=https://httpbin.org/json
set JOB_NAME=Test Pipeline Job

REM Test 1: Health Check
echo.
echo [INFO] Testing health check...
curl -s -o temp_health.json "%BASE_URL%/health"
if %errorlevel% equ 0 (
    echo [SUCCESS] Health check passed
    type temp_health.json
) else (
    echo [ERROR] Health check failed
    goto cleanup
)

REM Test 2: Upload File and Start Pipeline
echo.
echo [INFO] Testing file upload and pipeline start...
curl -s -X POST "%API_URL%/upload" ^
    -H "Content-Type: application/json" ^
    -d "{\"name\":\"%JOB_NAME%\",\"file_url\":\"%TEST_FILE_URL%\"}" ^
    -o temp_upload.json

if %errorlevel% equ 0 (
    echo [SUCCESS] Pipeline started successfully
    type temp_upload.json
    
    REM Extract job ID (simple method for Windows)
    for /f "tokens=2 delims=:" %%a in ('findstr "job_id" temp_upload.json') do (
        set JOB_ID=%%a
        set JOB_ID=!JOB_ID:"=!
        set JOB_ID=!JOB_ID:,=!
        set JOB_ID=!JOB_ID: =!
    )
    echo Job ID: !JOB_ID!
) else (
    echo [ERROR] Pipeline start failed
    goto cleanup
)

REM Test 3: Check Progress
echo.
echo [INFO] Testing progress check...
timeout /t 2 /nobreak >nul
curl -s "%API_URL%/progress/!JOB_ID!?include_steps=true" -o temp_progress.json

if %errorlevel% equ 0 (
    echo [SUCCESS] Progress check successful
    type temp_progress.json
) else (
    echo [ERROR] Progress check failed
)

REM Test 4: Wait for completion
echo.
echo [INFO] Waiting for pipeline completion...
set /a attempt=0
set /a max_attempts=15

:wait_loop
if !attempt! geq !max_attempts! (
    echo [ERROR] Pipeline did not complete within expected time
    goto cleanup
)

curl -s "%API_URL%/progress/!JOB_ID!" -o temp_status.json
findstr "completed" temp_status.json >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Pipeline completed successfully
    goto test_download
)

findstr "failed" temp_status.json >nul
if %errorlevel% equ 0 (
    echo [ERROR] Pipeline failed
    type temp_status.json
    goto cleanup
)

set /a attempt+=1
echo Attempt !attempt!/!max_attempts! - Waiting...
timeout /t 3 /nobreak >nul
goto wait_loop

:test_download
REM Test 5: Get Download URL
echo.
echo [INFO] Testing download URL generation...
curl -s "%API_URL%/download-url/!JOB_ID!" -o temp_download_url.json

if %errorlevel% equ 0 (
    echo [SUCCESS] Download URL generated successfully
    type temp_download_url.json
) else (
    echo [ERROR] Download URL generation failed
)

REM Test 6: Download File
echo.
echo [INFO] Testing file download...
curl -s "%API_URL%/download/!JOB_ID!" -o temp_downloaded_file.txt

if %errorlevel% equ 0 (
    echo [SUCCESS] File downloaded successfully
    echo Downloaded file content:
    type temp_downloaded_file.txt
) else (
    echo [ERROR] File download failed
)

REM Test 7: SSE Status
echo.
echo [INFO] Testing SSE status...
curl -s "%API_URL%/status" -o temp_sse_status.json

if %errorlevel% equ 0 (
    echo [SUCCESS] SSE status check successful
    type temp_sse_status.json
) else (
    echo [ERROR] SSE status check failed
)

:cleanup
echo.
echo [INFO] Cleaning up temporary files...
del /q temp_*.json temp_*.txt 2>nul

echo.
echo Testing completed!
echo.
echo To test SSE functionality, open examples/client.html in your browser
echo or use curl to connect to the SSE endpoint:
echo curl -N -H "Accept: text/event-stream" "%API_URL%/sse"

pause
