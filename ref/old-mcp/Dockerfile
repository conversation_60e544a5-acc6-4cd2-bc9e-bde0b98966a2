# Build stage
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata gcc musl-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main .

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/main .

# Create work directories
RUN mkdir -p work/uploads work/results && \
    chown -R appuser:appgroup /app

# Set environment variables
ENV PPT_NARRATOR_URL=http://localhost:38080
ENV MCP_ACCESS_KEY=""
# DStaff platform integration
ENV DSTAFF_ENABLED=false
ENV DSTAFF_ENDPOINT_URL=http://*********:8800
ENV DSTAFF_USE_OFFICIAL_AUTH=false
# Debug configuration
ENV MCP_DEBUG=false

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 48080

# Run the application
CMD ["./main"]
