# MCP Pipeline Server 日志和调试指南

本文档说明MCP Pipeline Server的日志记录和调试功能。

## 🔍 日志功能概述

### HTTP请求日志（始终启用）

所有HTTP请求都会被自动记录，包括：
- HTTP方法和完整URL路径
- 远程客户端地址
- 请求头信息（敏感信息会被掩码处理）
- 完整的请求体内容
- JSON格式的美化输出

### MCP工具调试日志（可选）

通过设置`MCP_DEBUG=true`可以启用MCP工具调用的额外调试信息：
- 工具调用的详细参数
- 请求体的JSON格式输出

## 🚀 HTTP请求日志（始终启用）

HTTP请求日志无需配置，服务启动后会自动记录所有请求。

## 🚀 启用MCP工具调试模式

### 方式1：环境变量

```bash
export MCP_DEBUG=true
```

### 方式2：Docker Compose

```yaml
environment:
  - MCP_DEBUG=true
```

### 方式3：直接运行

```bash
cd ppt-narrator/old-mcp-pipeline-server
MCP_DEBUG=true go run main.go
```

## 📋 日志示例

### 启动时的日志

```
Initializing MCP Pipeline Server...
DStaff integration enabled
HTTP Request Logging: Enabled - all HTTP requests will be logged with detailed information
MCP Tool Debug: Enabled - MCP tool calls will have additional debug output
Starting MCP Pipeline Server on :48080
```

### HTTP请求日志（始终输出）

```
=== HTTP Request ===
Method: POST
Path: /
Full URL: /
Remote Addr: 127.0.0.1:54321
Headers:
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
  User-Agent: MCP-Client/1.0
  Content-Length: 245
Request Body (JSON):
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx"
    }
  }
}
===================
```

### MCP工具调用的调试日志（需要MCP_DEBUG=true）

```
=== MCP Request Debug ===
Method: POST
URL: /
Headers:
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
  User-Agent: MCP-Client/1.0
  Content-Length: 245
Request Body (JSON):
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说"
    }
  }
}
========================

=== MCP Tool Call: upload_file ===
Request Body: {
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说"
    }
  }
}
=====================================
```

### 文件上传的调试日志

```
=== MCP Request Debug ===
Method: POST
URL: /
Headers:
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
  Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
  Content-Length: 2048576
Request Body: [multipart/form-data - 2048576 bytes]
========================

=== DStaff Transparent File Upload ===
Method: POST
URL: /
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
=====================================
```

### 普通HTTP请求的调试日志

```
=== MCP Request Debug ===
Method: GET
URL: /health
Headers:
  User-Agent: curl/7.68.0
  Accept: */*
Request Body: [empty]
========================
```

## 🔒 安全特性

### 敏感信息掩码

调试功能会自动掩码敏感的请求头信息：

```
Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
```

只显示前10个和后10个字符，中间用`...`替代。

### 文件内容保护

对于`multipart/form-data`请求，不会记录具体的文件内容，只显示大小：

```
Request Body: [multipart/form-data - 2048576 bytes]
```

### 大文本截断

对于超过1000字符的非JSON请求体，会自动截断：

```
Request Body (truncated): {"very":"long","json":"content"...
```

## 🛠️ 使用场景

### 1. 开发调试

在开发过程中启用调试模式，查看MCP客户端发送的具体请求：

```bash
export MCP_DEBUG=true
go run main.go
```

### 2. 问题排查

当遇到MCP工具调用问题时，启用调试模式查看请求格式：

```bash
# 启用调试
export MCP_DEBUG=true
docker-compose up old-mcp-pipeline-server

# 查看日志
docker-compose logs -f old-mcp-pipeline-server
```

### 3. API测试

测试不同的MCP请求格式和参数：

```bash
# 启用调试模式
export MCP_DEBUG=true

# 发送测试请求
curl -X POST http://localhost:48080/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}'
```

## 📊 性能影响

### 日志开销

启用调试模式会增加日志输出，可能影响性能：
- 每个请求都会读取和记录请求体
- JSON格式化会消耗额外CPU
- 大量日志输出会影响I/O性能

### 生产环境建议

- **开发环境**: 可以常开调试模式
- **测试环境**: 按需启用调试模式
- **生产环境**: 建议关闭调试模式，只在排查问题时临时启用

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_DEBUG` | `false` | 是否启用调试模式 |

### 运行时切换

调试模式需要重启服务才能生效，不支持运行时动态切换。

## 📝 最佳实践

### 1. 日志管理

```bash
# 将调试日志输出到文件
MCP_DEBUG=true go run main.go > debug.log 2>&1

# 使用logrotate管理日志文件
# 或使用Docker的日志驱动
```

### 2. 过滤敏感信息

虽然系统会自动掩码Authorization头，但建议：
- 不要在日志中记录完整的Token
- 定期清理包含敏感信息的日志文件
- 在生产环境中谨慎使用调试模式

### 3. 监控和告警

```bash
# 监控调试日志中的错误
tail -f debug.log | grep -i error

# 统计请求类型
grep "MCP Tool Call" debug.log | awk '{print $5}' | sort | uniq -c
```

## 🚨 注意事项

1. **性能影响**: 调试模式会增加CPU和I/O开销
2. **存储空间**: 详细日志会占用更多磁盘空间
3. **安全风险**: 日志可能包含敏感信息，需要妥善保管
4. **网络流量**: 在高并发环境下，日志输出可能成为瓶颈

## 🔄 故障排除

### 调试模式未生效

检查环境变量设置：
```bash
echo $MCP_DEBUG
```

查看启动日志确认调试模式状态：
```
MCP Debug mode enabled - all requests will be logged
```

### 日志过多

临时关闭调试模式：
```bash
export MCP_DEBUG=false
# 重启服务
```

### 日志格式问题

确保使用正确的JSON格式发送MCP请求，调试日志会显示格式错误。
