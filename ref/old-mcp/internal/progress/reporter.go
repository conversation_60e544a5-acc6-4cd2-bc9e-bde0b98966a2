package progress

import (
	"fmt"
	"log"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
)

// Reporter handles progress reporting for MCP operations
type Reporter struct {
	progressToken    string
	progressSendFunc func(notification *mcp.ProgressNotification) error
	logSendFunc      func(notification *mcp.LoggingMessageNotification) error
	currentStep      int
	totalSteps       int
	lastUpdate       time.Time
	minInterval      time.Duration // 最小更新间隔，避免过于频繁的更新
}

// NewReporter creates a new progress reporter
func NewReporter(progressToken string, totalSteps int, progressSendFunc func(notification *mcp.ProgressNotification) error) *Reporter {
	return &Reporter{
		progressToken:    progressToken,
		progressSendFunc: progressSendFunc,
		totalSteps:       totalSteps,
		currentStep:      0,
		minInterval:      500 * time.Millisecond, // 最小500ms间隔
	}
}

// SetLogSendFunc sets the function to send log notifications
func (r *Reporter) SetLogSendFunc(logSendFunc func(notification *mcp.LoggingMessageNotification) error) {
	r.logSendFunc = logSendFunc
}

// ReportStep reports progress for a specific step
func (r *Reporter) ReportStep(step int, message string) error {
	// 总是发送日志消息，这样步骤进度就会出现在任务日志中
	logMessage := fmt.Sprintf("📊 Step %d/%d: %s", step, r.totalSteps, message)
	if r.logSendFunc != nil {
		// 发送INFO级别的日志消息
		err := r.SendLogMessage("info", logMessage)
		if err != nil {
			log.Printf("⚠️ Failed to send log message: %v", err)
		}
	} else {
		// 如果没有日志发送函数，至少记录到控制台
		log.Printf("%s", logMessage)
	}

	if r.progressToken == "" || r.progressSendFunc == nil {
		// 如果没有进度令牌或发送函数，只记录日志（已经在上面处理了）
		return nil
	}

	// 检查是否需要限制更新频率
	now := time.Now()
	if now.Sub(r.lastUpdate) < r.minInterval && step != r.totalSteps {
		// 如果距离上次更新时间太短且不是最后一步，跳过更新
		return nil
	}

	r.currentStep = step
	r.lastUpdate = now

	// 创建进度通知
	totalPtr := float64(r.totalSteps)
	messagePtr := message
	notification := mcp.NewProgressNotification(
		r.progressToken,
		float64(step),
		&totalPtr,
		&messagePtr,
	)

	log.Printf("📊 Sending progress: %d/%d - %s", step, r.totalSteps, message)

	// 发送进度通知
	err := r.progressSendFunc(&notification)
	if err != nil {
		log.Printf("❌ Failed to send progress notification: %v", err)
		return err
	} else {
		log.Printf("✅ Progress notification sent successfully")
		return nil
	}
}

// ReportStepWithDelay reports progress and waits if the step might take time
func (r *Reporter) ReportStepWithDelay(step int, message string, expectedDuration time.Duration) error {
	// 先报告开始
	if err := r.ReportStep(step, message); err != nil {
		return err
	}

	// 如果预期时间较长，定期发送"正在执行"的更新
	if expectedDuration > 2*time.Second {
		ticker := time.NewTicker(5 * time.Second) // 改为5秒间隔，避免过于频繁
		defer ticker.Stop()

		done := make(chan bool, 1)

		// 启动一个goroutine来定期发送更新
		go func() {
			for {
				select {
				case <-ticker.C:
					// 发送"正在执行"更新，但不改变步骤数
					msgPtr := fmt.Sprintf("正在执行: %s (请稍候...)", message)

					// 发送日志消息
					if r.logSendFunc != nil {
						logMessage := fmt.Sprintf("📊 Step %d/%d: %s", step, r.totalSteps, msgPtr)
						err := r.SendLogMessage("info", logMessage)
						if err != nil {
							log.Printf("⚠️ Failed to send periodic log message: %v", err)
						}
					}

					// 发送进度通知
					if r.progressToken != "" && r.progressSendFunc != nil {
						totalPtr := float64(r.totalSteps)
						notification := mcp.NewProgressNotification(
							r.progressToken,
							float64(step),
							&totalPtr,
							&msgPtr,
						)
						log.Printf("📊 Sending periodic progress update: %d/%d - %s", step, r.totalSteps, msgPtr)
						err := r.progressSendFunc(&notification)
						if err != nil {
							log.Printf("❌ Failed to send periodic progress notification: %v", err)
						} else {
							log.Printf("✅ Periodic progress notification sent successfully")
						}
					}
				case <-done:
					return
				}
			}
		}()

		// 模拟等待（实际使用中这里会是真正的工作）
		time.Sleep(expectedDuration)
		done <- true
	}

	return nil
}

// ReportStepWithPeriodicUpdates reports progress and sends periodic updates without blocking
// This version doesn't wait for the expected duration, suitable for monitoring scenarios
func (r *Reporter) ReportStepWithPeriodicUpdates(step int, message string) (func(), error) {
	// 先报告开始
	if err := r.ReportStep(step, message); err != nil {
		return nil, err
	}

	ticker := time.NewTicker(10 * time.Second) // 每10秒发送一次更新
	done := make(chan bool, 1)

	// 启动一个goroutine来定期发送更新
	go func() {
		for {
			select {
			case <-ticker.C:
				// 发送"正在执行"更新，但不改变步骤数
				msgPtr := fmt.Sprintf("正在执行: %s (请稍候...)", message)

				// 发送日志消息
				if r.logSendFunc != nil {
					logMessage := fmt.Sprintf("📊 Step %d/%d: %s", step, r.totalSteps, msgPtr)
					err := r.SendLogMessage("info", logMessage)
					if err != nil {
						log.Printf("⚠️ Failed to send periodic log message: %v", err)
					}
				}

				// 发送进度通知
				if r.progressToken != "" && r.progressSendFunc != nil {
					totalPtr := float64(r.totalSteps)
					notification := mcp.NewProgressNotification(
						r.progressToken,
						float64(step),
						&totalPtr,
						&msgPtr,
					)
					log.Printf("📊 Sending periodic progress update: %d/%d - %s", step, r.totalSteps, msgPtr)
					err := r.progressSendFunc(&notification)
					if err != nil {
						log.Printf("❌ Failed to send periodic progress notification: %v", err)
					} else {
						log.Printf("✅ Periodic progress notification sent successfully")
					}
				}
			case <-done:
				ticker.Stop()
				return
			}
		}
	}()

	// 返回停止函数
	stopFunc := func() {
		done <- true
	}

	return stopFunc, nil
}

// SendLogMessage sends a log message notification
func (r *Reporter) SendLogMessage(level, message string) error {
	log.Printf("🔧 [DEBUG] SendLogMessage called with level: %s, message: %s", level, message)

	if r.progressToken == "" || r.logSendFunc == nil {
		log.Printf("🔧 [DEBUG] progressToken: '%s', logSendFunc is nil: %v", r.progressToken, r.logSendFunc == nil)
		log.Printf("📝 %s: %s", level, message)
		return nil
	}

	log.Printf("🔧 [DEBUG] Creating notification and calling logSendFunc...")
	log.Printf("📝 Sending log: [%s] %s", level, message)

	// 创建日志通知
	logNotification := mcp.NewLoggingMessageNotification(
		mcp.LoggingLevel(level),
		"ppt-narrator-old-mcp", // logger name
		message,
	)

	// 发送日志通知
	log.Printf("🔧 [DEBUG] Calling logSendFunc with notification...")
	err := r.logSendFunc(&logNotification)
	if err != nil {
		log.Printf("🔧 [DEBUG] logSendFunc returned error: %v", err)
		log.Printf("❌ Failed to send log notification: %v", err)
		return err
	} else {
		log.Printf("🔧 [DEBUG] logSendFunc completed successfully")
		log.Printf("✅ Log notification sent successfully")
		return nil
	}
}

// Complete marks the operation as complete
func (r *Reporter) Complete(message string) error {
	return r.ReportStep(r.totalSteps, message)
}

// Error reports an error occurred
func (r *Reporter) Error(message string) error {
	// 总是发送错误日志消息
	errorLogMessage := fmt.Sprintf("❌ 错误: %s", message)
	if r.logSendFunc != nil {
		// 发送ERROR级别的日志消息
		err := r.SendLogMessage("error", errorLogMessage)
		if err != nil {
			log.Printf("⚠️ Failed to send error log message: %v", err)
		}
	} else {
		// 如果没有日志发送函数，至少记录到控制台
		log.Printf("%s", errorLogMessage)
	}

	if r.progressToken == "" || r.progressSendFunc == nil {
		// 如果没有进度令牌或发送函数，只记录日志（已经在上面处理了）
		return nil
	}

	// 发送错误状态的进度通知
	totalPtr := float64(r.totalSteps)
	msgPtr := fmt.Sprintf("❌ 错误: %s", message)
	notification := mcp.NewProgressNotification(
		r.progressToken,
		float64(r.currentStep),
		&totalPtr,
		&msgPtr,
	)

	log.Printf("❌ Sending error progress: %s", message)
	err := r.progressSendFunc(&notification)
	if err != nil {
		log.Printf("❌ Failed to send error progress notification: %v", err)
		return err
	} else {
		log.Printf("✅ Error progress notification sent successfully")
		return nil
	}
}

// ExtractProgressToken extracts progress token from MCP request
func ExtractProgressToken(request mcp.CallToolRequest) string {
	log.Printf("🔍 Debug: ExtractProgressToken - request.Params.Meta: %v", request.Params.Meta)
	// 检查请求的元数据中是否有进度令牌
	if request.Params.Meta != nil {
		log.Printf("🔍 Debug: ExtractProgressToken - Meta.ProgressToken: %v", request.Params.Meta.ProgressToken)
		// 根据 old-mcp-go 官方示例，直接访问 ProgressToken
		if request.Params.Meta.ProgressToken != nil {
			// ProgressToken 可能是不同类型，需要转换为字符串
			switch v := request.Params.Meta.ProgressToken.(type) {
			case string:
				log.Printf("🔍 Debug: ExtractProgressToken - Found string token: %s", v)
				return v
			case float64:
				token := fmt.Sprintf("%.0f", v)
				log.Printf("🔍 Debug: ExtractProgressToken - Found float64 token: %s", token)
				return token
			case int:
				token := fmt.Sprintf("%d", v)
				log.Printf("🔍 Debug: ExtractProgressToken - Found int token: %s", token)
				return token
			default:
				token := fmt.Sprintf("%v", v)
				log.Printf("🔍 Debug: ExtractProgressToken - Found other type token: %s", token)
				return token
			}
		}
	}

	log.Printf("🔍 Debug: ExtractProgressToken - No token found")
	return ""
}

// CreateProgressReporter creates a progress reporter from MCP request
func CreateProgressReporter(request mcp.CallToolRequest, totalSteps int, progressSendFunc func(notification *mcp.ProgressNotification) error, logSendFunc func(notification *mcp.LoggingMessageNotification) error) *Reporter {
	progressToken := ExtractProgressToken(request)

	if progressToken == "" {
		// 没有进度令牌，返回一个只记录日志的reporter
		// 但仍然设置logSendFunc，这样日志可以发送给客户端
		reporter := NewReporter("", totalSteps, nil)
		reporter.SetLogSendFunc(logSendFunc)
		log.Printf("🔧 [DEBUG] Created reporter without progressToken, but with logSendFunc")
		return reporter
	}

	reporter := NewReporter(progressToken, totalSteps, progressSendFunc)
	reporter.SetLogSendFunc(logSendFunc)
	return reporter
}
