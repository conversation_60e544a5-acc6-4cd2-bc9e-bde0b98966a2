package binding

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TaskBinding represents a binding between task_id and project_id
type TaskBinding struct {
	TaskID    string    `json:"task_id"`
	ProjectID string    `json:"project_id"`
	CreatedAt time.Time `json:"created_at"`
}

// TaskBindingManager manages task-project bindings
type TaskBindingManager struct {
	bindings map[string]*TaskBinding // task_id -> TaskBinding
	mutex    sync.RWMutex
	dataFile string
}

// NewTaskBindingManager creates a new task binding manager
func NewTaskBindingManager(dataDir string) *TaskBindingManager {
	// Ensure data directory exists
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		log.Printf("Warning: Failed to create data directory %s: %v", dataDir, err)
	}

	manager := &TaskBindingManager{
		bindings: make(map[string]*TaskBinding),
		dataFile: filepath.Join(dataDir, "task_bindings.json"),
	}

	// Load existing bindings
	if err := manager.loadBindings(); err != nil {
		log.Printf("Warning: Failed to load existing bindings: %v", err)
	}

	return manager
}

// SaveBinding saves a new task-project binding
func (tbm *TaskBindingManager) SaveBinding(taskID, projectID string) error {
	if taskID == "" || projectID == "" {
		return fmt.Errorf("task_id and project_id cannot be empty")
	}

	tbm.mutex.Lock()
	defer tbm.mutex.Unlock()

	binding := &TaskBinding{
		TaskID:    taskID,
		ProjectID: projectID,
		CreatedAt: time.Now(),
	}

	tbm.bindings[taskID] = binding

	// Persist to file
	if err := tbm.saveBindings(); err != nil {
		return fmt.Errorf("failed to persist binding: %w", err)
	}

	log.Printf("Task binding saved: task_id=%s -> project_id=%s", taskID, projectID)
	return nil
}

// GetProjectID retrieves project_id by task_id
func (tbm *TaskBindingManager) GetProjectID(taskID string) (string, error) {
	if taskID == "" {
		return "", fmt.Errorf("task_id cannot be empty")
	}

	tbm.mutex.RLock()
	defer tbm.mutex.RUnlock()

	if binding, exists := tbm.bindings[taskID]; exists {
		log.Printf("Task binding found: task_id=%s -> project_id=%s", taskID, binding.ProjectID)
		return binding.ProjectID, nil
	}

	return "", fmt.Errorf("no project_id found for task_id: %s", taskID)
}

// GetBinding retrieves the full binding by task_id
func (tbm *TaskBindingManager) GetBinding(taskID string) (*TaskBinding, error) {
	if taskID == "" {
		return nil, fmt.Errorf("task_id cannot be empty")
	}

	tbm.mutex.RLock()
	defer tbm.mutex.RUnlock()

	if binding, exists := tbm.bindings[taskID]; exists {
		// Return a copy to avoid race conditions
		bindingCopy := *binding
		return &bindingCopy, nil
	}

	return nil, fmt.Errorf("no binding found for task_id: %s", taskID)
}

// ListBindings returns all bindings (for debugging/admin purposes)
func (tbm *TaskBindingManager) ListBindings() []*TaskBinding {
	tbm.mutex.RLock()
	defer tbm.mutex.RUnlock()

	bindings := make([]*TaskBinding, 0, len(tbm.bindings))
	for _, binding := range tbm.bindings {
		// Return copies to avoid race conditions
		bindingCopy := *binding
		bindings = append(bindings, &bindingCopy)
	}

	return bindings
}

// DeleteBinding removes a binding by task_id
func (tbm *TaskBindingManager) DeleteBinding(taskID string) error {
	if taskID == "" {
		return fmt.Errorf("task_id cannot be empty")
	}

	tbm.mutex.Lock()
	defer tbm.mutex.Unlock()

	if _, exists := tbm.bindings[taskID]; !exists {
		return fmt.Errorf("no binding found for task_id: %s", taskID)
	}

	delete(tbm.bindings, taskID)

	// Persist to file
	if err := tbm.saveBindings(); err != nil {
		return fmt.Errorf("failed to persist binding deletion: %w", err)
	}

	log.Printf("Task binding deleted: task_id=%s", taskID)
	return nil
}

// loadBindings loads bindings from the JSON file
func (tbm *TaskBindingManager) loadBindings() error {
	if _, err := os.Stat(tbm.dataFile); os.IsNotExist(err) {
		log.Printf("Task bindings file does not exist, starting with empty bindings: %s", tbm.dataFile)
		return nil
	}

	data, err := os.ReadFile(tbm.dataFile)
	if err != nil {
		return fmt.Errorf("failed to read bindings file: %w", err)
	}

	var bindingsList []*TaskBinding
	if err := json.Unmarshal(data, &bindingsList); err != nil {
		return fmt.Errorf("failed to unmarshal bindings: %w", err)
	}

	// Convert list to map
	for _, binding := range bindingsList {
		tbm.bindings[binding.TaskID] = binding
	}

	log.Printf("Loaded %d task bindings from %s", len(bindingsList), tbm.dataFile)
	return nil
}

// saveBindings saves bindings to the JSON file
func (tbm *TaskBindingManager) saveBindings() error {
	// Convert map to list for JSON serialization
	bindingsList := make([]*TaskBinding, 0, len(tbm.bindings))
	for _, binding := range tbm.bindings {
		bindingsList = append(bindingsList, binding)
	}

	data, err := json.MarshalIndent(bindingsList, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal bindings: %w", err)
	}

	// Write to temporary file first, then rename for atomic operation
	tempFile := tbm.dataFile + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temporary bindings file: %w", err)
	}

	if err := os.Rename(tempFile, tbm.dataFile); err != nil {
		// Clean up temporary file on failure
		os.Remove(tempFile)
		return fmt.Errorf("failed to rename temporary bindings file: %w", err)
	}

	return nil
}

// GetStats returns statistics about the bindings
func (tbm *TaskBindingManager) GetStats() map[string]interface{} {
	tbm.mutex.RLock()
	defer tbm.mutex.RUnlock()

	return map[string]interface{}{
		"total_bindings": len(tbm.bindings),
		"data_file":      tbm.dataFile,
	}
}
