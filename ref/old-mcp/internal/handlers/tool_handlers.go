package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"mcp-pipeline-server/internal/binding"
	"mcp-pipeline-server/internal/progress"
)

// PPTNarratorClient interface for PPT narrator operations
type PPTNarratorClient interface {
	UploadFileFromURL(fileURL, name, userRequirements string, enableSubtitles bool, subtitleStyleTemplate string) ([]byte, error)
	GetProgress(projectID string) ([]byte, error)
	GetDownloadURL(projectID string) ([]byte, error)
	RetryProject(projectID string) ([]byte, error)
}

// DStaffConfig holds DStaff platform configuration
type DStaffConfig struct {
	Enabled bool
	BaseURL string
	// Add other DStaff config fields as needed
}

// ToolHandlers contains all tool handler functions and notification senders
type ToolHandlers struct {
	pptClient          PPTNarratorClient
	dstaffConfig       *DStaffConfig
	taskBindingManager *binding.TaskBindingManager
	progressSendFunc   func(notification *mcp.ProgressNotification) error
	logSendFunc        func(notification *mcp.LoggingMessageNotification) error
	logMessages        []string
	logMutex           sync.RWMutex
}

// NewToolHandlers creates a new ToolHandlers instance
func NewToolHandlers(pptClient PPTNarratorClient, dstaffConfig *DStaffConfig, taskBindingManager *binding.TaskBindingManager) *ToolHandlers {
	return &ToolHandlers{
		pptClient:          pptClient,
		dstaffConfig:       dstaffConfig,
		taskBindingManager: taskBindingManager,
		logMessages:        make([]string, 0),
	}
}

// SetProgressSendFunc sets the function to send progress notifications
func (h *ToolHandlers) SetProgressSendFunc(sendFunc func(notification *mcp.ProgressNotification) error) {
	h.progressSendFunc = sendFunc
}

// SetLogSendFunc sets the function to send log notifications
func (h *ToolHandlers) SetLogSendFunc(sendFunc func(notification *mcp.LoggingMessageNotification) error) {
	h.logSendFunc = sendFunc
}

// GetProgressSendFunc returns the progress send function
func (h *ToolHandlers) GetProgressSendFunc() func(notification *mcp.ProgressNotification) error {
	return h.progressSendFunc
}

// GetLogSendFunc returns the log send function
func (h *ToolHandlers) GetLogSendFunc() func(notification *mcp.LoggingMessageNotification) error {
	return h.logSendFunc
}

// GetPPTClient returns the PPT narrator client
func (h *ToolHandlers) GetPPTClient() PPTNarratorClient {
	return h.pptClient
}

// GetDStaffConfig returns the DStaff configuration
func (h *ToolHandlers) GetDStaffConfig() *DStaffConfig {
	return h.dstaffConfig
}

// GetMainDStaffConfig returns a main.DStaffConfig compatible version
// This is a temporary method to maintain compatibility with existing code
func (h *ToolHandlers) GetMainDStaffConfig() interface{} {
	// Return a struct that matches main.DStaffConfig
	return struct {
		Enabled         bool
		AuthServiceURL  string
		UseOfficialAuth bool
	}{
		Enabled:         h.dstaffConfig.Enabled,
		AuthServiceURL:  h.dstaffConfig.BaseURL,
		UseOfficialAuth: true, // Default value
	}
}

// GetTaskBindingManager returns the task binding manager
func (h *ToolHandlers) GetTaskBindingManager() *binding.TaskBindingManager {
	return h.taskBindingManager
}

// ProxyRequest proxies a request to the PPT narrator service
func (h *ToolHandlers) ProxyRequest(method, path string, body []byte) ([]byte, error) {
	// This is a temporary method to maintain compatibility
	// In a real implementation, this should be part of the PPTNarratorClient interface
	if pptClient, ok := h.pptClient.(interface {
		ProxyRequest(method, path string, body []byte) ([]byte, error)
	}); ok {
		return pptClient.ProxyRequest(method, path, body)
	}
	return nil, fmt.Errorf("ProxyRequest not supported by PPT client")
}

// GetHTTPClient returns the HTTP client from PPT narrator client
func (h *ToolHandlers) GetHTTPClient() (interface{}, error) {
	// This is a temporary method to maintain compatibility
	if pptClient, ok := h.pptClient.(interface {
		GetHTTPClient() interface{}
	}); ok {
		return pptClient.GetHTTPClient(), nil
	}
	return nil, fmt.Errorf("GetHTTPClient not supported by PPT client")
}

// GetBaseURL returns the base URL from PPT narrator client
func (h *ToolHandlers) GetBaseURL() (string, error) {
	// This is a temporary method to maintain compatibility
	if pptClient, ok := h.pptClient.(interface {
		GetBaseURL() string
	}); ok {
		return pptClient.GetBaseURL(), nil
	}
	return "", fmt.Errorf("GetBaseURL not supported by PPT client")
}

// AddLogMessage adds a log message to the collection
func (h *ToolHandlers) AddLogMessage(level, message string) {
	h.logMutex.Lock()
	defer h.logMutex.Unlock()

	logEntry := fmt.Sprintf("[%s] %s", strings.ToUpper(level), message)
	h.logMessages = append(h.logMessages, logEntry)

	// Keep only the last 100 messages to prevent memory issues
	if len(h.logMessages) > 100 {
		h.logMessages = h.logMessages[len(h.logMessages)-100:]
	}
}

// GetAndClearLogMessages returns all log messages and clears the collection
func (h *ToolHandlers) GetAndClearLogMessages() []string {
	h.logMutex.Lock()
	defer h.logMutex.Unlock()

	messages := make([]string, len(h.logMessages))
	copy(messages, h.logMessages)
	h.logMessages = h.logMessages[:0] // Clear the slice
	return messages
}

// UploadFileWithProgress handles file upload with progress reporting
func (h *ToolHandlers) UploadFileWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	name, err := request.RequireString("name")
	if err != nil {
		log.Printf("upload_file: Missing 'name' parameter: %v", err)
		return nil, err
	}

	fileURL, err := request.RequireString("file_url")
	if err != nil {
		log.Printf("upload_file: Missing 'file_url' parameter: %v", err)
		return nil, err
	}

	userRequirements := request.GetString("user_requirements", "")
	enableSubtitles := request.GetBool("enable_subtitles", false)
	subtitleStyleTemplate := request.GetString("subtitle_style_template", "")

	// Get task_id from context parameter (dictionary)
	var taskID string
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	log.Printf("upload_file: 开始为PPT文件 '%s' 生成解说视频，URL: %s, task_id: %s, 字幕: %t, 样式: %s", name, fileURL, taskID, enableSubtitles, subtitleStyleTemplate)

	// Check if fileURL is a relative path (not HTTP URL)
	var actualFileURL string
	var relativeFilePath string

	// Check if it's a full HTTP/HTTPS URL
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// This is a regular HTTP URL
		actualFileURL = fileURL
		log.Printf("upload_file: Using HTTP URL: %s", actualFileURL)
	} else if strings.HasPrefix(fileURL, "file://") {
		// Extract relative path from file:// URL
		relativeFilePath = strings.TrimPrefix(fileURL, "file://")
		// Remove leading slash if present
		relativeFilePath = strings.TrimPrefix(relativeFilePath, "/")
		log.Printf("upload_file: Extracted relative path from file:// URL: %s", relativeFilePath)
	} else {
		// This is a relative path without protocol
		relativeFilePath = fileURL
		log.Printf("upload_file: Treating as relative path: %s", relativeFilePath)
	}

	// Handle relative paths (download from DStaff platform)
	if relativeFilePath != "" {
		if taskID == "" {
			log.Printf("upload_file: task_id is required for downloading files from DStaff platform")
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: "task_id is required for downloading files from DStaff platform",
					},
				},
				IsError: true,
			}, nil
		}

		// Get token from context
		token := ""
		if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
			if tokenStr, ok := tokenValue.(string); ok {
				token = tokenStr
			}
		}

		if token == "" {
			log.Printf("upload_file: Authorization token is required for downloading files from DStaff platform")
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: "Authorization token is required for downloading files from DStaff platform",
					},
				},
				IsError: true,
			}, nil
		}

		// Download file from DStaff platform
		localFilePath, err := h.downloadFileFromDStaff(relativeFilePath, taskID, token)
		if err != nil {
			log.Printf("upload_file: Failed to download file from DStaff: %v", err)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: fmt.Sprintf("Failed to download file from DStaff platform: %v", err),
					},
				},
				IsError: true,
			}, nil
		}

		// Convert local file path to file:// URL for upload
		actualFileURL = "file://" + localFilePath
		log.Printf("upload_file: File downloaded from DStaff, using local path: %s", actualFileURL)
	}

	// Download file from URL and upload to ppt-narrator
	respBody, err := h.pptClient.UploadFileFromURL(actualFileURL, name, userRequirements, enableSubtitles, subtitleStyleTemplate)
	if err != nil {
		log.Printf("upload_file: Failed to start pipeline: %v", err)
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to start pipeline: %v", err),
				},
			},
			IsError: true,
		}

		// Add error to log messages
		h.AddLogMessage("error", fmt.Sprintf("Failed to start pipeline: %v", err))
		return errorResult, nil
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Pipeline request sent. Response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Extract project_id from response and save task binding
	if projectIDValue, exists := response["project_id"]; exists {
		if projectID, ok := projectIDValue.(string); ok && taskID != "" {
			// Save task binding for future reference
			if err := h.taskBindingManager.SaveBinding(taskID, projectID); err != nil {
				log.Printf("upload_file: Failed to save task binding: %v", err)
			} else {
				log.Printf("upload_file: Task binding saved successfully (task_id=%s -> project_id=%s)", taskID, projectID)
			}
		}
	}

	result := &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: fmt.Sprintf("Pipeline started successfully. Project ID: %s", response["project_id"]),
			},
		},
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Pipeline started successfully. Project ID: %s", response["project_id"]))
	return result, nil
}

// GetProgressWithProgress handles progress query with progress reporting
func (h *ToolHandlers) GetProgressWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	// Smart parameter parsing: prioritize ppt_id, otherwise get from task_id binding
	var projectID string
	var taskID string

	// Try to get ppt_id directly
	if pptID := request.GetString("ppt_id", ""); pptID != "" {
		projectID = pptID
		log.Printf("get_progress: Using directly provided ppt_id: %s", projectID)
	}

	// Get task_id from context parameter (dictionary)
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	// If no ppt_id provided, try to get project_id from task binding
	if projectID == "" && taskID != "" {
		if boundProjectID, err := h.taskBindingManager.GetProjectID(taskID); err == nil {
			projectID = boundProjectID
			log.Printf("get_progress: Retrieved project_id from task binding: task_id=%s -> project_id=%s", taskID, projectID)
		} else {
			log.Printf("get_progress: Failed to get project_id from task binding for task_id=%s: %v", taskID, err)
		}
	}

	// Validate that we have a project_id
	if projectID == "" {
		errorMsg := "Either ppt_id parameter or valid task_id in context is required"
		log.Printf("get_progress: %s", errorMsg)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMsg,
				},
			},
			IsError: true,
		}, nil
	}

	includeSteps := request.GetBool("include_steps", false)
	log.Printf("get_progress: Getting progress for project '%s' (include_steps: %v, task_id: %s)", projectID, includeSteps, taskID)

	// Proxy request to ppt-narrator
	respBody, err := h.pptClient.GetProgress(projectID)
	if err != nil {
		log.Printf("get_progress: Failed to get progress for project '%s': %v", projectID, err)
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get progress: %v", err),
				},
			},
			IsError: true,
		}

		// Add error to log messages
		h.AddLogMessage("error", fmt.Sprintf("Failed to get progress: %v", err))
		return errorResult, nil
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Progress response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Format response
	resultText := fmt.Sprintf("Project ID: %s\nProgress response: %s", projectID, string(respBody))

	result := &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Progress retrieved for project: %s", projectID))
	return result, nil
}

// GetDownloadURLWithProgress handles download URL retrieval with progress reporting
func (h *ToolHandlers) GetDownloadURLWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	// Smart parameter parsing: prioritize ppt_id, otherwise get from task_id binding
	var projectID string
	var taskID string

	// Try to get ppt_id directly
	if pptID := request.GetString("ppt_id", ""); pptID != "" {
		projectID = pptID
		log.Printf("get_download_url: Using directly provided ppt_id: %s", projectID)
	}

	// Get task_id from context parameter (dictionary)
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	// If no ppt_id provided, try to get project_id from task binding
	if projectID == "" && taskID != "" {
		if boundProjectID, err := h.taskBindingManager.GetProjectID(taskID); err == nil {
			projectID = boundProjectID
			log.Printf("get_download_url: Retrieved project_id from task binding: task_id=%s -> project_id=%s", taskID, projectID)
		} else {
			log.Printf("get_download_url: Failed to get project_id from task binding for task_id=%s: %v", taskID, err)
		}
	}

	// Validate that we have a project_id
	if projectID == "" {
		errorMsg := "Either ppt_id parameter or valid task_id in context is required"
		log.Printf("get_download_url: %s", errorMsg)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMsg,
				},
			},
			IsError: true,
		}, nil
	}

	log.Printf("get_download_url: Getting download info for project '%s' (task_id: %s)", projectID, taskID)

	// TODO: Implement DStaff upload logic if needed
	// For now, just get download information from ppt-narrator

	// Get download information from ppt-narrator
	respBody, err := h.pptClient.GetDownloadURL(projectID)
	if err != nil {
		log.Printf("get_download_url: Failed to get download info for project '%s': %v", projectID, err)
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get download info: %v", err),
				},
			},
			IsError: true,
		}

		// Add error to log messages
		h.AddLogMessage("error", fmt.Sprintf("Failed to get download info: %v", err))
		return errorResult, nil
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Download info response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Get the actual server host from context (set by HTTP handler)
	var mcpBaseURL string
	if serverHost := ctx.Value("server_host"); serverHost != nil {
		if hostStr, ok := serverHost.(string); ok {
			mcpBaseURL = hostStr
		}
	}
	// Fallback to localhost if not available
	if mcpBaseURL == "" {
		mcpBaseURL = "http://localhost:48080"
	}

	// Convert relative URLs to MCP proxy URLs - only for video
	if data, ok := response["data"].(map[string]interface{}); ok {
		if downloads, ok := data["downloads"].(map[string]interface{}); ok {
			// Only process video download
			if videoInfo, ok := downloads["video"].(map[string]interface{}); ok {
				if _, exists := videoInfo["url"].(string); exists {
					// Convert to MCP proxy URL
					videoInfo["url"] = fmt.Sprintf("%s/proxy/download/%s/video", mcpBaseURL, projectID)
				}
			}
		}
	}

	// Convert back to JSON for consistent formatting
	updatedResponse, err := json.Marshal(response)
	if err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Download info response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Format the download information - only return video URL
	resultText := fmt.Sprintf("Project ID: %s\n\n", projectID)

	// Parse the updated response to extract video URL
	var updatedData map[string]interface{}
	if err := json.Unmarshal(updatedResponse, &updatedData); err == nil {
		if data, ok := updatedData["data"].(map[string]interface{}); ok {
			if downloads, ok := data["downloads"].(map[string]interface{}); ok {
				if videoInfo, ok := downloads["video"].(map[string]interface{}); ok {
					if videoURL, ok := videoInfo["url"].(string); ok {
						resultText += fmt.Sprintf("Video Download URL: %s", videoURL)
					}
				}
			}
		}
	}

	if resultText == fmt.Sprintf("Project ID: %s\n\n", projectID) {
		// No video URL found, return the full response
		resultText += fmt.Sprintf("Download info response: %s", string(updatedResponse))
	}

	result := &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Download URL retrieved for project: %s", projectID))
	return result, nil
}

// RetryProjectWithProgress handles project retry with progress reporting
func (h *ToolHandlers) RetryProjectWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	stage, err := request.RequireString("stage")
	if err != nil {
		log.Printf("retry_project: Missing 'stage' parameter: %v", err)
		return nil, err
	}

	userRequirements := request.GetString("user_requirements", "")

	// Smart parameter parsing: prioritize ppt_id, otherwise get from task_id binding
	var projectID string
	var taskID string

	// Try to get ppt_id directly
	if pptID := request.GetString("ppt_id", ""); pptID != "" {
		projectID = pptID
		log.Printf("retry_project: Using directly provided ppt_id: %s", projectID)
	}

	// Get task_id from context parameter (dictionary)
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	// If no ppt_id provided, try to get project_id from task binding
	if projectID == "" && taskID != "" {
		if boundProjectID, err := h.taskBindingManager.GetProjectID(taskID); err == nil {
			projectID = boundProjectID
			log.Printf("retry_project: Retrieved project_id from task binding: task_id=%s -> project_id=%s", taskID, projectID)
		} else {
			log.Printf("retry_project: Failed to get project_id from task binding for task_id=%s: %v", taskID, err)
		}
	}

	// Validate that we have a project_id
	if projectID == "" {
		errorMsg := "Either ppt_id parameter or valid task_id in context is required"
		log.Printf("retry_project: %s", errorMsg)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMsg,
				},
			},
			IsError: true,
		}, nil
	}

	log.Printf("retry_project: Retrying project '%s' from stage '%s' (task_id: %s, user_requirements: %s)", projectID, stage, taskID, userRequirements)

	// Send retry request to ppt-narrator
	respBody, err := h.pptClient.RetryProject(projectID)
	if err != nil {
		log.Printf("retry_project: Failed to retry project '%s': %v", projectID, err)
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to retry project: %v", err),
				},
			},
			IsError: true,
		}

		// Add error to log messages
		h.AddLogMessage("error", fmt.Sprintf("Failed to retry project: %v", err))
		return errorResult, nil
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Retry response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Format response
	var resultText string
	if success, ok := response["success"].(bool); ok && success {
		message := "Retry started successfully"
		if msg, ok := response["message"].(string); ok {
			message = msg
		}
		resultText = fmt.Sprintf("Project ID: %s\nStage: %s\nStatus: %s", projectID, stage, message)
	} else {
		errorMsg := "Unknown error"
		if msg, ok := response["error"].(string); ok {
			errorMsg = msg
		}
		resultText = fmt.Sprintf("Project ID: %s\nStage: %s\nError: %s", projectID, stage, errorMsg)
	}

	result := &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Retry initiated for project: %s, stage: %s", projectID, stage))
	return result, nil
}

// UploadAndMonitorWithProgress handles upload and monitoring with progress reporting
func (h *ToolHandlers) UploadAndMonitorWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (*mcp.CallToolResult, error) {
	// Get task_id from context parameter (dictionary)
	var taskID string
	if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
		// Try both "context" (lowercase) and "Context" (uppercase) for compatibility
		var contextParam interface{}
		var exists bool
		if contextParam, exists = argsMap["context"]; !exists {
			contextParam, exists = argsMap["Context"]
		}

		if exists {
			if contextMap, ok := contextParam.(map[string]interface{}); ok {
				if taskIDValue, exists := contextMap["task_id"]; exists {
					if taskIDStr, ok := taskIDValue.(string); ok {
						taskID = taskIDStr
					}
				}
			}
		}
	}

	// Fallback: try to get task_id from Go context for backward compatibility
	if taskID == "" {
		if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
			if taskIDStr, ok := taskIDValue.(string); ok {
				taskID = taskIDStr
			}
		}
	}

	name, err := request.RequireString("name")
	if err != nil {
		log.Printf("upload_and_monitor: Missing 'name' parameter: %v", err)
		return nil, err
	}

	fileURL, err := request.RequireString("file_url")
	if err != nil {
		log.Printf("upload_and_monitor: Missing 'file_url' parameter: %v", err)
		return nil, err
	}

	userRequirements := request.GetString("user_requirements", "")
	enableSubtitles := request.GetBool("enable_subtitles", false)
	subtitleStyleTemplate := request.GetString("subtitle_style_template", "")

	log.Printf("🚀 upload_and_monitor: 开始为PPT文件 '%s' 生成解说视频并监控进度，URL: %s, task_id: %s", name, fileURL, taskID)

	// Add log message
	h.AddLogMessage("info", fmt.Sprintf("🚀 开始为PPT文件 '%s' 生成解说视频并监控进度", name))

	// Step 1: Upload file (simplified version - just call the upload function)
	// For now, we'll just call the regular upload function
	// TODO: Implement full monitoring with progress reporting

	// Check if fileURL is a relative path (not HTTP URL)
	var actualFileURL string
	var relativeFilePath string

	// Check if it's a full HTTP/HTTPS URL
	if strings.HasPrefix(fileURL, "http://") || strings.HasPrefix(fileURL, "https://") {
		// This is a regular HTTP URL
		actualFileURL = fileURL
		log.Printf("upload_and_monitor: Using HTTP URL: %s", actualFileURL)
	} else if strings.HasPrefix(fileURL, "file://") {
		// Extract relative path from file:// URL
		relativeFilePath = strings.TrimPrefix(fileURL, "file://")
		// Remove leading slash if present
		relativeFilePath = strings.TrimPrefix(relativeFilePath, "/")
		log.Printf("upload_and_monitor: Extracted relative path from file:// URL: %s", relativeFilePath)
	} else {
		// This is a relative path without protocol
		relativeFilePath = fileURL
		log.Printf("upload_and_monitor: Treating as relative path: %s", relativeFilePath)
	}

	// Handle relative paths (download from DStaff platform)
	if relativeFilePath != "" {
		if taskID == "" {
			errorMsg := "task_id is required for downloading files from DStaff platform"
			log.Printf("upload_and_monitor: %s", errorMsg)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: errorMsg,
					},
				},
				IsError: true,
			}, nil
		}

		// Get token from context
		token := ""
		if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
			if tokenStr, ok := tokenValue.(string); ok {
				token = tokenStr
			}
		}

		if token == "" {
			errorMsg := "Authorization token is required for downloading files from DStaff platform"
			log.Printf("upload_and_monitor: %s", errorMsg)
			h.AddLogMessage("error", errorMsg)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: errorMsg,
					},
				},
				IsError: true,
			}, nil
		}

		// Download file from DStaff platform
		localFilePath, err := h.downloadFileFromDStaff(relativeFilePath, taskID, token)
		if err != nil {
			errorMsg := fmt.Sprintf("Failed to download file from DStaff platform: %v", err)
			log.Printf("upload_and_monitor: %s", errorMsg)
			h.AddLogMessage("error", errorMsg)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: errorMsg,
					},
				},
				IsError: true,
			}, nil
		}

		// Convert local file path to file:// URL for upload
		actualFileURL = "file://" + localFilePath
		log.Printf("upload_and_monitor: File downloaded from DStaff, using local path: %s", actualFileURL)
	}

	// Upload file to ppt-narrator
	respBody, err := h.pptClient.UploadFileFromURL(actualFileURL, name, userRequirements, enableSubtitles, subtitleStyleTemplate)
	if err != nil {
		errorMsg := fmt.Sprintf("Failed to start pipeline: %v", err)
		log.Printf("upload_and_monitor: %s", errorMsg)
		h.AddLogMessage("error", errorMsg)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMsg,
				},
			},
			IsError: true,
		}, nil
	}

	// Parse response to get project ID
	var response map[string]interface{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Pipeline request sent. Response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// Extract project ID and save task binding
	if projectIDValue, exists := response["project_id"]; exists {
		if projectID, ok := projectIDValue.(string); ok && taskID != "" {
			// Save task binding for future reference
			if err := h.taskBindingManager.SaveBinding(taskID, projectID); err != nil {
				log.Printf("upload_and_monitor: Failed to save task binding: %v", err)
			} else {
				log.Printf("upload_and_monitor: Task binding saved successfully (task_id=%s -> project_id=%s)", taskID, projectID)
			}
		}
	}

	// Extract project ID for monitoring
	var projectID string
	if projectIDValue, exists := response["project_id"]; exists {
		if projectIDStr, ok := projectIDValue.(string); ok {
			projectID = projectIDStr
		}
	}

	if projectID == "" {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: "Pipeline started but project ID not found in response",
				},
			},
			IsError: true,
		}, nil
	}

	// Add success to log messages
	h.AddLogMessage("info", fmt.Sprintf("Pipeline started successfully. Project ID: %s", projectID))
	log.Printf("✅ Step 1 完成，Pipeline启动成功. Project ID: %s", projectID)

	// 创建 progress reporter
	reporter := progress.NewReporter(taskID, 5, h.GetProgressSendFunc())
	reporter.SetLogSendFunc(h.GetLogSendFunc())

	// 报告 Step 1 完成
	if err := reporter.ReportStep(1, "PPT文件上传完成，Pipeline启动成功"); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	// 现在开始监控进度
	reporter.SendLogMessage("info", "🔍 开始监控Pipeline进度...")
	log.Printf("🔍 开始监控Pipeline进度...")
	return h.monitorPipelineProgressWithReporter(ctx, reporter, projectID, taskID)
}

// downloadFileFromDStaff downloads a file from DStaff platform
func (h *ToolHandlers) downloadFileFromDStaff(filePath, taskID, token string) (string, error) {
	if !h.dstaffConfig.Enabled {
		return "", fmt.Errorf("dstaff integration not enabled")
	}

	// Construct the file download URL
	// The BaseURL should be the base URL of the DStaff service
	fileDownloadURL := h.dstaffConfig.BaseURL + "/api/v1/old-mcp/file/download"

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   taskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", fileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	log.Printf("Downloading file from DStaff: taskId=%s, filePath=%s, url=%s", taskID, filePath, fileDownloadURL)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return "", fmt.Errorf("download failed: %v", errorResp)
		}
		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	filename := filepath.Base(filePath)
	if filename == "." || filename == "/" {
		filename = "downloaded_file"
	}

	// Generate unique filename to avoid conflicts
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("%s_%s_%s", taskID, timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("File downloaded successfully from DStaff: %s -> %s (size: %d bytes)", filePath, localFilePath, len(fileContent))
	return localFilePath, nil
}

// monitorPipelineProgressWithReporter monitors the pipeline progress with progress reporter
func (h *ToolHandlers) monitorPipelineProgressWithReporter(ctx context.Context, reporter *progress.Reporter, projectID, taskID string) (*mcp.CallToolResult, error) {
	reporter.SendLogMessage("info", fmt.Sprintf("🔍 开始监控项目进度 %s", projectID))
	log.Printf("🔍 upload_and_monitor: 开始监控项目进度 %s", projectID)

	// 定义阶段映射
	stageMap := map[string]int{
		"screenshot": 2,
		"narration":  3,
		"audio":      4,
		"video":      5,
	}

	stageNames := map[string]string{
		"screenshot": "正在生成PPT截图...",
		"narration":  "正在生成解说讲稿...",
		"audio":      "正在生成解说音频...",
		"video":      "正在合成最终视频...",
	}

	lastReportedStage := ""
	maxRetries := 3600 // 最多监控1小时 (3600 * 1秒)
	retryCount := 0

	for retryCount < maxRetries {
		// 查询进度
		respBody, err := h.pptClient.GetProgress(projectID)
		if err != nil {
			log.Printf("upload_and_monitor: Failed to get progress: %v", err)
			time.Sleep(1 * time.Second)
			retryCount++
			continue
		}

		// 解析进度响应
		var progressResponse map[string]interface{}
		if err := json.Unmarshal(respBody, &progressResponse); err != nil {
			log.Printf("upload_and_monitor: Failed to parse progress response: %v", err)
			time.Sleep(1 * time.Second)
			retryCount++
			continue
		}

		// 获取当前状态
		var currentStage string
		var isCompleted bool
		var hasError bool

		// 修复：API返回的是progress字段，不是data字段
		if data, ok := progressResponse["progress"].(map[string]interface{}); ok {
			if currentStageData, ok := data["current_stage"].(string); ok {
				currentStage = currentStageData
				log.Printf("upload_and_monitor: Current stage: %s", currentStage)
				// 修复：直接检查current_stage是否为completed或failed
				if currentStage == "completed" {
					isCompleted = true
					log.Printf("upload_and_monitor: Pipeline completed detected!")
				} else if currentStage == "failed" {
					hasError = true
					log.Printf("upload_and_monitor: Pipeline failed detected!")
				}
			}
		} else {
			log.Printf("upload_and_monitor: Warning - no progress data found in response")
		}

		// 报告当前阶段进度 - 使用 ReportStep
		if currentStage != "" && currentStage != lastReportedStage && !isCompleted && !hasError {
			if stepNum, exists := stageMap[currentStage]; exists {
				if stageName, exists := stageNames[currentStage]; exists {
					// 使用 ReportStep 进行步骤上报
					if err := reporter.ReportStep(stepNum, stageName); err != nil {
						log.Printf("⚠️ Failed to send progress: %v", err)
					}
					reporter.SendLogMessage("info", fmt.Sprintf("📊 Step %d: %s", stepNum, stageName))
					log.Printf("📊 Step %d: %s", stepNum, stageName)
					lastReportedStage = currentStage
				}
			}
		}

		// 如果有错误，返回错误结果
		if hasError {
			errorMsg := fmt.Sprintf("Pipeline failed at stage: %s", currentStage)
			reporter.SendLogMessage("error", errorMsg)
			log.Printf("❌ %s", errorMsg)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: errorMsg,
					},
				},
				IsError: true,
			}, nil
		}

		// 如果完成，获取下载链接并返回
		if isCompleted {
			// 使用 ReportStep 报告完成
			if err := reporter.ReportStep(5, "视频生成完成！"); err != nil {
				log.Printf("⚠️ Failed to send progress: %v", err)
			}
			reporter.SendLogMessage("info", "🎉 Step 5: 视频生成完成！")
			reporter.SendLogMessage("info", "📥 正在获取下载信息...")
			log.Printf("🎉 Step 5: 视频生成完成！")
			log.Printf("📥 正在获取下载信息...")

			// 获取下载信息
			return h.getCompletedVideoResultWithReporter(ctx, reporter, projectID, taskID)
		}

		// 等待1秒后继续监控
		time.Sleep(1 * time.Second)
		retryCount++
	}

	// 超时
	errorMsg := fmt.Sprintf("Pipeline monitoring timeout after %d seconds", maxRetries)
	reporter.SendLogMessage("error", errorMsg)
	log.Printf("⏰ %s", errorMsg)
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: errorMsg,
			},
		},
		IsError: true,
	}, nil
}

// monitorPipelineProgress monitors the pipeline progress and reports status updates (legacy version without reporter)
func (h *ToolHandlers) monitorPipelineProgress(ctx context.Context, projectID, taskID string) (*mcp.CallToolResult, error) {
	h.AddLogMessage("info", fmt.Sprintf("🔍 开始监控项目进度 %s", projectID))
	log.Printf("🔍 upload_and_monitor: 开始监控项目进度 %s", projectID)

	// 定义阶段映射
	stageMap := map[string]int{
		"screenshot": 2,
		"narration":  3,
		"audio":      4,
		"video":      5,
	}

	stageNames := map[string]string{
		"screenshot": "正在生成PPT截图...",
		"narration":  "正在生成解说讲稿...",
		"audio":      "正在生成解说音频...",
		"video":      "正在合成最终视频...",
	}

	lastReportedStage := ""
	maxRetries := 3600 // 最多监控1小时 (3600 * 1秒)
	retryCount := 0

	for retryCount < maxRetries {
		// 查询进度
		respBody, err := h.pptClient.GetProgress(projectID)
		if err != nil {
			log.Printf("upload_and_monitor: Failed to get progress: %v", err)
			time.Sleep(1 * time.Second)
			retryCount++
			continue
		}

		// 解析进度响应
		var progressResponse map[string]interface{}
		if err := json.Unmarshal(respBody, &progressResponse); err != nil {
			log.Printf("upload_and_monitor: Failed to parse progress response: %v", err)
			time.Sleep(1 * time.Second)
			retryCount++
			continue
		}

		// 获取当前状态
		var currentStage string
		var isCompleted bool
		var hasError bool

		// 修复：API返回的是progress字段，不是data字段
		if data, ok := progressResponse["progress"].(map[string]interface{}); ok {
			if currentStageData, ok := data["current_stage"].(string); ok {
				currentStage = currentStageData
				log.Printf("upload_and_monitor: Current stage: %s", currentStage)
				// 修复：直接检查current_stage是否为completed或failed
				if currentStage == "completed" {
					isCompleted = true
					log.Printf("upload_and_monitor: Pipeline completed detected!")
				} else if currentStage == "failed" {
					hasError = true
					log.Printf("upload_and_monitor: Pipeline failed detected!")
				}
			}
		} else {
			log.Printf("upload_and_monitor: Warning - no progress data found in response")
		}

		// 报告当前阶段进度
		if currentStage != "" && currentStage != lastReportedStage && !isCompleted && !hasError {
			if stepNum, exists := stageMap[currentStage]; exists {
				if stageName, exists := stageNames[currentStage]; exists {
					h.AddLogMessage("info", fmt.Sprintf("📊 Step %d: %s", stepNum, stageName))
					log.Printf("📊 Step %d: %s", stepNum, stageName)
					lastReportedStage = currentStage
				}
			}
		}

		// 如果有错误，返回错误结果
		if hasError {
			errorMsg := fmt.Sprintf("Pipeline failed at stage: %s", currentStage)
			h.AddLogMessage("error", errorMsg)
			log.Printf("❌ %s", errorMsg)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: errorMsg,
					},
				},
				IsError: true,
			}, nil
		}

		// 如果完成，获取下载链接并返回
		if isCompleted {
			h.AddLogMessage("info", "🎉 Step 5: 视频生成完成！")
			h.AddLogMessage("info", "📥 正在获取下载信息...")
			log.Printf("🎉 Step 5: 视频生成完成！")
			log.Printf("📥 正在获取下载信息...")

			// 获取下载信息
			return h.getCompletedVideoResult(ctx, projectID, taskID)
		}

		// 等待1秒后继续监控
		time.Sleep(1 * time.Second)
		retryCount++
	}

	// 超时
	errorMsg := fmt.Sprintf("Pipeline monitoring timeout after %d seconds", maxRetries)
	h.AddLogMessage("error", errorMsg)
	log.Printf("⏰ %s", errorMsg)
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: errorMsg,
			},
		},
		IsError: true,
	}, nil
}

// getCompletedVideoResult gets the final video file when pipeline is completed
func (h *ToolHandlers) getCompletedVideoResult(ctx context.Context, projectID, taskID string) (*mcp.CallToolResult, error) {
	h.AddLogMessage("info", fmt.Sprintf("📥 获取完成的视频结果，项目ID: %s", projectID))
	log.Printf("📥 获取完成的视频结果，项目ID: %s", projectID)

	// 获取下载信息
	respBody, err := h.pptClient.GetDownloadURL(projectID)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to get download info: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		h.AddLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 解析下载响应
	var downloadResponse map[string]interface{}
	if err := json.Unmarshal(respBody, &downloadResponse); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Download info response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// 检查是否启用DStaff并需要上传文件
	if h.dstaffConfig.Enabled && taskID != "" {
		// Get token from context
		token := ""
		if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
			if tokenStr, ok := tokenValue.(string); ok {
				token = tokenStr
			}
		}

		if token != "" {
			// 上传到DStaff平台
			return h.handleCompletedVideoWithDStaffUpload(ctx, projectID, taskID, token, downloadResponse)
		}
	}

	// 标准处理：返回下载链接
	var resultText string
	if data, ok := downloadResponse["data"].(map[string]interface{}); ok {
		if downloads, ok := data["downloads"].(map[string]interface{}); ok {
			if video, ok := downloads["video"].(map[string]interface{}); ok {
				if available, ok := video["available"].(bool); ok && available {
					if url, ok := video["url"].(string); ok {
						resultText = fmt.Sprintf("✅ 视频生成完成！\n\n项目ID: %s\n视频下载链接: %s", projectID, url)
					} else {
						resultText = fmt.Sprintf("❌ 视频文件不可用：URL获取失败\n项目ID: %s", projectID)
					}
				} else {
					resultText = fmt.Sprintf("❌ 视频文件不可用：尚未生成或文件丢失\n项目ID: %s", projectID)
				}
			} else {
				resultText = fmt.Sprintf("❌ 视频文件不可用：未找到视频信息\n项目ID: %s", projectID)
			}
		} else {
			resultText = fmt.Sprintf("❌ 下载信息不可用：未找到下载数据\n项目ID: %s", projectID)
		}
	} else {
		resultText = fmt.Sprintf("❌ 响应数据格式错误\n项目ID: %s", projectID)
	}

	h.AddLogMessage("info", "✅ 视频生成完成！")
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}, nil
}

// handleCompletedVideoWithDStaffUpload handles video completion with DStaff file upload
func (h *ToolHandlers) handleCompletedVideoWithDStaffUpload(ctx context.Context, projectID, taskID, token string, downloadResponse map[string]interface{}) (*mcp.CallToolResult, error) {
	log.Printf("☁️ 处理完成的视频并上传到DStaff，项目ID: %s", projectID)

	h.AddLogMessage("info", "📤 正在上传视频文件到DStaff平台...")
	log.Printf("📤 Step 5: 上传视频文件到DStaff平台...")

	// 构建视频下载URL
	videoPath := fmt.Sprintf("/api/v1/download/%s/video", projectID)

	// 这里需要从配置中获取 ppt-narrator 的基础URL
	// 暂时使用默认值，实际应该从配置中获取
	baseURL := "http://localhost:8080" // TODO: 从配置中获取

	// 下载视频文件
	log.Printf("📥 正在下载视频文件...")
	client := &http.Client{Timeout: 30 * time.Second}
	videoResp, err := client.Get(baseURL + videoPath)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to download video file: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		h.AddLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}
	defer videoResp.Body.Close()

	if videoResp.StatusCode != 200 {
		errorMessage := fmt.Sprintf("Failed to download video file: HTTP %d", videoResp.StatusCode)
		log.Printf("upload_and_monitor: %s", errorMessage)
		h.AddLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 读取视频内容
	videoContent, err := io.ReadAll(videoResp.Body)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to read video content: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		h.AddLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 上传到DStaff
	filename := fmt.Sprintf("PPT讲解视频_%s.mp4", projectID)
	targetPath := fmt.Sprintf("mcps_upload/%s", filename)

	uploadResult, err := h.uploadFileToDStaffFromContent(videoContent, targetPath, taskID, token, "video/mp4")
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to upload video to DStaff: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		h.AddLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	log.Printf("🎉 视频生成完成并已成功上传到DStaff平台！")
	h.AddLogMessage("info", "🎉 视频生成完成并已上传到DStaff平台！")

	// 返回上传结果
	resultJSON, _ := json.MarshalIndent(uploadResult, "", "  ")
	resultText := fmt.Sprintf("✅ 视频生成完成并已上传到DStaff平台！\n\n项目ID: %s\n上传结果:\n%s", projectID, string(resultJSON))

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}, nil
}

// uploadFileToDStaffFromContent uploads file content to DStaff platform
func (h *ToolHandlers) uploadFileToDStaffFromContent(content []byte, targetPath, taskID, token, contentType string) (map[string]interface{}, error) {
	if !h.dstaffConfig.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// 构建上传URL
	uploadURL := h.dstaffConfig.BaseURL + "/api/v1/old-mcp/file/upload"

	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件内容
	part, err := writer.CreateFormFile("file", filepath.Base(targetPath))
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(content); err != nil {
		return nil, fmt.Errorf("failed to write file content: %w", err)
	}

	// 添加其他字段
	if err := writer.WriteField("targetPath", targetPath); err != nil {
		return nil, fmt.Errorf("failed to write targetPath field: %w", err)
	}

	if err := writer.WriteField("taskId", taskID); err != nil {
		return nil, fmt.Errorf("failed to write taskId field: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建HTTP请求
	client := &http.Client{Timeout: 60 * time.Second}
	req, err := http.NewRequest("POST", uploadURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	log.Printf("Uploading file to DStaff: taskId=%s, targetPath=%s, size=%d bytes", taskID, targetPath, len(content))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != 200 {
		// 尝试解析错误响应
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return nil, fmt.Errorf("upload failed: %v", errorResp)
		}
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析成功响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.Printf("File uploaded successfully to DStaff: %s", targetPath)
	return result, nil
}

// getCompletedVideoResultWithReporter gets the final video file when pipeline is completed (with reporter)
func (h *ToolHandlers) getCompletedVideoResultWithReporter(ctx context.Context, reporter *progress.Reporter, projectID, taskID string) (*mcp.CallToolResult, error) {
	reporter.SendLogMessage("info", fmt.Sprintf("📥 获取完成的视频结果，项目ID: %s", projectID))
	log.Printf("📥 获取完成的视频结果，项目ID: %s", projectID)

	// 获取下载信息
	respBody, err := h.pptClient.GetDownloadURL(projectID)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to get download info: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		reporter.SendLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 解析下载响应
	var downloadResponse map[string]interface{}
	if err := json.Unmarshal(respBody, &downloadResponse); err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Download info response: %s", string(respBody)),
				},
			},
		}, nil
	}

	// 检查是否启用DStaff并需要上传文件
	if h.dstaffConfig.Enabled && taskID != "" {
		// Get token from context
		token := ""
		if tokenValue := ctx.Value("authorization_token"); tokenValue != nil {
			if tokenStr, ok := tokenValue.(string); ok {
				token = tokenStr
			}
		}

		if token != "" {
			// 上传到DStaff平台
			return h.handleCompletedVideoWithDStaffUploadAndReporter(ctx, reporter, projectID, taskID, token, downloadResponse)
		}
	}

	// 标准处理：返回下载链接
	var resultText string
	if data, ok := downloadResponse["data"].(map[string]interface{}); ok {
		if downloads, ok := data["downloads"].(map[string]interface{}); ok {
			if video, ok := downloads["video"].(map[string]interface{}); ok {
				if available, ok := video["available"].(bool); ok && available {
					if url, ok := video["url"].(string); ok {
						resultText = fmt.Sprintf("✅ 视频生成完成！\n\n项目ID: %s\n视频下载链接: %s", projectID, url)
						// 使用 reporter 报告完成
						if err := reporter.Complete("视频生成完成，可以下载了！"); err != nil {
							log.Printf("⚠️ Failed to send completion progress: %v", err)
						}
						reporter.SendLogMessage("info", "🎉 视频生成完成！下载链接已准备就绪")
					} else {
						resultText = fmt.Sprintf("❌ 视频文件不可用：URL获取失败\n项目ID: %s", projectID)
						reporter.SendLogMessage("error", "视频文件不可用：URL获取失败")
					}
				} else {
					resultText = fmt.Sprintf("❌ 视频文件不可用：尚未生成或文件丢失\n项目ID: %s", projectID)
					reporter.SendLogMessage("error", "视频文件不可用：尚未生成或文件丢失")
				}
			} else {
				resultText = fmt.Sprintf("❌ 视频文件不可用：未找到视频信息\n项目ID: %s", projectID)
				reporter.SendLogMessage("error", "视频文件不可用：未找到视频信息")
			}
		} else {
			resultText = fmt.Sprintf("❌ 下载信息不可用：未找到下载数据\n项目ID: %s", projectID)
			reporter.SendLogMessage("error", "下载信息不可用：未找到下载数据")
		}
	} else {
		resultText = fmt.Sprintf("❌ 响应数据格式错误\n项目ID: %s", projectID)
		reporter.SendLogMessage("error", "响应数据格式错误")
	}

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}, nil
}

// handleCompletedVideoWithDStaffUploadAndReporter handles video completion with DStaff file upload (with reporter)
func (h *ToolHandlers) handleCompletedVideoWithDStaffUploadAndReporter(ctx context.Context, reporter *progress.Reporter, projectID, taskID, token string, downloadResponse map[string]interface{}) (*mcp.CallToolResult, error) {
	log.Printf("☁️ 处理完成的视频并上传到DStaff，项目ID: %s", projectID)

	// 使用 reporter 报告正在上传到DStaff
	if err := reporter.ReportStep(5, "正在上传视频文件到DStaff平台..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}
	reporter.SendLogMessage("info", "📤 正在上传视频文件到DStaff平台...")
	log.Printf("📤 Step 5: 上传视频文件到DStaff平台...")

	// 构建视频下载URL
	videoPath := fmt.Sprintf("/api/v1/download/%s/video", projectID)

	// 这里需要从配置中获取 ppt-narrator 的基础URL
	// 暂时使用默认值，实际应该从配置中获取
	baseURL := "http://localhost:8080" // TODO: 从配置中获取

	// 下载视频文件
	log.Printf("📥 正在下载视频文件...")
	client := &http.Client{Timeout: 30 * time.Second}
	videoResp, err := client.Get(baseURL + videoPath)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to download video file: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		reporter.SendLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}
	defer videoResp.Body.Close()

	if videoResp.StatusCode != 200 {
		errorMessage := fmt.Sprintf("Failed to download video file: HTTP %d", videoResp.StatusCode)
		log.Printf("upload_and_monitor: %s", errorMessage)
		reporter.SendLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 读取视频内容
	videoContent, err := io.ReadAll(videoResp.Body)
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to read video content: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		reporter.SendLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 上传到DStaff
	filename := fmt.Sprintf("PPT讲解视频_%s.mp4", projectID)
	targetPath := fmt.Sprintf("mcps_upload/%s", filename)

	uploadResult, err := h.uploadFileToDStaffFromContent(videoContent, targetPath, taskID, token, "video/mp4")
	if err != nil {
		errorMessage := fmt.Sprintf("Failed to upload video to DStaff: %v", err)
		log.Printf("upload_and_monitor: %s", errorMessage)
		reporter.SendLogMessage("error", errorMessage)
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: errorMessage,
				},
			},
			IsError: true,
		}, nil
	}

	// 使用 reporter 报告完成
	if err := reporter.Complete("视频生成完成并已上传到DStaff平台！"); err != nil {
		log.Printf("⚠️ Failed to send completion progress: %v", err)
	}
	log.Printf("🎉 视频生成完成并已成功上传到DStaff平台！")
	reporter.SendLogMessage("info", "🎉 视频生成完成并已上传到DStaff平台！")

	// 返回上传结果
	resultJSON, _ := json.MarshalIndent(uploadResult, "", "  ")
	resultText := fmt.Sprintf("✅ 视频生成完成并已上传到DStaff平台！\n\n项目ID: %s\n上传结果:\n%s", projectID, string(resultJSON))

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: resultText,
			},
		},
	}, nil
}
