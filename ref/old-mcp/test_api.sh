#!/bin/bash

# MCP Pipeline Server API Test Script

BASE_URL="http://localhost:8080"
API_URL="$BASE_URL/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Test variables
JOB_ID=""
TEST_FILE_URL="https://httpbin.org/json"
JOB_NAME="Test Pipeline Job"

echo "🚀 Testing MCP Pipeline Server API"
echo "=================================="

# Test 1: Health Check
print_info "Testing health check..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response "$BASE_URL/health")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Health check passed"
    cat /tmp/health_response | jq '.' 2>/dev/null || cat /tmp/health_response
else
    print_status 1 "Health check failed (HTTP $http_code)"
    exit 1
fi

echo ""

# Test 2: Upload File and Start Pipeline
print_info "Testing file upload and pipeline start..."
response=$(curl -s -w "%{http_code}" -o /tmp/upload_response \
    -X POST "$API_URL/upload" \
    -H "Content-Type: application/json" \
    -d "{\"name\":\"$JOB_NAME\",\"file_url\":\"$TEST_FILE_URL\"}")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    JOB_ID=$(cat /tmp/upload_response | jq -r '.job_id')
    if [ "$JOB_ID" != "null" ] && [ "$JOB_ID" != "" ]; then
        print_status 0 "Pipeline started successfully"
        echo "Job ID: $JOB_ID"
    else
        print_status 1 "Pipeline start failed - no job ID returned"
        cat /tmp/upload_response
        exit 1
    fi
else
    print_status 1 "Pipeline start failed (HTTP $http_code)"
    cat /tmp/upload_response
    exit 1
fi

echo ""

# Test 3: Check Progress
print_info "Testing progress check..."
sleep 2  # Wait a bit for processing to start

response=$(curl -s -w "%{http_code}" -o /tmp/progress_response \
    "$API_URL/progress/$JOB_ID?include_steps=true")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Progress check successful"
    status=$(cat /tmp/progress_response | jq -r '.job.status')
    progress=$(cat /tmp/progress_response | jq -r '.job.progress')
    message=$(cat /tmp/progress_response | jq -r '.job.message')
    echo "Status: $status, Progress: $progress%, Message: $message"
else
    print_status 1 "Progress check failed (HTTP $http_code)"
    cat /tmp/progress_response
fi

echo ""

# Test 4: Wait for completion and test download
print_info "Waiting for pipeline completion..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    response=$(curl -s "$API_URL/progress/$JOB_ID")
    status=$(echo "$response" | jq -r '.job.status')
    progress=$(echo "$response" | jq -r '.job.progress')
    
    echo "Attempt $((attempt+1))/$max_attempts - Status: $status, Progress: $progress%"
    
    if [ "$status" = "completed" ]; then
        print_status 0 "Pipeline completed successfully"
        break
    elif [ "$status" = "failed" ]; then
        print_status 1 "Pipeline failed"
        echo "$response" | jq '.job.error_msg'
        exit 1
    fi
    
    sleep 2
    attempt=$((attempt+1))
done

if [ $attempt -eq $max_attempts ]; then
    print_status 1 "Pipeline did not complete within expected time"
    exit 1
fi

echo ""

# Test 5: Get Download URL
print_info "Testing download URL generation..."
response=$(curl -s -w "%{http_code}" -o /tmp/download_url_response \
    "$API_URL/download-url/$JOB_ID")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    download_url=$(cat /tmp/download_url_response | jq -r '.download_url')
    print_status 0 "Download URL generated successfully"
    echo "Download URL: $download_url"
else
    print_status 1 "Download URL generation failed (HTTP $http_code)"
    cat /tmp/download_url_response
fi

echo ""

# Test 6: Download File
print_info "Testing file download..."
response=$(curl -s -w "%{http_code}" -o /tmp/downloaded_file \
    "$API_URL/download/$JOB_ID")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    file_size=$(wc -c < /tmp/downloaded_file)
    print_status 0 "File downloaded successfully"
    echo "Downloaded file size: $file_size bytes"
    echo "First few lines of downloaded file:"
    head -5 /tmp/downloaded_file
else
    print_status 1 "File download failed (HTTP $http_code)"
fi

echo ""

# Test 7: SSE Status
print_info "Testing SSE status..."
response=$(curl -s -w "%{http_code}" -o /tmp/sse_status_response \
    "$API_URL/status")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    client_count=$(cat /tmp/sse_status_response | jq -r '.client_count')
    service_status=$(cat /tmp/sse_status_response | jq -r '.service_status')
    print_status 0 "SSE status check successful"
    echo "Connected clients: $client_count, Service status: $service_status"
else
    print_status 1 "SSE status check failed (HTTP $http_code)"
    cat /tmp/sse_status_response
fi

echo ""

# Test 8: Test another job and cancel it
print_info "Testing job cancellation..."
response=$(curl -s -X POST "$API_URL/upload" \
    -H "Content-Type: application/json" \
    -d "{\"name\":\"Cancel Test Job\",\"file_url\":\"$TEST_FILE_URL\"}")

cancel_job_id=$(echo "$response" | jq -r '.job_id')

if [ "$cancel_job_id" != "null" ] && [ "$cancel_job_id" != "" ]; then
    sleep 1  # Let it start
    
    response=$(curl -s -w "%{http_code}" -o /tmp/cancel_response \
        -X DELETE "$API_URL/cancel/$cancel_job_id")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        print_status 0 "Job cancellation successful"
    else
        print_status 1 "Job cancellation failed (HTTP $http_code)"
        cat /tmp/cancel_response
    fi
else
    print_status 1 "Could not create job for cancellation test"
fi

echo ""

# Cleanup
print_info "Cleaning up temporary files..."
rm -f /tmp/health_response /tmp/upload_response /tmp/progress_response 
rm -f /tmp/download_url_response /tmp/downloaded_file /tmp/sse_status_response
rm -f /tmp/cancel_response

echo ""
echo "🎉 API testing completed!"
echo ""
echo "To test SSE functionality, open examples/client.html in your browser"
echo "or use curl to connect to the SSE endpoint:"
echo "curl -N -H 'Accept: text/event-stream' '$API_URL/sse'"
