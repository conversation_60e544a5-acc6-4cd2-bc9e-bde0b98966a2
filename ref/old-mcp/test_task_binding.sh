#!/bin/bash

# 任务绑定功能测试脚本
# 测试任务绑定功能的各种场景

set -e

echo "=== 任务绑定功能测试 ==="

# 配置
MCP_SERVER_URL="http://localhost:48081"
TASK_ID="test_task_$(date +%s)"
TEST_PPT_URL="https://example.com/test.pptx"

echo "使用测试任务ID: $TASK_ID"
echo "MCP服务器地址: $MCP_SERVER_URL"

# 测试1: 上传文件并创建绑定
echo ""
echo "=== 测试1: 上传文件并创建绑定 ==="
UPLOAD_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"upload_file\",
      \"arguments\": {
        \"name\": \"测试演示文稿\",
        \"file_url\": \"$TEST_PPT_URL\",
        \"user_requirements\": \"请生成专业的解说\",
        \"Context\": {
          \"task_id\": \"$TASK_ID\"
        }
      }
    }
  }")

echo "上传响应: $UPLOAD_RESPONSE"

# 从响应中提取project_id
PROJECT_ID=$(echo "$UPLOAD_RESPONSE" | grep -o 'Project ID: [^"]*' | cut -d' ' -f3 || echo "")
if [ -z "$PROJECT_ID" ]; then
  echo "警告: 无法从响应中提取project_id，使用模拟ID进行后续测试"
  PROJECT_ID="proj_test_123"
fi

echo "提取的项目ID: $PROJECT_ID"

# 等待一秒让绑定保存
sleep 1

# 测试2: 使用task_id查询进度（应该自动获取project_id）
echo ""
echo "=== 测试2: 使用task_id查询进度 ==="
PROGRESS_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"get_progress\",
      \"arguments\": {
        \"include_steps\": true,
        \"Context\": {
          \"task_id\": \"$TASK_ID\"
        }
      }
    }
  }")

echo "进度查询响应: $PROGRESS_RESPONSE"

# 测试3: 直接使用ppt_id查询进度（向后兼容）
echo ""
echo "=== 测试3: 直接使用ppt_id查询进度 ==="
DIRECT_PROGRESS_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"get_progress\",
      \"arguments\": {
        \"ppt_id\": \"$PROJECT_ID\",
        \"include_steps\": true
      }
    }
  }")

echo "直接查询响应: $DIRECT_PROGRESS_RESPONSE"

# 测试4: 使用task_id获取下载链接
echo ""
echo "=== 测试4: 使用task_id获取下载链接 ==="
DOWNLOAD_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"get_download_url\",
      \"arguments\": {
        \"Context\": {
          \"task_id\": \"$TASK_ID\"
        }
      }
    }
  }")

echo "下载链接响应: $DOWNLOAD_RESPONSE"

# 测试5: 使用task_id重试项目
echo ""
echo "=== 测试5: 使用task_id重试项目 ==="
RETRY_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"retry_project\",
      \"arguments\": {
        \"stage\": \"narration\",
        \"user_requirements\": \"请重新生成更自然的解说\",
        \"Context\": {
          \"task_id\": \"$TASK_ID\"
        }
      }
    }
  }")

echo "重试响应: $RETRY_RESPONSE"

# 测试6: 错误场景 - 无效的task_id
echo ""
echo "=== 测试6: 错误场景 - 无效的task_id ==="
ERROR_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"get_progress\",
      \"arguments\": {
        \"Context\": {
          \"task_id\": \"invalid_task_id_12345\"
        }
      }
    }
  }")

echo "错误场景响应: $ERROR_RESPONSE"

# 测试7: 错误场景 - 既没有ppt_id也没有task_id
echo ""
echo "=== 测试7: 错误场景 - 缺少必要参数 ==="
MISSING_PARAMS_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/tools/call" \
  -H "Content-Type: application/json" \
  -d "{
    \"method\": \"tools/call\",
    \"params\": {
      \"name\": \"get_progress\",
      \"arguments\": {
        \"include_steps\": true
      }
    }
  }")

echo "缺少参数响应: $MISSING_PARAMS_RESPONSE"

echo ""
echo "=== 测试完成 ==="
echo "请检查上述响应，验证任务绑定功能是否正常工作："
echo "1. 上传文件应该成功并返回project_id"
echo "2. 使用task_id查询进度应该成功（自动获取project_id）"
echo "3. 直接使用ppt_id查询应该成功（向后兼容）"
echo "4. 错误场景应该返回明确的错误信息"

# 检查数据文件是否创建
echo ""
echo "=== 检查数据文件 ==="
if [ -f "./data/task_bindings.json" ]; then
  echo "绑定数据文件已创建: ./data/task_bindings.json"
  echo "文件内容:"
  cat ./data/task_bindings.json | head -20
else
  echo "警告: 绑定数据文件未找到"
fi
