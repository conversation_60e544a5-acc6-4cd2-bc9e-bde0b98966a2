package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mcp-pipeline-server/internal/handlers"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"mcp-pipeline-server/internal/binding"
)

// ToolHandlers definition moved to internal/handlers/tool_handlers.go

// All ToolHandlers methods are now in the handlers package

// convertDStaffConfig converts handlers.DStaffConfig to main.DStaffConfig
func convertDStaffConfig(handlerConfig *handlers.DStaffConfig) *DStaffConfig {
	// The BaseURL in handlers.DStaffConfig should be the endpoint base URL
	// We need to reconstruct the full URLs for the main.DStaffConfig
	baseURL := handlerConfig.BaseURL
	if baseURL == "" {
		// Fallback to environment variable or default
		baseURL = os.Getenv("DSTAFF_ENDPOINT_URL")
		if baseURL == "" {
			baseURL = "http://*********:8800" // Default endpoint
		}
	}

	return &DStaffConfig{
		Enabled:         handlerConfig.Enabled,
		AuthServiceURL:  baseURL + "/api/v1/old-mcp/validateToken",
		FileUploadURL:   baseURL + "/api/v1/old-mcp/file/upload",
		FileDownloadURL: baseURL + "/api/v1/old-mcp/file/download",
		UseOfficialAuth: true, // Default value
	}
}

// createToolHandler creates a handler function for a specific tool
func (w *MCPServerWrapper) createToolHandler(toolName string) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		log.Printf("🔧 Executing tool: %s", toolName)

		// 设置工具通知
		w.setupToolNotifications(ctx)

		// 输出详细的请求体信息（如果启用了MCP调试）
		if w.debugConfig.Enabled {
			requestJSON, _ := json.MarshalIndent(request, "", "  ")
			log.Printf("=== MCP Tool Call: %s ===", toolName)
			log.Printf("Request Body: %s", string(requestJSON))
			log.Printf("=====================================")
		}

		// Convert arguments to map[string]interface{}
		args, ok := request.Params.Arguments.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid arguments format")
		}

		// 路由到相应的工具处理器
		result, err := w.handleToolExecution(ctx, toolName, request, args)
		if err != nil {
			log.Printf("❌ Tool execution failed: %v", err)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.TextContent{
						Type: "text",
						Text: fmt.Sprintf("Error: %v", err),
					},
				},
				IsError: true,
			}, nil
		}

		log.Printf("✅ Tool executed successfully: %s", toolName)

		// Convert result to MCP content
		content := convertResultToContent(result)

		return &mcp.CallToolResult{
			Content: content,
			IsError: false,
		}, nil
	}
}

// convertResultToContent converts tool result to MCP content
func convertResultToContent(result map[string]interface{}) []mcp.Content {
	var content []mcp.Content

	// Check if result already contains properly formatted content
	if contentArray, exists := result["content"]; exists {
		if contentSlice, ok := contentArray.([]map[string]interface{}); ok {
			// Convert each content item to old-mcp.Content
			for _, item := range contentSlice {
				if itemType, hasType := item["type"]; hasType {
					switch itemType {
					case "text":
						if text, hasText := item["text"]; hasText {
							if textStr, isString := text.(string); isString {
								content = append(content, mcp.TextContent{
									Type: "text",
									Text: textStr,
								})
							}
						}
					case "image":
						if data, hasData := item["data"]; hasData {
							if dataStr, isString := data.(string); isString {
								if mimeType, hasMime := item["mimeType"]; hasMime {
									if mimeStr, isMimeString := mimeType.(string); isMimeString {
										content = append(content, mcp.ImageContent{
											Type:     "image",
											Data:     dataStr,
											MIMEType: mimeStr,
										})
									}
								}
							}
						}
					}
				}
			}
			return content
		}
	}

	// Fallback: Convert result to JSON-like text representation
	var parts []string
	for key, value := range result {
		switch v := value.(type) {
		case string:
			parts = append(parts, fmt.Sprintf("%s: %s", key, v))
		case []string:
			parts = append(parts, fmt.Sprintf("%s: [%s]", key, strings.Join(v, ", ")))
		case int:
			parts = append(parts, fmt.Sprintf("%s: %d", key, v))
		case bool:
			parts = append(parts, fmt.Sprintf("%s: %t", key, v))
		default:
			// Skip the content field to avoid double processing
			if key != "content" {
				parts = append(parts, fmt.Sprintf("%s: %v", key, v))
			}
		}
	}

	content = append(content, mcp.TextContent{
		Type: "text",
		Text: strings.Join(parts, "\n"),
	})

	return content
}

// handleToolExecution routes tool execution to appropriate handlers
func (w *MCPServerWrapper) handleToolExecution(ctx context.Context, toolName string, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	switch toolName {
	case "upload_file":
		return w.handleUploadFile(ctx, request, args)
	case "get_progress":
		return w.handleGetProgress(ctx, request, args)
	case "get_download_url":
		return w.handleGetDownloadURL(ctx, request, args)
	case "retry_project":
		return w.handleRetryProject(ctx, request, args)
	case "upload_and_monitor":
		return w.handleUploadAndMonitor(ctx, request, args)
	default:
		return nil, fmt.Errorf("unknown tool: %s", toolName)
	}
}

// convertMCPResultToMap converts MCP CallToolResult to map[string]interface{}
func convertMCPResultToMap(result *mcp.CallToolResult) map[string]interface{} {
	if result == nil {
		return map[string]interface{}{
			"success": false,
			"error":   "empty result",
		}
	}

	if len(result.Content) > 0 {
		if textContent, ok := result.Content[0].(mcp.TextContent); ok {
			// Try to parse the text content as JSON
			var jsonContent map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonContent); err == nil {
				// If it's valid JSON, return it directly
				return jsonContent
			} else {
				// If it's not JSON, return as a simple message
				return map[string]interface{}{
					"success": !result.IsError,
					"message": textContent.Text,
				}
			}
		}
	}

	return map[string]interface{}{
		"success": !result.IsError,
	}
}

// handleUploadFile handles the upload_file tool
func (w *MCPServerWrapper) handleUploadFile(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	result, err := w.toolHandlers.UploadFileWithProgress(ctx, request, args)
	if err != nil {
		return nil, err
	}
	return convertMCPResultToMap(result), nil
}

// handleGetProgress handles the get_progress tool
func (w *MCPServerWrapper) handleGetProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	result, err := w.toolHandlers.GetProgressWithProgress(ctx, request, args)
	if err != nil {
		return nil, err
	}
	return convertMCPResultToMap(result), nil
}

// handleGetDownloadURL handles the get_download_url tool
func (w *MCPServerWrapper) handleGetDownloadURL(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	result, err := w.toolHandlers.GetDownloadURLWithProgress(ctx, request, args)
	if err != nil {
		return nil, err
	}
	return convertMCPResultToMap(result), nil
}

// handleRetryProject handles the retry_project tool
func (w *MCPServerWrapper) handleRetryProject(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	result, err := w.toolHandlers.RetryProjectWithProgress(ctx, request, args)
	if err != nil {
		return nil, err
	}
	return convertMCPResultToMap(result), nil
}

// handleUploadAndMonitor handles the upload_and_monitor tool
func (w *MCPServerWrapper) handleUploadAndMonitor(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	result, err := w.toolHandlers.UploadAndMonitorWithProgress(ctx, request, args)
	if err != nil {
		return nil, err
	}
	return convertMCPResultToMap(result), nil
}

// MCPServerWrapper wraps the MCP server with additional functionality
type MCPServerWrapper struct {
	mcpServer          *server.MCPServer
	toolHandlers       *handlers.ToolHandlers
	taskBindingManager *binding.TaskBindingManager
	dstaffConfig       *DStaffConfig
	debugConfig        *DebugConfig
	accessKey          string
}

// NewMCPServerWrapper creates a new MCP server wrapper
func NewMCPServerWrapper(pptNarratorURL string) *MCPServerWrapper {
	// Initialize task binding manager
	dataDir := "./data"
	taskBindingManager := binding.NewTaskBindingManager(dataDir)
	log.Printf("Task binding manager initialized with data directory: %s", dataDir)

	// Load dstaff configuration
	dstaffConfig := LoadDStaffConfig()
	if dstaffConfig.Enabled {
		log.Println("DStaff integration enabled")
		log.Printf("DStaff auth service URL: %s", dstaffConfig.AuthServiceURL)
		log.Printf("DStaff use official auth: %v", dstaffConfig.UseOfficialAuth)
	} else {
		log.Println("DStaff integration disabled")
	}

	// Load debug configuration
	debugConfig := LoadDebugConfig()
	if debugConfig.Enabled {
		log.Println("MCP Debug mode enabled - all requests will be logged")
	} else {
		log.Println("MCP Debug mode disabled")
	}

	// Get access key from environment variable (legacy auth)
	accessKey := os.Getenv("MCP_ACCESS_KEY")
	if accessKey == "" && !dstaffConfig.Enabled {
		log.Println("Warning: Neither MCP_ACCESS_KEY nor DStaff auth is configured, server will be accessible without authentication")
	} else if accessKey != "" {
		log.Println("MCP access key authentication enabled (legacy)")
	}

	log.Printf("PPT Narrator service URL: %s", pptNarratorURL)

	// Initialize ppt-narrator client
	pptClient := NewPPTNarratorClient(pptNarratorURL)

	// Create MCP server
	mcpServer := server.NewMCPServer("pipeline-server",
		"1.0.0",
		server.WithToolCapabilities(true),
		server.WithLogging(),
	)

	// Convert DStaffConfig to handlers.DStaffConfig
	// Extract base URL from AuthServiceURL by removing the API path
	baseURL := ""
	if dstaffConfig.AuthServiceURL != "" {
		// AuthServiceURL is like "http://*********:8800/api/v1/mcp/validateToken"
		// We need to extract "http://*********:8800"
		if idx := strings.Index(dstaffConfig.AuthServiceURL, "/api/"); idx != -1 {
			baseURL = dstaffConfig.AuthServiceURL[:idx]
		} else {
			baseURL = dstaffConfig.AuthServiceURL
		}
	}

	handlersDStaffConfig := &handlers.DStaffConfig{
		Enabled: dstaffConfig.Enabled,
		BaseURL: baseURL,
	}

	// Create tool handlers using the new handlers package
	toolHandlers := handlers.NewToolHandlers(pptClient, handlersDStaffConfig, taskBindingManager)

	wrapper := &MCPServerWrapper{
		mcpServer:          mcpServer,
		toolHandlers:       toolHandlers,
		taskBindingManager: taskBindingManager,
		dstaffConfig:       dstaffConfig,
		debugConfig:        debugConfig,
		accessKey:          accessKey,
	}

	// Register tools
	wrapper.registerTools()

	// 设置进度发送函数（在注册工具之后）
	wrapper.setupProgressSending()

	return wrapper
}

// registerTools registers all available tools with the MCP server
func (w *MCPServerWrapper) registerTools() {
	// Add pipeline-related MCP tools using the new createToolHandler approach
	w.addPipelineTools()
	log.Printf("✅ All tools registered successfully")
}

// setupProgressSending sets up progress notification sending
func (w *MCPServerWrapper) setupProgressSending() {
	log.Printf("🔧 [DEBUG] setupProgressSending called")

	// 设置进度发送函数 - 输出详细的进度日志
	w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
		// 简化处理，直接使用默认值
		total := float64(5)
		message := "Processing..."

		// 输出格式化的进度日志，客户端可以解析这些日志
		log.Printf("🔄 [PROGRESS] %.0f/%.0f (%.1f%%) - %s",
			notification.Params.Progress,
			total,
			(notification.Params.Progress/total)*100,
			message)
		return nil
	})

	// 设置日志发送函数 - 参考smart-card-mcp实现，但这里我们还没有MCP服务器上下文
	// 所以先设置一个占位符函数，真正的发送函数会在工具执行时设置
	w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
		log.Printf("🔧 [DEBUG] LogSendFunc called with level: %s, data: %v",
			string(notification.Params.Level),
			notification.Params.Data)

		// 将日志消息添加到工具处理器的日志集合中
		// 需要将Data转换为字符串
		var dataStr string
		if str, ok := notification.Params.Data.(string); ok {
			dataStr = str
		} else {
			dataStr = fmt.Sprintf("%v", notification.Params.Data)
		}
		w.toolHandlers.AddLogMessage(string(notification.Params.Level), dataStr)

		// 记录到控制台作为后备
		log.Printf("📝 [%s] %s", strings.ToUpper(string(notification.Params.Level)), dataStr)
		return nil
	})

	log.Printf("✅ Progress and log sending functions configured")
}

// addPipelineTools adds pipeline-related tools using the new createToolHandler approach
func (w *MCPServerWrapper) addPipelineTools() {
	// Upload file and start pipeline tool
	w.mcpServer.AddTool(mcp.Tool{
		Name:        "upload_file",
		Description: "上传PPT文件并开始生成解说视频",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]any{
				"name": map[string]any{
					"type":        "string",
					"description": "PPT文件名称",
				},
				"file_url": map[string]any{
					"type":        "string",
					"description": "PPT文件的下载URL地址",
				},
				"user_requirements": map[string]any{
					"type":        "string",
					"description": "用户需求描述（可选）",
				},
				"enable_subtitles": map[string]any{
					"type":        "boolean",
					"description": "是否启用字幕（可选，默认为false）",
				},
				"subtitle_style_template": map[string]any{
					"type":        "string",
					"description": "字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal",
				},
				"Context": map[string]any{
					"type":        "object",
					"description": "上下文参数，包含task_id等信息",
					"properties": map[string]any{
						"task_id": map[string]any{
							"type":        "string",
							"description": "任务ID",
						},
					},
				},
			},
			Required: []string{"name", "file_url"},
		},
	}, w.createToolHandler("upload_file"))

	// Get progress tool
	w.mcpServer.AddTool(mcp.Tool{
		Name:        "get_progress",
		Description: "查询PPT解说视频生成进度",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]any{
				"ppt_id": map[string]any{
					"type":        "string",
					"description": "PPT项目ID（可选，如果未提供将从上下文获取）",
				},
				"Context": map[string]any{
					"type":        "object",
					"description": "上下文参数，包含task_id等信息",
					"properties": map[string]any{
						"task_id": map[string]any{
							"type":        "string",
							"description": "任务ID",
						},
					},
				},
			},
			Required: []string{},
		},
	}, w.createToolHandler("get_progress"))

	// Get download URL tool
	w.mcpServer.AddTool(mcp.Tool{
		Name:        "get_download_url",
		Description: "获取生成的PPT解说视频下载链接",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]any{
				"ppt_id": map[string]any{
					"type":        "string",
					"description": "PPT项目ID（可选，如果未提供将从上下文获取）",
				},
				"Context": map[string]any{
					"type":        "object",
					"description": "上下文参数，包含task_id等信息",
					"properties": map[string]any{
						"task_id": map[string]any{
							"type":        "string",
							"description": "任务ID",
						},
					},
				},
			},
			Required: []string{},
		},
	}, w.createToolHandler("get_download_url"))

	// Retry project tool
	w.mcpServer.AddTool(mcp.Tool{
		Name:        "retry_project",
		Description: "重试失败的PPT解说视频生成项目",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]any{
				"stage": map[string]any{
					"type":        "string",
					"description": "重试的阶段：extract_text, generate_script, generate_audio, generate_video",
				},
				"ppt_id": map[string]any{
					"type":        "string",
					"description": "PPT项目ID（可选，如果未提供将从上下文获取）",
				},
				"Context": map[string]any{
					"type":        "object",
					"description": "上下文参数，包含task_id等信息",
					"properties": map[string]any{
						"task_id": map[string]any{
							"type":        "string",
							"description": "任务ID",
						},
					},
				},
			},
			Required: []string{"stage"},
		},
	}, w.createToolHandler("retry_project"))

	// Upload and monitor tool
	w.mcpServer.AddTool(mcp.Tool{
		Name:        "upload_and_monitor",
		Description: "上传PPT文件并监控整个生成过程直到完成",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]any{
				"name": map[string]any{
					"type":        "string",
					"description": "PPT文件名称",
				},
				"file_url": map[string]any{
					"type":        "string",
					"description": "PPT文件的下载URL地址",
				},
				"user_requirements": map[string]any{
					"type":        "string",
					"description": "用户需求描述（可选）",
				},
				"enable_subtitles": map[string]any{
					"type":        "boolean",
					"description": "是否启用字幕（可选，默认为false）",
				},
				"subtitle_style_template": map[string]any{
					"type":        "string",
					"description": "字幕样式模板（可选）：classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal",
				},
				"Context": map[string]any{
					"type":        "object",
					"description": "上下文参数，包含task_id等信息",
					"properties": map[string]any{
						"task_id": map[string]any{
							"type":        "string",
							"description": "任务ID",
						},
					},
				},
			},
			Required: []string{"name", "file_url"},
		},
	}, w.createToolHandler("upload_and_monitor"))
}

// PPTNarratorClient handles requests to ppt-narrator service
type PPTNarratorClient struct {
	baseURL string
	client  *http.Client
}

// DStaffConfig holds dstaff platform configuration
type DStaffConfig struct {
	Enabled         bool
	AuthServiceURL  string
	FileUploadURL   string
	FileDownloadURL string
	UseOfficialAuth bool
}

// DebugConfig holds debug configuration
type DebugConfig struct {
	Enabled bool
}

// AuthContext holds authentication context for requests
type AuthContext struct {
	Token  string
	TaskID string
}

// FileUploadResponse represents the response format for dstaff file uploads
type FileUploadResponse struct {
	WorkType    string       `json:"work_type"`
	Compression bool         `json:"compression"`
	Status      string       `json:"status"`
	Message     string       `json:"message"`
	Attachments []Attachment `json:"attachments"`
}

// Attachment represents a file attachment in the response
type Attachment struct {
	Path          string `json:"path"`
	Filename      string `json:"filename"`
	Type          string `json:"type"`
	ContentType   string `json:"content_type"`
	ContentLength int64  `json:"content_length"`
}

// NewPPTNarratorClient creates a new client for ppt-narrator service
func NewPPTNarratorClient(baseURL string) *PPTNarratorClient {
	return &PPTNarratorClient{
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 5 * time.Minute, // Long timeout for file processing
		},
	}
}

// LoadDStaffConfig loads dstaff configuration from environment variables
func LoadDStaffConfig() *DStaffConfig {
	enabled := os.Getenv("DSTAFF_ENABLED") == "true"
	if !enabled {
		return &DStaffConfig{Enabled: false}
	}

	endpointURL := os.Getenv("DSTAFF_ENDPOINT_URL")
	if endpointURL == "" {
		endpointURL = "http://*********:8800" // Default endpoint
	}

	return &DStaffConfig{
		Enabled:         true,
		AuthServiceURL:  endpointURL + "/api/v1/old-mcp/validateToken",
		FileUploadURL:   endpointURL + "/api/v1/old-mcp/file/upload",
		FileDownloadURL: endpointURL + "/api/v1/old-mcp/file/download",
		UseOfficialAuth: os.Getenv("DSTAFF_USE_OFFICIAL_AUTH") == "true",
	}
}

// LoadDebugConfig loads debug configuration from environment variables
func LoadDebugConfig() *DebugConfig {
	return &DebugConfig{
		Enabled: os.Getenv("MCP_DEBUG") == "true",
	}
}

// ValidateTokenWithDStaff validates token using dstaff official auth service
func ValidateTokenWithDStaff(config *DStaffConfig, token string) bool {
	log.Printf("=== DStaff Token Validation ===")
	log.Printf("Config enabled: %v, UseOfficialAuth: %v", config.Enabled, config.UseOfficialAuth)
	log.Printf("Token to validate: '%s' (length: %d)", token, len(token))
	log.Printf("Auth service URL: %s", config.AuthServiceURL)

	if !config.Enabled || !config.UseOfficialAuth {
		log.Printf("DStaff validation skipped: config not enabled or not using official auth")
		return false
	}

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", config.AuthServiceURL, nil)
	if err != nil {
		log.Printf("Failed to create auth request to %s: %v", config.AuthServiceURL, err)
		return false
	}

	authHeaderValue := "Bearer " + token
	req.Header.Set("Authorization", authHeaderValue)
	log.Printf("Sending validation request with Authorization header: '%s'", authHeaderValue)

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to validate token with %s: %v", config.AuthServiceURL, err)
		return false
	}
	defer resp.Body.Close()

	// Read response body for logging
	respBody, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		log.Printf("Failed to read validation response body: %v", readErr)
	} else {
		log.Printf("Validation response (HTTP %d): %s", resp.StatusCode, string(respBody))
	}

	if resp.StatusCode == 200 {
		var result map[string]interface{}
		if err := json.Unmarshal(respBody, &result); err == nil {
			log.Printf("Parsed validation response: %+v", result)
			if data, ok := result["data"].(bool); ok && data {
				log.Printf("Token validation successful for token: '%s'", token)
				return true
			} else {
				log.Printf("Token validation failed: data field is %v (type: %T)", result["data"], result["data"])
			}
		} else {
			log.Printf("Failed to parse validation response JSON: %v", err)
		}
	}

	log.Printf("Token validation failed for token '%s': HTTP %d", token, resp.StatusCode)
	log.Printf("===============================")
	return false
}

// ExtractAuthContext extracts authentication context from MCP request
func ExtractAuthContext(request mcp.CallToolRequest) *AuthContext {
	// Try to extract from request context or headers
	// Note: This is a simplified implementation. In a real scenario,
	// the token and task_id would be extracted from the MCP request context
	// or passed as parameters in the tool call.

	// For now, we'll try to get them from environment variables as fallback
	// In production, these should come from the actual MCP request context
	token := os.Getenv("DSTAFF_TOKEN")
	taskID := os.Getenv("DSTAFF_TASK_ID")

	// Try to extract from request parameters if available
	if tokenParam := request.GetString("token", ""); tokenParam != "" {
		token = tokenParam
	}
	if taskIDParam := request.GetString("task_id", ""); taskIDParam != "" {
		taskID = taskIDParam
	}

	return &AuthContext{
		Token:  token,
		TaskID: taskID,
	}
}

// ProxyRequest proxies a request to ppt-narrator service
func (c *PPTNarratorClient) ProxyRequest(method, path string, body []byte) ([]byte, error) {
	url := c.baseURL + path

	log.Printf("Proxying %s request to: %s", method, url)

	var bodyReader io.Reader
	if body != nil {
		bodyReader = bytes.NewReader(body)
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		log.Printf("Failed to create request: %v", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		log.Printf("Failed to send request to %s: %v", url, err)
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read response from %s: %v", url, err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode >= 400 {
		log.Printf("Request to %s failed with status %d: %s", url, resp.StatusCode, string(respBody))
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	log.Printf("Request to %s successful (status %d)", url, resp.StatusCode)
	return respBody, nil
}

// UploadFileFromURL downloads a file from URL and uploads it to ppt-narrator service
func (c *PPTNarratorClient) UploadFileFromURL(fileURL, projectName, userRequirements string, enableSubtitles bool, subtitleStyleTemplate string) ([]byte, error) {
	var fileContent []byte
	var err error

	// Check if this is a local file path (starts with file://)
	if strings.HasPrefix(fileURL, "file://") {
		// This is a local file path
		localPath := strings.TrimPrefix(fileURL, "file://")
		log.Printf("Reading local file: %s", localPath)

		fileContent, err = os.ReadFile(localPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read local file: %w", err)
		}

		log.Printf("Local file read successfully, size: %d bytes", len(fileContent))
	} else {
		// Download file from URL
		log.Printf("Downloading file from URL: %s", fileURL)
		resp, err := c.client.Get(fileURL)
		if err != nil {
			return nil, fmt.Errorf("failed to download file: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode >= 400 {
			return nil, fmt.Errorf("failed to download file: HTTP %d", resp.StatusCode)
		}

		// Read file content
		fileContent, err = io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read file content: %w", err)
		}
	}

	// Extract filename from URL or local path
	var filename string
	if strings.HasPrefix(fileURL, "file://") {
		// For local files, extract filename from the local path
		localPath := strings.TrimPrefix(fileURL, "file://")
		filename = filepath.Base(localPath)
	} else {
		// For URLs, extract filename from URL
		filename = filepath.Base(fileURL)
	}

	// Ensure we have a valid filename with .pptx extension
	if !strings.HasSuffix(strings.ToLower(filename), ".pptx") {
		filename = "presentation.pptx" // Default filename if not detected
	}

	// Create multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add file field
	fileWriter, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := fileWriter.Write(fileContent); err != nil {
		return nil, fmt.Errorf("failed to write file content: %w", err)
	}

	// Add other form fields
	if projectName != "" {
		if err := writer.WriteField("project_name", projectName); err != nil {
			return nil, fmt.Errorf("failed to write project_name field: %w", err)
		}
	}

	if userRequirements != "" {
		if err := writer.WriteField("user_requirements", userRequirements); err != nil {
			return nil, fmt.Errorf("failed to write user_requirements field: %w", err)
		}
	}

	// Add subtitle parameters
	if enableSubtitles {
		if err := writer.WriteField("enable_subtitles", "true"); err != nil {
			return nil, fmt.Errorf("failed to write enable_subtitles field: %w", err)
		}
	}

	if subtitleStyleTemplate != "" {
		if err := writer.WriteField("subtitle_style_template", subtitleStyleTemplate); err != nil {
			return nil, fmt.Errorf("failed to write subtitle_style_template field: %w", err)
		}
	}

	writer.Close()

	// Create request
	url := c.baseURL + "/api/v1/pipeline/upload-and-process"
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send request
	log.Printf("Uploading file to: %s", url)
	uploadResp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send upload request: %w", err)
	}
	defer uploadResp.Body.Close()

	respBody, err := io.ReadAll(uploadResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if uploadResp.StatusCode >= 400 {
		log.Printf("Upload request failed with status %d: %s", uploadResp.StatusCode, string(respBody))
		return nil, fmt.Errorf("upload failed with status %d: %s", uploadResp.StatusCode, string(respBody))
	}

	log.Printf("Upload successful (status %d)", uploadResp.StatusCode)
	return respBody, nil
}

// GetProgress gets the progress of a project
func (c *PPTNarratorClient) GetProgress(projectID string) ([]byte, error) {
	return c.ProxyRequest("GET", "/api/v1/pipeline/"+projectID+"/progress", nil)
}

// GetDownloadURL gets the download URL for a completed project
func (c *PPTNarratorClient) GetDownloadURL(projectID string) ([]byte, error) {
	return c.ProxyRequest("GET", "/api/v1/pipeline/"+projectID+"/download", nil)
}

// RetryProject retries a failed project
func (c *PPTNarratorClient) RetryProject(projectID string) ([]byte, error) {
	return c.ProxyRequest("POST", "/api/v1/pipeline/"+projectID+"/retry", nil)
}

// GetHTTPClient returns the HTTP client
func (c *PPTNarratorClient) GetHTTPClient() interface{} {
	return c.client
}

// GetBaseURL returns the base URL
func (c *PPTNarratorClient) GetBaseURL() string {
	return c.baseURL
}

// UploadFileToDStaff uploads a file to dstaff platform
func UploadFileToDStaff(config *DStaffConfig, authCtx *AuthContext, filePath string, targetPath string) (*FileUploadResponse, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// Read file content
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Encode file content to base64
	fileBase64 := base64.StdEncoding.EncodeToString(fileContent)

	// Prepare request payload
	payload := map[string]interface{}{
		"file":     fileBase64,
		"taskId":   authCtx.TaskID,
		"filePath": targetPath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileUploadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Determine content type
	contentType := "application/octet-stream"
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".py":
		contentType = "text/x-python"
	case ".txt":
		contentType = "text/plain"
	case ".sh":
		contentType = "text/x-shellscript"
	case ".json":
		contentType = "application/json"
	case ".html":
		contentType = "text/html"
	case ".pptx":
		contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
	case ".mp4":
		contentType = "video/mp4"
	case ".zip":
		contentType = "application/zip"
	}

	// Create successful response
	response := &FileUploadResponse{
		WorkType:    "mcp_tool",
		Compression: false,
		Status:      "success",
		Message:     fmt.Sprintf("文件上传成功！上传的文件路径：%s", targetPath),
		Attachments: []Attachment{
			{
				Path:          targetPath,
				Filename:      filepath.Base(targetPath),
				Type:          "file",
				ContentType:   contentType,
				ContentLength: int64(len(fileContent)),
			},
		},
	}

	log.Printf("File uploaded successfully to dstaff: %s", targetPath)
	return response, nil
}

// downloadFileFromDStaff downloads a file from dstaff platform for upload tool
func downloadFileFromDStaff(filePath, taskID, token string, config *DStaffConfig) (string, error) {
	if !config.Enabled {
		return "", fmt.Errorf("dstaff integration not enabled")
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   taskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	log.Printf("Downloading file from DStaff: taskId=%s, filePath=%s", taskID, filePath)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return "", fmt.Errorf("download failed: %v", errorResp)
		}
		return "", fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	filename := filepath.Base(filePath)
	if filename == "." || filename == "/" {
		filename = "downloaded_file"
	}

	// Generate unique filename to avoid conflicts
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("%s_%s_%s", taskID, timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("File downloaded successfully from DStaff: %s -> %s (size: %d bytes)", filePath, localFilePath, len(fileContent))
	return localFilePath, nil
}

// DownloadFileFromDStaff downloads a file from dstaff platform
func DownloadFileFromDStaff(config *DStaffConfig, authCtx *AuthContext, filePath string, savePath string) error {
	if !config.Enabled {
		return fmt.Errorf("dstaff integration not enabled")
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"taskId":   authCtx.TaskID,
		"filePath": filePath,
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", config.FileDownloadURL, bytes.NewReader(payloadJSON))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+authCtx.Token)
	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		respBody, _ := io.ReadAll(resp.Body)
		// Try to parse as JSON error response
		var errorResp map[string]interface{}
		if json.Unmarshal(respBody, &errorResp) == nil {
			return fmt.Errorf("download failed: %v", errorResp)
		}
		return fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	// Ensure save directory exists
	if dir := filepath.Dir(savePath); dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
	}

	// Save file
	if err := os.WriteFile(savePath, fileContent, 0644); err != nil {
		return fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("File downloaded successfully from dstaff: %s -> %s", filePath, savePath)
	return nil
}

// getServerBaseURL determines the base URL for the server based on the request
func getServerBaseURL(r *http.Request, defaultPort string) string {
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}

	// Check for X-Forwarded-Proto header (common in reverse proxy setups)
	if proto := r.Header.Get("X-Forwarded-Proto"); proto != "" {
		scheme = proto
	}

	host := r.Host
	if host == "" {
		// Fallback to localhost with default port
		host = "localhost" + defaultPort
	}

	return fmt.Sprintf("%s://%s", scheme, host)
}

// HTTPRequestLogger logs all HTTP requests with detailed information
func HTTPRequestLogger(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Log basic request info
		log.Printf("=== HTTP Request ===")
		log.Printf("Method: %s", r.Method)
		log.Printf("Path: %s", r.URL.Path)
		log.Printf("Full URL: %s", r.URL.String())
		log.Printf("Remote Addr: %s", r.RemoteAddr)

		// Log headers
		log.Printf("Headers:")
		for name, values := range r.Header {
			for _, value := range values {
				// Mask sensitive headers
				if strings.ToLower(name) == "authorization" && len(value) > 20 {
					log.Printf("  %s: %s...%s", name, value[:10], value[len(value)-10:])
				} else {
					log.Printf("  %s: %s", name, value)
				}
			}
		}

		// Read and log request body if present
		if r.Body != nil && r.ContentLength != 0 {
			bodyBytes, err := io.ReadAll(r.Body)
			if err != nil {
				log.Printf("Error reading request body: %v", err)
			} else {
				// Restore the body for the next handler
				r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// Log the body content
				contentType := r.Header.Get("Content-Type")
				if strings.Contains(contentType, "application/json") {
					// Pretty print JSON
					var jsonData interface{}
					if err := json.Unmarshal(bodyBytes, &jsonData); err == nil {
						prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
						log.Printf("Request Body (JSON):\n%s", string(prettyJSON))
					} else {
						log.Printf("Request Body (Raw): %s", string(bodyBytes))
					}
				} else if strings.Contains(contentType, "multipart/form-data") {
					log.Printf("Request Body: [multipart/form-data - %d bytes]", len(bodyBytes))
					// Don't log multipart data as it contains binary content
				} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
					log.Printf("Request Body (Form): %s", string(bodyBytes))
				} else {
					// Log other content types as raw text (truncated if too long)
					bodyStr := string(bodyBytes)
					if len(bodyStr) > 1000 {
						log.Printf("Request Body (truncated): %s...", bodyStr[:1000])
					} else {
						log.Printf("Request Body: %s", bodyStr)
					}
				}
			}
		} else {
			log.Printf("Request Body: [empty]")
		}
		log.Printf("===================")

		// Call the next handler
		next.ServeHTTP(w, r)
	})
}

func main() {
	log.Println("🚀🚀🚀 [DEBUG] MAIN FUNCTION STARTED - CODE CHANGES ARE ACTIVE! 🚀🚀🚀")
	log.Println("Initializing MCP Pipeline Server...")

	// Get ppt-narrator service URL from environment variable
	pptNarratorURL := os.Getenv("PPT_NARRATOR_URL")
	if pptNarratorURL == "" {
		pptNarratorURL = "http://localhost:38080" // Default URL
	}

	// Create MCP server wrapper with all initialization
	wrapper := NewMCPServerWrapper(pptNarratorURL)

	// Create authenticated MCP HTTP server (SSE)
	sseServer := createAuthenticatedMCPServer(wrapper.mcpServer, wrapper.accessKey, wrapper.toolHandlers)

	// Create streamable HTTP server with authentication
	streamableHTTPServer := createAuthenticatedStreamableHTTPServer(wrapper.mcpServer, wrapper.accessKey, wrapper.toolHandlers)

	log.Println("Starting MCP Pipeline Server on :48080 (SSE)")
	log.Println("Starting Streamable HTTP Server on :48081")
	log.Println("PPT Narrator URL:", pptNarratorURL)

	// Log authentication configuration
	if wrapper.dstaffConfig.Enabled && wrapper.dstaffConfig.UseOfficialAuth {
		log.Println("Authentication: DStaff official auth enabled")
		log.Println("Token validation: Bearer token required for all requests")
	} else if wrapper.accessKey != "" {
		log.Println("Authentication: Legacy access key enabled")
		log.Println("Access Key: Required for /sse endpoint only (set via MCP_ACCESS_KEY)")
		log.Println("SSE Usage: Connect to /sse with ?key=YOUR_ACCESS_KEY")
		log.Println("Other endpoints: No authentication required")
	} else {
		log.Println("Authentication: Disabled - all endpoints accessible without authentication")
	}

	// Log request logging configuration
	log.Println("HTTP Request Logging: Enabled - all HTTP requests will be logged with detailed information")

	// Log debug configuration (for MCP tool calls)
	if wrapper.debugConfig.Enabled {
		log.Println("MCP Tool Debug: Enabled - MCP tool calls will have additional debug output")
	} else {
		log.Println("MCP Tool Debug: Disabled - set MCP_DEBUG=true to enable additional MCP tool debug output")
	}

	log.Println("")
	log.Println("可用的MCP工具:")
	log.Println("  - upload_file: 上传PPT文件并开始生成解说视频")
	log.Println("    参数: name, file_url, user_requirements, enable_subtitles, subtitle_style_template, context")
	log.Println("    字幕样式: classic_yellow, professional_white, elegant_blue, bold_red, soft_green, large_text, minimal")
	log.Println("  - upload_and_monitor: 上传PPT文件并持续监控生成进度，直到完成后返回视频文件 ⭐ 新功能")
	log.Println("    参数: name, file_url, user_requirements, enable_subtitles, subtitle_style_template, context")
	log.Println("    特点: 自动监控进度，实时状态回报，完成后直接返回视频文件")
	log.Println("  - get_progress: 查询PPT解说视频生成任务的进度 (参数: ppt_id[可选], include_steps, context; 优先使用ppt_id，否则从task_id自动获取)")
	log.Println("  - get_download_url: 获取已完成的PPT解说视频的下载链接 (参数: ppt_id[可选], context; 优先使用ppt_id，否则从task_id自动获取)")
	log.Println("  - retry_project: 对项目进行重试，从指定阶段重新开始处理 (参数: ppt_id[可选], stage, user_requirements, context; 优先使用ppt_id，否则从task_id自动获取)")

	log.Println("")
	log.Println("服务器端点:")
	log.Println("  - SSE Server: http://localhost:48080 (Server-Sent Events)")
	log.Println("  - Streamable HTTP Server: http://localhost:48081 (Regular HTTP)")

	// Use WaitGroup to run both servers concurrently
	var wg sync.WaitGroup
	wg.Add(2)

	// Start SSE server on port 48080
	go func() {
		defer wg.Done()
		log.Println("SSE Server starting on :48080...")
		if err := sseServer.ListenAndServe(); err != nil {
			log.Printf("SSE Server error: %v", err)
		}
	}()

	// Start streamable HTTP server on port 48081
	go func() {
		defer wg.Done()
		log.Println("Streamable HTTP Server starting on :48081...")
		if err := streamableHTTPServer.ListenAndServe(); err != nil {
			log.Printf("Streamable HTTP Server error: %v", err)
		}
	}()

	// Wait for both servers
	wg.Wait()
}

// setupToolNotifications sets up progress and log notification sending for a tool call
func (w *MCPServerWrapper) setupToolNotifications(ctx context.Context) {
	// 设置当前请求的进度发送函数，使用当前的 context
	w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
		log.Printf("📊 Sending progress notification: %.0f/%.0f - %s",
			notification.Params.Progress,
			notification.Params.Total,
			notification.Params.Message)

		// 检查客户端会话是否已初始化
		session := server.ClientSessionFromContext(ctx)
		if session == nil {
			log.Printf("⚠️ No client session in context, using fallback logging")
			log.Printf("📊 Progress: %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)
			return nil
		}

		if !session.Initialized() {
			log.Printf("⚠️ Client session not initialized, forcing initialization...")
			session.Initialize()
			log.Printf("✅ Client session initialized successfully")
		}

		// 发送到当前客户端
		err := w.mcpServer.SendNotificationToClient(ctx, "notifications/progress", map[string]any{
			"progress":      notification.Params.Progress,
			"total":         notification.Params.Total,
			"progressToken": notification.Params.ProgressToken,
			"message":       notification.Params.Message,
		})

		if err != nil {
			log.Printf("❌ Failed to send progress notification: %v", err)
			// 使用日志作为后备
			log.Printf("📊 Progress (fallback): %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)
			return nil // 不返回错误，使用后备方案
		}

		log.Printf("✅ Progress notification sent to current client")
		return nil
	})

	// 设置当前请求的日志发送函数
	w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
		log.Printf("📝 Sending log notification: [%s] %s",
			string(notification.Params.Level),
			notification.Params.Data)

		// 检查客户端会话是否已初始化
		session := server.ClientSessionFromContext(ctx)
		if session == nil {
			log.Printf("⚠️ No client session in context, using fallback logging")
			log.Printf("📝 Log [%s]: %s",
				string(notification.Params.Level),
				notification.Params.Data)
			return nil
		}

		if !session.Initialized() {
			log.Printf("⚠️ Client session not initialized, forcing initialization...")
			session.Initialize()
			log.Printf("✅ Client session initialized successfully")
		}
		// 发送到当前客户端
		err := w.mcpServer.SendNotificationToClient(ctx, "notifications/message", map[string]any{
			"level":  string(notification.Params.Level),
			"logger": notification.Params.Logger,
			"data":   notification.Params.Data,
		})

		if err != nil {
			log.Printf("❌ Failed to send log notification: %v", err)
			// 使用日志作为后备
			log.Printf("📝 Log (fallback) [%s]: %s",
				string(notification.Params.Level),
				notification.Params.Data)
			return nil // 不返回错误，使用后备方案
		}

		log.Printf("✅ Log notification sent to current client")
		return nil
	})
}

// authenticatedMCPHandler handles authenticated MCP requests
func (w *MCPServerWrapper) authenticatedMCPHandler(rw http.ResponseWriter, r *http.Request) {
	// Create the base streamable HTTP server
	streamableServer := server.NewStreamableHTTPServer(w.mcpServer)

	// Add DStaff auth context if enabled
	ctx := r.Context()
	if w.dstaffConfig.Enabled {
		log.Printf("🌐 === DStaff HTTP API Request Processing ===")
		log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		log.Printf("🔍 Request Headers:")

		// Log relevant headers
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
		} else {
			log.Printf("   - Authorization: Not provided")
		}

		taskIDHeader := r.Header.Get("X-Task-ID")
		if taskIDHeader != "" {
			log.Printf("   - X-Task-ID: %s", taskIDHeader)
		} else {
			log.Printf("   - X-Task-ID: Not provided")
		}

		// Extract task_id from headers or query parameters
		taskID := taskIDHeader
		if taskID == "" {
			taskID = r.URL.Query().Get("task_id")
		}
		if taskID != "" {
			ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
			log.Printf("📋 Task ID extracted and added to context: %s", taskID)
		} else {
			log.Printf("⚠️ No task ID found in request")
		}

		// Add authorization token to context if available
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			ctx = context.WithValue(ctx, "authorization_token", token)
			log.Printf("🔑 Authorization token added to context")
		}

		r = r.WithContext(ctx)
	}

	// Forward authenticated requests to the streamable server with status code interceptor
	interceptor := &StatusCodeInterceptor{ResponseWriter: rw}
	streamableServer.ServeHTTP(interceptor, r)
}

// handleAPIHealth handles health check requests
func (w *MCPServerWrapper) handleAPIHealth(rw http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":    "healthy",
		"service":   "old-mcp-pipeline-server",
		"version":   "1.0.0",
		"timestamp": time.Now().Unix(),
	}

	w.writeAPIResponse(rw, http.StatusOK, response)
}

// handleAPIListTools handles tool listing requests
func (w *MCPServerWrapper) handleAPIListTools(rw http.ResponseWriter, r *http.Request) {
	tools := []map[string]interface{}{
		{
			"name":        "upload_file",
			"description": "上传PPT文件并开始生成解说视频",
			"parameters": map[string]interface{}{
				"name":                    "文件名称",
				"file_url":                "文件URL",
				"user_requirements":       "用户需求（可选）",
				"enable_subtitles":        "是否启用字幕（可选）",
				"subtitle_style_template": "字幕样式模板（可选）",
				"context":                 "上下文参数（可选）",
			},
		},
		{
			"name":        "upload_and_monitor",
			"description": "上传PPT文件并持续监控生成进度，直到完成后返回视频文件",
			"parameters": map[string]interface{}{
				"name":                    "文件名称",
				"file_url":                "文件URL",
				"user_requirements":       "用户需求（可选）",
				"enable_subtitles":        "是否启用字幕（可选）",
				"subtitle_style_template": "字幕样式模板（可选）",
				"context":                 "上下文参数（可选）",
			},
		},
		{
			"name":        "get_progress",
			"description": "查询PPT解说视频生成任务的进度",
			"parameters": map[string]interface{}{
				"ppt_id":        "项目ID（可选）",
				"include_steps": "是否包含详细步骤（可选）",
				"context":       "上下文参数（可选）",
			},
		},
	}

	w.writeAPIResponse(rw, http.StatusOK, map[string]interface{}{
		"tools": tools,
		"count": len(tools),
	})
}

// handleAPIToolCall handles tool execution requests
func (w *MCPServerWrapper) handleAPIToolCall(rw http.ResponseWriter, r *http.Request) {
	// Extract tool name from URL path
	pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/v1/tools/"), "/")
	if len(pathParts) == 0 || pathParts[0] == "" {
		w.writeAPIErrorResponse(rw, http.StatusBadRequest, "INVALID_TOOL", "Tool name is required", nil)
		return
	}

	toolName := pathParts[0]

	// Only allow POST requests for tool calls
	if r.Method != "POST" {
		w.writeAPIErrorResponse(rw, http.StatusMethodNotAllowed, "METHOD_NOT_ALLOWED", "Only POST method is allowed for tool calls", nil)
		return
	}

	// Parse request body
	var requestBody map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		w.writeAPIErrorResponse(rw, http.StatusBadRequest, "INVALID_JSON", "Invalid JSON in request body", nil)
		return
	}

	// Extract arguments from request body
	arguments, ok := requestBody["arguments"].(map[string]interface{})
	if !ok {
		arguments = make(map[string]interface{})
	}

	// Create MCP tool call request
	toolRequest := mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name:      toolName,
			Arguments: arguments,
		},
	}

	// Execute the tool based on tool name
	var result map[string]interface{}
	var err error

	switch toolName {
	case "upload_file":
		result, err = w.handleUploadFile(r.Context(), toolRequest, arguments)
	case "upload_and_monitor":
		result, err = w.handleUploadAndMonitor(r.Context(), toolRequest, arguments)
	case "get_progress":
		result, err = w.handleGetProgress(r.Context(), toolRequest, arguments)
	default:
		w.writeAPIErrorResponse(rw, http.StatusNotFound, "TOOL_NOT_FOUND", fmt.Sprintf("Tool '%s' not found", toolName), nil)
		return
	}

	if err != nil {
		w.writeAPIErrorResponse(rw, http.StatusInternalServerError, "TOOL_EXECUTION_ERROR", err.Error(), nil)
		return
	}

	w.writeAPIResponse(rw, http.StatusOK, result)
}

// StatusCodeInterceptor wraps an http.ResponseWriter to intercept and modify status codes
type StatusCodeInterceptor struct {
	http.ResponseWriter
	statusCode int
}

func (w *StatusCodeInterceptor) WriteHeader(code int) {
	// Convert 202 Accepted to 200 OK
	if code == http.StatusAccepted {
		w.statusCode = http.StatusOK
		w.ResponseWriter.WriteHeader(http.StatusOK)
	} else {
		w.statusCode = code
		w.ResponseWriter.WriteHeader(code)
	}
}

func (w *StatusCodeInterceptor) Write(data []byte) (int, error) {
	// If WriteHeader hasn't been called yet, call it with 200
	if w.statusCode == 0 {
		w.WriteHeader(http.StatusOK)
	}
	return w.ResponseWriter.Write(data)
}

// createAuthenticatedMCPServer creates an HTTP server with authentication and proxy routes
func createAuthenticatedMCPServer(mcpServer *server.MCPServer, accessKey string, toolHandlers *handlers.ToolHandlers) *http.Server {
	// Create the base MCP HTTP handler
	mcpHandler := server.NewSSEServer(mcpServer)

	// Create main router
	mux := http.NewServeMux()

	// Add proxy download routes
	mux.HandleFunc("/proxy/download/", func(w http.ResponseWriter, r *http.Request) {
		handleProxyDownload(w, r, toolHandlers)
	})

	// Add MCP routes with authentication middleware
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Get server base URL and add to context
		serverBaseURL := getServerBaseURL(r, ":48080")
		ctx := context.WithValue(r.Context(), "server_host", serverBaseURL)

		// Check if dstaff official auth is enabled
		dstaffConfig := convertDStaffConfig(toolHandlers.GetDStaffConfig())
		if dstaffConfig.Enabled && dstaffConfig.UseOfficialAuth {
			// Extract Bearer token from Authorization header
			authHeader := r.Header.Get("Authorization")
			log.Printf("DStaff Auth Check: %s %s from %s, Auth header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)

			if authHeader == "" {
				log.Printf("Access denied: No Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
				http.Error(w, `{"error": "Authorization header required"}`, http.StatusUnauthorized)
				return
			}

			if !strings.HasPrefix(authHeader, "Bearer ") {
				log.Printf("Access denied: Invalid Authorization header format for %s %s from %s, header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)
				http.Error(w, `{"error": "Bearer token required"}`, http.StatusUnauthorized)
				return
			}

			token := strings.TrimPrefix(authHeader, "Bearer ")
			log.Printf("Extracted token for validation: '%s' (length: %d)", token, len(token))

			if !ValidateTokenWithDStaff(dstaffConfig, token) {
				log.Printf("Access denied: Invalid token '%s' for %s %s from %s", token, r.Method, r.URL.Path, r.RemoteAddr)
				http.Error(w, `{"error": "Invalid token"}`, http.StatusForbidden)
				return
			}

			log.Printf("Access granted (dstaff auth): %s %s from %s, token: '%s'", r.Method, r.URL.Path, r.RemoteAddr, token)

			// Add token to request context for use in MCP tools
			ctx = context.WithValue(ctx, "authorization_token", token)
			rWithContext := r.WithContext(ctx)

			// Use status code interceptor to convert 202 to 200
			interceptor := &StatusCodeInterceptor{ResponseWriter: w}
			mcpHandler.ServeHTTP(interceptor, rWithContext)
			return
		}

		// Legacy authentication logic for /sse endpoint only
		if r.URL.Path != "/sse" {
			log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			rWithContext := r.WithContext(ctx)
			// Use status code interceptor to convert 202 to 200
			interceptor := &StatusCodeInterceptor{ResponseWriter: w}
			mcpHandler.ServeHTTP(interceptor, rWithContext)
			return
		}

		// For /sse endpoint, check if access key is configured
		if accessKey == "" {
			log.Printf("Access granted (no auth configured): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			rWithContext := r.WithContext(ctx)
			// Use status code interceptor to convert 202 to 200
			interceptor := &StatusCodeInterceptor{ResponseWriter: w}
			mcpHandler.ServeHTTP(interceptor, rWithContext)
			return
		}

		// Check for key parameter in query string for /sse
		providedKey := r.URL.Query().Get("key")
		log.Printf("Legacy Auth Check: %s %s from %s, query key: '%s', expected key: '%s'", r.Method, r.URL.Path, r.RemoteAddr, providedKey, accessKey)

		if providedKey == "" {
			log.Printf("Access denied: No key provided for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			http.Error(w, `{"error": "Access key required for SSE endpoint. Use ?key=YOUR_ACCESS_KEY"}`, http.StatusUnauthorized)
			return
		}

		// Validate the key for /sse
		if providedKey != accessKey {
			log.Printf("Access denied: Invalid key '%s' (expected: '%s') for %s %s from %s", providedKey, accessKey, r.Method, r.URL.Path, r.RemoteAddr)
			http.Error(w, `{"error": "Invalid access key"}`, http.StatusForbidden)
			return
		}

		// Key is valid for /sse, proceed with the request
		log.Printf("Access granted: Valid key '%s' for %s %s from %s", providedKey, r.Method, r.URL.Path, r.RemoteAddr)
		rWithContext := r.WithContext(ctx)
		// Use status code interceptor to convert 202 to 200
		interceptor := &StatusCodeInterceptor{ResponseWriter: w}
		mcpHandler.ServeHTTP(interceptor, rWithContext)
	})

	// Apply HTTP request logging middleware to all requests
	finalHandler := HTTPRequestLogger(mux)

	// Create HTTP server
	return &http.Server{
		Addr:    ":48080",
		Handler: finalHandler,
	}
}

// handleProxyDownload handles proxy download requests
func handleProxyDownload(w http.ResponseWriter, r *http.Request, toolHandlers *handlers.ToolHandlers) {

	// Parse URL path: /proxy/download/{projectId}/{downloadType}
	path := strings.TrimPrefix(r.URL.Path, "/proxy/download/")
	parts := strings.Split(path, "/")

	if len(parts) != 2 {
		http.Error(w, "Invalid download URL format. Expected: /proxy/download/{projectId}/{downloadType}", http.StatusBadRequest)
		return
	}

	projectID := parts[0]
	downloadType := parts[1]

	log.Printf("Proxy download request: project=%s, type=%s", projectID, downloadType)

	// Map download types to actual API paths
	var apiPath string
	switch downloadType {
	case "video":
		apiPath = fmt.Sprintf("/api/v1/download/%s/video", projectID)
	case "audio":
		apiPath = fmt.Sprintf("/api/v1/download/%s/audio", projectID)
	case "screenshots":
		apiPath = fmt.Sprintf("/api/v1/download/%s/screenshots", projectID)
	case "narration":
		apiPath = fmt.Sprintf("/api/v1/download/%s/narration", projectID)
	case "all":
		apiPath = fmt.Sprintf("/api/v1/download/%s/all", projectID)
	default:
		http.Error(w, "Invalid download type. Supported types: video, audio, screenshots, narration, all", http.StatusBadRequest)
		return
	}

	// Create request to ppt-narrator service
	baseURL, _ := toolHandlers.GetBaseURL()
	url := baseURL + apiPath
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create proxy request: %v", err)
		http.Error(w, "Failed to create download request", http.StatusInternalServerError)
		return
	}

	// Forward the request
	httpClient, _ := toolHandlers.GetHTTPClient()
	client := httpClient.(*http.Client)
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to proxy download request: %v", err)
		http.Error(w, "Failed to download file", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Check if dstaff is enabled and we should upload to dstaff instead of streaming
	dstaffConfig := convertDStaffConfig(toolHandlers.GetDStaffConfig())
	if dstaffConfig.Enabled && dstaffConfig.UseOfficialAuth {
		// Extract token and task_id from request
		authHeader := r.Header.Get("Authorization")
		taskID := r.URL.Query().Get("task_id") // Get task_id from query parameter

		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("Missing or invalid Authorization header for dstaff upload")
			http.Error(w, "Authorization header required for dstaff upload", http.StatusUnauthorized)
			return
		}

		if taskID == "" {
			log.Printf("Missing task_id parameter for dstaff upload")
			http.Error(w, "task_id parameter required for dstaff upload", http.StatusBadRequest)
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")

		// Handle dstaff upload
		handleProxyDownloadWithDStaffUpload(w, r, resp, projectID, downloadType, token, taskID, dstaffConfig)
		return
	}

	// Original behavior: stream directly to client
	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Set status code
	w.WriteHeader(resp.StatusCode)

	// Stream the response body
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		log.Printf("Failed to stream download response: %v", err)
	}

	log.Printf("Proxy download completed: project=%s, type=%s, status=%d", projectID, downloadType, resp.StatusCode)
}

// handleProxyDownloadWithDStaffUpload handles proxy download and uploads to dstaff platform
func handleProxyDownloadWithDStaffUpload(w http.ResponseWriter, r *http.Request, resp *http.Response, projectID, downloadType, token, taskID string, config *DStaffConfig) {
	// TODO: Read and process file content for DStaff upload
	// For now, just return not implemented
	log.Printf("DStaff upload not implemented for project=%s, type=%s", projectID, downloadType)

	// Check if the download was successful
	if resp.StatusCode != 200 {
		log.Printf("Download failed with status %d", resp.StatusCode)
		http.Error(w, fmt.Sprintf("Download failed with status %d", resp.StatusCode), resp.StatusCode)
		return
	}

	// TODO: Implement DStaff file upload
	log.Printf("DStaff file upload not implemented yet: project=%s, type=%s", projectID, downloadType)
	http.Error(w, "DStaff file upload not implemented yet", http.StatusNotImplemented)
}

// createAuthenticatedStreamableHTTPServer creates a streamable HTTP server with authentication
func createAuthenticatedStreamableHTTPServer(mcpServer *server.MCPServer, accessKey string, toolHandlers *handlers.ToolHandlers) *http.Server {
	// Create main router
	mux := http.NewServeMux()

	// Add MCP streamable HTTP handler with DStaff auth context support
	mux.HandleFunc("/mcp", func(rw http.ResponseWriter, r *http.Request) {
		// Add DStaff auth context if enabled
		ctx := r.Context()
		dstaffConfig := convertDStaffConfig(toolHandlers.GetDStaffConfig())
		if dstaffConfig.Enabled {
			log.Printf("🌐 === DStaff HTTP API Request Processing ===")
			log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			log.Printf("🔍 Request Headers:")

			// Log relevant headers
			authHeader := r.Header.Get("Authorization")
			if authHeader != "" {
				log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
			} else {
				log.Printf("   - Authorization: Not provided")
			}

			taskIDHeader := r.Header.Get("X-Task-ID")
			if taskIDHeader != "" {
				log.Printf("   - X-Task-ID: %s", taskIDHeader)
			} else {
				log.Printf("   - X-Task-ID: Not provided")
			}

			contentType := r.Header.Get("Content-Type")
			if contentType != "" {
				log.Printf("   - Content-Type: %s", contentType)
			}

			// Log query parameters
			log.Printf("🔍 Query Parameters:")
			if len(r.URL.Query()) > 0 {
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}
			} else {
				log.Printf("   - No query parameters")
			}

			// Extract token from Authorization header and add to context
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				ctx = context.WithValue(ctx, "authorization_token", token)
				log.Printf("🔐 Bearer token extracted and added to context")
			}

			// Extract task_id from headers or query parameters
			taskID := taskIDHeader
			if taskID == "" {
				taskID = r.URL.Query().Get("task_id")
			}
			if taskID != "" {
				ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
				log.Printf("📋 Task ID extracted and added to context: %s", taskID)
			} else {
				log.Printf("⚠️ No task ID found in request")
			}

			log.Printf("✅ DStaff HTTP API request processing completed")
		}

		// Update request with new context
		r = r.WithContext(ctx)

		// Handle with streamable server
		streamableServer := server.NewStreamableHTTPServer(mcpServer)
		streamableServer.ServeHTTP(rw, r)
	})

	// Add proxy download routes with authentication
	mux.HandleFunc("/proxy/download/", func(w http.ResponseWriter, r *http.Request) {
		// Check authentication for proxy downloads
		dstaffConfig := convertDStaffConfig(toolHandlers.GetDStaffConfig())
		if !authenticateStreamableHTTPRequest(r, dstaffConfig, accessKey) {
			log.Printf("Access denied for proxy download: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		handleProxyDownload(w, r, toolHandlers)
	})

	// Add authenticated wrapper for all other requests
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.NotFound(w, r)
	})

	// Add health check
	mux.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"ppt-narrator-old-mcp","version":"1.0.0"}`))
	})

	// Apply HTTP request logging middleware to all requests
	finalHandler := HTTPRequestLogger(mux)

	// Create HTTP server
	return &http.Server{
		Addr:    ":48081",
		Handler: finalHandler,
	}
}

// maskTokenForLog masks a token for safe logging
func maskTokenForLog(token string) string {
	if token == "" {
		return "Not set"
	}
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskAuthHeaderForLog masks an Authorization header for safe logging
func maskAuthHeaderForLog(authHeader string) string {
	if authHeader == "" {
		return "Not provided"
	}

	if strings.HasPrefix(authHeader, "Bearer ") {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		return "Bearer " + maskTokenForLog(token)
	}

	// For other auth types, just show the type
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) == 2 {
		return parts[0] + " ***"
	}

	return "***"
}

// authenticateStreamableHTTPRequest handles authentication for streamable HTTP requests
func authenticateStreamableHTTPRequest(r *http.Request, dstaffConfig *DStaffConfig, accessKey string) bool {
	// Check if dstaff official auth is enabled
	if dstaffConfig.Enabled && dstaffConfig.UseOfficialAuth {
		// Extract Bearer token from Authorization header
		authHeader := r.Header.Get("Authorization")
		log.Printf("Streamable DStaff Auth Check: %s %s from %s, Auth header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)

		if authHeader == "" {
			log.Printf("Access denied: No Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		if !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("Access denied: Invalid Authorization header format for %s %s from %s, header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)
			return false
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		log.Printf("Streamable extracted token for validation: '%s' (length: %d)", token, len(token))

		if !ValidateTokenWithDStaff(dstaffConfig, token) {
			log.Printf("Access denied: Invalid token '%s' for %s %s from %s", token, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted (streamable dstaff auth): %s %s from %s, token: '%s'", r.Method, r.URL.Path, r.RemoteAddr, token)
		return true
	}

	// Legacy authentication logic for tool endpoints
	if accessKey != "" {
		// Check for key parameter in query string or Authorization header
		providedKey := r.URL.Query().Get("key")
		authHeader := r.Header.Get("Authorization")

		log.Printf("Streamable Legacy Auth Check: %s %s from %s, query key: '%s', auth header: '%s', expected key: '%s'",
			r.Method, r.URL.Path, r.RemoteAddr, providedKey, authHeader, accessKey)

		if providedKey == "" {
			// Try Authorization header as fallback
			if strings.HasPrefix(authHeader, "Bearer ") {
				providedKey = strings.TrimPrefix(authHeader, "Bearer ")
				log.Printf("Using token from Authorization header as key: '%s'", providedKey)
			}
		}

		if providedKey == "" {
			log.Printf("Access denied: No key provided for %s %s from %s (query: '%s', auth: '%s')",
				r.Method, r.URL.Path, r.RemoteAddr, r.URL.Query().Get("key"), authHeader)
			return false
		}

		// Validate the key
		if providedKey != accessKey {
			log.Printf("Access denied: Invalid key '%s' (expected: '%s') for %s %s from %s",
				providedKey, accessKey, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted: Valid key '%s' for %s %s from %s", providedKey, r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// No authentication configured
	log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	return true
}

// API Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Code    string      `json:"code,omitempty"`
	Details interface{} `json:"details,omitempty"`
}
