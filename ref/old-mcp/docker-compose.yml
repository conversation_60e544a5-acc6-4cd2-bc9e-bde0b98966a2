version: '3.8'

services:
  mcp-pipeline-server:
    build: .
    ports:
      - "48080:48080"
    environment:
      - PORT=48080
      - PPT_NARRATOR_URL=http://ppt-narrator:8080
      - MCP_ACCESS_KEY=test123
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  work_data:
  db_data:
