# PPT Narrator MCP - 进度监控功能

## 概述

基于 smart-card-mcp 的实现，为 ppt-narrator MCP 服务器添加了状态回报功能。新增了一个 `upload_and_monitor` 工具，实现调用后持续回报状态，直到生成成功后直接返回文件。

## 新增功能

### 1. Progress Reporter 模块

创建了 `internal/progress/reporter.go` 模块，提供：

- **进度报告器 (Reporter)**：管理进度通知和日志消息
- **步骤报告**：`ReportStep(step, message)` 报告当前步骤
- **延迟报告**：`ReportStepWithDelay()` 用于长时间运行的步骤
- **完成报告**：`Complete(message)` 标记操作完成
- **错误报告**：`Error(message)` 报告错误状态
- **日志消息**：`SendLogMessage(level, message)` 发送日志通知

### 2. upload_and_monitor 工具

新增的核心工具，功能包括：

#### 参数
- `name`: PPT文件名称 (必需)
- `file_url`: PPT文件的下载URL地址 (必需)
- `user_requirements`: 用户需求描述 (可选)
- `enable_subtitles`: 是否启用字幕 (可选，默认false)
- `subtitle_style_template`: 字幕样式模板 (可选)
- `Context`: 上下文参数，包含task_id等信息 (可选)

#### 工作流程
1. **Step 1**: 上传PPT文件并启动处理
2. **Step 2**: 监控截图生成阶段
3. **Step 3**: 监控讲稿生成阶段  
4. **Step 4**: 监控音频生成阶段
5. **Step 5**: 监控视频合成阶段，完成后返回结果

#### 特点
- **实时监控**：每秒查询一次进度状态
- **状态回报**：通过MCP进度通知实时更新状态
- **自动完成**：生成完成后自动获取并返回视频文件
- **错误处理**：自动检测和报告处理错误
- **DStaff集成**：支持自动上传结果到DStaff平台
- **超时保护**：最多监控3分钟，防止无限等待

### 3. 监控函数

#### monitorPipelineProgress()
- 持续监控管道进度
- 映射处理阶段到进度步骤
- 实时发送进度通知
- 处理完成和错误状态

#### getCompletedVideoResult()
- 获取完成后的视频文件信息
- 生成下载链接
- 支持DStaff平台上传

#### handleCompletedVideoWithDStaffUpload()
- 处理DStaff平台的文件上传
- 下载视频文件并上传到指定位置
- 返回上传结果

## 使用方法

### 基本用法
```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_and_monitor",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说视频",
      "enable_subtitles": true,
      "subtitle_style_template": "professional_white"
    }
  }
}
```

### 带进度令牌的用法
```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_and_monitor",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "file://presentation.pptx",
      "Context": {
        "task_id": "task_123456"
      }
    },
    "_meta": {
      "progressToken": "progress_token_123"
    }
  }
}
```

## 进度通知格式

工具会发送以下类型的通知：

### 进度通知
```json
{
  "method": "notifications/progress",
  "params": {
    "progressToken": "progress_token_123",
    "progress": 2.0,
    "total": 5.0,
    "message": "正在生成PPT截图..."
  }
}
```

### 日志通知
```json
{
  "method": "notifications/message",
  "params": {
    "level": "info",
    "logger": "ppt-narrator-old-mcp",
    "data": "📊 Step 2/5: 正在生成PPT截图..."
  }
}
```

## 阶段映射

| 处理阶段 | 步骤编号 | 描述 |
|---------|---------|------|
| upload | 1 | 上传PPT文件并启动处理 |
| screenshot | 2 | 生成PPT截图 |
| narration | 3 | 生成解说讲稿 |
| audio | 4 | 生成解说音频 |
| video | 5 | 合成最终视频 |

## 错误处理

- **上传失败**：报告文件上传或处理启动错误
- **进度查询失败**：自动重试，记录警告
- **处理错误**：检测pipeline错误状态并报告
- **超时处理**：3分钟超时保护，避免无限等待
- **DStaff错误**：处理文件下载和上传错误

## 与现有工具的区别

| 功能 | upload_file | upload_and_monitor |
|------|-------------|-------------------|
| 上传文件 | ✅ | ✅ |
| 启动处理 | ✅ | ✅ |
| 进度监控 | ❌ | ✅ |
| 状态回报 | ❌ | ✅ |
| 自动完成 | ❌ | ✅ |
| 返回文件 | ❌ | ✅ |

## 配置要求

- MCP服务器需要支持进度通知
- 客户端需要处理progress和logging通知
- 可选：DStaff平台集成配置

## 日志输出

服务器启动时会显示新工具信息：
```
可用的MCP工具:
  - upload_and_monitor: 上传PPT文件并持续监控生成进度，直到完成后返回视频文件 ⭐ 新功能
    参数: name, file_url, user_requirements, enable_subtitles, subtitle_style_template, context
    特点: 自动监控进度，实时状态回报，完成后直接返回视频文件
```

## 技术实现

- 基于 smart-card-mcp 的 progress reporter 设计
- 使用 MCP 标准的进度通知协议
- 支持异步处理和实时状态更新
- 集成现有的认证和文件处理机制
