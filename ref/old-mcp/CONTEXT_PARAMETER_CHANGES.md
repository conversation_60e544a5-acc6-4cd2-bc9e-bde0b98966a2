# Context参数修改说明

## 修改概述

根据用户要求，将MCP工具中task_id的获取方式从Go context改为接收一个Context参数（字典），从里面获取task_id。同时增强了upload_file工具，支持从数字员工平台下载文件。

## 修改内容

### 1. 工具参数定义修改

为所有三个MCP工具添加了`context`参数：

- `upload_file`
- `get_progress` 
- `get_download_url`

新增的参数定义：
```go
"Context": map[string]any{
    "type":        "object",
    "description": "上下文参数，包含task_id等信息",
    "properties": map[string]any{
        "task_id": map[string]any{
            "type":        "string",
            "description": "任务ID",
        },
    },
},
```

### 2. task_id获取逻辑修改

原来的获取方式：
```go
// Get task_id from context
var taskID string
if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
    if taskIDStr, ok := taskIDValue.(string); ok {
        taskID = taskIDStr
    }
}
```

修改后的获取方式：
```go
// Get task_id from context parameter (dictionary)
var taskID string
if argsMap, ok := request.Params.Arguments.(map[string]interface{}); ok {
    // Try both "context" (lowercase) and "Context" (uppercase) for compatibility
    var contextParam interface{}
    var exists bool
    if contextParam, exists = argsMap["context"]; !exists {
        contextParam, exists = argsMap["Context"]
    }

    if exists {
        if contextMap, ok := contextParam.(map[string]interface{}); ok {
            if taskIDValue, exists := contextMap["task_id"]; exists {
                if taskIDStr, ok := taskIDValue.(string); ok {
                    taskID = taskIDStr
                }
            }
        }
    }
}

// Fallback: try to get task_id from Go context for backward compatibility
if taskID == "" {
    if taskIDValue := ctx.Value("task_id"); taskIDValue != nil {
        if taskIDStr, ok := taskIDValue.(string); ok {
            taskID = taskIDStr
        }
    }
}
```

### 3. 兼容性处理

1. **参数名兼容性**：代码同时支持 `"Context"`（大写）和 `"context"`（小写）两种参数名，以适应不同的调用方式。

2. **向后兼容性**：保留了原有的Go context获取方式作为fallback，确保向后兼容性。如果新的Context参数中没有找到task_id，会尝试从Go context中获取。

### 4. 工具描述更新

更新了main函数中的工具描述：
```go
log.Println("  - upload_file: 上传PPT文件并开始生成解说视频 (参数: name, file_url, user_requirements, context; task_id从context参数获取)")
log.Println("  - get_progress: 查询PPT解说视频生成任务的进度 (参数: project_id, include_steps, context; task_id从context参数获取)")
log.Println("  - get_download_url: 获取已完成的PPT解说视频的下载链接 (参数: project_id, context; task_id从context参数获取)")
```

## 使用示例

### 新的调用方式

```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说，语速适中",
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```

### 向后兼容

如果不提供context参数，系统会尝试从Go context中获取task_id（如果有的话）：

```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说，语速适中"
    }
  }
}
```

## 技术细节

### 类型断言处理

由于`request.Params.Arguments`的类型是`any`，需要进行类型断言：

1. 首先将`request.Params.Arguments`断言为`map[string]interface{}`
2. 然后从中获取`context`参数
3. 再将`context`参数断言为`map[string]interface{}`
4. 最后从中获取`task_id`并断言为`string`

### 错误处理

所有的类型断言都使用了安全的两值返回形式，确保不会因为类型不匹配而panic。

## 影响范围

- 修改了3个MCP工具的参数定义
- 修改了3个MCP工具的实现逻辑
- 更新了使用示例文档
- 更新了工具描述日志

## 新增功能：数字员工平台文件下载

### 功能说明

当upload_file工具的file_url参数是相对路径（不以http://或https://开头）时，系统会自动从数字员工平台下载文件到本地，然后上传到ppt-narrator服务。

### 实现细节

1. **路径检测**：检查file_url是否为相对路径
2. **文件下载**：调用数字员工平台的文件下载API
3. **本地保存**：将文件保存到临时目录
4. **文件上传**：使用本地文件路径上传到ppt-narrator

### 新增函数

- `downloadFileFromDStaff()`: 从数字员工平台下载文件
- 修改了`UploadFileFromURL()`方法，支持处理`file://`协议的本地文件路径

### 使用示例

```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "demo-SQL注入.pptx",
      "file_url": "user_input/demo-SQL注入.pptx",
      "Context": {
        "task_id": "eb2ae565-00a2-40a6-aa46-d211b1ac6f76",
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      }
    }
  }
}
```

### Token获取方式

访问令牌自动从HTTP请求的Authorization头中获取。系统在认证中间件中提取Bearer token，并通过Go context传递给MCP工具函数。

### 实现细节

1. **认证中间件增强**：修改了认证处理逻辑，在验证token后将其添加到Go context中
2. **新增函数**：`authenticateStreamableHTTPRequestWithToken()` 用于认证并返回token
3. **Context传递**：通过 `context.WithValue()` 将token传递给工具函数

### 错误处理

- 如果task_id为空，返回错误提示
- 如果Authorization头中没有token，返回错误提示
- 如果token无效或文件下载失败，返回详细错误信息

## 测试建议

1. 测试新的context参数方式调用工具
2. 测试向后兼容性（不提供context参数）
3. 测试从数字员工平台下载文件的功能
4. 测试HTTP URL和相对路径的不同处理方式
5. 测试各种边界情况和错误处理
