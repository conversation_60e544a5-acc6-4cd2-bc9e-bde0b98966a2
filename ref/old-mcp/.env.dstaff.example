# MCP Pipeline Server - DStaff平台集成配置示例
# 复制此文件为 .env 并填入实际值

# ===================
# 基础配置
# ===================
PPT_NARRATOR_URL=http://localhost:38080

# ===================
# 传统鉴权配置（可选）
# ===================
# 如果不使用DStaff官方鉴权，可以使用传统的访问密钥
MCP_ACCESS_KEY=your_secret_key_here

# ===================
# DStaff平台集成配置
# ===================
# 是否启用DStaff平台集成
DSTAFF_ENABLED=true

# DStaff平台端点URL
DSTAFF_ENDPOINT_URL=http://*********:8800

# 是否使用DStaff官方鉴权
# true: 使用Bearer Token验证，所有请求都需要有效token
# false: 使用自定义鉴权或无鉴权
DSTAFF_USE_OFFICIAL_AUTH=true

# ===================
# 调试配置
# ===================
# 是否启用MCP调试模式
# true: 输出所有MCP请求的详细信息（包括请求体）
# false: 只输出基本日志
MCP_DEBUG=true

# ===================
# 使用说明
# ===================
# 1. 如果DSTAFF_ENABLED=false，则只使用传统的MCP_ACCESS_KEY鉴权
# 2. 如果DSTAFF_ENABLED=true且DSTAFF_USE_OFFICIAL_AUTH=true，则使用DStaff官方鉴权
#    - 所有请求都需要在Authorization头中提供Bearer Token
#    - Token会通过DStaff的/api/v1/mcp/validateToken接口验证
# 3. 如果DSTAFF_ENABLED=true且DSTAFF_USE_OFFICIAL_AUTH=false，则启用文件上传下载功能但不使用官方鉴权

# ===================
# 可用的MCP工具
# ===================
# 基础工具（始终可用）：
# - upload_file: 上传PPT文件并开始生成解说视频
# - get_progress: 查询PPT解说视频生成任务的进度
# - get_download_url: 获取已完成的PPT解说视频的下载链接

# DStaff集成工具（仅在DSTAFF_ENABLED=true时可用）：
# - upload_file_to_dstaff: 上传文件到DStaff平台
# - download_file_from_dstaff: 从DStaff平台下载文件

# ===================
# 文件路径规范
# ===================
# 上传到DStaff平台的文件路径必须以"mcps_upload/"开头
# 示例：
# - mcps_upload/test.txt
# - mcps_upload/videos/output.mp4
# - mcps_upload/documents/report.pdf
