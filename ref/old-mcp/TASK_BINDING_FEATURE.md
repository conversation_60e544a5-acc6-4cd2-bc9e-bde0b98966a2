# 任务绑定功能实现说明

## 功能概述

实现了任务绑定功能，解决MCP调用时AI上下文传递问题。AI在使用MCP工具时不再需要记住project_id，只需要传递task_id就能自动获取对应的project_id。

## 核心功能

### 1. 自动绑定创建
- 生成PPT时自动创建绑定: `task_id ↔ project_id`
- 在`upload_file`工具成功创建项目后自动保存绑定关系
- 数据持久化存储在`./data/task_bindings.json`

### 2. 智能参数解析
- **优先使用直接传递的ppt_id**：如果工具调用时直接提供了`ppt_id`参数，优先使用
- **自动从绑定获取**：如果没有提供`ppt_id`，从`task_id`自动获取对应的`project_id`
- **完全向后兼容**：保持原有的参数传递方式不变

### 3. 工具参数更新
- `ppt_id`参数改为可选（原来的`project_id`重命名为`ppt_id`）
- 所有查询工具支持智能参数解析：
  - `get_progress`
  - `get_download_url` 
  - `retry_project`

## 技术实现

### 1. 任务绑定管理模块
**文件**: `mcp/internal/binding/task_binding.go`

**核心结构**:
```go
type TaskBinding struct {
    TaskID    string    `json:"task_id"`
    ProjectID string    `json:"project_id"`
    CreatedAt time.Time `json:"created_at"`
}

type TaskBindingManager struct {
    bindings map[string]*TaskBinding
    mutex    sync.RWMutex
    dataFile string
}
```

**主要方法**:
- `SaveBinding(taskID, projectID string)`: 保存绑定关系
- `GetProjectID(taskID string)`: 根据task_id获取project_id
- `GetBinding(taskID string)`: 获取完整绑定信息
- `ListBindings()`: 列出所有绑定（调试用）
- `DeleteBinding(taskID string)`: 删除绑定

### 2. 数据持久化
- **存储位置**: `./data/task_bindings.json`
- **格式**: JSON数组，包含所有绑定关系
- **原子操作**: 使用临时文件+重命名确保数据一致性
- **线程安全**: 使用读写锁保护并发访问

### 3. 智能参数解析逻辑
```go
// 1. 优先使用直接提供的ppt_id
if pptID := request.GetString("ppt_id", ""); pptID != "" {
    projectID = pptID
}

// 2. 从context获取task_id
// ... 获取task_id逻辑 ...

// 3. 如果没有ppt_id，从绑定获取project_id
if projectID == "" && taskID != "" {
    if boundProjectID, err := taskBindingManager.GetProjectID(taskID); err == nil {
        projectID = boundProjectID
    }
}

// 4. 验证必须有project_id
if projectID == "" {
    return error("Either ppt_id parameter or valid task_id in context is required")
}
```

## 使用示例

### 1. 上传文件（自动创建绑定）
```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```
**结果**: 自动创建绑定 `task_123456 ↔ proj_abcdef`

### 2. 查询进度（使用绑定）
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_progress",
    "arguments": {
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```
**结果**: 自动从绑定获取`project_id`并查询进度

### 3. 直接指定项目ID（向后兼容）
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_progress",
    "arguments": {
      "ppt_id": "proj_abcdef"
    }
  }
}
```
**结果**: 直接使用指定的`project_id`

## 配置更新

### Docker配置
**文件**: `mcp/docker-compose.yml`
```yaml
volumes:
  - ./data:/app/data  # 挂载数据目录
```

### 环境变量
无需额外环境变量，数据目录默认为`./data`

## 向后兼容性

### 1. 参数兼容
- 原有的直接传递`project_id`的方式仍然有效（现在叫`ppt_id`）
- 原有的`Context`参数获取`task_id`的方式保持不变
- Go context fallback机制保持不变

### 2. 工具行为兼容
- 如果同时提供`ppt_id`和`task_id`，优先使用`ppt_id`
- 如果只提供`task_id`，自动从绑定获取`project_id`
- 如果都没有提供，返回明确的错误信息

### 3. 数据兼容
- 新功能不影响现有数据
- 绑定数据独立存储，不影响原有业务逻辑

## 错误处理

### 1. 绑定创建失败
- 记录警告日志，不影响主流程
- 用户仍可通过直接传递`ppt_id`的方式使用

### 2. 绑定查询失败
- 返回明确的错误信息
- 提示用户可以直接传递`ppt_id`参数

### 3. 数据文件问题
- 文件不存在：自动创建
- 文件损坏：记录错误，从空状态开始
- 权限问题：记录错误，功能降级

## 日志记录

### 1. 绑定操作日志
```
Task binding saved: task_id=task_123456 -> project_id=proj_abcdef
Task binding found: task_id=task_123456 -> project_id=proj_abcdef
```

### 2. 智能解析日志
```
get_progress: Using directly provided ppt_id: proj_abcdef
get_progress: Retrieved project_id from task binding: task_id=task_123456 -> project_id=proj_abcdef
```

### 3. 错误日志
```
get_progress: Failed to get project_id from task binding for task_id=task_123456: no project_id found for task_id: task_123456
```

## 性能考虑

### 1. 内存使用
- 绑定数据存储在内存中，快速访问
- 使用map结构，O(1)查询复杂度

### 2. 磁盘I/O
- 只在创建/删除绑定时写入磁盘
- 使用原子写入操作，避免数据损坏

### 3. 并发安全
- 使用读写锁，支持并发读取
- 写操作互斥，确保数据一致性

## 监控和调试

### 1. 统计信息
```go
stats := taskBindingManager.GetStats()
// 返回: {"total_bindings": 10, "data_file": "./data/task_bindings.json"}
```

### 2. 绑定列表
```go
bindings := taskBindingManager.ListBindings()
// 返回所有绑定关系，用于调试
```

### 3. 数据文件
可直接查看`./data/task_bindings.json`文件内容进行调试
