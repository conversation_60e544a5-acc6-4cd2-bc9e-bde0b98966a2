# MCP Pipeline Server

一个集成了 MCP (Model Context Protocol) 的代理服务器，将 MCP 工具请求代理到 ppt-narrator 服务。

## 功能特性

- **纯 MCP 服务器**: 直接提供 MCP 协议接口，无需 HTTP 框架
- **代理功能**: 将 MCP 工具请求代理到 ppt-narrator 服务
- **访问控制**: 支持基于查询参数的访问密钥验证
- **轻量级**: 无数据库、无 Web 框架，最小化依赖
- **简单部署**: 单一可执行文件，易于容器化

## 快速开始

### 1. 安装依赖

```bash
cd old-mcp-pipeline-server
go mod tidy
```

### 2. 配置环境变量

```bash
export PPT_NARRATOR_URL=http://localhost:38080
export MCP_ACCESS_KEY=your_secret_key_here  # 可选，传统访问控制

# DStaff平台集成配置（可选）
export DSTAFF_ENABLED=true
export DSTAFF_ENDPOINT_URL=http://*********:8800
export DSTAFF_USE_OFFICIAL_AUTH=true

# 调试配置（可选）
export MCP_DEBUG=true  # 启用详细的请求日志
```

### 3. 启动服务

```bash
go run main.go
```

服务将在端口 `48080` 启动 MCP 服务器，并代理请求到 ppt-narrator 服务。

### 4. Docker 运行

```bash
docker build -t old-mcp-pipeline-server .
docker run -p 48080:48080 \
  -e PPT_NARRATOR_URL=http://host.docker.internal:38080 \
  -e MCP_ACCESS_KEY=your_secret_key_here \
  old-mcp-pipeline-server
```

### 5. 访问控制

**重要**: 只有 `/sse` 端点需要验证，其他所有端点（包括 `/.well-known/oauth-authorization-server`、MCP 端点等）都无需验证。

```bash
# 所有端点都可以无验证访问（除了 /sse）
curl http://localhost:48080                                    # MCP 端点
curl http://localhost:48080/.well-known/oauth-authorization-server  # OAuth 发现端点

# 只有 /sse 端点需要验证（如果设置了 MCP_ACCESS_KEY）
curl "http://localhost:48080/sse?key=your_secret_key_here"     # SSE 端点
```

**验证策略**:
- **MCP 端点**: 无需验证，直接访问
- **OAuth 发现端点**: 无需验证，直接访问
- **其他端点**: 无需验证，直接访问
- **SSE 端点**: 仅当设置了 `MCP_ACCESS_KEY` 时需要验证

**安全提示**:
- SSE 端点通常用于实时数据推送，建议设置访问密钥
- 使用强密码作为访问密钥
- 通过 HTTPS 传输以保护密钥安全

### 7. DStaff平台集成

服务器支持与DStaff平台的集成，提供官方鉴权和文件上传下载功能。

**配置DStaff集成**:
```bash
# 启用DStaff集成
export DSTAFF_ENABLED=true
export DSTAFF_ENDPOINT_URL=http://*********:8800
export DSTAFF_USE_OFFICIAL_AUTH=true

# 启动服务
go run main.go
```

**鉴权模式**:
- **DStaff官方鉴权**: 所有请求需要Bearer Token，通过DStaff API验证
- **传统鉴权**: 仅/sse端点需要访问密钥
- **混合模式**: 启用DStaff功能但使用传统鉴权

**新增工具**:
- `upload_file_to_dstaff`: 上传文件到DStaff平台
- `download_file_from_dstaff`: 从DStaff平台下载文件

详细配置说明请参考 [DSTAFF_INTEGRATION.md](./DSTAFF_INTEGRATION.md)

### 6. 日志功能

服务器会在终端输出详细的HTTP请求和操作日志：

**HTTP请求日志（始终启用）**：
- 记录所有HTTP请求的方法、路径、请求头和请求体
- 自动掩码敏感信息（如Authorization头）
- 支持JSON格式美化输出

**MCP工具调试日志（可选）**：
```bash
export MCP_DEBUG=true  # 启用MCP工具调用的额外调试信息
```

- **访问日志**: 记录所有访问请求，包括端点、方法、来源 IP
- **验证日志**: 记录访问控制的成功和失败情况
- **代理日志**: 记录到 ppt-narrator 的请求和响应状态
- **MCP 工具日志**: 记录 MCP 工具的调用和参数

示例日志输出：
```
2025/08/04 08:58:44 Access granted (no auth required): GET /.well-known/oauth-authorization-server from **********:42502
2025/08/04 08:58:45 Access granted (no auth required): POST /mcp from **********:42518
2025/08/04 08:58:45 Access denied: No key provided for GET /sse from **********:42520
2025/08/04 08:58:46 Access granted: Valid key for GET /sse from **********:42522
2025/08/04 08:58:45 upload_file: Starting pipeline for 'test.pptx' with URL: http://example.com/file.pptx
2025/08/04 08:58:45 Proxying POST request to: http://localhost:38080/api/upload
```

## API 接口

### MCP 工具

服务提供以下 MCP 工具：

#### upload_file
上传PPT文件并启动完整的处理pipeline

**参数:**
- `name` (string, 必需): PPT文件名称
- `file_url` (string, 必需): PPT文件的下载URL地址
- `user_requirements` (string, 可选): 用户需求描述
- `enable_subtitles` (boolean, 可选): 是否启用字幕，默认为false
- `subtitle_style_template` (string, 可选): 字幕样式模板，可选值：
  - `classic_yellow`: 经典黄色字幕
  - `professional_white`: 专业白色字幕（带半透明背景）
  - `elegant_blue`: 优雅蓝色字幕
  - `bold_red`: 醒目红色字幕
  - `soft_green`: 柔和绿色字幕
  - `large_text`: 大字体字幕
  - `minimal`: 简约风格字幕
- `Context` (object, 可选): 上下文参数，包含task_id等信息

#### get_progress
获取任务进度

#### get_download_url
获取下载链接

### REST API

#### 上传文件并启动 Pipeline

```http
POST /api/upload
Content-Type: application/json

{
  "name": "任务名称",
  "file_url": "https://example.com/file.pdf"
}
```

响应：
```json
{
  "success": true,
  "job_id": "uuid",
  "message": "Pipeline started successfully"
}
```

#### 查询进度

```http
GET /api/progress/{jobId}?include_steps=true
```

响应：
```json
{
  "success": true,
  "job": {
    "id": "uuid",
    "name": "任务名称",
    "status": "processing",
    "progress": 45.5,
    "message": "Processing file...",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:01:00Z"
  },
  "steps": [
    {
      "id": "step-uuid",
      "step_name": "download",
      "status": "completed",
      "progress": 100,
      "message": "File downloaded successfully"
    }
  ]
}
```

#### 下载结果文件

```http
GET /api/download/{jobId}
```

直接返回文件内容。

#### 获取下载链接

```http
GET /api/download-url/{jobId}
```

响应：
```json
{
  "success": true,
  "download_url": "/api/download/uuid",
  "message": "Download URL generated successfully"
}
```

#### 取消任务

```http
DELETE /api/cancel/{jobId}
```

#### SSE 实时更新

```http
GET /api/sse?job_id={jobId}
```

连接到 Server-Sent Events 流，接收实时进度更新。

消息格式：
```json
{
  "type": "progress",
  "job_id": "uuid",
  "data": {
    "job_id": "uuid",
    "status": "processing",
    "progress": 45.5,
    "message": "Processing file...",
    "step_name": "process"
  },
  "timestamp": "2024-01-01T00:01:00Z"
}
```

## Pipeline 处理流程

1. **下载文件**: 从提供的 URL 下载文件
2. **处理文件**: 模拟文件处理过程
3. **生成结果**: 创建处理结果文件

每个步骤都会更新进度并通过 SSE 推送给客户端。

## 配置

### 环境变量

- `PORT`: 服务端口（默认: 8080）
- `DB_PATH`: 数据库文件路径（默认: pipeline.db）
- `WORK_DIR`: 工作目录（默认: ./work）

### 目录结构

```
work/
├── uploads/     # 上传的文件
└── results/     # 处理结果
```

## 开发

### 项目结构

```
mcp-pipeline-server/
├── main.go                    # 主服务文件
├── internal/
│   ├── models/               # 数据模型
│   ├── services/             # 业务逻辑
│   │   ├── database.go       # 数据库服务
│   │   ├── pipeline.go       # Pipeline 服务
│   │   └── sse.go           # SSE 服务
│   └── handlers/            # HTTP 处理器
│       └── pipeline_handler.go
├── examples/
│   └── client.html          # 客户端示例
└── README.md
```

### 扩展 Pipeline

要添加新的处理步骤，修改 `services/pipeline.go` 中的 `processJob` 方法：

```go
func (ps *PipelineService) processJob(ctx context.Context, jobID string) {
    // 现有步骤...
    
    // 添加新步骤
    if err := ps.customProcessStep(ctx, job); err != nil {
        ps.updateJobStatus(jobID, "failed", 70, fmt.Sprintf("Custom step failed: %v", err), "")
        return
    }
}
```

### 添加新的 MCP 工具

在 `main.go` 的 `addPipelineTools` 函数中添加新工具：

```go
mcpServer.AddTool(mcp.Tool{
    Name:        "custom_tool",
    Description: "Custom tool description",
    InputSchema: mcp.ToolInputSchema{
        Type: "object",
        Properties: map[string]any{
            "param": map[string]any{
                "type":        "string",
                "description": "Parameter description",
            },
        },
        Required: []string{"param"},
    },
}, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 工具逻辑
    return &mcp.CallToolResult{
        Content: []mcp.Content{
            mcp.TextContent{
                Type: "text",
                Text: "Tool result",
            },
        },
    }, nil
})
```

## 许可证

MIT License
