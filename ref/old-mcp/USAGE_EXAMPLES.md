# MCP Pipeline Server 使用示例

本文档提供了MCP Pipeline Server的详细使用示例，包括传统功能和DStaff集成功能。

## 🚀 启动服务

### 方式1：Docker Compose（推荐）

```bash
# 启用DStaff集成
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export DSTAFF_ENDPOINT_URL=http://*********:8800

# 启动服务
docker-compose up old-mcp-pipeline-server
```

### 方式2：直接运行

```bash
cd ppt-narrator/old-mcp-pipeline-server

# 设置环境变量
export PPT_NARRATOR_URL=http://localhost:38080
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export DSTAFF_ENDPOINT_URL=http://*********:8800

# 启动服务
go run main.go
```

## 📋 日志输出说明

**HTTP请求日志**：所有HTTP请求都会输出详细的日志信息，包括方法、路径、请求头和请求体。这个功能始终启用，无需配置。

**MCP工具调试日志**：如果设置了`MCP_DEBUG=true`，MCP工具调用会输出额外的调试信息。

## 📋 MCP工具使用示例

### 1. upload_file - 上传PPT并生成解说视频

```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说，语速适中",
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```

**服务器日志输出：**
```
=== HTTP Request ===
Method: POST
Path: /
Full URL: /
Remote Addr: 127.0.0.1:54321
Headers:
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOi...dGVzdF90b2tlbg
  User-Agent: MCP-Client/1.0
  Content-Length: 245
Request Body (JSON):
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说"
    }
  }
}
===================

=== MCP Tool Call: upload_file ===
Request Body: {
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "我的演示文稿",
      "file_url": "https://example.com/presentation.pptx",
      "user_requirements": "请生成专业的解说，语速适中"
    }
  }
}
=====================================
upload_file: 开始为PPT文件 '我的演示文稿' 生成解说视频，URL: https://example.com/presentation.pptx
```

### 1.2. upload_file - 从数字员工平台下载文件

当file_url是相对路径时，系统会自动从数字员工平台下载文件。

**认证方式：** 系统会自动从HTTP请求的Authorization头中获取Bearer token用于访问数字员工平台。

```json
{
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "demo-SQL注入.pptx",
      "file_url": "user_input/demo-SQL注入.pptx",
      "user_requirements": "请生成专业的解说，语速适中",
      "Context": {
        "task_id": "eb2ae565-00a2-40a6-aa46-d211b1ac6f76"
      }
    }
  }
}
```

**支持的文件路径格式：**
- 相对路径：`user_input/demo-SQL注入.pptx`
- file:// URL：`file:///user_input/demo-SQL注入.pptx`（系统会自动提取相对路径部分）

**服务器日志输出：**
```
=== MCP Tool Call: upload_file ===
upload_file: 开始为PPT文件 'demo-SQL注入.pptx' 生成解说视频，URL: file:///user_input/demo-SQL注入.pptx, task_id: eb2ae565-00a2-40a6-aa46-d211b1ac6f76
Downloading file from DStaff: taskId=eb2ae565-00a2-40a6-aa46-d211b1ac6f76, filePath=user_input/demo-SQL注入.pptx
File downloaded successfully from DStaff: user_input/demo-SQL注入.pptx -> /tmp/eb2ae565-00a2-40a6-aa46-d211b1ac6f76_20250805_145057_demo-SQL注入.pptx (size: 1234567 bytes)
upload_file: Downloaded file to local path: /tmp/eb2ae565-00a2-40a6-aa46-d211b1ac6f76_20250805_145057_demo-SQL注入.pptx
Local file read successfully, size: 1234567 bytes
Uploading file to: http://localhost:38080/api/v1/pipeline/upload-and-process
Upload successful (status 200)
```

### 2. get_progress - 查询进度

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_progress",
    "arguments": {
      "project_id": "proj_123456",
      "include_steps": true,
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```

**服务器日志输出：**
```
=== MCP Tool Call: get_progress ===
Request Body: {
  "method": "tools/call",
  "params": {
    "name": "get_progress",
    "arguments": {
      "project_id": "proj_123456",
      "include_steps": true
    }
  }
}
===================================
get_progress: Getting progress for project 'proj_123456' (include_steps: true)
```

### 3. get_download_url - 获取下载链接

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_download_url",
    "arguments": {
      "project_id": "proj_123456",
      "Context": {
        "task_id": "task_123456"
      }
    }
  }
}
```

**服务器日志输出：**
```
=== MCP Tool Call: get_download_url ===
Request Body: {
  "method": "tools/call",
  "params": {
    "name": "get_download_url",
    "arguments": {
      "project_id": "proj_123456"
    }
  }
}
======================================
get_download_url: Getting download info for project 'proj_123456'
```

## 📁 DStaff透明文件处理示例

### 透明文件上传

**HTTP请求：**
```bash
curl -X POST \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@/path/to/presentation.pptx" \
  -F "project_name=my_presentation" \
  -F "task_id=task_20241205_001" \
  http://localhost:48080/
```

**服务器日志输出：**
```
=== DStaff Transparent File Upload ===
Method: POST
URL: /
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
=====================================
Uploading file to DStaff: presentation.pptx -> mcps_upload/my_presentation/presentation.pptx
File uploaded successfully to dstaff: mcps_upload/my_presentation/presentation.pptx
```

**响应示例：**
```json
{
  "work_type": "mcp_tool",
  "compression": false,
  "status": "success",
  "message": "文件上传成功！上传的文件路径：mcps_upload/my_presentation/presentation.pptx",
  "attachments": [
    {
      "path": "mcps_upload/my_presentation/presentation.pptx",
      "filename": "presentation.pptx",
      "type": "file",
      "content_type": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "content_length": 2048576
    }
  ]
}
```

### 透明文件下载

**HTTP请求：**
```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  http://localhost:48080/download/task_20241205_001/videos/output.mp4 \
  -o downloaded_video.mp4
```

**服务器日志输出：**
```
=== DStaff Transparent File Download ===
Method: GET
URL: /download/task_20241205_001/videos/output.mp4
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
=======================================
Downloading file from DStaff: task=task_20241205_001, file=videos/output.mp4
File downloaded successfully from dstaff: videos/output.mp4 -> /tmp/download_output.mp4
```

## 🔐 鉴权示例

### DStaff官方鉴权

```bash
# 所有请求都需要Bearer Token
curl -H "Authorization: Bearer your_dstaff_token" \
  http://localhost:48080/

# Token验证失败的响应
HTTP/1.1 403 Forbidden
{"error": "Invalid token"}
```

### 传统鉴权

```bash
# 只有/sse端点需要key参数
curl "http://localhost:48080/sse?key=your_access_key"

# 其他端点无需鉴权
curl http://localhost:48080/
```

## 🧪 测试脚本使用

```bash
cd ppt-narrator/old-mcp-pipeline-server

# 设置测试参数
export DSTAFF_ENABLED=true
export DSTAFF_USE_OFFICIAL_AUTH=true
export TEST_TOKEN="your_test_token"
export TEST_TASK_ID="test_task_123"

# 运行测试
./test_dstaff.sh
```

**测试输出示例：**
```
=== MCP Pipeline Server - DStaff集成测试 ===

[INFO] 当前配置信息:
  MCP服务器URL: http://localhost:48080
  测试Token: your_test_token
  测试TaskID: test_task_123

[INFO] 环境变量检查:
  DSTAFF_ENABLED: true
  DSTAFF_USE_OFFICIAL_AUTH: true
  DSTAFF_ENDPOINT_URL: http://*********:8800
  MCP_ACCESS_KEY: 未设置

[SUCCESS] MCP服务器正在运行
[SUCCESS] 无鉴权访问成功
[INFO] Bearer Token鉴权被拒绝（预期行为，如果启用了DStaff鉴权）
[SUCCESS] 测试文件创建完成: test_upload.txt
[INFO] DStaff集成已启用，测试透明文件处理功能...
[SUCCESS] 透明文件上传测试成功
[SUCCESS] 透明文件下载测试成功
[SUCCESS] 所有测试完成！
```

## 🔍 调试技巧

### 1. 查看详细日志

所有MCP工具调用都会输出详细的请求体信息，便于调试：

```
=== MCP Tool Call: upload_file ===
Request Body: {
  "method": "tools/call",
  "params": {
    "name": "upload_file",
    "arguments": {
      "name": "test",
      "file_url": "https://example.com/test.pptx"
    }
  }
}
=====================================
```

### 2. 文件操作日志

透明文件处理会输出详细的操作日志：

```
=== DStaff Transparent File Upload ===
Method: POST
URL: /
Content-Type: multipart/form-data
Authorization: Bearer xxx...
=====================================
Uploading file to DStaff: test.txt -> mcps_upload/test.txt
```

### 3. 错误排查

- **Token验证失败**: 检查DSTAFF_ENDPOINT_URL和Token有效性
- **文件上传失败**: 确认文件路径和权限
- **服务连接失败**: 检查PPT_NARRATOR_URL配置

## 📝 最佳实践

1. **生产环境**: 使用DStaff官方鉴权
2. **开发环境**: 可以禁用鉴权或使用传统鉴权
3. **文件管理**: 定期清理临时文件
4. **日志监控**: 关注请求体日志进行调试
5. **错误处理**: 检查返回的错误信息进行问题定位
