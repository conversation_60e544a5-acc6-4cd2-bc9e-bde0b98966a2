# BaseURL 动态获取示例

## 问题描述

之前的代码中，`mcpBaseURL` 被硬编码为 `"http://localhost:48080"`，这在以下场景中会出现问题：

1. 服务器部署在不同的主机上（如 `*************`）
2. 使用不同的端口
3. 通过反向代理访问（如 nginx）
4. 使用 HTTPS

## 解决方案

### 1. 添加了 `getServerBaseURL` 函数

```go
// getServerBaseURL determines the base URL for the server based on the request
func getServerBaseURL(r *http.Request, defaultPort string) string {
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}
	
	// Check for X-Forwarded-Proto header (common in reverse proxy setups)
	if proto := r.Header.Get("X-Forwarded-Proto"); proto != "" {
		scheme = proto
	}
	
	host := r.Host
	if host == "" {
		// Fallback to localhost with default port
		host = "localhost" + defaultPort
	}
	
	return fmt.Sprintf("%s://%s", scheme, host)
}
```

### 2. 修改了 HTTP 处理器

在两个 HTTP 服务器的处理器中都添加了：

```go
// Get server base URL and add to context
serverBaseURL := getServerBaseURL(r, ":48080") // 或 ":48081"
ctx := context.WithValue(r.Context(), "server_host", serverBaseURL)
```

### 3. 修改了 MCP 工具中的 baseURL 获取

在 `get_download_url` 工具中：

```go
// Get the actual server host from context (set by HTTP handler)
var mcpBaseURL string
if serverHost := ctx.Value("server_host"); serverHost != nil {
	if hostStr, ok := serverHost.(string); ok {
		mcpBaseURL = hostStr
	}
}
// Fallback to localhost if not available
if mcpBaseURL == "" {
	mcpBaseURL = "http://localhost:48080"
}
```

## 使用场景示例

### 场景 1: 本地开发
- 访问: `http://localhost:48080`
- 生成的下载链接: `http://localhost:48080/proxy/download/{project_id}/video`

### 场景 2: 局域网部署
- 访问: `http://*************:48080`
- 生成的下载链接: `http://*************:48080/proxy/download/{project_id}/video`

### 场景 3: 通过反向代理
- 访问: `https://api.example.com/mcp`
- 请求头: `X-Forwarded-Proto: https`
- 生成的下载链接: `https://api.example.com/proxy/download/{project_id}/video`

### 场景 4: 自定义端口
- 访问: `http://example.com:8080`
- 生成的下载链接: `http://example.com:8080/proxy/download/{project_id}/video`

## 优势

1. **动态适应**: 自动根据客户端访问的实际地址生成正确的 URL
2. **反向代理支持**: 支持 `X-Forwarded-Proto` 头，适用于 nginx、Apache 等反向代理
3. **HTTPS 支持**: 自动检测 TLS 连接并使用正确的协议
4. **向后兼容**: 如果无法获取主机信息，仍然回退到默认的 localhost 地址

## 测试方法

可以通过以下方式测试：

1. 本地测试: `curl http://localhost:48080/...`
2. 局域网测试: `curl http://*************:48080/...`
3. 反向代理测试: 配置 nginx 并通过域名访问
