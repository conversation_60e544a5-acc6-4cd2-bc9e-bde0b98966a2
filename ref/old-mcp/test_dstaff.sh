#!/bin/bash

# DStaff集成测试脚本
# 用于测试MCP Pipeline Server的DStaff集成功能

set -e

echo "=== MCP Pipeline Server - DStaff集成测试 ==="
echo

# 配置变量
MCP_SERVER_URL="http://localhost:48080"
TEST_TOKEN="your_test_token_here"
TEST_TASK_ID="test_task_123"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务器是否运行
check_server() {
    log_info "检查MCP服务器状态..."
    if curl -s "$MCP_SERVER_URL" > /dev/null 2>&1; then
        log_success "MCP服务器正在运行"
    else
        log_error "MCP服务器未运行，请先启动服务器"
        exit 1
    fi
}

# 测试无鉴权访问
test_no_auth() {
    log_info "测试无鉴权访问..."
    response=$(curl -s -w "%{http_code}" "$MCP_SERVER_URL" -o /dev/null)
    if [ "$response" = "200" ]; then
        log_success "无鉴权访问成功"
    else
        log_error "无鉴权访问失败，HTTP状态码: $response"
    fi
}

# 测试Bearer Token鉴权
test_bearer_auth() {
    log_info "测试Bearer Token鉴权..."
    response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TEST_TOKEN" "$MCP_SERVER_URL" -o /dev/null)
    if [ "$response" = "200" ]; then
        log_success "Bearer Token鉴权成功"
    elif [ "$response" = "401" ] || [ "$response" = "403" ]; then
        log_info "Bearer Token鉴权被拒绝（预期行为，如果启用了DStaff鉴权）"
    else
        log_error "Bearer Token鉴权测试失败，HTTP状态码: $response"
    fi
}

# 测试MCP工具列表
test_tools_list() {
    log_info "测试MCP工具列表..."
    # 发送一个简单的JSON请求来测试HTTP请求日志
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TEST_TOKEN" \
        -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' \
        "$MCP_SERVER_URL" \
        -o /dev/null)

    if [ "$response" = "200" ] || [ "$response" = "404" ]; then
        log_success "HTTP请求日志测试成功（检查服务器日志查看详细信息）"
    else
        log_info "HTTP请求返回状态码: $response"
    fi
}

# 创建测试文件
create_test_file() {
    log_info "创建测试文件..."
    echo "这是一个测试文件，用于测试DStaff透明文件上传功能。" > test_upload.txt
    echo "创建时间: $(date)" >> test_upload.txt
    echo "测试Token: $TEST_TOKEN" >> test_upload.txt
    echo "测试TaskID: $TEST_TASK_ID" >> test_upload.txt
    log_success "测试文件创建完成: test_upload.txt"
}

# 测试透明文件上传
test_transparent_upload() {
    log_info "测试透明文件上传..."
    if [ ! -f "test_upload.txt" ]; then
        log_error "测试文件不存在"
        return
    fi

    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $TEST_TOKEN" \
        -F "file=@test_upload.txt" \
        -F "project_name=test_project" \
        -F "task_id=$TEST_TASK_ID" \
        "$MCP_SERVER_URL" \
        -o /dev/null)

    if [ "$response" = "200" ]; then
        log_success "透明文件上传测试成功"
    else
        log_info "透明文件上传测试返回状态码: $response（可能需要有效的DStaff配置）"
    fi
}

# 测试透明文件下载
test_transparent_download() {
    log_info "测试透明文件下载..."

    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $TEST_TOKEN" \
        "$MCP_SERVER_URL/download/$TEST_TASK_ID/test_upload.txt" \
        -o test_download.txt)

    if [ "$response" = "200" ] && [ -f "test_download.txt" ]; then
        log_success "透明文件下载测试成功"
        log_info "下载的文件内容:"
        cat test_download.txt | head -3
    else
        log_info "透明文件下载测试返回状态码: $response（可能需要有效的DStaff配置）"
    fi
}

# 清理测试文件
cleanup() {
    log_info "清理测试文件..."
    rm -f test_upload.txt
    rm -f test_download.txt
    log_success "清理完成"
}

# 显示配置信息
show_config() {
    echo
    log_info "当前配置信息:"
    echo "  MCP服务器URL: $MCP_SERVER_URL"
    echo "  测试Token: $TEST_TOKEN"
    echo "  测试TaskID: $TEST_TASK_ID"
    echo
    log_info "环境变量检查:"
    echo "  DSTAFF_ENABLED: ${DSTAFF_ENABLED:-未设置}"
    echo "  DSTAFF_USE_OFFICIAL_AUTH: ${DSTAFF_USE_OFFICIAL_AUTH:-未设置}"
    echo "  DSTAFF_ENDPOINT_URL: ${DSTAFF_ENDPOINT_URL:-未设置}"
    echo "  MCP_ACCESS_KEY: ${MCP_ACCESS_KEY:-未设置}"
    echo "  MCP_DEBUG: ${MCP_DEBUG:-未设置}"
    echo
}

# 显示使用说明
show_usage() {
    echo
    log_info "使用说明:"
    echo "1. 确保MCP Pipeline Server正在运行"
    echo "2. 根据需要设置环境变量:"
    echo "   export DSTAFF_ENABLED=true"
    echo "   export DSTAFF_USE_OFFICIAL_AUTH=true"
    echo "   export DSTAFF_ENDPOINT_URL=http://*********:8800"
    echo "   export MCP_DEBUG=true  # 启用详细调试日志"
    echo "3. 运行此脚本进行测试"
    echo
    echo "如果启用了DStaff官方鉴权，请确保提供有效的测试Token"
    echo
}

# 主测试流程
main() {
    show_config
    show_usage

    check_server
    test_no_auth
    test_bearer_auth
    test_tools_list

    create_test_file

    # 测试DStaff透明文件处理功能
    if [ "${DSTAFF_ENABLED:-false}" = "true" ]; then
        log_info "DStaff集成已启用，测试透明文件处理功能..."
        test_transparent_upload
        test_transparent_download
    else
        log_info "DStaff集成未启用，跳过文件处理测试"
    fi

    echo
    log_success "所有测试完成！"
    log_info "检查服务器日志以查看详细的请求体信息"
    echo

    cleanup
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 运行主程序
main "$@"
