#!/usr/bin/env python3
"""
测试 MCP 进度报告功能的脚本
"""

import json
import requests
import time

def test_progress_reporting():
    """测试进度报告功能"""
    
    # MCP 服务器配置
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # 测试请求 - 带有进度令牌（正确的 MCP 格式）
    request_data = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "generate_card_from_text",
            "arguments": {
                "text": "这是一个测试文本，用于验证 MCP 进度报告功能是否正常工作。我们希望看到实时的进度更新，而不是简单的日志输出。"
            },
            "meta": {
                "progressToken": "test-progress-token-123"
            }
        }
    }
    
    print("🧪 Testing MCP Progress Reporting...")
    print(f"📡 Sending request to: {base_url}")
    print(f"🎯 Progress Token: {request_data['params']['meta']['progressToken']}")
    print()
    
    try:
        # 发送请求
        response = requests.post(
            base_url,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📝 Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📄 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ Request failed!")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        print(f"📄 Raw response: {response.text}")

def test_without_progress_token():
    """测试不带进度令牌的请求"""
    
    # MCP 服务器配置
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # 测试请求 - 不带进度令牌
    request_data = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": "generate_card_from_text",
            "arguments": {
                "text": "这是一个不带进度令牌的测试请求。"
            }
        }
    }
    
    print("\n" + "="*50)
    print("🧪 Testing WITHOUT Progress Token...")
    print(f"📡 Sending request to: {base_url}")
    print("🎯 No Progress Token")
    print()
    
    try:
        # 发送请求
        response = requests.post(
            base_url,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📄 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ Request failed!")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        print(f"📄 Raw response: {response.text}")

if __name__ == "__main__":
    print("🚀 MCP Progress Reporting Test Suite")
    print("="*50)
    
    # 测试带进度令牌的请求
    test_progress_reporting()
    
    # 等待一下
    time.sleep(2)
    
    # 测试不带进度令牌的请求
    test_without_progress_token()
    
    print("\n" + "="*50)
    print("🏁 Test completed!")
    print("💡 Check the server logs for progress reporting details.")
