#!/usr/bin/env python3
"""
测试 SSE 进度通知
"""

import json
import requests
import time
import threading
from urllib.parse import urlencode

def listen_to_sse():
    """建立 SSE 连接来接收通知"""
    print("🔗 Establishing SSE connection...")
    try:
        # 建立 SSE 连接
        url = "http://localhost:48088/sse?key=your_sse_access_key_here"
        response = requests.get(url, stream=True, timeout=60)
        
        if response.status_code == 200:
            print("✅ SSE connection established")
            
            # 读取 SSE 流
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"📡 SSE Line: {line}")
                    
                    # 解析 SSE 事件
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        try:
                            data = json.loads(data_str)
                            method = data.get("method", "")
                            
                            if method == "notifications/progress":
                                params = data.get("params", {})
                                progress = params.get("progress", 0)
                                total = params.get("total", 0)
                                token = params.get("progressToken", "")
                                message = params.get("message", "")
                                print(f"🎯 Progress: {progress}/{total} - {message} (token: {token})")
                                
                            elif method == "notifications/message":
                                params = data.get("params", {})
                                level = params.get("level", "")
                                logger = params.get("logger", "")
                                msg = params.get("data", "")
                                print(f"📝 Log: [{level}] {logger}: {msg}")
                                
                        except json.JSONDecodeError:
                            # 可能不是 JSON 数据，跳过
                            pass
                            
        else:
            print(f"❌ SSE connection failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ SSE Error: {e}")

def send_tool_request():
    """发送工具调用请求"""
    print("📤 Sending tool request...")
    
    request_data = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "generate_card_from_text",
            "arguments": {
                "text": "简单测试文本"
            },
            "meta": {
                "progressToken": "sse-test-token-456"
            }
        }
    }
    
    print(f"📝 Request: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            "http://localhost:48089/mcp",
            json=request_data,
            params={"key": "your_sse_access_key_here"},
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Request Error: {e}")

def test_sse_progress():
    """测试 SSE 进度通知"""
    print("🧪 Testing SSE Progress Notifications...")
    print("=" * 50)
    
    # 启动 SSE 监听线程
    sse_thread = threading.Thread(target=listen_to_sse, daemon=True)
    sse_thread.start()
    
    # 等待 SSE 连接建立
    print("⏳ Waiting for SSE connection to establish...")
    time.sleep(3)
    
    # 发送工具请求
    send_tool_request()
    
    # 等待接收通知
    print("⏳ Waiting for notifications...")
    time.sleep(15)

if __name__ == "__main__":
    test_sse_progress()
    print("\n💡 Check server logs for debug information!")
