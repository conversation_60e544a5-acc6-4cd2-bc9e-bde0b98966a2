#!/bin/bash
# Smart Card MCP 异步HTTP API客户端示例 (Bash/curl)
# 支持从URL生成卡片的完整异步流程

set -e  # 遇到错误立即退出

# 配置
BASE_URL="http://localhost:48089/api/v1"
API_KEY="smart-card-mcp-your-secure-key-here"
TIMEOUT=300  # 5分钟超时
POLL_INTERVAL=2  # 2秒轮询间隔

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 创建异步任务
create_task_from_url() {
    local url="$1"
    local summary_prompt="$2"
    local style_prompt="$3"
    
    log_info "发起异步任务: $url"
    log_info "总结提示词: ${summary_prompt:-默认}"
    log_info "风格提示词: ${style_prompt:-默认}"
    
    # 构建JSON payload
    local payload="{\"arguments\":{\"url\":\"$url\""
    
    if [[ -n "$summary_prompt" ]]; then
        payload+=",\"summary_prompt\":\"$summary_prompt\""
    fi
    
    if [[ -n "$style_prompt" ]]; then
        payload+=",\"style_prompt\":\"$style_prompt\""
    fi
    
    payload+="}}"
    
    # 发送请求（成功时返回200状态码）
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "$payload" \
        "$BASE_URL/tools/create_task_from_url/async")

    # 检查响应
    if [[ $? -ne 0 ]]; then
        log_error "请求失败"
        return 1
    fi
    
    # 解析任务ID
    local task_id=$(echo "$response" | jq -r '.data.task_id // empty')
    
    if [[ -z "$task_id" ]]; then
        log_error "无法获取任务ID"
        echo "响应: $response"
        return 1
    fi
    
    log_success "任务创建成功: $task_id"
    echo "$task_id"
}

# 获取任务状态
get_task_status() {
    local task_id="$1"
    
    local response=$(curl -s \
        -H "X-API-Key: $API_KEY" \
        "$BASE_URL/tasks/$task_id/status")
    
    if [[ $? -ne 0 ]]; then
        log_error "获取任务状态失败"
        return 1
    fi
    
    echo "$response"
}

# 获取任务结果
get_task_result() {
    local task_id="$1"
    
    local response=$(curl -s \
        -H "X-API-Key: $API_KEY" \
        "$BASE_URL/tasks/$task_id/result")
    
    if [[ $? -ne 0 ]]; then
        log_error "获取任务结果失败"
        return 1
    fi
    
    echo "$response"
}

# 等待任务完成
wait_for_completion() {
    local task_id="$1"
    local start_time=$(date +%s)
    
    log_info "等待任务完成: $task_id"
    
    while true; do
        # 检查超时
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $TIMEOUT ]]; then
            log_error "任务超时 (${TIMEOUT}秒)"
            return 1
        fi
        
        # 获取任务状态
        local status_response=$(get_task_status "$task_id")
        if [[ $? -ne 0 ]]; then
            return 1
        fi
        
        local status=$(echo "$status_response" | jq -r '.status // empty')
        local progress=$(echo "$status_response" | jq -r '.progress // {}')
        
        # 显示进度
        if [[ "$progress" != "{}" ]]; then
            local current_step=$(echo "$progress" | jq -r '.current_step // 0')
            local total_steps=$(echo "$progress" | jq -r '.total_steps // 0')
            local message=$(echo "$progress" | jq -r '.message // ""')
            
            if [[ $current_step -gt 0 && $total_steps -gt 0 ]]; then
                log_info "进度: $current_step/$total_steps - $message"
            fi
        fi
        
        # 检查任务状态
        case "$status" in
            "completed")
                log_success "任务完成!"
                return 0
                ;;
            "failed")
                local error_msg=$(echo "$status_response" | jq -r '.error // "未知错误"')
                log_error "任务失败: $error_msg"
                return 1
                ;;
            "cancelled")
                log_error "任务已取消"
                return 1
                ;;
        esac
        
        # 等待下次轮询
        sleep $POLL_INTERVAL
    done
}

# 保存base64图片
save_image() {
    local image_data="$1"
    local filename="$2"
    
    if [[ -z "$image_data" || -z "$filename" ]]; then
        log_warning "图片数据或文件名为空，跳过保存"
        return 0
    fi
    
    # 解码并保存
    echo "$image_data" | base64 -d > "$filename"
    
    if [[ $? -eq 0 ]]; then
        log_success "图片已保存: $filename"
    else
        log_error "保存图片失败: $filename"
        return 1
    fi
}

# 完整的异步卡片生成流程
generate_card_from_url() {
    local url="$1"
    local summary_prompt="$2"
    local style_prompt="$3"
    local save_path="$4"
    
    # 1. 创建异步任务
    local task_id=$(create_task_from_url "$url" "$summary_prompt" "$style_prompt")
    if [[ $? -ne 0 ]]; then
        return 1
    fi
    
    # 2. 等待任务完成
    if ! wait_for_completion "$task_id"; then
        return 1
    fi
    
    # 3. 获取任务结果
    local result=$(get_task_result "$task_id")
    if [[ $? -ne 0 ]]; then
        return 1
    fi
    
    # 4. 处理结果
    local success=$(echo "$result" | jq -r '.success // false')
    
    if [[ "$success" == "true" ]]; then
        local execution_time=$(echo "$result" | jq -r '.data.execution_time // 0')
        log_success "卡片生成成功! 执行时间: ${execution_time}秒"
        
        # 保存图片
        if [[ -n "$save_path" ]]; then
            local image_data=$(echo "$result" | jq -r '.data.content[]? | select(.type == "image") | .data // empty')
            if [[ -n "$image_data" ]]; then
                save_image "$image_data" "$save_path"
            fi
        fi
        
        # 输出完整结果
        echo "完整结果:"
        echo "$result" | jq '.'
        
        return 0
    else
        log_error "任务执行失败"
        echo "$result"
        return 1
    fi
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装"
        exit 1
    fi
    
    # 示例用法
    local url="https://example.com/article"
    local summary_prompt="重点关注技术细节"
    local style_prompt="简约现代风格"
    local save_path="generated_card.png"
    
    log_info "开始异步卡片生成流程"
    
    if generate_card_from_url "$url" "$summary_prompt" "$style_prompt" "$save_path"; then
        log_success "流程完成!"
    else
        log_error "流程失败!"
        exit 1
    fi
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
