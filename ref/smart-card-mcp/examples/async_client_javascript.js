#!/usr/bin/env node
/**
 * Smart Card MCP 异步HTTP API客户端示例 (JavaScript/Node.js)
 * 支持从URL生成卡片的完整异步流程
 */

const axios = require('axios');
const fs = require('fs').promises;

class SmartCardAsyncClient {
    constructor(config = {}) {
        this.config = {
            baseUrl: config.baseUrl || 'http://localhost:48089/api/v1',
            apiKey: config.apiKey || 'smart-card-mcp-your-secure-key-here',
            timeout: config.timeout || 300000, // 5分钟
            pollInterval: config.pollInterval || 2000, // 2秒
        };
        
        // 配置axios实例
        this.client = axios.create({
            baseURL: this.config.baseUrl,
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': this.config.apiKey,
            },
        });
    }
    
    /**
     * 从URL创建异步卡片生成任务
     * @param {string} url - 网页URL
     * @param {string} summaryPrompt - 可选的总结提示词
     * @param {string} stylePrompt - 可选的风格提示词
     * @returns {Promise<string>} 任务ID
     */
    async createTaskFromUrl(url, summaryPrompt = null, stylePrompt = null) {
        const payload = {
            arguments: { url }
        };
        
        // 添加可选参数
        if (summaryPrompt) {
            payload.arguments.summary_prompt = summaryPrompt;
        }
        if (stylePrompt) {
            payload.arguments.style_prompt = stylePrompt;
        }
        
        console.log(`🚀 发起异步任务: ${url}`);
        console.log(`📝 总结提示词: ${summaryPrompt || '默认'}`);
        console.log(`🎨 风格提示词: ${stylePrompt || '默认'}`);
        
        try {
            const response = await this.client.post('/tools/create_task_from_url/async', payload);
            // 成功时返回200状态码
            const taskId = response.data.data.task_id;

            console.log(`✅ 任务创建成功: ${taskId}`);
            return taskId;
        } catch (error) {
            console.error(`❌ 任务创建失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 获取任务状态
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 任务状态信息
     */
    async getTaskStatus(taskId) {
        try {
            const response = await this.client.get(`/tasks/${taskId}/status`);
            return response.data;
        } catch (error) {
            console.error(`❌ 获取任务状态失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 获取任务结果
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 任务结果
     */
    async getTaskResult(taskId) {
        try {
            const response = await this.client.get(`/tasks/${taskId}/result`);
            return response.data;
        } catch (error) {
            console.error(`❌ 获取任务结果失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 等待任务完成并返回结果
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 任务结果
     */
    async waitForCompletion(taskId) {
        const startTime = Date.now();
        
        console.log(`⏳ 等待任务完成: ${taskId}`);
        
        return new Promise((resolve, reject) => {
            const checkStatus = async () => {
                try {
                    // 检查超时
                    if (Date.now() - startTime > this.config.timeout) {
                        reject(new Error(`任务超时 (${this.config.timeout / 1000}秒)`));
                        return;
                    }
                    
                    // 获取任务状态
                    const statusInfo = await this.getTaskStatus(taskId);
                    const status = statusInfo.status;
                    const progress = statusInfo.progress || {};
                    
                    // 显示进度
                    if (progress.current_step && progress.total_steps) {
                        const currentStep = progress.current_step;
                        const totalSteps = progress.total_steps;
                        const message = progress.message || '';
                        console.log(`📊 进度: ${currentStep}/${totalSteps} - ${message}`);
                    }
                    
                    // 检查任务状态
                    if (status === 'completed') {
                        console.log('✅ 任务完成!');
                        const result = await this.getTaskResult(taskId);
                        resolve(result);
                        return;
                    } else if (status === 'failed') {
                        const errorMsg = statusInfo.error || '未知错误';
                        reject(new Error(`任务失败: ${errorMsg}`));
                        return;
                    } else if (status === 'cancelled') {
                        reject(new Error('任务已取消'));
                        return;
                    }
                    
                    // 继续轮询
                    setTimeout(checkStatus, this.config.pollInterval);
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            // 开始轮询
            checkStatus();
        });
    }
    
    /**
     * 保存base64编码的图片
     * @param {string} imageData - base64编码的图片数据
     * @param {string} filename - 保存的文件名
     */
    async saveImage(imageData, filename) {
        try {
            // 解码base64数据
            const buffer = Buffer.from(imageData, 'base64');
            
            // 保存到文件
            await fs.writeFile(filename, buffer);
            
            console.log(`💾 图片已保存: ${filename}`);
        } catch (error) {
            console.error(`❌ 保存图片失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 完整的异步卡片生成流程
     * @param {string} url - 网页URL
     * @param {string} summaryPrompt - 可选的总结提示词
     * @param {string} stylePrompt - 可选的风格提示词
     * @param {string} savePath - 可选的图片保存路径
     * @returns {Promise<Object>} 完整的任务结果
     */
    async generateCardFromUrl(url, summaryPrompt = null, stylePrompt = null, savePath = null) {
        try {
            // 1. 创建异步任务
            const taskId = await this.createTaskFromUrl(url, summaryPrompt, stylePrompt);
            
            // 2. 等待任务完成
            const result = await this.waitForCompletion(taskId);
            
            // 3. 处理结果
            if (result.success) {
                const data = result.data || {};
                const content = data.content || [];
                
                // 查找图片内容
                for (const item of content) {
                    if (item.type === 'image' && item.data && savePath) {
                        await this.saveImage(item.data, savePath);
                        break;
                    }
                }
                
                const executionTime = data.execution_time || 0;
                console.log(`🎉 卡片生成成功! 执行时间: ${executionTime.toFixed(2)}秒`);
                return result;
            } else {
                throw new Error('任务执行失败');
            }
        } catch (error) {
            console.error(`❌ 错误: ${error.message}`);
            throw error;
        }
    }
}

// 示例用法
async function main() {
    // 配置
    const config = {
        baseUrl: 'http://localhost:48089/api/v1',
        apiKey: 'smart-card-mcp-your-secure-key-here',
        timeout: 300000, // 5分钟
        pollInterval: 2000, // 2秒
    };
    
    // 创建客户端
    const client = new SmartCardAsyncClient(config);
    
    // 示例：从URL生成卡片
    try {
        const result = await client.generateCardFromUrl(
            'https://example.com/article',
            '重点关注技术细节',
            '简约现代风格',
            'generated_card.png'
        );
        
        console.log('🎉 任务完成!');
        console.log('结果:', JSON.stringify(result, null, 2));
        
    } catch (error) {
        console.error(`❌ 错误: ${error.message}`);
        process.exit(1);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main();
}

module.exports = SmartCardAsyncClient;
