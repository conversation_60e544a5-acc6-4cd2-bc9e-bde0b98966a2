{"name": "smart-card-mcp-client-examples", "version": "1.0.0", "description": "Smart Card MCP HTTP API客户端示例", "main": "async_client_javascript.js", "scripts": {"start": "node async_client_javascript.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["smart-card", "mcp", "api-client", "async", "http"], "author": "Smart Card MCP Team", "license": "MIT", "dependencies": {"axios": "^1.6.0"}, "engines": {"node": ">=14.0.0"}}