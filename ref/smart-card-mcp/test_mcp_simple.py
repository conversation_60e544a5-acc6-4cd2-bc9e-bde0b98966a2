#!/usr/bin/env python3
"""
简单的 MCP 协议测试
"""

import json
import requests
import time

def test_mcp_initialize():
    """测试 MCP 初始化"""
    
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # MCP 初始化请求
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {
                    "listChanged": True
                },
                "sampling": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }
    
    print("🚀 Testing MCP Initialize...")
    print(f"📡 Sending to: {base_url}")
    print(f"📝 Request: {json.dumps(init_request, indent=2)}")
    print()
    
    try:
        response = requests.post(
            base_url,
            json=init_request,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Initialize successful!")
            return result
        else:
            print("❌ Initialize failed!")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_mcp_tools_list():
    """测试获取工具列表"""
    
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # 获取工具列表
    tools_request = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list",
        "params": {}
    }
    
    print("\n" + "="*50)
    print("🔧 Testing MCP Tools List...")
    print(f"📡 Sending to: {base_url}")
    print(f"📝 Request: {json.dumps(tools_request, indent=2)}")
    print()
    
    try:
        response = requests.post(
            base_url,
            json=tools_request,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Tools list successful!")
            return result
        else:
            print("❌ Tools list failed!")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_mcp_tool_call_with_progress():
    """测试带进度令牌的工具调用"""
    
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # 工具调用请求
    call_request = {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "tools/call",
        "params": {
            "name": "generate_card_from_text",
            "arguments": {
                "text": "这是一个测试文本，用于验证 MCP 进度报告功能。"
            },
            "meta": {
                "progressToken": "test-progress-token-123"
            }
        }
    }
    
    print("\n" + "="*50)
    print("🧪 Testing MCP Tool Call with Progress...")
    print(f"📡 Sending to: {base_url}")
    print(f"🎯 Progress Token: test-progress-token-123")
    print(f"📝 Request: {json.dumps(call_request, indent=2)}")
    print()
    
    try:
        response = requests.post(
            base_url,
            json=call_request,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Tool call successful!")
            return result
        else:
            print("❌ Tool call failed!")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 MCP Protocol Test Suite")
    print("="*50)
    
    # 测试初始化
    init_result = test_mcp_initialize()
    
    if init_result:
        time.sleep(1)
        
        # 测试工具列表
        tools_result = test_mcp_tools_list()
        
        if tools_result:
            time.sleep(1)
            
            # 测试工具调用
            call_result = test_mcp_tool_call_with_progress()
    
    print("\n" + "="*50)
    print("🏁 Test completed!")
    print("💡 Check the server logs for detailed progress information.")
