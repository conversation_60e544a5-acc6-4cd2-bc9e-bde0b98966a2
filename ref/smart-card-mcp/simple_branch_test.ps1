# Simple Branch Debug Test
Write-Host "Testing branch debug output..." -ForegroundColor Cyan

$BaseURL = "http://localhost:48089"
$MCPEndpoint = "$BaseURL/mcp"

$headers = @{
    "Authorization" = "Bearer test_token_123"
    "Content-Type" = "application/json"
}

# Create task
Write-Host "Creating task..." -ForegroundColor Yellow
$createBody = @"
{
    "method": "tools/call",
    "params": {
        "name": "create_task_from_text",
        "arguments": {
            "text": "Test branch debug output",
            "Context": {
                "task_id": "branch_test_123"
            }
        }
    }
}
"@

try {
    $createResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $createBody -TimeoutSec 10
    $createResult = $createResponse.Content | ConvertFrom-Json
    $taskId = $createResult.result.task_id
    Write-Host "Task created: $taskId" -ForegroundColor Green
} catch {
    Write-Host "Task creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait for completion
Write-Host "Waiting for task completion..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Get result
Write-Host "Getting task result (this will show branch debug)..." -ForegroundColor Yellow
$resultBody = @"
{
    "method": "tools/call",
    "params": {
        "name": "get_task_result",
        "arguments": {
            "task_id": "$taskId",
            "Context": {
                "task_id": "branch_test_123"
            }
        }
    }
}
"@

try {
    $resultResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $resultBody -TimeoutSec 15
    Write-Host "Result retrieved successfully!" -ForegroundColor Green
    
    $result = $resultResponse.Content | ConvertFrom-Json
    if ($result.result.content -and $result.result.content[0].type -eq "image") {
        Write-Host "Image result received!" -ForegroundColor Green
    }
} catch {
    Write-Host "Result retrieval failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Check server logs for branch debug output:" -ForegroundColor Cyan
Write-Host "docker compose logs smart-card-mcp --tail=50" -ForegroundColor Gray
