package tasks

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// TaskProgress represents the progress of a task
type TaskProgress struct {
	Current    int     `json:"current"`
	Total      int     `json:"total"`
	Message    string  `json:"message"`
	Percentage float64 `json:"percentage"`
}

// Task represents a task in the system
type Task struct {
	ID          string                 `json:"task_id"`
	ToolName    string                 `json:"tool_name"`
	Arguments   map[string]interface{} `json:"arguments"`
	Status      TaskStatus             `json:"status"`
	Progress    *TaskProgress          `json:"progress,omitempty"`
	Result      interface{}            `json:"result,omitempty"`
	Error       string                 `json:"error,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	CallbackURL string                 `json:"callback_url,omitempty"`

	// Internal fields
	ctx        context.Context
	cancelFunc context.CancelFunc
	mu         sync.RWMutex
}

// TaskManager manages tasks in memory
type TaskManager struct {
	tasks      map[string]*Task
	mu         sync.RWMutex
	maxTasks   int
	taskTTL    time.Duration

	// Statistics
	stats struct {
		TotalTasks     int64 `json:"total_tasks"`
		CompletedTasks int64 `json:"completed_tasks"`
		FailedTasks    int64 `json:"failed_tasks"`
		mu             sync.RWMutex
	}

	// Cleanup ticker
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewTaskManager creates a new task manager
func NewTaskManager(maxTasks int, taskTTL time.Duration) *TaskManager {
	tm := &TaskManager{
		tasks:    make(map[string]*Task),
		maxTasks: maxTasks,
		taskTTL:  taskTTL,
		stopCleanup: make(chan struct{}),
	}

	// Start cleanup routine
	tm.startCleanup()

	return tm
}

// CreateTask creates a new task
func (tm *TaskManager) CreateTask(toolName string, arguments map[string]interface{}, callbackURL string) (interface{}, error) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// Check if we've reached the maximum number of active tasks
	activeTasks := tm.countActiveTasksLocked()
	if activeTasks >= tm.maxTasks {
		return nil, fmt.Errorf("maximum number of active tasks (%d) reached", tm.maxTasks)
	}

	// Create task context
	ctx, cancelFunc := context.WithCancel(context.Background())

	// Generate task ID
	taskID := fmt.Sprintf("task_%s", uuid.New().String()[:8])

	task := &Task{
		ID:          taskID,
		ToolName:    toolName,
		Arguments:   arguments,
		Status:      TaskStatusPending,
		CreatedAt:   time.Now(),
		CallbackURL: callbackURL,
		ctx:         ctx,
		cancelFunc:  cancelFunc,
	}

	tm.tasks[taskID] = task

	// Update statistics
	tm.stats.mu.Lock()
	tm.stats.TotalTasks++
	tm.stats.mu.Unlock()

	log.Printf("📋 Created task %s for tool %s", taskID, toolName)

	return tm.taskToMap(task), nil
}

// GetTask retrieves a task by ID
func (tm *TaskManager) GetTask(taskID string) (interface{}, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return tm.taskToMap(task), nil
}

// GetTaskByID retrieves the actual Task struct by ID (for internal use)
func (tm *TaskManager) GetTaskByID(taskID string) (*Task, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return task, nil
}
// GetTaskResult retrieves the result of a completed task
func (tm *TaskManager) GetTaskResult(taskID string) (interface{}, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	if task.Status != TaskStatusCompleted {
		return nil, fmt.Errorf("task %s is not completed, current status: %s", taskID, task.Status)
	}

	return task.Result, nil
}

// UpdateTaskStatus updates the status of a task
func (tm *TaskManager) UpdateTaskStatus(taskID string, status interface{}) error {
	// Convert interface{} to TaskStatus
	var taskStatus TaskStatus
	switch s := status.(type) {
	case TaskStatus:
		taskStatus = s
	case string:
		taskStatus = TaskStatus(s)
	default:
		return fmt.Errorf("invalid status type: %T", status)
	}

	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	oldStatus := task.Status
	task.Status = taskStatus

	now := time.Now()
	switch taskStatus {
	case TaskStatusRunning:
		if task.StartedAt == nil {
			task.StartedAt = &now
		}
	case TaskStatusCompleted:
		task.CompletedAt = &now
		tm.stats.mu.Lock()
		tm.stats.CompletedTasks++
		tm.stats.mu.Unlock()
	case TaskStatusFailed:
		task.CompletedAt = &now
		tm.stats.mu.Lock()
		tm.stats.FailedTasks++
		tm.stats.mu.Unlock()
	case TaskStatusCancelled:
		task.CompletedAt = &now
	}

	log.Printf("📋 Task %s status changed: %s -> %s", taskID, oldStatus, taskStatus)

	return nil
}

// UpdateTaskProgress updates the progress of a task
func (tm *TaskManager) UpdateTaskProgress(taskID string, current, total int, message string) error {
	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	percentage := float64(current) / float64(total) * 100
	task.Progress = &TaskProgress{
		Current:    current,
		Total:      total,
		Message:    message,
		Percentage: percentage,
	}

	log.Printf("📊 Task %s progress: %d/%d (%.1f%%) - %s", taskID, current, total, percentage, message)

	return nil
}

// SetTaskResult sets the result of a completed task
func (tm *TaskManager) SetTaskResult(taskID string, result interface{}) error {
	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	task.Result = result

	log.Printf("📋 Task %s result set", taskID)

	return nil
}

// SetTaskError sets the error of a failed task
func (tm *TaskManager) SetTaskError(taskID string, err error) error {
	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	task.Error = err.Error()

	log.Printf("📋 Task %s error set: %v", taskID, err)

	return nil
}

// CancelTask cancels a task
func (tm *TaskManager) CancelTask(taskID string) error {
	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	// Cancel the context
	if task.cancelFunc != nil {
		task.cancelFunc()
	}

	// Update status
	task.Status = TaskStatusCancelled
	now := time.Now()
	task.CompletedAt = &now

	log.Printf("📋 Task %s cancelled", taskID)

	return nil
}

// ListTasks returns a list of tasks with optional filtering
func (tm *TaskManager) ListTasks(status TaskStatus, limit, offset int) ([]*Task, int, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	var filteredTasks []*Task

	for _, task := range tm.tasks {
		if status == "" || task.Status == status {
			filteredTasks = append(filteredTasks, task)
		}
	}

	total := len(filteredTasks)

	// Apply pagination
	start := offset
	if start > total {
		start = total
	}

	end := start + limit
	if end > total {
		end = total
	}

	if start >= total {
		return []*Task{}, total, nil
	}

	return filteredTasks[start:end], total, nil
}

// GetActiveTaskCount returns the number of active tasks
func (tm *TaskManager) GetActiveTaskCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	return tm.countActiveTasksLocked()
}

// countActiveTasksLocked counts active tasks (must be called with lock held)
func (tm *TaskManager) countActiveTasksLocked() int {
	count := 0
	for _, task := range tm.tasks {
		if task.Status == TaskStatusPending || task.Status == TaskStatusRunning {
			count++
		}
	}
	return count
}

// GetStats returns task statistics
func (tm *TaskManager) GetStats() map[string]interface{} {
	tm.stats.mu.RLock()
	defer tm.stats.mu.RUnlock()

	tm.mu.RLock()
	activeTasks := tm.countActiveTasksLocked()
	tm.mu.RUnlock()

	return map[string]interface{}{
		"total_tasks":     tm.stats.TotalTasks,
		"active_tasks":    activeTasks,
		"completed_tasks": tm.stats.CompletedTasks,
		"failed_tasks":    tm.stats.FailedTasks,
	}
}

// startCleanup starts the cleanup routine for expired tasks
func (tm *TaskManager) startCleanup() {
	tm.cleanupTicker = time.NewTicker(5 * time.Minute) // Cleanup every 5 minutes

	go func() {
		for {
			select {
			case <-tm.cleanupTicker.C:
				tm.cleanupExpiredTasks()
			case <-tm.stopCleanup:
				tm.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanupExpiredTasks removes expired completed tasks
func (tm *TaskManager) cleanupExpiredTasks() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	now := time.Now()
	var toDelete []string

	for taskID, task := range tm.tasks {
		// Only cleanup completed, failed, or cancelled tasks
		if task.Status == TaskStatusCompleted || task.Status == TaskStatusFailed || task.Status == TaskStatusCancelled {
			if task.CompletedAt != nil && now.Sub(*task.CompletedAt) > tm.taskTTL {
				toDelete = append(toDelete, taskID)
			}
		}
	}

	for _, taskID := range toDelete {
		delete(tm.tasks, taskID)
		log.Printf("🧹 Cleaned up expired task %s", taskID)
	}

	if len(toDelete) > 0 {
		log.Printf("🧹 Cleaned up %d expired tasks", len(toDelete))
	}
}

// Stop stops the task manager and cleanup routines
func (tm *TaskManager) Stop() {
	close(tm.stopCleanup)

	// Cancel all active tasks
	tm.mu.Lock()
	defer tm.mu.Unlock()

	for _, task := range tm.tasks {
		if task.Status == TaskStatusPending || task.Status == TaskStatusRunning {
			if task.cancelFunc != nil {
				task.cancelFunc()
			}
		}
	}

	log.Println("📋 Task manager stopped")
}

// GetTaskContext returns the context for a task
func (tm *TaskManager) GetTaskContext(taskID string) (context.Context, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return task.ctx, nil
}

// TaskSummary represents a summary of a task for listing
type TaskSummary struct {
	ID        string        `json:"task_id"`
	ToolName  string        `json:"tool_name"`
	Status    TaskStatus    `json:"status"`
	Progress  *TaskProgress `json:"progress,omitempty"`
	CreatedAt time.Time     `json:"created_at"`
}

// GetTaskSummaries returns task summaries for listing
func (tm *TaskManager) GetTaskSummaries(status TaskStatus, limit, offset int) ([]*TaskSummary, int, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	var summaries []*TaskSummary

	for _, task := range tm.tasks {
		if status == "" || task.Status == status {
			task.mu.RLock()
			summary := &TaskSummary{
				ID:        task.ID,
				ToolName:  task.ToolName,
				Status:    task.Status,
				Progress:  task.Progress,
				CreatedAt: task.CreatedAt,
			}
			task.mu.RUnlock()
			summaries = append(summaries, summary)
		}
	}

	total := len(summaries)

	// Apply pagination
	start := offset
	if start > total {
		start = total
	}

	end := start + limit
	if end > total {
		end = total
	}

	if start >= total {
		return []*TaskSummary{}, total, nil
	}

	return summaries[start:end], total, nil
}

// GetTaskCopy returns a copy of the task (safe for JSON serialization)
func (tm *TaskManager) GetTaskCopy(taskID string) (*Task, error) {
	tm.mu.RLock()
	task, exists := tm.tasks[taskID]
	tm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	task.mu.RLock()
	defer task.mu.RUnlock()

	// Create a copy without internal fields
	taskCopy := &Task{
		ID:          task.ID,
		ToolName:    task.ToolName,
		Arguments:   task.Arguments,
		Status:      task.Status,
		Progress:    task.Progress,
		Result:      task.Result,
		Error:       task.Error,
		CreatedAt:   task.CreatedAt,
		StartedAt:   task.StartedAt,
		CompletedAt: task.CompletedAt,
		CallbackURL: task.CallbackURL,
	}

	return taskCopy, nil
}
// taskToMap converts a Task struct to a map for interface{} compatibility
func (tm *TaskManager) taskToMap(task *Task) map[string]interface{} {
	result := map[string]interface{}{
		"task_id":    task.ID,
		"tool_name":  task.ToolName,
		"arguments":  task.Arguments,
		"status":     string(task.Status),
		"created_at": task.CreatedAt,
	}

	if task.Progress != nil {
		result["progress"] = map[string]interface{}{
			"current":    task.Progress.Current,
			"total":      task.Progress.Total,
			"message":    task.Progress.Message,
			"percentage": task.Progress.Percentage,
		}
	}

	if task.Result != nil {
		result["result"] = task.Result
	}

	if task.Error != "" {
		result["error"] = task.Error
	}

	if task.StartedAt != nil {
		result["started_at"] = *task.StartedAt
	}

	if task.CompletedAt != nil {
		result["completed_at"] = *task.CompletedAt
	}

	if task.CallbackURL != "" {
		result["callback_url"] = task.CallbackURL
	}

	return result
}