package tools

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/mcp"
	"smart-card-mcp/internal/auth"
	"smart-card-mcp/internal/config"
	"smart-card-mcp/internal/progress"
)

// ToolHandlers contains all tool handler functions
type ToolHandlers struct {
	config           *config.Config
	progressSendFunc func(notification *mcp.ProgressNotification) error
	logSendFunc      func(notification *mcp.LoggingMessageNotification) error
	taskManager      TaskManager          // Interface for task management
	taskExecutor     TaskExecutor         // Interface for task execution
	dstaffConfig     *config.DStaffConfig // DStaff configuration
	contentSaver     *SavedContentManager // Content saving manager
}

// TaskManager interface for task management operations
type TaskManager interface {
	// Task lifecycle methods
	CreateTask(toolName string, arguments map[string]interface{}, callbackURL string) (interface{}, error)
	GetTask(taskID string) (interface{}, error)
	GetTaskResult(taskID string) (interface{}, error)

	// Task status and progress methods
	UpdateTaskProgress(taskID string, current, total int, message string) error
	UpdateTaskStatus(taskID string, status interface{}) error
	SetTaskResult(taskID string, result interface{}) error
	SetTaskError(taskID string, err error) error
}

// TaskExecutor interface for task execution operations
type TaskExecutor interface {
	ExecuteTask(taskID string) error
}

// NewToolHandlers creates a new ToolHandlers instance
func NewToolHandlers(cfg *config.Config) *ToolHandlers {
	return &ToolHandlers{
		config:       cfg,
		dstaffConfig: cfg.DStaff,
		contentSaver: NewSavedContentManager(cfg),
	}
}

// SetTaskManager sets the task manager for async operations
func (h *ToolHandlers) SetTaskManager(tm TaskManager) {
	h.taskManager = tm
}

// SetTaskExecutor sets the task executor for async operations
func (h *ToolHandlers) SetTaskExecutor(te TaskExecutor) {
	h.taskExecutor = te
}

// extractTaskID extracts task ID from context or request for async operations
func (h *ToolHandlers) extractTaskID(ctx context.Context, request interface{}) string {
	// Try to extract from request meta (for API calls)
	if reqMap, ok := request.(map[string]interface{}); ok {
		if meta, ok := reqMap["_meta"].(map[string]interface{}); ok {
			if progressToken, ok := meta["progressToken"].(string); ok {
				return progressToken
			}
		}
	}

	// Try to extract from MCP request
	if mcpReq, ok := request.(mcp.CallToolRequest); ok {
		if progressToken := progress.ExtractProgressToken(mcpReq); progressToken != "" {
			return progressToken
		}
	}

	// Try to extract from context (for async operations)
	if taskID := ctx.Value("taskID"); taskID != nil {
		if taskIDStr, ok := taskID.(string); ok {
			return taskIDStr
		}
	}

	return ""
}

// extractTaskIDFromRequest extracts task_id from request arguments (similar to ref implementation)
func (h *ToolHandlers) extractTaskIDFromRequest(argsMap map[string]interface{}) string {
	log.Printf("🔍 === Task ID Extraction from Request Arguments ===")
	log.Printf("🔧 Available arguments: %v", getArgKeys(argsMap))

	// Try to extract from Context parameter (uppercase)
	if contextParam, ok := argsMap["Context"].(map[string]interface{}); ok {
		log.Printf("📋 Found Context parameter: %v", contextParam)
		if taskID, ok := contextParam["task_id"].(string); ok {
			log.Printf("✅ Task ID extracted from Context.task_id: %s", taskID)
			return taskID
		}
	}

	// Try to extract from context parameter (lowercase)
	if contextParam, ok := argsMap["context"].(map[string]interface{}); ok {
		log.Printf("📋 Found context parameter: %v", contextParam)
		if taskID, ok := contextParam["task_id"].(string); ok {
			log.Printf("✅ Task ID extracted from context.task_id: %s", taskID)
			return taskID
		}
	}

	// Try to extract directly from arguments
	if taskID, ok := argsMap["task_id"].(string); ok {
		log.Printf("✅ Task ID extracted directly from arguments: %s", taskID)
		return taskID
	}

	log.Printf("⚠️ No task_id found in request arguments")
	return ""
}

// getArgKeys returns the keys of the arguments map for logging
func getArgKeys(argsMap map[string]interface{}) []string {
	keys := make([]string, 0, len(argsMap))
	for key := range argsMap {
		keys = append(keys, key)
	}
	return keys
}

// getMapKeys returns the keys of any map for logging
func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// getArrayLength safely gets the length of an interface{} that might be an array
func getArrayLength(arr interface{}) int {
	if slice, ok := arr.([]interface{}); ok {
		return len(slice)
	}
	return -1 // Not an array
}

// extractDStaffAuthContext extracts DStaff authentication context from request context and arguments
func (h *ToolHandlers) extractDStaffAuthContext(ctx context.Context, argsMap map[string]interface{}) *auth.DStaffAuthContext {
	log.Printf("🔍 === DStaff Auth Context Extraction ===")
	log.Printf("🔧 DStaff Config Enabled: %v", h.dstaffConfig.Enabled)

	if !h.dstaffConfig.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping auth context extraction")
		return nil
	}

	// Extract token from context
	token := ""
	if authToken := ctx.Value("authorization_token"); authToken != nil {
		if tokenStr, ok := authToken.(string); ok {
			token = tokenStr
			log.Printf("🔐 Authorization Token extracted from context: %s", maskToken(token))
		}
	} else {
		log.Printf("⚠️ No authorization_token found in context")
	}

	// Extract task ID from context first
	taskID := ""
	if dstaffTaskID := ctx.Value("dstaff_task_id"); dstaffTaskID != nil {
		if taskIDStr, ok := dstaffTaskID.(string); ok {
			taskID = taskIDStr
			log.Printf("📋 DStaff Task ID extracted from context: %s", taskID)
		}
	}

	// If no task ID from context, try to extract from request arguments
	if taskID == "" && argsMap != nil {
		taskID = h.extractTaskIDFromRequest(argsMap)
		if taskID != "" {
			log.Printf("📋 DStaff Task ID extracted from request arguments: %s", taskID)
		}
	}

	if taskID == "" {
		log.Printf("⚠️ No dstaff_task_id found in context or request arguments")
	}

	// Log all context values for debugging
	log.Printf("🔍 Context Debug Info:")
	log.Printf("   - Token present: %v", token != "")
	log.Printf("   - Task ID present: %v", taskID != "")
	log.Printf("   - Token length: %d", len(token))
	log.Printf("   - Task ID: '%s'", taskID)

	// Only return auth context if both token and task ID are present
	if token != "" && taskID != "" {
		authCtx := &auth.DStaffAuthContext{
			Token:  token,
			TaskID: taskID,
		}
		log.Printf("✅ DStaff Auth Context created successfully")
		log.Printf("   - Token: %s", maskToken(authCtx.Token))
		log.Printf("   - Task ID: %s", authCtx.TaskID)
		return authCtx
	}

	log.Printf("❌ DStaff Auth Context creation failed - missing required parameters")
	if token == "" {
		log.Printf("   - Missing: Authorization token")
	}
	if taskID == "" {
		log.Printf("   - Missing: Task ID")
	}

	return nil
}

// SetProgressSendFunc sets the function to send progress notifications
func (h *ToolHandlers) SetProgressSendFunc(sendFunc func(notification *mcp.ProgressNotification) error) {
	h.progressSendFunc = sendFunc
}

// SetLogSendFunc sets the function to send log notifications
func (h *ToolHandlers) SetLogSendFunc(sendFunc func(notification *mcp.LoggingMessageNotification) error) {
	h.logSendFunc = sendFunc
}

// OpenAIRequest represents a request to OpenAI API
type OpenAIRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature"`
	MaxTokens   *int      `json:"max_tokens,omitempty"` // 可选字段，不设置则无限制
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAIResponse represents a response from OpenAI API
type OpenAIResponse struct {
	Choices []Choice  `json:"choices"`
	Error   *APIError `json:"error,omitempty"`
}

// Choice represents a choice in OpenAI response
type Choice struct {
	Message Message `json:"message"`
}

// APIError represents an API error
type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
}

// GenerateCardFromText handles card generation from text content
func (h *ToolHandlers) GenerateCardFromText(args map[string]interface{}) (map[string]interface{}, error) {
	return h.GenerateCardFromTextWithProgress(context.Background(), mcp.CallToolRequest{}, args)
}

// GenerateCardFromTextWithProgress handles card generation from text content with progress reporting
func (h *ToolHandlers) GenerateCardFromTextWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	text, ok := args["text"].(string)
	if !ok || text == "" {
		return nil, fmt.Errorf("text parameter is required")
	}

	// Extract optional summary prompt
	summaryPrompt, _ := args["summary_prompt"].(string)
	if summaryPrompt != "" {
		log.Printf("📝 Summary prompt provided: %s", summaryPrompt)
	}

	// Extract optional style prompt
	stylePrompt, _ := args["style_prompt"].(string)
	if stylePrompt != "" {
		log.Printf("🎨 Style prompt provided: %s", stylePrompt)
	}

	// Extract task ID from Context parameter (DStaff integration)
	taskID := h.extractTaskIDFromRequest(args)
	log.Printf("📋 Extracted task_id from request: %s", taskID)

	// Also extract from context for async operations compatibility
	if taskID == "" {
		taskID = h.extractTaskID(ctx, request)
	}

	// 使用工具处理器中预设的进度发送函数，如果没有则使用默认的日志记录
	var progressSendFunc func(notification *mcp.ProgressNotification) error
	var logSendFunc func(notification *mcp.LoggingMessageNotification) error

	if h.progressSendFunc != nil {
		progressSendFunc = h.progressSendFunc
	} else {
		// 如果没有设置进度发送函数，使用默认的日志记录或任务管理器
		progressSendFunc = func(notification *mcp.ProgressNotification) error {
			if taskID != "" && h.taskManager != nil {
				// Update task progress if we have task ID and task manager
				current := int(notification.Params.Progress)
				total := int(notification.Params.Total)
				if total == 0 {
					total = 3 // default total steps
				}
				message := notification.Params.Message
				return h.taskManager.UpdateTaskProgress(taskID, current, total, message)
			}
			log.Printf("📊 Progress: %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)
			return nil
		}
	}

	if h.logSendFunc != nil {
		logSendFunc = h.logSendFunc
	} else {
		// 如果没有设置日志发送函数，使用默认的日志记录
		logSendFunc = func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Log [%s]: %s",
				string(notification.Params.Level),
				notification.Params.Data)
			return nil
		}
	}

	// 创建进度报告器 (3个主要步骤)
	reporter := progress.CreateProgressReporter(request, 3, progressSendFunc, logSendFunc, taskID)

	log.Printf("🎨 开始从文本生成卡片...")
	log.Printf("📝 文本长度: %d 字符", len(text))

	// Step 1: Summarize content first (following ref project pattern)
	if err := reporter.ReportStep(1, "正在分析和总结文本内容..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("📝 Step 1: Summarizing content...")
	summarizedContent, err := h.summarizeForCardWithPrompt(text, summaryPrompt)
	if err != nil {
		log.Printf("⚠️ Summarization failed, using original content: %v", err)
		summarizedContent = text
		if err := reporter.SendLogMessage("warning", fmt.Sprintf("文本总结失败，使用原始内容: %v", err)); err != nil {
			log.Printf("⚠️ Failed to send log: %v", err)
		}
	} else {
		log.Printf("✅ Step 1 完成，总结内容: %s", summarizedContent)
		if err := reporter.SendLogMessage("info", "文本总结完成"); err != nil {
			log.Printf("⚠️ Failed to send log: %v", err)
		}
	}

	// Step 2: Generate HTML card
	if err := reporter.ReportStep(2, "正在生成智能卡片设计..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🎨 Step 2: Generating HTML card...")
	htmlContent, err := h.generateHTMLCardFromContentWithStyle(summarizedContent, stylePrompt)
	if err != nil {
		reporter.Error(fmt.Sprintf("HTML卡片生成失败: %v", err))
		return nil, fmt.Errorf("failed to generate HTML card: %w", err)
	}
	log.Printf("✅ Step 2 完成，HTML卡片生成成功")

	// Step 3: Convert to image (always generate image)
	if err := reporter.ReportStep(3, "正在转换为高清图片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🖼️ Step 3: Converting to image...")
	imageBase64, err := h.convertHTMLToImage(htmlContent)
	if err != nil {
		reporter.Error(fmt.Sprintf("图片转换失败: %v", err))
		return nil, fmt.Errorf("failed to convert HTML to image: %w", err)
	}
	log.Printf("✅ Step 3 完成，图片生成成功")

	// Save generated content to output/saved directory
	go func() {
		// Create a temporary image file for saving
		tempImagePath := filepath.Join(h.config.OutputDir, fmt.Sprintf("temp_card_%s.png", taskID))
		if imageData, err := base64.StdEncoding.DecodeString(imageBase64); err == nil {
			if err := os.WriteFile(tempImagePath, imageData, 0644); err == nil {
				// Save all content
				if err := h.contentSaver.SaveContent("text", text, summaryPrompt, stylePrompt, summarizedContent, htmlContent, tempImagePath); err != nil {
					log.Printf("⚠️ Failed to save content: %v", err)
				}
				// Clean up temp file
				os.Remove(tempImagePath)
			}
		}
	}()

	// 报告完成
	if err := reporter.Complete("智能卡片生成完成！"); err != nil {
		log.Printf("⚠️ Failed to send completion: %v", err)
	}

	// Check if we should upload to DStaff directly (when DStaff context is available)
	dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, taskID)
	if h.shouldUploadToDStaff(dstaffAuth) {
		log.Printf("📤 DStaff context available, uploading card directly...")

		fileSize, err := h.uploadCardToDStaffWithSize(ctx, taskID, imageBase64, dstaffAuth)
		if err != nil {
			log.Printf("⚠️ Failed to upload card to DStaff: %v", err)
			// Create error response with attachment info - return MCP format directly
			mcpResult := h.createDStaffUploadResponseWithSize(taskID, imageBase64, dstaffAuth, false, err.Error(), 0)
			return h.convertMCPResultToContent(mcpResult), nil
		} else {
			log.Printf("✅ Successfully uploaded card to DStaff platform")
			// Create success response with attachment info - return MCP format directly
			mcpResult := h.createDStaffUploadResponseWithSize(taskID, imageBase64, dstaffAuth, true, "", fileSize)
			return h.convertMCPResultToContent(mcpResult), nil
		}
	} else {
		log.Printf("📋 Card generation completed for task_id: %s (no DStaff upload)", taskID)

		// Return only image content in MCP format
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "image",
					"data":     imageBase64,
					"mimeType": "image/png",
				},
			},
			"success": true,
		}

		return result, nil
	}
}

// GenerateDirectCardFromText handles direct card generation from text content (no summarization)
func (h *ToolHandlers) GenerateDirectCardFromText(args map[string]interface{}) (map[string]interface{}, error) {
	return h.GenerateDirectCardFromTextWithProgress(context.Background(), mcp.CallToolRequest{}, args)
}

// GenerateDirectCardFromTextWithProgress handles direct card generation from text content with progress reporting (no summarization)
func (h *ToolHandlers) GenerateDirectCardFromTextWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	text, ok := args["text"].(string)
	if !ok || text == "" {
		return nil, fmt.Errorf("text parameter is required")
	}

	// Extract optional style prompt
	stylePrompt, _ := args["style_prompt"].(string)

	// Extract task ID from Context parameter (DStaff integration)
	taskID := h.extractTaskIDFromRequest(args)
	log.Printf("📋 Extracted task_id from request: %s", taskID)

	// Also extract from context for async operations compatibility
	if taskID == "" {
		taskID = h.extractTaskID(ctx, request)
	}

	// 使用工具处理器中预设的进度发送函数，如果没有则使用默认的日志记录
	var progressSendFunc func(notification *mcp.ProgressNotification) error
	var logSendFunc func(notification *mcp.LoggingMessageNotification) error

	if h.progressSendFunc != nil {
		progressSendFunc = h.progressSendFunc
	} else {
		progressSendFunc = func(notification *mcp.ProgressNotification) error {
			log.Printf("📊 Progress: %s", notification.Params.Message)
			return nil
		}
	}

	if h.logSendFunc != nil {
		logSendFunc = h.logSendFunc
	} else {
		logSendFunc = func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Log: %s", notification.Params.Data)
			return nil
		}
	}

	// 创建进度报告器
	reporter := progress.NewReporter(taskID, 3, progressSendFunc)
	reporter.SetLogSendFunc(logSendFunc)

	// 报告开始
	if err := reporter.ReportStep(0, "开始直接生成智能卡片（无总结）..."); err != nil {
		log.Printf("⚠️ Failed to send start notification: %v", err)
	}

	log.Printf("🚀 开始直接从文本生成智能卡片（无总结）")
	log.Printf("📝 文本长度: %d 字符", len(text))
	if stylePrompt != "" {
		log.Printf("🎨 风格提示: %s", stylePrompt)
	}

	// Step 1: 直接生成HTML卡片（跳过总结步骤）
	if err := reporter.ReportStep(1, "正在直接生成HTML卡片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🎨 Step 1: 直接生成HTML卡片")
	htmlContent, err := h.generateDirectHTMLCardFromContentWithStyle(text, stylePrompt)
	if err != nil {
		log.Printf("❌ HTML生成失败: %v", err)
		if reportErr := reporter.Error(fmt.Sprintf("HTML生成失败: %v", err)); reportErr != nil {
			log.Printf("⚠️ Failed to send error: %v", reportErr)
		}
		return nil, fmt.Errorf("failed to generate HTML card: %w", err)
	}

	log.Printf("✅ Step 1 完成，HTML内容长度: %d", len(htmlContent))

	// Step 2: 生成图片
	if err := reporter.ReportStep(2, "正在生成卡片图片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("📸 Step 2: 生成图片")
	imageBase64, err := h.convertHTMLToImage(htmlContent)
	if err != nil {
		log.Printf("❌ 图片生成失败: %v", err)
		if reportErr := reporter.Error(fmt.Sprintf("图片生成失败: %v", err)); reportErr != nil {
			log.Printf("⚠️ Failed to send error: %v", reportErr)
		}
		return nil, fmt.Errorf("failed to generate image: %w", err)
	}

	log.Printf("✅ Step 2 完成，图片生成成功")

	// Save generated content to output/saved directory
	go func() {
		// Create a temporary image file for saving
		tempImagePath := filepath.Join(h.config.OutputDir, fmt.Sprintf("temp_card_%s.png", taskID))
		if imageData, err := base64.StdEncoding.DecodeString(imageBase64); err == nil {
			if err := os.WriteFile(tempImagePath, imageData, 0644); err == nil {
				// Save all content (no summary for direct generation)
				if err := h.contentSaver.SaveContent("text_direct", text, "", stylePrompt, text, htmlContent, tempImagePath); err != nil {
					log.Printf("⚠️ Failed to save content: %v", err)
				}
				// Clean up temp file
				os.Remove(tempImagePath)
			}
		}
	}()

	// Step 3: 处理DStaff上传或返回结果
	if err := reporter.ReportStep(3, "正在处理结果..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	// Check if we should upload to DStaff
	dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, taskID)
	if h.shouldUploadToDStaff(dstaffAuth) {
		log.Printf("📤 Uploading card to DStaff platform...")

		// Upload to DStaff and return structured response
		fileSize, err := h.uploadCardToDStaffWithSize(ctx, taskID, imageBase64, dstaffAuth)
		if err != nil {
			log.Printf("❌ DStaff upload failed: %v", err)
			if reportErr := reporter.Error(fmt.Sprintf("DStaff上传失败: %v", err)); reportErr != nil {
				log.Printf("⚠️ Failed to send error: %v", reportErr)
			}
			return nil, fmt.Errorf("failed to upload to DStaff: %w", err)
		}

		log.Printf("📋 Direct card generation completed for task_id: %s (uploaded to DStaff, size: %d bytes)", taskID, fileSize)

		// Return DStaff upload response
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "text",
					"text":     fmt.Sprintf("智能卡片直接生成完成！已上传到DStaff平台，文件大小: %d 字节", fileSize),
					"metadata": map[string]interface{}{
						"task_id":     taskID,
						"file_size":   fileSize,
						"upload_path": fmt.Sprintf("mcps_upload/smart_cards/smart_card_%s.png", taskID),
						"generation_type": "direct",
					},
				},
			},
			"success": true,
		}

		return result, nil

	} else {
		log.Printf("📋 Direct card generation completed for task_id: %s (no DStaff upload)", taskID)

		// Return only image content in MCP format
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "image",
					"data":     imageBase64,
					"mimeType": "image/png",
				},
			},
			"success": true,
		}

		return result, nil
	}
}

// GenerateCardFromURL handles card generation from URL content
func (h *ToolHandlers) GenerateCardFromURL(args map[string]interface{}) (map[string]interface{}, error) {
	return h.GenerateCardFromURLWithProgress(context.Background(), mcp.CallToolRequest{}, args)
}

// GenerateCardFromURLWithProgress handles card generation from URL content with progress reporting
func (h *ToolHandlers) GenerateCardFromURLWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	url, ok := args["url"].(string)
	if !ok || url == "" {
		return nil, fmt.Errorf("url parameter is required")
	}

	// Extract optional summary prompt
	summaryPrompt, _ := args["summary_prompt"].(string)
	if summaryPrompt != "" {
		log.Printf("📝 Summary prompt provided: %s", summaryPrompt)
	}

	// Extract optional style prompt
	stylePrompt, _ := args["style_prompt"].(string)
	if stylePrompt != "" {
		log.Printf("🎨 Style prompt provided: %s", stylePrompt)
	}

	if h.config.JinaAPIKey == "" {
		return nil, fmt.Errorf("Jina API key not configured")
	}

	// Extract task ID from Context parameter (DStaff integration)
	taskID := h.extractTaskIDFromRequest(args)
	log.Printf("📋 Extracted task_id from request: %s", taskID)

	// Also extract from context for async operations compatibility
	if taskID == "" {
		taskID = h.extractTaskID(ctx, request)
		log.Printf("🔍 Debug: Extracted task ID from context: '%s'", taskID)
	}

	// 使用工具处理器中预设的进度发送函数，如果没有则使用默认的日志记录
	var progressSendFunc func(notification *mcp.ProgressNotification) error
	var logSendFunc func(notification *mcp.LoggingMessageNotification) error

	if h.progressSendFunc != nil {
		progressSendFunc = h.progressSendFunc
	} else {
		// 如果没有设置进度发送函数，使用默认的日志记录或任务管理器
		progressSendFunc = func(notification *mcp.ProgressNotification) error {
			log.Printf("🔍 Debug: Progress callback - taskID: '%s', taskManager: %v", taskID, h.taskManager != nil)
			if taskID != "" && h.taskManager != nil {
				// Update task progress if we have task ID and task manager
				current := int(notification.Params.Progress)
				total := int(notification.Params.Total)
				if total == 0 {
					total = 4 // default total steps
				}
				message := notification.Params.Message
				log.Printf("🔍 Debug: Calling UpdateTaskProgress - taskID: %s, current: %d, total: %d, message: %s", taskID, current, total, message)
				return h.taskManager.UpdateTaskProgress(taskID, current, total, message)
			}
			log.Printf("📊 Progress: %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)
			return nil
		}
	}

	if h.logSendFunc != nil {
		logSendFunc = h.logSendFunc
	} else {
		// 如果没有设置日志发送函数，使用默认的日志记录
		logSendFunc = func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Log [%s]: %s",
				string(notification.Params.Level),
				notification.Params.Data)
			return nil
		}
	}

	// 创建进度报告器 (4个主要步骤)
	reporter := progress.CreateProgressReporter(request, 4, progressSendFunc, logSendFunc, taskID)

	log.Printf("🌐 开始从URL生成卡片: %s", url)

	// Step 1: Fetch web content using Jina API
	if err := reporter.ReportStepWithDelay(1, "正在获取网页内容", 3*time.Second); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("📡 Step 1: Fetching web content...")
	content, err := h.fetchWebContent(url)
	if err != nil {
		reporter.Error(fmt.Sprintf("网页内容获取失败: %v", err))
		return nil, fmt.Errorf("failed to fetch web content: %w", err)
	}
	log.Printf("✅ Step 1 完成，获取内容长度: %d 字符", len(content))

	// Step 2: Summarize content first (following ref project pattern)
	if err := reporter.ReportStep(2, "正在分析和总结网页内容..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("📝 Step 2: Summarizing content...")
	summarizedContent, err := h.summarizeForCardWithPrompt(content, summaryPrompt)
	if err != nil {
		log.Printf("⚠️ Summarization failed, using original content: %v", err)
		summarizedContent = content
		if err := reporter.SendLogMessage("warning", fmt.Sprintf("内容总结失败，使用原始内容: %v", err)); err != nil {
			log.Printf("⚠️ Failed to send log: %v", err)
		}
	} else {
		log.Printf("✅ Step 2 完成，总结内容: %s", summarizedContent)
		if err := reporter.SendLogMessage("info", "网页内容总结完成"); err != nil {
			log.Printf("⚠️ Failed to send log: %v", err)
		}
	}

	// Step 3: Generate HTML card
	if err := reporter.ReportStep(3, "正在生成智能卡片设计..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🎨 Step 3: Generating HTML card...")
	htmlContent, err := h.generateHTMLCardFromContentWithStyle(summarizedContent, stylePrompt)
	if err != nil {
		reporter.Error(fmt.Sprintf("HTML卡片生成失败: %v", err))
		return nil, fmt.Errorf("failed to generate HTML card: %w", err)
	}
	log.Printf("✅ Step 3 完成，HTML卡片生成成功")

	// Step 4: Convert to image (always generate image)
	if err := reporter.ReportStep(4, "正在转换为高清图片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🖼️ Step 4: Converting to image...")
	imageBase64, err := h.convertHTMLToImage(htmlContent)
	if err != nil {
		reporter.Error(fmt.Sprintf("图片转换失败: %v", err))
		return nil, fmt.Errorf("failed to convert HTML to image: %w", err)
	}
	log.Printf("✅ Step 4 完成，图片生成成功")

	// Save generated content to output/saved directory
	go func() {
		// Create a temporary image file for saving
		tempImagePath := filepath.Join(h.config.OutputDir, fmt.Sprintf("temp_card_%s.png", taskID))
		if imageData, err := base64.StdEncoding.DecodeString(imageBase64); err == nil {
			if err := os.WriteFile(tempImagePath, imageData, 0644); err == nil {
				// Save all content
				if err := h.contentSaver.SaveContent("url", url, summaryPrompt, stylePrompt, summarizedContent, htmlContent, tempImagePath); err != nil {
					log.Printf("⚠️ Failed to save content: %v", err)
				}
				// Clean up temp file
				os.Remove(tempImagePath)
			}
		}
	}()

	// 报告完成
	if err := reporter.Complete("智能卡片生成完成！"); err != nil {
		log.Printf("⚠️ Failed to send completion: %v", err)
	}

	// Check if we should upload to DStaff directly (when DStaff context is available)
	dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, taskID)
	if h.shouldUploadToDStaff(dstaffAuth) {
		log.Printf("📤 DStaff context available, uploading card directly...")

		fileSize, err := h.uploadCardToDStaffWithSize(ctx, taskID, imageBase64, dstaffAuth)
		if err != nil {
			log.Printf("⚠️ Failed to upload card to DStaff: %v", err)
			// Create error response with attachment info - return MCP format directly
			mcpResult := h.createDStaffUploadResponseWithSize(taskID, imageBase64, dstaffAuth, false, err.Error(), 0)
			return h.convertMCPResultToContent(mcpResult), nil
		} else {
			log.Printf("✅ Successfully uploaded card to DStaff platform")
			// Create success response with attachment info - return MCP format directly
			mcpResult := h.createDStaffUploadResponseWithSize(taskID, imageBase64, dstaffAuth, true, "", fileSize)
			return h.convertMCPResultToContent(mcpResult), nil
		}
	} else {
		log.Printf("📋 Card generation completed for task_id: %s (no DStaff upload)", taskID)

		// Return only image content in MCP format
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "image",
					"data":     imageBase64,
					"mimeType": "image/png",
				},
			},
			"success": true,
		}

		return result, nil
	}
}

// GenerateDirectCardFromURL handles direct card generation from URL content (no summarization)
func (h *ToolHandlers) GenerateDirectCardFromURL(args map[string]interface{}) (map[string]interface{}, error) {
	return h.GenerateDirectCardFromURLWithProgress(context.Background(), mcp.CallToolRequest{}, args)
}

// GenerateDirectCardFromURLWithProgress handles direct card generation from URL content with progress reporting (no summarization)
func (h *ToolHandlers) GenerateDirectCardFromURLWithProgress(ctx context.Context, request mcp.CallToolRequest, args map[string]interface{}) (map[string]interface{}, error) {
	url, ok := args["url"].(string)
	if !ok || url == "" {
		return nil, fmt.Errorf("url parameter is required")
	}

	// Extract optional style prompt
	stylePrompt, _ := args["style_prompt"].(string)

	if h.config.JinaAPIKey == "" {
		return nil, fmt.Errorf("Jina API key not configured")
	}

	// Extract task ID from Context parameter (DStaff integration)
	taskID := h.extractTaskIDFromRequest(args)
	log.Printf("📋 Extracted task_id from request: %s", taskID)

	// Also extract from context for async operations compatibility
	if taskID == "" {
		taskID = h.extractTaskID(ctx, request)
		log.Printf("🔍 Debug: Extracted task ID from context: '%s'", taskID)
	}

	// 使用工具处理器中预设的进度发送函数，如果没有则使用默认的日志记录
	var progressSendFunc func(notification *mcp.ProgressNotification) error
	var logSendFunc func(notification *mcp.LoggingMessageNotification) error

	if h.progressSendFunc != nil {
		progressSendFunc = h.progressSendFunc
	} else {
		progressSendFunc = func(notification *mcp.ProgressNotification) error {
			log.Printf("📊 Progress: %s", notification.Params.Message)
			return nil
		}
	}

	if h.logSendFunc != nil {
		logSendFunc = h.logSendFunc
	} else {
		logSendFunc = func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Log: %s", notification.Params.Data)
			return nil
		}
	}

	// 创建进度报告器
	reporter := progress.NewReporter(taskID, 4, progressSendFunc)
	reporter.SetLogSendFunc(logSendFunc)

	// 报告开始
	if err := reporter.ReportStep(0, "开始直接从URL生成智能卡片（无总结）..."); err != nil {
		log.Printf("⚠️ Failed to send start notification: %v", err)
	}

	log.Printf("🚀 开始直接从URL生成智能卡片（无总结）")
	log.Printf("🌐 URL: %s", url)
	if stylePrompt != "" {
		log.Printf("🎨 风格提示: %s", stylePrompt)
	}

	// Step 1: 获取网页内容
	if err := reporter.ReportStep(1, "正在获取网页内容..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🌐 Step 1: 获取网页内容")
	content, err := h.fetchWebContent(url)
	if err != nil {
		log.Printf("❌ 网页内容获取失败: %v", err)
		if reportErr := reporter.Error(fmt.Sprintf("网页内容获取失败: %v", err)); reportErr != nil {
			log.Printf("⚠️ Failed to send error: %v", reportErr)
		}
		return nil, fmt.Errorf("failed to fetch web content: %w", err)
	}

	log.Printf("✅ Step 1 完成，内容长度: %d", len(content))

	// Step 2: 直接生成HTML卡片（跳过总结步骤）
	if err := reporter.ReportStep(2, "正在直接生成HTML卡片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("🎨 Step 2: 直接生成HTML卡片")
	htmlContent, err := h.generateDirectHTMLCardFromContentWithStyle(content, stylePrompt)
	if err != nil {
		log.Printf("❌ HTML生成失败: %v", err)
		if reportErr := reporter.Error(fmt.Sprintf("HTML生成失败: %v", err)); reportErr != nil {
			log.Printf("⚠️ Failed to send error: %v", reportErr)
		}
		return nil, fmt.Errorf("failed to generate HTML card: %w", err)
	}

	log.Printf("✅ Step 2 完成，HTML内容长度: %d", len(htmlContent))

	// Step 3: 生成图片
	if err := reporter.ReportStep(3, "正在生成卡片图片..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	log.Printf("📸 Step 3: 生成图片")
	imageBase64, err := h.convertHTMLToImage(htmlContent)
	if err != nil {
		log.Printf("❌ 图片生成失败: %v", err)
		if reportErr := reporter.Error(fmt.Sprintf("图片生成失败: %v", err)); reportErr != nil {
			log.Printf("⚠️ Failed to send error: %v", reportErr)
		}
		return nil, fmt.Errorf("failed to generate image: %w", err)
	}

	log.Printf("✅ Step 3 完成，图片生成成功")

	// Save generated content to output/saved directory
	go func() {
		// Create a temporary image file for saving
		tempImagePath := filepath.Join(h.config.OutputDir, fmt.Sprintf("temp_card_%s.png", taskID))
		if imageData, err := base64.StdEncoding.DecodeString(imageBase64); err == nil {
			if err := os.WriteFile(tempImagePath, imageData, 0644); err == nil {
				// Save all content (no summary for direct generation)
				if err := h.contentSaver.SaveContent("url_direct", url, "", stylePrompt, content, htmlContent, tempImagePath); err != nil {
					log.Printf("⚠️ Failed to save content: %v", err)
				}
				// Clean up temp file
				os.Remove(tempImagePath)
			}
		}
	}()

	// Step 4: 处理DStaff上传或返回结果
	if err := reporter.ReportStep(4, "正在处理结果..."); err != nil {
		log.Printf("⚠️ Failed to send progress: %v", err)
	}

	// Check if we should upload to DStaff
	dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, taskID)
	if h.shouldUploadToDStaff(dstaffAuth) {
		log.Printf("📤 Uploading card to DStaff platform...")

		// Upload to DStaff and return structured response
		fileSize, err := h.uploadCardToDStaffWithSize(ctx, taskID, imageBase64, dstaffAuth)
		if err != nil {
			log.Printf("❌ DStaff upload failed: %v", err)
			if reportErr := reporter.Error(fmt.Sprintf("DStaff上传失败: %v", err)); reportErr != nil {
				log.Printf("⚠️ Failed to send error: %v", reportErr)
			}
			return nil, fmt.Errorf("failed to upload to DStaff: %w", err)
		}

		log.Printf("📋 Direct card generation from URL completed for task_id: %s (uploaded to DStaff, size: %d bytes)", taskID, fileSize)

		// Return DStaff upload response
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "text",
					"text":     fmt.Sprintf("智能卡片直接生成完成！已上传到DStaff平台，文件大小: %d 字节", fileSize),
					"metadata": map[string]interface{}{
						"task_id":     taskID,
						"file_size":   fileSize,
						"upload_path": fmt.Sprintf("mcps_upload/smart_cards/smart_card_%s.png", taskID),
						"generation_type": "direct",
						"source_url": url,
					},
				},
			},
			"success": true,
		}

		return result, nil

	} else {
		log.Printf("📋 Direct card generation from URL completed for task_id: %s (no DStaff upload)", taskID)

		// Return only image content in MCP format
		result := map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type":     "image",
					"data":     imageBase64,
					"mimeType": "image/png",
				},
			},
			"success": true,
		}

		return result, nil
	}
}

// === 异步工具处理器 (Async Tool Handlers) ===

// CreateTaskFromText creates an async task for text-based card generation
func (h *ToolHandlers) CreateTaskFromText(args map[string]interface{}) (map[string]interface{}, error) {
	text, ok := args["text"].(string)
	if !ok || text == "" {
		return nil, fmt.Errorf("text parameter is required")
	}

	if h.taskManager == nil {
		return nil, fmt.Errorf("task manager not available")
	}

	// Extract task ID from Context parameter (DStaff integration)
	contextTaskID := h.extractTaskIDFromRequest(args)
	if contextTaskID != "" {
		log.Printf("📋 Extracted task_id from Context for async task: %s", contextTaskID)
		// Store the task_id in args for later use
		args["dstaff_task_id"] = contextTaskID
	}

	// Create async task
	taskInterface, err := h.taskManager.CreateTask("generate_card_from_text", args, "")
	if err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Convert to map for JSON response
	taskMap, ok := taskInterface.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected task type returned from CreateTask")
	}

	taskID, _ := taskMap["task_id"].(string)
	log.Printf("📋 Created async task %s for text generation", taskID)

	// Start task execution in background
	if h.taskExecutor != nil {
		go h.taskExecutor.ExecuteTask(taskID)
	}

	return map[string]interface{}{
		"task_id":    taskMap["task_id"],
		"status":     taskMap["status"],
		"created_at": taskMap["created_at"],
		"tool_name":  taskMap["tool_name"],
		"message":    "异步任务已创建，请使用task_id查询状态和获取结果",
	}, nil
}

// CreateTaskFromURL creates an async task for URL-based card generation
func (h *ToolHandlers) CreateTaskFromURL(args map[string]interface{}) (map[string]interface{}, error) {
	url, ok := args["url"].(string)
	if !ok || url == "" {
		return nil, fmt.Errorf("url parameter is required")
	}

	if h.taskManager == nil {
		return nil, fmt.Errorf("task manager not available")
	}

	// Extract task ID from Context parameter (DStaff integration)
	contextTaskID := h.extractTaskIDFromRequest(args)
	if contextTaskID != "" {
		log.Printf("📋 Extracted task_id from Context for async URL task: %s", contextTaskID)
		// Store the task_id in args for later use
		args["dstaff_task_id"] = contextTaskID
	}

	// Create async task
	taskInterface, err := h.taskManager.CreateTask("generate_card_from_url", args, "")
	if err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Convert to map for JSON response
	taskMap, ok := taskInterface.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected task type returned from CreateTask")
	}

	taskID, _ := taskMap["task_id"].(string)
	log.Printf("📋 Created async task %s for URL generation", taskID)

	// Start task execution in background
	if h.taskExecutor != nil {
		go h.taskExecutor.ExecuteTask(taskID)
	}

	return map[string]interface{}{
		"task_id":    taskMap["task_id"],
		"status":     taskMap["status"],
		"created_at": taskMap["created_at"],
		"tool_name":  taskMap["tool_name"],
		"message":    "异步任务已创建，请使用task_id查询状态和获取结果",
	}, nil
}

// GetTaskStatus retrieves the status of an async task
func (h *ToolHandlers) GetTaskStatus(args map[string]interface{}) (map[string]interface{}, error) {
	taskID, ok := args["task_id"].(string)
	if !ok || taskID == "" {
		return nil, fmt.Errorf("task_id parameter is required")
	}

	if h.taskManager == nil {
		return nil, fmt.Errorf("task manager not available")
	}

	// Get task status
	taskInterface, err := h.taskManager.GetTask(taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Convert to map for JSON response
	taskMap, ok := taskInterface.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected task type returned from GetTask")
	}

	result := map[string]interface{}{
		"task_id":    taskMap["task_id"],
		"status":     taskMap["status"],
		"tool_name":  taskMap["tool_name"],
		"created_at": taskMap["created_at"],
		"progress":   taskMap["progress"],
	}

	// Add timestamps based on status
	if startedAt, exists := taskMap["started_at"]; exists && startedAt != nil {
		result["started_at"] = startedAt
	}
	if completedAt, exists := taskMap["completed_at"]; exists && completedAt != nil {
		result["completed_at"] = completedAt
	}

	// Add error information if failed
	status, _ := taskMap["status"].(string)
	if status == "failed" {
		if errorMsg, exists := taskMap["error"]; exists && errorMsg != nil {
			result["error"] = errorMsg
		}
	}

	// Add status message
	switch status {
	case "pending":
		result["message"] = "任务等待执行中"
	case "running":
		result["message"] = "任务执行中"
		if progress, exists := taskMap["progress"]; exists && progress != nil {
			if progressMap, ok := progress.(map[string]interface{}); ok {
				// Extract progress details
				current, _ := progressMap["current"].(int)
				total, _ := progressMap["total"].(int)
				message, _ := progressMap["message"].(string)

				if total > 0 {
					percentage := current * 100 / total
					if message != "" {
						result["message"] = fmt.Sprintf("步骤 %d/%d (%d%%) - %s", current, total, percentage, message)
					} else {
						result["message"] = fmt.Sprintf("任务执行中，进度: %d%% (%d/%d)", percentage, current, total)
					}
				} else if message != "" {
					result["message"] = fmt.Sprintf("任务执行中 - %s", message)
				}
			}
		}
	case "completed":
		result["message"] = "任务已完成，可以获取结果"
	case "failed":
		result["message"] = "任务执行失败"
	case "cancelled":
		result["message"] = "任务已取消"
	default:
		result["message"] = "未知状态"
	}

	log.Printf("📊 Task %s status: %s", taskID, status)

	return result, nil
}

// GetTaskResult retrieves the result of a completed async task
func (h *ToolHandlers) GetTaskResult(args map[string]interface{}) (map[string]interface{}, error) {
	return h.GetTaskResultWithContext(context.Background(), args)
}

// GetTaskResultWithContext retrieves the result of a completed async task with context
func (h *ToolHandlers) GetTaskResultWithContext(ctx context.Context, args map[string]interface{}) (map[string]interface{}, error) {
	taskID, ok := args["task_id"].(string)
	if !ok || taskID == "" {
		return nil, fmt.Errorf("task_id parameter is required")
	}

	if h.taskManager == nil {
		return nil, fmt.Errorf("task manager not available")
	}

	// Get task
	taskInterface, err := h.taskManager.GetTask(taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Convert to map for JSON response
	taskMap, ok := taskInterface.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected task type returned from GetTask")
	}

	// Check if task is completed
	status, _ := taskMap["status"].(string)
	if status != "completed" {
		return nil, fmt.Errorf("task is not completed yet, current status: %s", status)
	}

	// Get task result
	result, err := h.taskManager.GetTaskResult(taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task result: %w", err)
	}

	log.Printf("📥 Retrieved result for task %s", taskID)

	// Extract task ID from Context parameter (DStaff integration)
	contextTaskID := h.extractTaskIDFromRequest(args)
	if contextTaskID != "" {
		log.Printf("📋 Extracted task_id from Context for result retrieval: %s", contextTaskID)
	}

	// Check if result contains image data and format it properly
	log.Printf("🔍 === GetTaskResult Branch Analysis ===")
	if resultMap, ok := result.(map[string]interface{}); ok {
		log.Printf("✅ Branch 1: Result is a map[string]interface{}")
		log.Printf("🔧 Result map keys: %v", getMapKeys(resultMap))

		// Check for direct image field (old format)
		if imageData, exists := resultMap["image"]; exists {
			log.Printf("✅ Branch 1.1: Found direct 'image' field (old format)")
			if imageBase64, ok := imageData.(string); ok {
				log.Printf("✅ Branch 1.1.1: Image data is string, length: %d", len(imageBase64))

				// Create DStaff auth context
				dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, contextTaskID)

				// If DStaff is enabled and auth context is available, upload the image to DStaff platform
				if h.dstaffConfig != nil && h.dstaffConfig.Enabled && dstaffAuth != nil && dstaffAuth.Token != "" && dstaffAuth.TaskID != "" {
					log.Printf("🔗 DStaff enabled, attempting to upload card image to DStaff platform")

					fileSize, err := h.uploadCardToDStaffWithSize(ctx, contextTaskID, imageBase64, dstaffAuth)
					if err != nil {
						log.Printf("⚠️ Failed to upload card to DStaff: %v", err)
						// Don't fail the entire operation, just add error info to the response
						mcpResult := h.createDStaffUploadResponseWithSize(contextTaskID, imageBase64, dstaffAuth, false, err.Error(), 0)
						return h.convertMCPResultToMap(mcpResult), nil
					} else {
						log.Printf("✅ Successfully uploaded card to DStaff platform")
						// Create success response with attachment info
						mcpResult := h.createDStaffUploadResponseWithSize(contextTaskID, imageBase64, dstaffAuth, true, "", fileSize)
						return h.convertMCPResultToMap(mcpResult), nil
					}
				} else {
					log.Printf("⚠️ DStaff upload skipped - either disabled or missing auth context")
				}

				// Fallback: Return image in MCP format if DStaff upload not applicable
				log.Printf("📤 Returning image in MCP format (converted from old format)")
				return map[string]interface{}{
					"content": []map[string]interface{}{
						{
							"type":     "image",
							"data":     imageBase64,
							"mimeType": "image/png",
						},
					},
					"success": true,
				}, nil
			} else {
				log.Printf("❌ Branch 1.1.2: Image data is not string, type: %T", imageData)
			}
		} else {
			log.Printf("⚠️ Branch 1.2: No direct 'image' field found")
		}

		// Check for content array format (new format)
		if contentArray, exists := resultMap["content"]; exists {
			log.Printf("✅ Branch 1.3: Found 'content' field (new format)")
			log.Printf("🔧 Content field type: %T", contentArray)

			// Try different array types
			var contentSlice []interface{}
			var contentLength int

			// First try []interface{}
			if slice, ok := contentArray.([]interface{}); ok {
				contentSlice = slice
				contentLength = len(slice)
				log.Printf("✅ Branch 1.3.1a: Content is []interface{} with %d items", contentLength)
			} else if mapSlice, ok := contentArray.([]map[string]interface{}); ok {
				// Convert []map[string]interface{} to []interface{}
				contentSlice = make([]interface{}, len(mapSlice))
				for i, item := range mapSlice {
					contentSlice[i] = item
				}
				contentLength = len(mapSlice)
				log.Printf("✅ Branch 1.3.1b: Content is []map[string]interface{} with %d items, converted to []interface{}", contentLength)
			} else {
				log.Printf("❌ Branch 1.3.1c: Content is not a supported array type: %T", contentArray)
				contentLength = 0
			}

			if contentLength > 0 {
				log.Printf("✅ Branch 1.3.2: Processing first content item")
				if contentItem, ok := contentSlice[0].(map[string]interface{}); ok {
					log.Printf("✅ Branch 1.3.3: First content item is map")
					log.Printf("🔧 Content item keys: %v", getMapKeys(contentItem))
					if contentType, exists := contentItem["type"]; exists && contentType == "image" {
						log.Printf("✅ Branch 1.3.4: Content type is 'image'")

						// Try to upload to DStaff if enabled and context available
						if imageData, exists := contentItem["data"]; exists {
							if imageBase64, ok := imageData.(string); ok {
								log.Printf("✅ Branch 1.3.5: Image data found, length: %d", len(imageBase64))

								// Create DStaff auth context
								dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, contextTaskID)

								// If DStaff is enabled and auth context is available, upload the image to DStaff platform
								if h.dstaffConfig != nil && h.dstaffConfig.Enabled && dstaffAuth != nil && dstaffAuth.Token != "" && dstaffAuth.TaskID != "" {
									log.Printf("🔗 DStaff enabled, attempting to upload card image to DStaff platform")

									fileSize, err := h.uploadCardToDStaffWithSize(ctx, contextTaskID, imageBase64, dstaffAuth)
									if err != nil {
										log.Printf("⚠️ Failed to upload card to DStaff: %v", err)
										// Don't fail the entire operation, just add error info to the response
										mcpResult := h.createDStaffUploadResponseWithSize(contextTaskID, imageBase64, dstaffAuth, false, err.Error(), 0)
										return h.convertMCPResultToMap(mcpResult), nil
									} else {
										log.Printf("✅ Successfully uploaded card to DStaff platform")
										// Create success response with attachment info
										mcpResult := h.createDStaffUploadResponseWithSize(contextTaskID, imageBase64, dstaffAuth, true, "", fileSize)
										return h.convertMCPResultToMap(mcpResult), nil
									}
								} else {
									log.Printf("⚠️ DStaff upload skipped - either disabled or missing auth context")
								}
							} else {
								log.Printf("❌ Branch 1.3.6: Image data is not string, type: %T", imageData)
							}
						} else {
							log.Printf("⚠️ Branch 1.3.7: No 'data' field in content item")
						}

						// Fallback: Result is already in correct MCP format, return it directly
						log.Printf("📤 Returning result in original MCP format (new format)")
						return resultMap, nil
					} else {
						log.Printf("❌ Branch 1.3.8: Content type is not 'image', type: %v", contentType)
					}
				} else {
					log.Printf("❌ Branch 1.3.9: First content item is not map, type: %T", contentSlice[0])
				}
			} else {
				log.Printf("❌ Branch 1.3.10: Content array is empty or unsupported type")
			}
		} else {
			log.Printf("⚠️ Branch 1.4: No 'content' field found")
		}
	} else {
		log.Printf("❌ Branch 2: Result is not a map[string]interface{}, type: %T", result)
	}

	// Fallback: return result directly if not recognized as image data
	// For MCP tools, we should return the actual result, not task metadata
	if resultMap, ok := result.(map[string]interface{}); ok {
		log.Printf("✅ Branch 3: Fallback - returning result as map directly")
		log.Printf("🔧 Fallback result keys: %v", getMapKeys(resultMap))
		return resultMap, nil
	}

	// If result is not a map, wrap it in a simple response
	log.Printf("✅ Branch 4: Final fallback - wrapping non-map result")
	log.Printf("🔧 Final result type: %T, value: %v", result, result)
	return map[string]interface{}{
		"result": result,
	}, nil
}

// callLLM makes a request to LLM API (ARK preferred, fallback to OpenAI)
func (h *ToolHandlers) callLLM(prompt string, temperature float64) (string, error) {
	return h.callLLMWithSystem("", prompt, temperature)
}

// callLLMWithSystem makes a request to LLM API with system prompt
func (h *ToolHandlers) callLLMWithSystem(systemPrompt, userPrompt string, temperature float64) (string, error) {
	// Prefer ARK API if available
	if h.config.ARKAPIKey != "" {
		return h.callARKAPI(systemPrompt, userPrompt, temperature)
	}

	// Fallback to OpenAI API
	if h.config.OpenAIAPIKey != "" {
		return h.callOpenAIAPI(systemPrompt, userPrompt, temperature)
	}

	return "", fmt.Errorf("no API key configured for LLM service")
}

// callARKAPI makes a request to ARK API
func (h *ToolHandlers) callARKAPI(systemPrompt, userPrompt string, temperature float64) (string, error) {
	messages := []map[string]string{}

	if systemPrompt != "" {
		messages = append(messages, map[string]string{
			"role":    "system",
			"content": systemPrompt,
		})
	}

	messages = append(messages, map[string]string{
		"role":    "user",
		"content": userPrompt,
	})

	requestBody := map[string]interface{}{
		"model":       h.config.ARKModel,
		"messages":    messages,
		"temperature": temperature,
		// 移除max_tokens限制，让模型生成完整内容
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", h.config.ARKBaseURL+"/chat/completions", bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+h.config.ARKAPIKey)

	client := &http.Client{Timeout: 180 * time.Second} // ARK API may take longer
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Error *struct {
			Message string `json:"message"`
			Type    string `json:"type"`
		} `json:"error,omitempty"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Error != nil {
		return "", fmt.Errorf("ARK API error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from ARK API")
	}

	return strings.TrimSpace(response.Choices[0].Message.Content), nil
}

// callOpenAIAPI makes a request to OpenAI API (fallback)
func (h *ToolHandlers) callOpenAIAPI(systemPrompt, userPrompt string, temperature float64) (string, error) {
	messages := []Message{}

	if systemPrompt != "" {
		messages = append(messages, Message{Role: "system", Content: systemPrompt})
	}

	messages = append(messages, Message{Role: "user", Content: userPrompt})

	reqBody := OpenAIRequest{
		Model:       h.config.OpenAIModel,
		Messages:    messages,
		Temperature: temperature,
		// 移除MaxTokens限制，让模型生成完整内容
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", h.config.OpenAIBaseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+h.config.OpenAIAPIKey)

	client := &http.Client{Timeout: 180 * time.Second} // OpenAI API may also take longer
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	var openAIResp OpenAIResponse
	if err := json.Unmarshal(body, &openAIResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if openAIResp.Error != nil {
		return "", fmt.Errorf("OpenAI API error: %s", openAIResp.Error.Message)
	}

	if len(openAIResp.Choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	return strings.TrimSpace(openAIResp.Choices[0].Message.Content), nil
}

// summarizeForCard summarizes content specifically for card generation
func (h *ToolHandlers) summarizeForCard(content string) (string, error) {
	return h.summarizeForCardWithPrompt(content, "")
}

// summarizeForCardWithPrompt summarizes content with optional summary guidance
func (h *ToolHandlers) summarizeForCardWithPrompt(content string, summaryPrompt string) (string, error) {
	// Use prompts from prompts.go
	prompts := &SmartCardPrompts{}
	prompt := prompts.GetContentSummarizationPromptWithGuidance(content, summaryPrompt)

	summary, err := h.callLLM(prompt, 0.7)
	if err != nil {
		return "", fmt.Errorf("failed to summarize content: %w", err)
	}

	return strings.TrimSpace(summary), nil
}

// extractHTMLFromResponse extracts HTML content from LLM response
func (h *ToolHandlers) extractHTMLFromResponse(response string) string {
	// Try to find HTML content in various formats
	patterns := []string{
		"(?s)```html\\s*(.*?)\\s*```",             // ```html ... ```
		"(?s)```\\s*(<!DOCTYPE.*?</html>)\\s*```", // ``` <!DOCTYPE ... </html> ```
		"(?s)(<!DOCTYPE.*?</html>)",               // Direct HTML
		"(?s)(<html.*?</html>)",                   // <html>...</html>
		"(?s)(<div.*?</div>)",                     // <div>...</div> (for card content)
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(response)
		if len(matches) > 1 {
			html := strings.TrimSpace(matches[1])
			if html != "" {
				return html
			}
		}
	}

	// If no HTML pattern found, return the original response
	return response
}

// fetchWebContent fetches web content using Jina API
func (h *ToolHandlers) fetchWebContent(url string) (string, error) {
	jinaURL := h.config.JinaAPIURL + url

	req, err := http.NewRequest("GET", jinaURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+h.config.JinaAPIKey)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	return string(body), nil
}

// CardData represents data for generating HTML cards
type CardData struct {
	Title               string
	Content             string
	SourceURL           string
	Keywords            []string
	Style               string
	ColorTheme          string
	IncludeSummaryStats bool
	WordCount           int
	CharCount           int
	EstimatedReadTime   int
}

// generateHTMLCardFromContent generates HTML card directly from content using LLM (following ref project pattern)
func (h *ToolHandlers) generateHTMLCardFromContent(content string) (string, error) {
	return h.generateHTMLCardFromContentWithStyle(content, "")
}

// generateHTMLCardFromContentWithStyle generates HTML card with optional style prompt
func (h *ToolHandlers) generateHTMLCardFromContentWithStyle(content string, stylePrompt string) (string, error) {
	// Use prompts from prompts.go
	prompts := &SmartCardPrompts{}
	userPrompt := prompts.GetHTMLCardPromptWithStyle(content, stylePrompt)

	// Call LLM to generate HTML with system prompt
	response, err := h.callLLMWithSystem(SystemPromptCardDesigner, userPrompt, 0.7)
	if err != nil {
		return "", fmt.Errorf("failed to call LLM for HTML generation: %w", err)
	}

	// Extract HTML from response
	html := h.extractHTMLFromResponse(response)
	if html == "" {
		return "", fmt.Errorf("no valid HTML found in LLM response")
	}

	return html, nil
}

// generateDirectHTMLCardFromContentWithStyle generates HTML card directly from content with optional style prompt (no summarization)
func (h *ToolHandlers) generateDirectHTMLCardFromContentWithStyle(content string, stylePrompt string) (string, error) {
	// Use prompts from prompts.go
	prompts := &SmartCardPrompts{}
	userPrompt := prompts.GetDirectHTMLCardPromptWithStyle(content, stylePrompt)

	// Call LLM to generate HTML with system prompt
	response, err := h.callLLMWithSystem(SystemPromptCardDesigner, userPrompt, 0.7)
	if err != nil {
		return "", fmt.Errorf("failed to call LLM for direct HTML generation: %w", err)
	}

	// Extract HTML from response
	html := h.extractHTMLFromResponse(response)
	if html == "" {
		return "", fmt.Errorf("no valid HTML found in LLM response")
	}

	return html, nil
}

// generateHTMLCard generates an HTML card from card data (legacy function for compatibility)
func (h *ToolHandlers) generateHTMLCard(data CardData) (string, error) {
	// Use prompts from prompts.go
	prompts := &SmartCardPrompts{}
	userPrompt := prompts.GetHTMLCardPrompt(data.Content)

	// 调用LLM生成HTML - 使用参考项目的系统提示词
	modelName := h.config.ARKModel
	if modelName == "" {
		modelName = h.config.OpenAIModel
	}
	log.Printf("🤖 调用LLM生成HTML卡片，模型: %s", modelName)
	htmlContent, err := h.callLLMWithSystem(SystemPromptCardDesigner, userPrompt, 0.7)
	if err != nil {
		// 如果LLM调用失败，返回错误
		log.Printf("❌ LLM调用失败: %v", err)
		return "", fmt.Errorf("failed to call LLM for HTML generation: %w", err)
	}

	log.Printf("✅ LLM调用成功，响应长度: %d 字符", len(htmlContent))

	// 输出LLM原始响应（截取前500字符避免日志过长）
	if len(htmlContent) > 500 {
		log.Printf("📝 LLM原始响应（前500字符）: %s...", htmlContent[:500])
	} else {
		log.Printf("📝 LLM原始响应: %s", htmlContent)
	}

	// 提取HTML内容
	originalContent := htmlContent
	htmlContent = h.extractHTMLFromResponse(htmlContent)

	log.Printf("🔧 HTML提取完成，原始长度: %d -> 提取后长度: %d", len(originalContent), len(htmlContent))

	// 输出提取后的完整HTML
	log.Printf("🎨 提取后完整HTML:\n%s", htmlContent)

	// 检查是否包含393px宽度设置
	if strings.Contains(htmlContent, "393px") {
		log.Printf("✅ 检测到393px宽度设置，LLM生成符合要求")
	} else {
		log.Printf("⚠️  未检测到393px宽度设置，可能需要优化提示词")
	}

	return htmlContent, nil
}

// convertHTMLToImage converts HTML content to base64 encoded PNG image
func (h *ToolHandlers) convertHTMLToImage(htmlContent string) (string, error) {
	log.Printf("🖼️ 开始将HTML转换为图片...")

	converter := NewHTML2ImageConverter()
	options := &HTML2ImageOptions{
		Width:   393,  // iPhone 15 width - exact size
		Height:  2000, // Larger height to capture full content, will be clipped to actual content
		Quality: 100,  // Maximum quality
		Format:  "png",
	}

	imageBase64, err := converter.ConvertHTMLToImage(htmlContent, options)
	if err != nil {
		return "", fmt.Errorf("failed to convert HTML to image: %w", err)
	}

	log.Printf("✅ HTML转图片成功，图片大小: %d bytes", len(imageBase64))
	return imageBase64, nil
}

// getColorScheme returns color gradient, accent color, and dark accent color for a theme

func getColorScheme(theme string) (string, string, string) {
	switch theme {
	case "blue":
		return "#667eea, #764ba2", "#667eea", "#5a67d8"
	case "green":
		return "#11998e, #38ef7d", "#11998e", "#0d7377"
	case "purple":
		return "#667eea, #764ba2", "#764ba2", "#553c9a"
	case "orange":
		return "#f093fb, #f5576c", "#f093fb", "#e53e3e"
	case "red":
		return "#ff6b6b, #ee5a24", "#ff6b6b", "#e53e3e"
	case "cyan":
		return "#74b9ff, #0984e3", "#74b9ff", "#0984e3"
	case "yellow":
		return "#ffecd2, #fcb69f", "#f6ad55", "#ed8936"
	case "pink":
		return "#ffecd2, #fcb69f", "#ed64a6", "#d53f8c"
	default:
		return "#667eea, #764ba2", "#667eea", "#5a67d8"
	}
}

// sanitizeContent cleans and optimizes content for card display
func (h *ToolHandlers) sanitizeContent(content string) string {
	// Remove excessive whitespace
	content = strings.TrimSpace(content)

	// Replace multiple spaces with single space
	content = strings.ReplaceAll(content, "  ", " ")

	// Replace multiple newlines with double newlines
	lines := strings.Split(content, "\n")
	var cleanLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}
	content = strings.Join(cleanLines, "\n\n")

	// Remove HTML tags if present
	content = strings.ReplaceAll(content, "<", "&lt;")
	content = strings.ReplaceAll(content, ">", "&gt;")

	return content
}

// calculateReadTime estimates reading time in minutes
func calculateReadTime(text string) int {
	// Count Chinese characters and English words separately
	chineseCount := 0
	englishWordCount := 0

	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			chineseCount++
		}
	}

	englishWords := strings.Fields(text)
	for _, word := range englishWords {
		hasEnglish := false
		for _, r := range word {
			if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
				hasEnglish = true
				break
			}
		}
		if hasEnglish {
			englishWordCount++
		}
	}

	// Chinese: 300 characters per minute, English: 200 words per minute
	chineseTime := float64(chineseCount) / 300.0
	englishTime := float64(englishWordCount) / 200.0

	totalTime := int(chineseTime + englishTime)
	if totalTime < 1 {
		totalTime = 1
	}
	return totalTime
}

// Helper functions for argument parsing
func getStringArg(args map[string]interface{}, key, defaultValue string) string {
	if val, ok := args[key].(string); ok {
		return val
	}
	return defaultValue
}

func getIntArg(args map[string]interface{}, key string, defaultValue int) int {
	if val, ok := args[key].(string); ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			return intVal
		}
	}
	if val, ok := args[key].(float64); ok {
		return int(val)
	}
	if val, ok := args[key].(int); ok {
		return val
	}
	return defaultValue
}

func getBoolArg(args map[string]interface{}, key string, defaultValue bool) bool {
	if val, ok := args[key].(bool); ok {
		return val
	}
	if val, ok := args[key].(string); ok {
		if boolVal, err := strconv.ParseBool(val); err == nil {
			return boolVal
		}
	}
	return defaultValue
}

// generateSimpleCardImage generates a simple card image from content (following ref project pattern)
func (h *ToolHandlers) generateSimpleCardImage(content string) (string, error) {
	// Create a simple image with the content
	img := image.NewRGBA(image.Rect(0, 0, 393, 600)) // iPhone 15 width

	// Fill with white background
	for y := 0; y < 600; y++ {
		for x := 0; x < 393; x++ {
			img.Set(x, y, color.RGBA{255, 255, 255, 255})
		}
	}

	// Add a simple border
	for x := 0; x < 393; x++ {
		img.Set(x, 0, color.RGBA{200, 200, 200, 255})
		img.Set(x, 599, color.RGBA{200, 200, 200, 255})
	}
	for y := 0; y < 600; y++ {
		img.Set(0, y, color.RGBA{200, 200, 200, 255})
		img.Set(392, y, color.RGBA{200, 200, 200, 255})
	}

	// Convert to base64
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return "", fmt.Errorf("failed to encode image: %w", err)
	}

	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

// uploadCardToDStaffWithSize uploads a card image to DStaff platform and returns the file size
func (h *ToolHandlers) uploadCardToDStaffWithSize(ctx context.Context, contextTaskID, imageBase64 string, dstaffAuth *auth.DStaffAuthContext) (int64, error) {
	log.Printf("🔄 Processing card image for DStaff upload (task_id: %s)", contextTaskID)

	if imageBase64 == "" {
		return 0, fmt.Errorf("no image data provided")
	}

	// Decode base64 image to get actual file size
	imageData, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		return 0, fmt.Errorf("failed to decode base64 image: %w", err)
	}

	fileSize := int64(len(imageData))
	log.Printf("📊 Image size: %d bytes", fileSize)

	// Create temporary file for upload
	tempFile, err := os.CreateTemp("", "smart_card_*.png")
	if err != nil {
		return 0, fmt.Errorf("failed to create temporary file: %w", err)
	}
	defer func() {
		tempFile.Close()
		if err := os.Remove(tempFile.Name()); err != nil {
			log.Printf("⚠️ Failed to clean up temporary file %s: %v", tempFile.Name(), err)
		}
	}()

	// Write image data to temporary file
	if _, err := tempFile.Write(imageData); err != nil {
		return 0, fmt.Errorf("failed to write image data to temporary file: %w", err)
	}
	tempFile.Close()

	log.Printf("📁 Card image written to temporary file: %s", tempFile.Name())

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/smart_cards/smart_card_%s.png", contextTaskID)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, tempFile.Name(), targetPath)
	if err != nil {
		return 0, fmt.Errorf("failed to upload card to DStaff: %w", err)
	}

	log.Printf("📤 Card uploaded successfully to DStaff path: %s (task_id: %s)", targetPath, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📊 File size: %d bytes", fileSize)

	return fileSize, nil
}

// uploadCardToDStaff uploads the generated card image to DStaff platform (legacy function)
// Returns the upload response with actual file information from DStaff
func (h *ToolHandlers) uploadCardToDStaff(ctx context.Context, imageBase64 string, dstaffAuth *auth.DStaffAuthContext) (*auth.FileUploadResponse, error) {
	if !h.dstaffConfig.Enabled {
		return nil, fmt.Errorf("dstaff integration not enabled")
	}

	// Create a temporary file to save the image
	tempDir := h.config.OutputDir
	if tempDir == "" {
		tempDir = "output"
	}

	// Ensure output directory exists
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %w", err)
	}

	// Generate unique filename with UUID
	fileUUID := uuid.New().String()
	filename := fmt.Sprintf("smart_card_%s.png", fileUUID)
	localFilePath := filepath.Join(tempDir, filename)

	// Decode base64 image data
	imageData, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image data: %w", err)
	}

	// Write image to temporary file
	if err := os.WriteFile(localFilePath, imageData, 0644); err != nil {
		return nil, fmt.Errorf("failed to write image file: %w", err)
	}

	// Clean up temporary file after upload (only if auto cleanup is enabled)
	if h.config.AutoCleanupFiles {
		defer func() {
			if err := os.Remove(localFilePath); err != nil {
				log.Printf("⚠️ Failed to remove temporary file %s: %v", localFilePath, err)
			} else {
				log.Printf("🧹 Cleaned up temporary file: %s", localFilePath)
			}
		}()
	} else {
		log.Printf("📁 File saved to: %s (auto cleanup disabled)", localFilePath)
	}

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/smart_cards/%s", filename)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, localFilePath, targetPath)
	if err != nil {
		return nil, fmt.Errorf("failed to upload card to DStaff: %w", err)
	}

	log.Printf("📤 Card uploaded successfully to DStaff path: %s (task_id: %s)", uploadResponse.Attachments[0].Path, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📄 Actual filename from DStaff: %s", uploadResponse.Attachments[0].Filename)

	return uploadResponse, nil
}

// extractDStaffAuthContextWithTaskID creates DStaff auth context with provided task_id
func (h *ToolHandlers) extractDStaffAuthContextWithTaskID(ctx context.Context, taskID string) *auth.DStaffAuthContext {
	log.Printf("🔍 === DStaff Auth Context Creation with Task ID ===")
	log.Printf("🔧 DStaff Config Enabled: %v", h.dstaffConfig.Enabled)
	log.Printf("📋 Provided Task ID: %s", taskID)

	if !h.dstaffConfig.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping auth context creation")
		return nil
	}

	// Extract token from context
	token := ""
	if authToken := ctx.Value("authorization_token"); authToken != nil {
		if tokenStr, ok := authToken.(string); ok {
			token = tokenStr
			log.Printf("🔐 Authorization Token extracted from context: %s", maskToken(token))
		}
	} else {
		log.Printf("⚠️ No authorization_token found in context")
	}

	// Log context values for debugging
	log.Printf("🔍 Auth Context Debug Info:")
	log.Printf("   - Token present: %v", token != "")
	log.Printf("   - Task ID present: %v", taskID != "")
	log.Printf("   - Token length: %d", len(token))
	log.Printf("   - Task ID: '%s'", taskID)

	// Only return auth context if both token and task ID are present
	if token != "" && taskID != "" {
		authCtx := &auth.DStaffAuthContext{
			Token:  token,
			TaskID: taskID,
		}
		log.Printf("✅ DStaff Auth Context created successfully")
		log.Printf("   - Token: %s", maskToken(authCtx.Token))
		log.Printf("   - Task ID: %s", authCtx.TaskID)
		return authCtx
	}

	log.Printf("❌ DStaff Auth Context creation failed - missing required parameters")
	if token == "" {
		log.Printf("   - Missing: Authorization token")
	}
	if taskID == "" {
		log.Printf("   - Missing: Task ID")
	}

	return nil
}

// tryUploadToDStaff attempts to upload image to DStaff platform if conditions are met
// Returns the upload response if successful, nil otherwise
func (h *ToolHandlers) tryUploadToDStaff(ctx context.Context, contextTaskID, imageBase64 string) *auth.FileUploadResponse {
	log.Printf("🔍 === DStaff Upload Attempt ===")
	log.Printf("🔧 DStaff Config Enabled: %v", h.dstaffConfig.Enabled)
	log.Printf("📋 Context Task ID: %s", contextTaskID)
	log.Printf("🖼️ Image Data Length: %d", len(imageBase64))

	// Check if DStaff is enabled
	if !h.dstaffConfig.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping upload")
		return nil
	}

	// Check if we have a context task ID
	if contextTaskID == "" {
		log.Printf("⚠️ No context task_id provided, skipping DStaff upload")
		return nil
	}

	// Check if we have image data
	if imageBase64 == "" {
		log.Printf("⚠️ No image data provided, skipping DStaff upload")
		return nil
	}

	// Create DStaff auth context with context task ID
	dstaffAuth := h.extractDStaffAuthContextWithTaskID(ctx, contextTaskID)
	if dstaffAuth != nil {
		log.Printf("📤 DStaff upload conditions met:")
		log.Printf("   - DStaff enabled: ✅")
		log.Printf("   - Task ID available: ✅ (%s)", contextTaskID)
		log.Printf("   - Image data available: ✅ (%d bytes)", len(imageBase64))
		log.Printf("   - Authorization token: ✅")

		log.Printf("📤 Uploading card to DStaff platform...")
		if uploadResponse, err := h.uploadCardToDStaff(ctx, imageBase64, dstaffAuth); err != nil {
			log.Printf("⚠️ Failed to upload card to DStaff: %v", err)
			return nil // Upload failed
		} else {
			log.Printf("✅ Successfully uploaded card to DStaff platform")
			log.Printf("📁 Final file path: %s", uploadResponse.Attachments[0].Path)
			log.Printf("📄 Final filename: %s", uploadResponse.Attachments[0].Filename)
			return uploadResponse // Return actual upload response
		}
	} else {
		log.Printf("⚠️ DStaff auth context not available - skipping upload")
		log.Printf("   - Missing: Authorization token or task ID")
		return nil
	}
}

// createDStaffUploadResponseWithSize creates a MCP response for DStaff card upload operations with file size
func (h *ToolHandlers) createDStaffUploadResponseWithSize(contextTaskID, imageBase64 string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string, fileSize int64) *mcp.CallToolResult {
	targetPath := fmt.Sprintf("mcps_upload/smart_cards/smart_card_%s.png", contextTaskID)
	filename := fmt.Sprintf("smart_card_%s.png", contextTaskID)

	var response *auth.FileUploadResponse

	if success {
		// Create successful response using the same format as the reference
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "success",
			Message:     fmt.Sprintf("智能卡片上传成功！上传的文件路径：%s", targetPath),
			Attachments: []auth.Attachment{
				{
					Path:          targetPath,
					Filename:      filename,
					Type:          "file",
					ContentType:   "image/png",
					ContentLength: fileSize,
				},
			},
		}
	} else {
		// Create error response
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "error",
			Message:     fmt.Sprintf("智能卡片上传失败：%s", errorMsg),
			Attachments: []auth.Attachment{},
		}
	}

	// Convert to JSON
	responseJSON, _ := json.Marshal(response)

	// Return the JSON response
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(responseJSON),
			},
		},
		IsError: !success,
	}
}

// createDStaffUploadResponse creates a MCP response for DStaff card upload operations (legacy function)
// This function now performs the actual upload and uses the real response from DStaff
func (h *ToolHandlers) createDStaffUploadResponse(contextTaskID, imageBase64 string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string) *mcp.CallToolResult {
	return h.createDStaffUploadResponseWithSize(contextTaskID, imageBase64, dstaffAuth, success, errorMsg, 0)
}

// shouldUploadToDStaff checks if we should upload to DStaff platform
func (h *ToolHandlers) shouldUploadToDStaff(dstaffAuth *auth.DStaffAuthContext) bool {
	if !h.dstaffConfig.Enabled {
		log.Printf("⚠️ DStaff integration disabled")
		return false
	}

	if dstaffAuth == nil {
		log.Printf("⚠️ DStaff auth context is nil")
		return false
	}

	if dstaffAuth.Token == "" {
		log.Printf("⚠️ DStaff auth token is empty")
		return false
	}

	if dstaffAuth.TaskID == "" {
		log.Printf("⚠️ DStaff task ID is empty")
		return false
	}

	log.Printf("✅ DStaff upload conditions met (enabled: %v, token: present, task_id: %s)",
		h.dstaffConfig.Enabled, dstaffAuth.TaskID)
	return true
}

// convertMCPResultToMap converts MCP CallToolResult to map[string]interface{}
func (h *ToolHandlers) convertMCPResultToMap(result *mcp.CallToolResult) map[string]interface{} {
	if result == nil {
		return map[string]interface{}{
			"success": false,
			"error":   "empty result",
		}
	}

	if len(result.Content) > 0 {
		if textContent, ok := result.Content[0].(mcp.TextContent); ok {
			// Try to parse the text content as JSON (for DStaff responses)
			var jsonContent map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonContent); err == nil {
				// If it's valid JSON, return it directly
				return jsonContent
			} else {
				// If it's not JSON, return as a simple message
				return map[string]interface{}{
					"success": !result.IsError,
					"message": textContent.Text,
				}
			}
		}
	}

	return map[string]interface{}{
		"success": !result.IsError,
	}
}

// convertMCPResultToContent converts MCP CallToolResult to content format for DStaff responses
func (h *ToolHandlers) convertMCPResultToContent(result *mcp.CallToolResult) map[string]interface{} {
	if result == nil {
		return map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": "Error: empty result",
				},
			},
			"success": false,
		}
	}

	// Convert MCP content to content array format
	contentArray := make([]map[string]interface{}, len(result.Content))
	for i, content := range result.Content {
		switch c := content.(type) {
		case mcp.TextContent:
			contentArray[i] = map[string]interface{}{
				"type": c.Type,
				"text": c.Text,
			}
		case mcp.ImageContent:
			contentArray[i] = map[string]interface{}{
				"type":     c.Type,
				"data":     c.Data,
				"mimeType": c.MIMEType,
			}
		default:
			contentArray[i] = map[string]interface{}{
				"type": "text",
				"text": fmt.Sprintf("%v", content),
			}
		}
	}

	return map[string]interface{}{
		"content": contentArray,
		"success": !result.IsError,
	}
}

// maskToken masks a token for safe logging
func maskToken(token string) string {
	if token == "" {
		return "Not set"
	}
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// SavedContentMetadata represents metadata for saved content
type SavedContentMetadata struct {
	TaskID        string    `json:"task_id"`
	CreatedAt     time.Time `json:"created_at"`
	InputType     string    `json:"input_type"` // "text" or "url"
	InputContent  string    `json:"input_content"`
	SummaryPrompt string    `json:"summary_prompt,omitempty"`
	StylePrompt   string    `json:"style_prompt,omitempty"`
	Files         struct {
		Summary  string `json:"summary"`
		HTML     string `json:"html"`
		Image    string `json:"image"`
		Metadata string `json:"metadata"`
	} `json:"files"`
	FileSizes struct {
		Summary  int64 `json:"summary_bytes"`
		HTML     int64 `json:"html_bytes"`
		Image    int64 `json:"image_bytes"`
		Metadata int64 `json:"metadata_bytes"`
	} `json:"file_sizes"`
	Status string `json:"status"` // "processing", "completed", "error"
}

// SavedContentManager manages saving generated content
type SavedContentManager struct {
	baseDir string
	enabled bool
}

// NewSavedContentManager creates a new SavedContentManager
func NewSavedContentManager(config *config.Config) *SavedContentManager {
	return &SavedContentManager{
		baseDir: filepath.Join(config.OutputDir, "saved"),
		enabled: config.SaveGeneratedContent,
	}
}

// generateTaskID generates a unique task ID based on input content
func (scm *SavedContentManager) generateTaskID(inputContent string) string {
	hash := md5.Sum([]byte(inputContent + time.Now().Format("2006-01-02-15:04:05")))
	return hex.EncodeToString(hash[:])[:12]
}

// getTaskDir returns the directory path for a specific task
func (scm *SavedContentManager) getTaskDir(taskID string) string {
	dateDir := time.Now().Format("2006-01-02")
	return filepath.Join(scm.baseDir, dateDir, "task_"+taskID)
}

// ensureTaskDir creates the task directory if it doesn't exist
func (scm *SavedContentManager) ensureTaskDir(taskDir string) error {
	return os.MkdirAll(taskDir, 0755)
}

// SaveContent saves all generated content for a task
func (scm *SavedContentManager) SaveContent(inputType, inputContent, summaryPrompt, stylePrompt, summary, htmlContent, imagePath string) error {
	if !scm.enabled {
		return nil
	}

	taskID := scm.generateTaskID(inputContent)
	taskDir := scm.getTaskDir(taskID)

	if err := scm.ensureTaskDir(taskDir); err != nil {
		return fmt.Errorf("failed to create task directory: %w", err)
	}

	// Create metadata
	metadata := SavedContentMetadata{
		TaskID:        taskID,
		CreatedAt:     time.Now(),
		InputType:     inputType,
		InputContent:  inputContent,
		SummaryPrompt: summaryPrompt,
		StylePrompt:   stylePrompt,
		Status:        "processing",
	}

	// Set file paths
	metadata.Files.Summary = "summary.md"
	metadata.Files.HTML = "card.html"
	metadata.Files.Image = "card.png"
	metadata.Files.Metadata = "metadata.json"

	// Save summary
	if summary != "" {
		summaryPath := filepath.Join(taskDir, metadata.Files.Summary)
		if err := scm.saveTextFile(summaryPath, summary); err != nil {
			log.Printf("⚠️ Failed to save summary: %v", err)
		} else {
			if stat, err := os.Stat(summaryPath); err == nil {
				metadata.FileSizes.Summary = stat.Size()
			}
		}
	}

	// Save HTML
	if htmlContent != "" {
		htmlPath := filepath.Join(taskDir, metadata.Files.HTML)
		if err := scm.saveTextFile(htmlPath, htmlContent); err != nil {
			log.Printf("⚠️ Failed to save HTML: %v", err)
		} else {
			if stat, err := os.Stat(htmlPath); err == nil {
				metadata.FileSizes.HTML = stat.Size()
			}
		}
	}

	// Copy image
	if imagePath != "" && scm.fileExists(imagePath) {
		destImagePath := filepath.Join(taskDir, metadata.Files.Image)
		if err := scm.copyFile(imagePath, destImagePath); err != nil {
			log.Printf("⚠️ Failed to copy image: %v", err)
		} else {
			if stat, err := os.Stat(destImagePath); err == nil {
				metadata.FileSizes.Image = stat.Size()
			}
		}
	}

	// Mark as completed
	metadata.Status = "completed"

	// Save metadata
	metadataPath := filepath.Join(taskDir, metadata.Files.Metadata)
	if err := scm.saveMetadata(metadataPath, &metadata); err != nil {
		log.Printf("⚠️ Failed to save metadata: %v", err)
	} else {
		if stat, err := os.Stat(metadataPath); err == nil {
			metadata.FileSizes.Metadata = stat.Size()
		}
		log.Printf("✅ Content saved to: %s", taskDir)
	}

	return nil
}

// saveTextFile saves text content to a file
func (scm *SavedContentManager) saveTextFile(path, content string) error {
	return os.WriteFile(path, []byte(content), 0644)
}

// saveMetadata saves metadata as JSON
func (scm *SavedContentManager) saveMetadata(path string, metadata *SavedContentMetadata) error {
	data, err := json.MarshalIndent(metadata, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(path, data, 0644)
}

// copyFile copies a file from src to dst
func (scm *SavedContentManager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// fileExists checks if a file exists
func (scm *SavedContentManager) fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}
