package tools

import (
	"github.com/mark3labs/mcp-go/mcp"
	"smart-card-mcp/internal/config"
)

// GetAllTools returns all available tools for the Smart Card MCP server
func GetAllTools(cfg *config.Config) []mcp.Tool {
	// Base tools without Context parameter
	baseTools := []mcp.Tool{
		// === 同步工具 (Synchronous Tools) ===

		// Generate Card from Text Tool (Sync)
		mcp.NewTool("generate_card_from_text",
			mcp.WithDescription("从文本生成智能卡片（同步）。接收完整的原始文本内容，自动使用专业的文本分析专家进行总结，生成包含智能标题的结构化摘要，然后转换为精美的HTML卡片和高清图片。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// Generate Card from URL Tool (Sync)
		mcp.NewTool("generate_card_from_url",
			mcp.WithDescription("从网页链接生成智能卡片（同步）。输入完整的网页URL，自动抓取网页内容，提取主要信息，使用专业的文本分析专家进行总结，生成包含智能标题的结构化摘要，然后转换为精美的HTML卡片和高清图片。支持大多数网页格式，自动过滤广告和无关内容。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// Generate Direct HTML Card from Text Tool (Sync) - No Summarization
		mcp.NewTool("generate_direct_card_from_text",
			mcp.WithDescription("从文本直接生成HTML智能卡片（同步，无总结）。接收完整的原始文本内容，不进行总结处理，直接根据原文的内容结构生成HTML卡片，尽量保持原文的信息完整性和层次结构。适合需要展示完整内容的场景。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本。内容将完整保留，不进行总结"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// Generate Direct HTML Card from URL Tool (Sync) - No Summarization
		mcp.NewTool("generate_direct_card_from_url",
			mcp.WithDescription("从网页链接直接生成HTML智能卡片（同步，无总结）。输入完整的网页URL，自动抓取网页内容，不进行总结处理，直接根据原网页的内容结构生成HTML卡片，尽量保持原网页的信息完整性和层次结构。适合需要展示完整网页内容的场景。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// === 异步工具 (Asynchronous Tools) ===

		// Create Async Task from Text Tool
		mcp.NewTool("create_task_from_text",
			mcp.WithDescription("从文本创建异步卡片生成任务。创建异步任务，返回任务ID用于后续查询状态和获取结果。适合处理大量文本或需要长时间处理的内容。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// Create Async Task from URL Tool
		mcp.NewTool("create_task_from_url",
			mcp.WithDescription("从网页链接创建异步卡片生成任务。创建异步任务，返回任务ID用于后续查询状态和获取结果。适合处理复杂网页或需要长时间处理的内容。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
		),

		// Get Task Status Tool
		mcp.NewTool("get_task_status",
			mcp.WithDescription("获取异步任务状态。通过任务ID查询任务的当前状态、进度信息和执行结果。返回任务状态包括：pending（等待中）、running（执行中）、completed（已完成）、failed（失败）、cancelled（已取消）。"),
			mcp.WithString("task_id",
				mcp.Required(),
				mcp.Description("异步任务的唯一标识符，由异步任务创建工具返回"),
			),
		),

		// Get Task Result Tool
		mcp.NewTool("get_task_result",
			mcp.WithDescription("获取已完成任务的结果图片。返回完整的任务执行结果，包括生成的卡片图片数据（base64编码的PNG格式）。只有状态为completed的任务才能获取结果。"),
			mcp.WithString("task_id",
				mcp.Required(),
				mcp.Description("已完成任务的唯一标识符"),
			),
		),
	}

	// If DStaff is enabled, add Context parameter to all tools
	if cfg.DStaff != nil && cfg.DStaff.Enabled {
		tools := make([]mcp.Tool, len(baseTools))
		for i, baseTool := range baseTools {
			// Create new tool with Context parameter
			tools[i] = addContextParameterToTool(baseTool)
		}
		return tools
	}

	return baseTools
}

// addContextParameterToTool adds a Context parameter to an existing tool for DStaff integration
func addContextParameterToTool(baseTool mcp.Tool) mcp.Tool {
	// Create a new tool with the same name and description, but add Context parameter
	switch baseTool.Name {
	case "generate_card_from_text":
		return mcp.NewTool("generate_card_from_text",
			mcp.WithDescription("从文本生成智能卡片（同步）。接收完整的原始文本内容，自动使用专业的文本分析专家进行总结，生成包含智能标题的结构化摘要，然后转换为精美的HTML卡片和高清图片。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "generate_card_from_url":
		return mcp.NewTool("generate_card_from_url",
			mcp.WithDescription("从网页链接生成智能卡片（同步）。输入完整的网页URL，自动抓取网页内容，提取主要信息，使用专业的文本分析专家进行总结，生成包含智能标题的结构化摘要，然后转换为精美的HTML卡片和高清图片。支持大多数网页格式，自动过滤广告和无关内容。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "generate_direct_card_from_text":
		return mcp.NewTool("generate_direct_card_from_text",
			mcp.WithDescription("从文本直接生成HTML智能卡片（同步，无总结）。接收完整的原始文本内容，不进行总结处理，直接根据原文的内容结构生成HTML卡片，尽量保持原文的信息完整性和层次结构。适合需要展示完整内容的场景。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本。内容将完整保留，不进行总结"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "generate_direct_card_from_url":
		return mcp.NewTool("generate_direct_card_from_url",
			mcp.WithDescription("从网页链接直接生成HTML智能卡片（同步，无总结）。输入完整的网页URL，自动抓取网页内容，不进行总结处理，直接根据原网页的内容结构生成HTML卡片，尽量保持原网页的信息完整性和层次结构。适合需要展示完整网页内容的场景。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "create_task_from_text":
		return mcp.NewTool("create_task_from_text",
			mcp.WithDescription("从文本创建异步卡片生成任务。创建异步任务，返回任务ID用于后续查询状态和获取结果。适合处理大量文本或需要长时间处理的内容。"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("完整的原始文本内容，支持任意长度的文章、报告、新闻等文本"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "create_task_from_url":
		return mcp.NewTool("create_task_from_url",
			mcp.WithDescription("从网页链接创建异步卡片生成任务。创建异步任务，返回任务ID用于后续查询状态和获取结果。适合处理复杂网页或需要长时间处理的内容。"),
			mcp.WithString("url",
				mcp.Required(),
				mcp.Description("完整的网页URL地址，需要是有效的HTTP/HTTPS链接"),
			),
			mcp.WithString("summary_prompt",
				mcp.Description("可选的总结提示词，用于指导AI的总结方向。例如：'重点关注技术细节'、'突出商业价值'、'强调实用性'、'侧重数据分析'等。如果不提供，将使用默认的智能总结策略"),
			),
			mcp.WithString("style_prompt",
				mcp.Description("可选的风格提示词，用于指导HTML卡片的设计风格。例如：'简约现代风格'、'科技感十足'、'温馨可爱'、'商务专业'等。如果不提供，将使用默认的智能配色方案"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "get_task_status":
		return mcp.NewTool("get_task_status",
			mcp.WithDescription("获取异步任务状态。通过任务ID查询任务的当前状态、进度信息和执行结果。返回任务状态包括：pending（等待中）、running（执行中）、completed（已完成）、failed（失败）、cancelled（已取消）。"),
			mcp.WithString("task_id",
				mcp.Required(),
				mcp.Description("异步任务的唯一标识符，由异步任务创建工具返回"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	case "get_task_result":
		return mcp.NewTool("get_task_result",
			mcp.WithDescription("获取已完成任务的结果图片。返回完整的任务执行结果，包括生成的卡片图片数据（base64编码的PNG格式）。只有状态为completed的任务才能获取结果。"),
			mcp.WithString("task_id",
				mcp.Required(),
				mcp.Description("已完成任务的唯一标识符"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于DStaff平台集成",
					},
				}),
			),
		)
	default:
		// Return the original tool if we don't have a specific handler
		return baseTool
	}
}
