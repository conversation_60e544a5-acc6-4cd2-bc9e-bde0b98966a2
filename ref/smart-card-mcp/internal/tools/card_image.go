package tools

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"image/color"
	"image/png"
	"io/ioutil"
	"os"
	"strings"

	"github.com/fogleman/gg"
	"github.com/golang/freetype/truetype"
	"golang.org/x/image/font/gofont/goregular"
)

// CardImageData represents data for generating card images
type CardImageData struct {
	Title               string
	Content             string
	SourceURL           string
	Keywords            []string
	ColorTheme          string
	IncludeSummaryStats bool
	WordCount           int
	CharCount           int
	EstimatedReadTime   int
}

// loadChineseFont loads a Chinese font from the system
func loadChineseFont() (*truetype.Font, error) {
	// Try to load Chinese fonts in order of preference
	fontPaths := []string{
		"/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc",     // WenQuanYi Zen Hei
		"/usr/share/fonts/noto/NotoSansCJK-Regular.ttc",  // Noto Sans CJK
		"/usr/share/fonts/noto/NotoSerifCJK-Regular.ttc", // Noto Serif CJK
		"/usr/share/fonts/dejavu/DejaVuSans.ttf",         // DejaVu Sans
		"/System/Library/Fonts/PingFang.ttc",             // macOS
		"/Windows/Fonts/msyh.ttc",                        // Windows
	}

	for _, fontPath := range fontPaths {
		if _, err := os.Stat(fontPath); err == nil {
			fontBytes, err := ioutil.ReadFile(fontPath)
			if err != nil {
				continue
			}
			font, err := truetype.Parse(fontBytes)
			if err != nil {
				continue
			}
			return font, nil
		}
	}

	// Fallback to embedded font
	return truetype.Parse(goregular.TTF)
}

// generateCardImage generates a card image and returns base64 encoded PNG
func (h *ToolHandlers) generateCardImage(data CardImageData) (string, error) {
	// Card dimensions
	const (
		cardWidth    = 800
		cardHeight   = 600
		padding      = 40
		cornerRadius = 20
	)

	// Create drawing context
	dc := gg.NewContext(cardWidth, cardHeight)

	// Load Chinese font
	font, err := loadChineseFont()
	if err != nil {
		return "", fmt.Errorf("failed to load font: %w", err)
	}

	// Get color scheme
	bgColor, accentColor, textColor := getImageColorScheme(data.ColorTheme)

	// Draw background with gradient effect
	dc.DrawRoundedRectangle(0, 0, cardWidth, cardHeight, cornerRadius)
	dc.SetColor(bgColor)
	dc.Fill()

	// Draw accent border
	dc.SetLineWidth(4)
	dc.SetColor(accentColor)
	dc.DrawRoundedRectangle(2, 2, cardWidth-4, cardHeight-4, cornerRadius-2)
	dc.Stroke()

	// Set up text rendering
	dc.SetColor(textColor)

	// Draw title
	titleFontSize := 32.0
	dc.SetFontFace(truetype.NewFace(font, &truetype.Options{Size: titleFontSize}))

	// Wrap title text
	titleLines := wrapText(data.Title, cardWidth-2*padding, dc)
	titleY := float64(padding + 40)
	for i, line := range titleLines {
		dc.DrawString(line, padding, titleY+float64(i)*40)
	}

	// Draw content
	contentFontSize := 18.0
	dc.SetFontFace(truetype.NewFace(font, &truetype.Options{Size: contentFontSize}))

	contentY := titleY + float64(len(titleLines))*40 + 30
	contentLines := wrapText(data.Content, cardWidth-2*padding, dc)

	// Limit content lines to fit in card
	maxContentLines := int((cardHeight - contentY - 150) / 25)
	if len(contentLines) > maxContentLines {
		contentLines = contentLines[:maxContentLines-1]
		contentLines = append(contentLines, "...")
	}

	for i, line := range contentLines {
		dc.DrawString(line, padding, contentY+float64(i)*25)
	}

	// Draw keywords
	if len(data.Keywords) > 0 {
		keywordY := contentY + float64(len(contentLines))*25 + 30
		dc.SetFontFace(truetype.NewFace(font, &truetype.Options{Size: 14}))

		keywordX := float64(padding)
		for i, keyword := range data.Keywords {
			if i >= 6 { // Limit to 6 keywords
				break
			}

			// Draw keyword background
			keywordWidth, _ := dc.MeasureString(keyword)
			keywordWidth += 20
			dc.SetColor(accentColor)
			dc.DrawRoundedRectangle(keywordX, keywordY-5, keywordWidth, 25, 12)
			dc.Fill()

			// Draw keyword text
			dc.SetColor(color.RGBA{255, 255, 255, 255}) // White text
			dc.DrawString(keyword, keywordX+10, keywordY+10)

			keywordX += keywordWidth + 10
			if keywordX > float64(cardWidth-padding-100) {
				break
			}
		}
		dc.SetColor(textColor) // Reset text color
	}

	// Draw stats at bottom
	if data.IncludeSummaryStats {
		statsY := float64(cardHeight - 60)
		dc.SetFontFace(truetype.NewFace(font, &truetype.Options{Size: 14}))

		stats := fmt.Sprintf("字数: %d  |  字符: %d  |  预计阅读: %d分钟",
			data.WordCount, data.CharCount, data.EstimatedReadTime)
		dc.DrawString(stats, padding, statsY)
	}

	// Draw source URL if provided
	if data.SourceURL != "" {
		urlY := float64(cardHeight - 30)
		dc.SetFontFace(truetype.NewFace(font, &truetype.Options{Size: 12}))
		dc.SetColor(accentColor)

		// Truncate URL if too long
		displayURL := data.SourceURL
		if len(displayURL) > 60 {
			displayURL = displayURL[:57] + "..."
		}
		dc.DrawString("来源: "+displayURL, padding, urlY)
	}

	// Convert to PNG and encode as base64
	img := dc.Image()
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return "", fmt.Errorf("failed to encode PNG: %w", err)
	}

	base64Str := base64.StdEncoding.EncodeToString(buf.Bytes())
	return base64Str, nil
}

// getImageColorScheme returns colors for the specified theme
func getImageColorScheme(theme string) (bg, accent, text color.Color) {
	switch theme {
	case "blue":
		return color.RGBA{240, 248, 255, 255}, color.RGBA{102, 126, 234, 255}, color.RGBA{26, 26, 26, 255}
	case "green":
		return color.RGBA{240, 255, 240, 255}, color.RGBA{17, 153, 142, 255}, color.RGBA{26, 26, 26, 255}
	case "purple":
		return color.RGBA{248, 240, 255, 255}, color.RGBA{118, 75, 162, 255}, color.RGBA{26, 26, 26, 255}
	case "orange":
		return color.RGBA{255, 248, 240, 255}, color.RGBA{240, 147, 251, 255}, color.RGBA{26, 26, 26, 255}
	case "red":
		return color.RGBA{255, 240, 240, 255}, color.RGBA{255, 107, 107, 255}, color.RGBA{26, 26, 26, 255}
	case "cyan":
		return color.RGBA{240, 255, 255, 255}, color.RGBA{116, 185, 255, 255}, color.RGBA{26, 26, 26, 255}
	default:
		return color.RGBA{248, 250, 252, 255}, color.RGBA{102, 126, 234, 255}, color.RGBA{26, 26, 26, 255}
	}
}

// wrapText wraps text to fit within the specified width
func wrapText(text string, maxWidth float64, dc *gg.Context) []string {
	words := strings.Fields(text)
	if len(words) == 0 {
		return []string{}
	}

	var lines []string
	var currentLine strings.Builder

	for _, word := range words {
		testLine := currentLine.String()
		if testLine != "" {
			testLine += " "
		}
		testLine += word

		testWidth, _ := dc.MeasureString(testLine)
		if testWidth <= maxWidth {
			if currentLine.Len() > 0 {
				currentLine.WriteString(" ")
			}
			currentLine.WriteString(word)
		} else {
			if currentLine.Len() > 0 {
				lines = append(lines, currentLine.String())
				currentLine.Reset()
			}
			currentLine.WriteString(word)
		}
	}

	if currentLine.Len() > 0 {
		lines = append(lines, currentLine.String())
	}

	return lines
}
