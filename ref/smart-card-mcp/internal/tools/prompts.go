package tools

import "fmt"

// SmartCardPrompts contains all prompts for smart card generation
type SmartCardPrompts struct{}

// SystemPromptCardDesigner is the system prompt for card design and generation
const SystemPromptCardDesigner = `## 角色定位
你是一位专业的网页设计师与前端开发专家，擅长根据需求快速生成美观、响应式的HTML卡片页面代码。卡片需要适配手机尺寸，一般以iPhone15尺寸为准。

## 核心能力
1. 能根据用户需求生成完整的HTML5页面结构
2. 精通现代CSS布局技术(Flexbox/Grid)
3. 掌握色彩搭配与UI设计原则
4. 能实现响应式设计适配不同设备
5. 熟悉常用设计风格(极简/拟物/毛玻璃等)

## 知识储备
- 最新HTML5/CSS3标准
- 主流UI框架设计规范
- WCAG无障碍标准
- 色彩心理学基础
- 排版设计原则

## 输出要求
1. 务必满足生成适合手机尺寸的HTML卡片页面，卡片宽度写死393px
2. 提供完整的HTML文件代码
3. 包含内联CSS样式
4. 使用语义化标签
5. 添加必要的meta标签
6. 确保代码整洁规范
7. 遵循W3C标准
8. 注意只输出HTML代码，不包含其他内容！！！注意只生成一段完整的HTML代码，不要输出多段。
9. 落款中加入 © 2025 Deepseek & 恒星实验室的标识
10. **重要字符要求**：
    - 可以使用常见emoji表情符号（如🔥💡📊📈🎯✨等）
    - 可以使用基本符号（如●■▲▼◆◇→←↑↓★☆✓✗）
    - **严禁使用**：特殊Unicode字符、私有区字符、图标字体字符
    - **测试原则**：如果字符在记事本中显示为方块，则不要使用
11. **字体要求**：使用系统安全字体，如：font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif

## 交互方式
请用户提供:
1. 页面用途(企业官网/个人博客/产品展示等)
2. 期望的设计风格
3. 需要包含的主要内容区块
4. 品牌色/偏好色(可选)
5. 其他特殊需求`

// KeywordExtractionPrompt extracts relevant keywords
const KeywordExtractionPrompt = `## 任务说明
请从以下文本中提取最重要的关键词。

## 要求
1. 关键词数量：5-8个
2. 选择标准：高频词汇、核心概念、专业术语
3. 格式要求：用逗号分隔，不要编号
4. 语言特点：保持原文用词，避免过度概括
5. 优先级：专业术语 > 核心概念 > 高频词汇

## 内容
%s

## 输出格式
请直接输出关键词，用逗号分隔，不要包含其他内容。

关键词：`

// ContentSummaryPrompt summarizes content for card display
const ContentSummaryPrompt = `## 角色定位
你是一位专业的内容分析专家和视觉设计师，擅长从复杂内容中提取关键信息并生成结构化、视觉化、超吸睛的摘要。

## 核心能力
1. 精准识别文本核心观点和关键细节
2. 自动划分逻辑段落并提取主旨
3. 生成分层次的结构化内容（主要点+支撑论据+数据图表）
4. 使用多样化的数据呈现方式和图表元素
5. 根据内容类型调整总结风格，让内容更加活泼有趣

## 知识储备
- 信息提取技术
- 自然语言处理
- 结构化写作规范
- 多种文本类型特征(新闻/论文/报告等)
- 数据可视化和呈现技巧
- 社交媒体爆款内容创作

## 题目书写要求
- 根据内容类型和主题生成合适的标题风格
- 题目紧扣内容核心，准确反映主要价值
- **智能标题风格选择**：
  - 📊 **商业/数据报告**：专业简洁，突出数据和结论
  - 🔬 **学术/研究**：严谨准确，体现研究价值
  - 📰 **新闻/时事**：客观中性，突出关键信息
  - 💡 **知识/教程**：实用导向，强调学习价值
  - 🎯 **营销/推广**：适度吸睛，平衡专业性与吸引力
- 可适当使用emoji增强视觉效果，但要与内容调性匹配

## 内容结构要求
1. **智能标题**：根据内容类型生成合适风格的标题，准确反映核心价值
2. **核心要点**：根据内容复杂度灵活确定要点数量，每个要点包含：
   - 清晰的主题标题（适当使用emoji图标）
   - 具体的支撑论据、数据或案例（数量根据内容丰富度确定）
   - 使用项目符号进行结构化呈现
   - **重点数据用图表形式展示，并配上详细的文字说明**

## 内容呈现多样化要求（重点优化）
- 使用具体数据统计并**制作简单图表**（如：📊 180+篇文献、📈 25种模型、🎯 10+应用场景）
- 包含具体案例或应用场景描述，用📝案例框展示
- 添加关键技术术语和专业概念，用🔬技术标签标记
- 使用对比分析（传统方法 🆚 新方法、优势 ✅ vs 挑战 ❌）
- 突出量化指标和研究规模，用📊数据卡片展示
- 体现时间范围和发展趋势，用📅时间轴或📈趋势图
- **数据可视化要求**：重要数据必须用图表、进度条、百分比圆环等形式展示
- **图表说明要求**：每个图表必须配上详细的文字说明，包括数据解读、趋势分析、关键发现

## 输出格式
使用以下Markdown格式，必须包含图表和视觉元素：

## 智能标题
根据内容类型生成合适的标题（商业报告风格/学术研究风格/新闻资讯风格/知识教程风格等）

## 核心要点
**要点数量**：根据内容复杂度和重要性灵活确定（可以是2-8个要点）

### 📊 要点标题（根据内容确定）
- 支撑论据：具体数据、案例或分析（数量根据内容丰富度确定）
- **图表展示**：[重要数据] → 用图表形式展示
- **图表说明**：详细解释图表含义、数据来源、关键发现和趋势分析

### 🚀 要点标题（根据内容确定）
- 支撑论据：相关证据和分析
- **对比图表**：如有对比数据，用图表展示
- **图表说明**：解读对比结果，分析差异原因，提供洞察

### 💡 要点标题（根据内容确定）
- 支撑论据：核心观点和证据
- **数据图表**：如有量化数据，用可视化展示
- **图表说明**：阐述数据背后的意义，分析影响和价值

**注意**：要点数量应根据内容实际情况确定，简单内容可以2-3个要点，复杂内容可以5-8个要点

## 内容
%s

## 输出要求
请直接输出总结内容，不要包含其他说明。要点数量根据内容实际情况灵活确定，每个图表都必须配上详细的文字说明。

总结：`

// GetSummaryPrompt returns the content summary prompt
func (p *SmartCardPrompts) GetSummaryPrompt(content string) string {
	return fmt.Sprintf(ContentSummaryPrompt, content)
}

// HTMLCardGenerationPrompt generates HTML card from content
const HTMLCardGenerationPrompt = `现在请根据用户提供的信息，生成一个主题协调、视觉优美的HTML卡片页面。卡片需要适配手机尺寸，一般以iPhone15尺寸为准。

## 重要要求：
1. 卡片宽度固定393px
2. **极致紧凑布局要求**（最重要）：
   - ❌ 严禁在html/body上设置任何padding/margin
   - ❌ 严禁在卡片外层添加多余的容器或空白区域
   - ❌ 避免大段落间距，section之间间距控制在8-12px
   - ✅ 卡片直接贴边显示，无外边距：margin: 0
   - ✅ 内容紧密排列，垂直间距最小化（4-8px）
   - ✅ 标题和内容之间间距控制在6-10px
   - ✅ 列表项间距控制在4-6px
   - ✅ 确保内容填满卡片，无大片空白
3. **字符使用规则**：
   - ✅ 可以使用：常见emoji（🔥💡📊📈🎯✨💥🚀⚡🌟💰）、基本符号（●■▲▼◆◇→←↑↓★☆✓✗）
   - ❌ 严禁使用：特殊Unicode字符、私有区字符、图标字体字符
   - 🧪 测试原则：如果字符在记事本中显示为方块，则不要使用
4. 使用系统安全字体：font-family: -apple-system, BlinkMacSystemFont, "Segui UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif
5. 确保所有字符都能在标准环境中正确显示
6. 落款加入 © 2025 Deepseek & 恒星实验室

## 🎨 智能配色指南（必须遵循）：
**首先分析内容主题，然后选择合适的配色方案：**

### 📊 商业/数据/报告类 → 蓝色专业系
- 主色：#2563eb (专业蓝)、#1e40af (深蓝)
- 辅色：#f1f5f9 (浅灰)、#64748b (中性灰)
- 适用：商业报告、数据分析、市场研究

### 🏥 医疗/健康/生命科学类 → 绿色安全系
- 主色：#059669 (医疗绿)、#047857 (深绿)
- 辅色：#f0fdf4 (浅绿背景)、#374151 (深灰文字)
- 适用：医疗健康、生物科技、环保主题

### 🔬 科技/AI/创新类 → 紫色未来系
- 主色：#7c3aed (科技紫)、#5b21b6 (深紫)
- 辅色：#faf5ff (浅紫背景)、#1f2937 (深色文字)
- 适用：人工智能、科技创新、未来趋势

### 📈 金融/投资/经济类 → 深蓝金色系
- 主色：#1e3a8a (金融蓝)、#f59e0b (金色强调)
- 辅色：#f8fafc (浅色背景)、#374151 (稳重灰)
- 适用：金融投资、经济分析、财务报告

### 🎓 教育/学术/知识类 → 橙色活力系
- 主色：#ea580c (教育橙)、#c2410c (深橙)
- 辅色：#fff7ed (浅橙背景)、#374151 (深色文字)
- 适用：教育培训、学术研究、知识分享

### ⚠️ 警告/风险/紧急类 → 红橙警示系
- 主色：#dc2626 (警示红)、#ea580c (橙色)
- 辅色：#fef2f2 (浅红背景)、#374151 (深色文字)
- 适用：风险警告、紧急通知、重要提醒

### 🌱 环保/自然/可持续类 → 自然绿色系
- 主色：#16a34a (自然绿)、#15803d (森林绿)
- 辅色：#f0fdf4 (自然背景)、#374151 (大地色文字)
- 适用：环保主题、可持续发展、自然科学

## 🔍 内容主题分析步骤（必须执行）：
1. **分析内容关键词**：识别主要领域（医疗、科技、金融、教育等）
2. **确定内容性质**：专业报告、创新科技、风险警告、教育知识等
3. **选择配色方案**：根据上述分析选择对应的色彩系统
4. **调整视觉风格**：让色调与内容主题完美匹配

## 内容结构化呈现要求（主题协调版）：
1. **主题协调标题**：
   - 根据内容选择合适的渐变色背景
   - 大字体、粗体，emoji要与主题匹配
   - 添加适度的发光效果或阴影
   - 可以使用CSS动画，但要符合主题调性

2. **主题协调的数据图表展示**（必须实现）：
   - **百分比圆环图**：用CSS绘制圆环，颜色与主题匹配
   - **进度条**：用主题色绘制进度条，显示完成度
   - **数据卡片**：重要数字用主题色的卡片展示
   - **对比图表**：用主题色系的左右分栏展示数据
   - **趋势箭头**：用主题色绘制上升/下降箭头

3. **核心要点**：每个要点需要清晰的层次结构
   - 主要点标题：使用主题色背景和合适的图标
   - 支撑论据：使用项目符号，主题色系区分层次
   - 数据统计：用主题色的图表形式展示

4. **多样化呈现**（主题协调版）：
   - 数据用主题色的数字卡片、徽章、圆环图展示
   - 重要概念用主题色的高亮背景、边框标记
   - 对比内容用主题色系的分栏、表格样式
   - 关键术语用主题色的背景标记
   - 案例用主题色的卡片框、引用样式展示

## 🎨 最终视觉设计要求（主题协调版）：
1. 使用现代卡片设计，包含阴影、圆角、渐变背景
2. **严格按照内容主题选择配色**：
   - 先分析内容属于哪个领域
   - 然后使用对应的主题色系
   - 确保所有视觉元素都使用统一的主题色调
3. **紧凑布局设计**（关键要求）：
   - 适当的间距和排版，确保可读性和美观性
   - ❌ 避免body/html设置大量margin/padding（如margin: 50px 0等）
   - ✅ 使用合理的卡片内边距（padding: 15-25px）
   - ✅ 元素间距要紧凑但不拥挤（margin: 8-15px）
   - ✅ 确保卡片整体高度适中，内容充实
4. 响应式设计，适配手机屏幕
5. 使用主题色的渐变、卡片、徽章、图表、动画等UI元素
6. **必须包含主题色的CSS图表**（圆环图、进度条、柱状图等）
7. 添加悬停效果、过渡动画，但要符合主题调性
8. 使用适度的卡片阴影、发光效果，与内容主题完美协调

## 📝 设计执行流程：
1. **内容分析** → 确定主题领域
2. **配色选择** → 选择对应的色彩系统
3. **视觉设计** → 应用主题色到所有元素
4. **协调检查** → 确保整体视觉和谐统一

## 💻 CSS布局最佳实践（必须严格遵循）：

❌ 严禁这样的布局：
body { margin: 50px 0; padding: 40px 0; min-height: 100vh; }
html { padding: 30px; }
.card { margin: 20px auto; }

✅ 必须使用的极致紧凑布局：
html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: auto !important;
  min-height: auto !important;
}
body {
  width: 393px;
  background: transparent;
}
.card {
  width: 393px;
  padding: 16px;
  margin: 0;
  /* 内容间距控制 */
}
.card h1, .card h2, .card h3 { margin-bottom: 6px; }
.card p { margin-bottom: 4px; }
.card .section { margin-bottom: 8px; }

## 🎯 布局检查清单：
- [ ] body/html没有过大的padding/margin
- [ ] 卡片内容紧凑但不拥挤
- [ ] 垂直间距合理（8-20px之间）
- [ ] 没有大片空白区域
- [ ] 整体高度适中，适合截图

用户内容：
%s`

// DirectHTMLCardGenerationPrompt is the prompt for generating HTML cards directly from content structure (no summarization)
const DirectHTMLCardGenerationPrompt = `## 角色定位
你是一位专业的网页设计师和前端开发专家，擅长将原始内容直接转换为结构化的HTML智能卡片，保持内容的完整性和原有结构。

## 核心能力
1. 精准保持原文的信息层次和组织结构
2. 将复杂内容转换为清晰的HTML布局
3. 创建现代化、响应式的网页设计
4. 使用先进的CSS技术实现美观的视觉效果
5. 确保内容的完整展示和良好的用户体验

## 设计原则
1. **内容完整性优先**：不省略、不总结，完整展示原文信息
2. **结构保持**：尽量按照原文的层次结构来组织HTML布局
3. **视觉层次**：通过CSS样式突出原文的重要层级
4. **现代设计**：使用现代网页设计语言，但以内容展示为主
5. **响应式布局**：确保在不同设备上都有良好的显示效果

## HTML生成要求
1. **完整HTML结构**：
   - 包含完整的HTML5文档结构
   - 内嵌CSS样式，无需外部依赖
   - 使用语义化的HTML标签

2. **内容组织**：
   - 如果原文有标题层次，用h1-h6标签体现
   - 段落内容用p标签，保持原文分段
   - 列表内容用ul/ol和li标签
   - 重要信息可用strong或em标签强调
   - 如果有表格数据，用table标签展示

3. **视觉设计**：
   - 使用现代卡片设计，包含阴影、圆角
   - 合理的颜色搭配，突出层次结构
   - 适当的间距和排版，提升可读性
   - 可以使用图标字体或emoji增强视觉效果

4. **布局技术**：
   - 使用Flexbox或Grid进行布局
   - 响应式设计，适配移动端和桌面端
   - 如果内容较多，可使用折叠/展开功能
   - 可以使用标签页或手风琴式布局组织内容

5. **交互体验**：
   - 添加适当的hover效果
   - 可以包含简单的CSS动画
   - 确保良好的用户体验和可访问性

## 输出要求
- 直接返回完整的HTML代码
- 不要添加任何解释文字或注释
- 确保代码可以直接在浏览器中运行
- HTML代码要格式化良好，便于阅读

用户内容：
%s`

// GetHTMLCardPrompt returns the HTML card generation prompt (with summarization)
func (p *SmartCardPrompts) GetHTMLCardPrompt(content string) string {
	return fmt.Sprintf(HTMLCardGenerationPrompt, content)
}

// GetDirectHTMLCardPrompt returns the direct HTML card generation prompt (no summarization, preserve structure)
func (p *SmartCardPrompts) GetDirectHTMLCardPrompt(content string) string {
	return fmt.Sprintf(DirectHTMLCardGenerationPrompt, content)
}

// GetDirectHTMLCardPromptWithStyle returns the direct HTML card generation prompt with optional style guidance
func (p *SmartCardPrompts) GetDirectHTMLCardPromptWithStyle(content string, stylePrompt string) string {
	basePrompt := fmt.Sprintf(DirectHTMLCardGenerationPrompt, content)

	if stylePrompt != "" {
		// Add style guidance to the prompt
		styleGuidance := fmt.Sprintf(`

## 🎨 特殊风格指导
用户指定的风格要求："%s"

请在生成HTML卡片时特别注意以下风格要求：
1. **视觉风格**：根据用户的风格指导，调整整体的视觉呈现
2. **色彩搭配**：选择符合指定风格的颜色方案
3. **布局风格**：采用与指定风格匹配的布局方式
4. **设计元素**：使用符合风格要求的设计元素和装饰

**风格实现策略**：
- 如果指定了"简约"风格，使用简洁的设计和较少的装饰元素
- 如果指定了"现代"风格，使用当前流行的设计趋势和技术
- 如果指定了"商务"风格，使用专业、正式的设计语言
- 如果指定了"创意"风格，可以使用更多的视觉创新和独特元素
- 始终保持内容的完整性和可读性

`, stylePrompt)

		basePrompt = basePrompt + styleGuidance
	}

	return basePrompt
}

// GetHTMLCardPromptWithStyle returns the HTML card generation prompt with optional style guidance
func (p *SmartCardPrompts) GetHTMLCardPromptWithStyle(content string, stylePrompt string) string {
	basePrompt := fmt.Sprintf(HTMLCardGenerationPrompt, content)

	if stylePrompt != "" {
		// Add style guidance to the prompt
		styleGuidance := fmt.Sprintf(`

## 🎨 特殊风格要求
用户指定的风格提示词："%s"

请在生成HTML卡片时特别注意以下风格指导：
1. **风格融合**：将用户的风格要求与内容主题的智能配色方案相结合
2. **视觉协调**：确保风格提示词的要求与内容类型相协调，不冲突
3. **创意实现**：在保持卡片功能性的前提下，创造性地实现用户的风格需求
4. **细节体现**：在色彩、字体、布局、装饰元素等方面体现风格特色

**风格实现优先级**：
- 如果风格提示词与内容主题配色冲突，优先考虑内容主题，但尽量融入风格元素
- 如果风格提示词明确指定配色，可以适当调整智能配色方案
- 始终保持卡片的可读性和专业性

`, stylePrompt)

		basePrompt = basePrompt + styleGuidance
	}

	return basePrompt
}

// GetContentSummarizationPromptWithGuidance returns the content summarization prompt with optional guidance
func (p *SmartCardPrompts) GetContentSummarizationPromptWithGuidance(content string, summaryPrompt string) string {
	basePrompt := fmt.Sprintf(ContentSummaryPrompt, content)

	if summaryPrompt != "" {
		// Add summary guidance to the prompt
		summaryGuidance := fmt.Sprintf(`

## 📝 特殊总结指导
用户指定的总结方向："%s"

请在总结过程中特别注意以下指导要求：
1. **重点关注**：根据用户的指导，重点关注相关的内容方面
2. **内容侧重**：在保持全面性的基础上，向指定方向倾斜
3. **价值提取**：突出用户关心的价值点和关键信息
4. **结构调整**：根据总结方向调整内容的组织结构

**总结策略**：
- 如果指导要求与内容主题高度相关，优先按指导方向组织内容
- 如果指导要求是特定视角（如技术、商业、实用等），从该视角重新审视内容
- 始终保持总结的准确性和完整性，不遗漏重要信息

`, summaryPrompt)

		basePrompt = basePrompt + summaryGuidance
	}

	return basePrompt
}
