package tools

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// HTML2ImageConverter handles HTML to image conversion
type HTML2ImageConverter struct {
	tempDir string
}

// NewHTML2ImageConverter creates a new HTML to image converter
func NewHTML2ImageConverter() *HTML2ImageConverter {
	return &HTML2ImageConverter{
		tempDir: os.TempDir(),
	}
}

// ConvertHTMLToImage converts HTML content to PNG image
func (h *HTML2ImageConverter) ConvertHTMLToImage(htmlContent string, options *HTML2ImageOptions) (string, error) {
	if options == nil {
		options = &HTML2ImageOptions{
			Width:   393,
			Height:  0, // Auto height
			Quality: 100,
			Format:  "png",
		}
	}

	// Try different conversion methods
	methods := []func(string, *HTML2ImageOptions) (string, error){
		h.convertWith<PERSON>hrome,
		h.convertWithWkhtmltopdf,
		h.convertWithPuppeteer,
	}

	var lastErr error
	for _, method := range methods {
		result, err := method(htmlContent, options)
		if err == nil {
			return result, nil
		}
		lastErr = err
	}

	return "", fmt.Errorf("all conversion methods failed, last error: %w", lastErr)
}

// HTML2ImageOptions defines options for HTML to image conversion
type HTML2ImageOptions struct {
	Width   int    `json:"width"`
	Height  int    `json:"height"`
	Quality int    `json:"quality"`
	Format  string `json:"format"`
}

// convertWithChrome uses Chrome/Chromium for conversion
func (h *HTML2ImageConverter) convertWithChrome(htmlContent string, options *HTML2ImageOptions) (string, error) {
	// Create temporary HTML file
	tempHTML := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.html", time.Now().UnixNano()))
	tempPNG := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.png", time.Now().UnixNano()))

	defer func() {
		os.Remove(tempHTML)
		os.Remove(tempPNG)
	}()

	// Write HTML content to file
	if err := ioutil.WriteFile(tempHTML, []byte(htmlContent), 0644); err != nil {
		return "", fmt.Errorf("failed to write HTML file: %w", err)
	}

	// Try different Chrome/Chromium executables
	chromeExes := []string{
		"google-chrome",
		"chromium",
		"chromium-browser",
		"chrome",
		"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
		"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
	}

	var chromeExe string
	for _, exe := range chromeExes {
		if _, err := exec.LookPath(exe); err == nil {
			chromeExe = exe
			break
		}
		// Check if file exists (for Windows paths)
		if _, err := os.Stat(exe); err == nil {
			chromeExe = exe
			break
		}
	}

	if chromeExe == "" {
		return "", fmt.Errorf("Chrome/Chromium not found")
	}

	// Prepare Chrome arguments with mobile emulation (iPhone 15) - optimized for no margins and high quality
	args := []string{
		"--headless",
		"--disable-gpu",
		"--no-sandbox",
		"--disable-dev-shm-usage",
		"--disable-web-security",
		"--allow-running-insecure-content",
		"--hide-scrollbars",
		"--force-device-scale-factor=2", // Higher DPI for better quality
		"--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
		"--virtual-time-budget=5000", // More time for rendering
		fmt.Sprintf("--window-size=%d,%d", options.Width, 3000), // Use larger height, will be cropped by content
		"--default-background-color=0",
		"--disable-background-timer-throttling",
		"--font-render-hinting=none", // Better font rendering for emoji
		"--enable-font-antialiasing", // Enable font antialiasing
		"--force-color-profile=srgb", // Better color rendering
		"--disable-renderer-backgrounding",
		"--disable-backgrounding-occluded-windows",
		fmt.Sprintf("--screenshot=%s", tempPNG),
		fmt.Sprintf("file://%s", tempHTML),
	}

	// Execute Chrome
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, chromeExe, args...)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("Chrome execution failed: %w, stderr: %s", err, stderr.String())
	}

	// Read the generated image
	imageData, err := ioutil.ReadFile(tempPNG)
	if err != nil {
		return "", fmt.Errorf("failed to read generated image: %w", err)
	}

	// Convert to base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	return base64Data, nil
}

// convertWithWkhtmltopdf uses wkhtmltoimage for conversion
func (h *HTML2ImageConverter) convertWithWkhtmltopdf(htmlContent string, options *HTML2ImageOptions) (string, error) {
	// Check if wkhtmltoimage is available
	if _, err := exec.LookPath("wkhtmltoimage"); err != nil {
		return "", fmt.Errorf("wkhtmltoimage not found: %w", err)
	}

	// Create temporary files
	tempHTML := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.html", time.Now().UnixNano()))
	tempPNG := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.png", time.Now().UnixNano()))

	defer func() {
		os.Remove(tempHTML)
		os.Remove(tempPNG)
	}()

	// Write HTML content to file
	if err := ioutil.WriteFile(tempHTML, []byte(htmlContent), 0644); err != nil {
		return "", fmt.Errorf("failed to write HTML file: %w", err)
	}

	// Prepare wkhtmltoimage arguments
	args := []string{
		"--format", "png",
		"--width", fmt.Sprintf("%d", options.Width),
		"--quality", fmt.Sprintf("%d", options.Quality),
		"--enable-local-file-access",
		"--disable-smart-width",
		tempHTML,
		tempPNG,
	}

	// Execute wkhtmltoimage
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "wkhtmltoimage", args...)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("wkhtmltoimage execution failed: %w, stderr: %s", err, stderr.String())
	}

	// Read the generated image
	imageData, err := ioutil.ReadFile(tempPNG)
	if err != nil {
		return "", fmt.Errorf("failed to read generated image: %w", err)
	}

	// Convert to base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	return base64Data, nil
}

// convertWithPuppeteer uses Node.js Puppeteer for conversion
func (h *HTML2ImageConverter) convertWithPuppeteer(htmlContent string, options *HTML2ImageOptions) (string, error) {
	// Check if node is available
	if _, err := exec.LookPath("node"); err != nil {
		return "", fmt.Errorf("Node.js not found: %w", err)
	}

	// Create temporary files
	tempHTML := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.html", time.Now().UnixNano()))
	tempJS := filepath.Join(h.tempDir, fmt.Sprintf("puppeteer_%d.js", time.Now().UnixNano()))
	tempPNG := filepath.Join(h.tempDir, fmt.Sprintf("card_%d.png", time.Now().UnixNano()))

	defer func() {
		os.Remove(tempHTML)
		os.Remove(tempJS)
		os.Remove(tempPNG)
	}()

	// Write HTML content to file
	if err := ioutil.WriteFile(tempHTML, []byte(htmlContent), 0644); err != nil {
		return "", fmt.Errorf("failed to write HTML file: %w", err)
	}

	// Create Puppeteer script with global module path
	puppeteerScript := fmt.Sprintf(`
// Add global node_modules to module paths
const Module = require('module');
const originalResolveFilename = Module._resolveFilename;
Module._resolveFilename = function (request, parent, isMain) {
  try {
    return originalResolveFilename(request, parent, isMain);
  } catch (error) {
    if (request === 'puppeteer') {
      try {
        return require.resolve('/usr/local/lib/node_modules/puppeteer');
      } catch (e) {
        // Try other common global paths
        const globalPaths = [
          '/usr/lib/node_modules/puppeteer',
          '/opt/node_modules/puppeteer'
        ];
        for (const globalPath of globalPaths) {
          try {
            return require.resolve(globalPath);
          } catch (e) {
            continue;
          }
        }
      }
    }
    throw error;
  }
};

const puppeteer = require('puppeteer');
const path = require('path');

(async () => {
  try {
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: '/usr/bin/chromium-browser', // Use system Chromium
      args: [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-setuid-sandbox',
        '--no-first-run',
        '--no-zygote',
        '--force-device-scale-factor=2', // Higher DPI for better quality
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--font-render-hinting=none', // Better font rendering
        '--enable-font-antialiasing', // Enable font antialiasing
        '--force-color-profile=srgb' // Better color rendering
      ]
    });
    const page = await browser.newPage();
    await page.setViewport({
      width: %d,
      height: %d || 800,
      deviceScaleFactor: 2 // Higher DPI for better quality
    });
    await page.goto('file://%s', { waitUntil: 'networkidle0' });

    // Wait a bit more for complete rendering
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Get the actual content height more precisely with smart cropping
    const contentHeight = await page.evaluate(() => {
      // Ensure no default margins/padding
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.documentElement.style.margin = '0';
      document.documentElement.style.padding = '0';

      // Find the card element first (most accurate)
      const cardElement = document.querySelector('.card');
      if (cardElement) {
        const cardRect = cardElement.getBoundingClientRect();
        const cardStyle = window.getComputedStyle(cardElement);
        const marginBottom = parseFloat(cardStyle.marginBottom) || 0;
        const paddingBottom = parseFloat(cardStyle.paddingBottom) || 0;

        // Return card height with minimal buffer
        return Math.ceil(cardRect.bottom + marginBottom) + 2;
      }

      // Fallback: find the actual content boundary
      const allElements = document.querySelectorAll('*');
      let maxBottom = 0;
      let hasContent = false;

      for (let element of allElements) {
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);

        // Skip elements that are hidden, empty, or just containers
        if (computedStyle.display === 'none' ||
            computedStyle.visibility === 'hidden' ||
            rect.height === 0 ||
            element.tagName === 'HTML' ||
            element.tagName === 'BODY') {
          continue;
        }

        // Check if element has actual content (text, images, etc.)
        const hasText = element.textContent && element.textContent.trim().length > 0;
        const hasImage = element.tagName === 'IMG' || element.tagName === 'SVG';
        const hasBackground = computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
                             computedStyle.backgroundColor !== 'transparent';

        if (hasText || hasImage || hasBackground) {
          hasContent = true;
          const bottom = rect.bottom;
          if (bottom > maxBottom) {
            maxBottom = bottom;
          }
        }
      }

      // Return minimal height with tiny buffer
      return hasContent ? Math.ceil(maxBottom) + 2 : 400;
    });

    console.log('Content height detected:', contentHeight);

    await page.screenshot({
      path: '%s',
      clip: {
        x: 0,
        y: 0,
        width: %d,
        height: contentHeight
      },
      type: 'png'
    });
    await browser.close();
    console.log('Screenshot saved successfully');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
})();
`, options.Width, options.Height, strings.ReplaceAll(tempHTML, "\\", "/"), strings.ReplaceAll(tempPNG, "\\", "/"), options.Width)

	if err := ioutil.WriteFile(tempJS, []byte(puppeteerScript), 0644); err != nil {
		return "", fmt.Errorf("failed to write Puppeteer script: %w", err)
	}

	// Execute Puppeteer script
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "node", tempJS)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("Puppeteer execution failed: %w, stderr: %s", err, stderr.String())
	}

	// Read the generated image
	imageData, err := ioutil.ReadFile(tempPNG)
	if err != nil {
		return "", fmt.Errorf("failed to read generated image: %w", err)
	}

	// Convert to base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	return base64Data, nil
}

// ConvertHTMLToImageTool handles the MCP tool for HTML to image conversion
func (h *ToolHandlers) ConvertHTMLToImage(args map[string]interface{}) (map[string]interface{}, error) {
	htmlContent, ok := args["html_content"].(string)
	if !ok || htmlContent == "" {
		return nil, fmt.Errorf("html_content parameter is required")
	}

	width := getIntArg(args, "width", 393)
	height := getIntArg(args, "height", 0)
	quality := getIntArg(args, "quality", 100)
	format := getStringArg(args, "format", "png")

	options := &HTML2ImageOptions{
		Width:   width,
		Height:  height,
		Quality: quality,
		Format:  format,
	}

	converter := NewHTML2ImageConverter()
	imageBase64, err := converter.ConvertHTMLToImage(htmlContent, options)
	if err != nil {
		return nil, fmt.Errorf("failed to convert HTML to image: %w", err)
	}

	return map[string]interface{}{
		"content": []map[string]interface{}{
			{
				"type": "text",
				"text": "HTML转图片成功！",
			},
			{
				"type":     "image",
				"data":     imageBase64,
				"mimeType": "image/png",
			},
		},
		"image_data": imageBase64,
		"success":    true,
	}, nil
}
