package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the Smart Card MCP server
type Config struct {
	// Transport configuration
	Transport string
	Ports     []string
	BaseURL   string
	Debug     bool

	// API configuration
	OpenAIAPIKey  string
	OpenAIBaseURL string
	OpenAIModel   string

	// ARK API configuration (preferred)
	ARKAPIKey     string
	ARKBaseURL    string
	ARKModel      string

	JinaAPIKey    string
	JinaAPIURL    string

	// Server configuration
	LogLevel     string
	APIKey       string
	SSEAccessKey string
	OutputDir    string
	TemplatesDir string

	// File management configuration
	AutoCleanupFiles bool
	SaveGeneratedContent bool
	PublicURLBase string

	// DStaff Integration
	DStaff *DStaffConfig
}

// DStaffConfig holds dstaff platform configuration
type DStaffConfig struct {
	Enabled         bool
	EndpointURL     string
	AuthServiceURL  string
	FileUploadURL   string
	FileDownloadURL string
	UseOfficialAuth bool
}

// Load loads configuration from environment variables and .env file
func Load() (*Config, error) {
	// Try to load .env file (optional)
	if err := godotenv.Load(); err != nil {
		log.Printf("⚠️  No .env file found or error loading it: %v", err)
	}

	cfg := &Config{
		// Default values
		Transport: getEnv("MCP_TRANSPORT", "stdio"),
		Ports:     strings.Split(getEnv("MCP_PORTS", "48083,48084"), ","),
		BaseURL:   getEnv("MCP_BASE_URL", "http://localhost:48083"),
		Debug:     getEnvBool("MCP_DEBUG", false),

		// API configuration
		OpenAIAPIKey:  getEnv("OPENAI_API_KEY", ""),
		OpenAIBaseURL: getEnv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
		OpenAIModel:   getEnv("OPENAI_MODEL", "gpt-3.5-turbo"),

		// ARK API configuration (preferred)
		ARKAPIKey:     getEnv("ARK_API_KEY", ""),
		ARKBaseURL:    getEnv("ARK_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3"),
		ARKModel:      getEnv("ARK_MODEL", "deepseek-v3-250324"),

		JinaAPIKey:    getEnv("JINA_API_KEY", ""),
		JinaAPIURL:    getEnv("JINA_API_URL", "https://r.jina.ai/"),

		// Server configuration
		LogLevel:     getEnv("LOG_LEVEL", "INFO"),
		APIKey:       getEnv("API_KEY", ""),
		SSEAccessKey: getEnv("SSE_ACCESS_KEY", ""),
		OutputDir:    getEnv("OUTPUT_DIR", "output"),
		TemplatesDir: getEnv("TEMPLATES_DIR", "templates"),

		// File management configuration
		AutoCleanupFiles: getEnvBool("AUTO_CLEANUP_FILES", false),
		SaveGeneratedContent: getEnvBool("SAVE_GENERATED_CONTENT", true),
		PublicURLBase: getEnv("PUBLIC_URL_BASE", "http://localhost:48089"),

		// DStaff Integration
		DStaff: loadDStaffConfig(),
	}

	// Validate required configuration
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// validate checks if required configuration is present
func (c *Config) validate() error {
	// Prefer ARK API, fallback to OpenAI
	if c.ARKAPIKey == "" && c.OpenAIAPIKey == "" {
		return fmt.Errorf("either ARK_API_KEY or OPENAI_API_KEY is required")
	}

	if c.ARKAPIKey != "" {
		log.Printf("✅ Using ARK API with model: %s", c.ARKModel)
	} else {
		log.Printf("✅ Using OpenAI API with model: %s", c.OpenAIModel)
	}

	if c.JinaAPIKey == "" {
		log.Printf("⚠️  JINA_API_KEY not set, web fetching will be disabled")
	}

	return nil
}

// LogConfig logs the current configuration (without sensitive data)
func (c *Config) LogConfig() {
	log.Printf("🔧 Smart Card MCP Server Configuration:")
	log.Printf("   Transport: %s", c.Transport)
	log.Printf("   Ports: %v", c.Ports)
	log.Printf("   Base URL: %s", c.BaseURL)
	log.Printf("   Debug: %v", c.Debug)

	// Log ARK API configuration if available
	if c.ARKAPIKey != "" {
		log.Printf("   ARK Model: %s", c.ARKModel)
		log.Printf("   ARK Base URL: %s", c.ARKBaseURL)
		log.Printf("   ARK API Key: %s", maskAPIKey(c.ARKAPIKey))
	}

	// Log OpenAI configuration if available
	if c.OpenAIAPIKey != "" {
		log.Printf("   OpenAI Model: %s", c.OpenAIModel)
		log.Printf("   OpenAI Base URL: %s", c.OpenAIBaseURL)
		log.Printf("   OpenAI API Key: %s", maskAPIKey(c.OpenAIAPIKey))
	}

	log.Printf("   Jina API Key: %s", maskAPIKey(c.JinaAPIKey))
	log.Printf("   Log Level: %s", c.LogLevel)
	log.Printf("   API Key: %s", maskAPIKey(c.APIKey))
	log.Printf("   Output Dir: %s", c.OutputDir)
	log.Printf("   Templates Dir: %s", c.TemplatesDir)
	log.Printf("   Auto Cleanup Files: %v", c.AutoCleanupFiles)

	// Log DStaff configuration if available
	if c.DStaff.Enabled {
		log.Printf("   DStaff Enabled: %v", c.DStaff.Enabled)
		log.Printf("   DStaff Endpoint: %s", c.DStaff.EndpointURL)
		log.Printf("   DStaff Use Official Auth: %v", c.DStaff.UseOfficialAuth)
	} else {
		log.Printf("   DStaff: Disabled")
	}
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool gets a boolean environment variable with a default value
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// maskAPIKey masks an API key for logging
func maskAPIKey(key string) string {
	if key == "" {
		return "Not set"
	}
	if len(key) <= 8 {
		return "***"
	}
	return key[:4] + "..." + key[len(key)-4:]
}

// loadDStaffConfig loads dstaff configuration from environment variables
func loadDStaffConfig() *DStaffConfig {
	enabled := os.Getenv("DSTAFF_ENABLED") == "true"
	if !enabled {
		return &DStaffConfig{Enabled: false}
	}

	endpointURL := os.Getenv("DSTAFF_ENDPOINT_URL")
	if endpointURL == "" {
		endpointURL = "http://10.50.5.3:8800" // Default endpoint
	}

	return &DStaffConfig{
		Enabled:         true,
		EndpointURL:     endpointURL,
		AuthServiceURL:  endpointURL + "/api/v1/mcp/validateToken",
		FileUploadURL:   endpointURL + "/api/v1/mcp/file/upload",
		FileDownloadURL: endpointURL + "/api/v1/mcp/file/download",
		UseOfficialAuth: os.Getenv("DSTAFF_USE_OFFICIAL_AUTH") == "true",
	}
}
