package transport

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"

	"smart-card-mcp/internal/api"
	"smart-card-mcp/internal/auth"
	"smart-card-mcp/internal/config"
	"smart-card-mcp/internal/tasks"
	"smart-card-mcp/internal/tools"
)

// MCPServerWrapper wraps the MCP server with different transport modes
type MCPServerWrapper struct {
	config       *config.Config
	mcpServer    *server.MCPServer
	toolHandlers *tools.ToolHandlers
	taskManager  *tasks.TaskManager
	apiServer    *api.APIServer
	// 用于发送通知的函数，在不同传输模式下会有不同的实现
	notificationSender func(notification interface{}) error
}

// NewMCPServerWrapper creates a new MCP server wrapper
func NewMCPServerWrapper(cfg *config.Config) *MCPServerWrapper {
	toolHandlers := tools.NewToolHandlers(cfg)

	// Create task manager (max 10 concurrent tasks, 1 hour TTL)
	taskManager := tasks.NewTaskManager(10, time.Hour)

	// Create task executor
	taskExecutor := tasks.NewTaskExecutor(taskManager, toolHandlers)

	// Set task manager and executor in tool handlers for async operations
	toolHandlers.SetTaskManager(taskManager)
	toolHandlers.SetTaskExecutor(taskExecutor)

	// Create MCP server with necessary capabilities
	mcpServer := server.NewMCPServer(
		"smart-card-mcp",
		"1.0.0",
		server.WithToolCapabilities(true),
		server.WithLogging(),
	)

	// Create API server
	apiServer := api.NewAPIServer(cfg, taskManager, toolHandlers)

	// Server info will be set automatically by the MCP library

	wrapper := &MCPServerWrapper{
		config:       cfg,
		mcpServer:    mcpServer,
		toolHandlers: toolHandlers,
		taskManager:  taskManager,
		apiServer:    apiServer,
	}

	// Register tools
	wrapper.registerTools()

	// 设置进度发送函数（在注册工具之后）
	wrapper.setupProgressSending()

	return wrapper
}

// setupProgressSending sets up progress notification sending
func (w *MCPServerWrapper) setupProgressSending() {
	// 设置工具处理器的进度发送函数
	// 这里只是设置占位符，实际的发送逻辑在工具处理函数中
	w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
		log.Printf("📊 Progress notification prepared: %.0f/%.0f - %s",
			notification.Params.Progress,
			notification.Params.Total,
			notification.Params.Message)
		// 实际发送逻辑在工具处理函数中处理
		return nil
	})

	// 设置工具处理器的日志发送函数
	w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
		log.Printf("📝 Log notification prepared: [%s] %s",
			string(notification.Params.Level),
			notification.Params.Data)
		// 实际发送逻辑在工具处理函数中处理
		return nil
	})
}



// registerTools registers all available tools with the MCP server
func (w *MCPServerWrapper) registerTools() {
	allTools := tools.GetAllTools(w.config)

	for _, tool := range allTools {
		w.mcpServer.AddTool(tool, w.createToolHandler(tool.Name))
	}

	log.Printf("✅ Registered %d tools", len(allTools))
}

// createToolHandler creates a handler function for a specific tool
func (w *MCPServerWrapper) createToolHandler(toolName string) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		log.Printf("🔧 Executing tool: %s", toolName)

		// Add DStaff auth context if available
		ctx = w.addDStaffAuthContext(ctx, request)

		// 设置当前请求的进度发送函数，使用当前的 context
		w.toolHandlers.SetProgressSendFunc(func(notification *mcp.ProgressNotification) error {
			log.Printf("📊 Sending progress notification: %.0f/%.0f - %s",
				notification.Params.Progress,
				notification.Params.Total,
				notification.Params.Message)

			// 如果有进度令牌，更新任务管理器中的进度
			if notification.Params.ProgressToken != nil {
				if taskID, ok := notification.Params.ProgressToken.(string); ok && taskID != "" {
					if w.taskManager != nil {
						current := int(notification.Params.Progress)
						total := int(notification.Params.Total)
						message := notification.Params.Message

						log.Printf("🔍 Debug: Updating task progress - taskID: %s, current: %d, total: %d, message: %s", taskID, current, total, message)
						err := w.taskManager.UpdateTaskProgress(taskID, current, total, message)
						if err != nil {
							log.Printf("❌ Failed to update task progress: %v", err)
						}
					}
				}
			}

			// 检查客户端会话是否已初始化
			session := server.ClientSessionFromContext(ctx)
			if session == nil {
				log.Printf("⚠️ No client session in context, using fallback logging")
				log.Printf("📊 Progress: %.0f/%.0f - %s",
					notification.Params.Progress,
					notification.Params.Total,
					notification.Params.Message)
				return nil
			}

			
			if !session.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				session.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.mcpServer.SendNotificationToClient(ctx, "notifications/progress", map[string]any{
				"progress":      notification.Params.Progress,
				"total":         notification.Params.Total,
				"progressToken": notification.Params.ProgressToken,
				"message":       notification.Params.Message,
			})

			if err != nil {
				log.Printf("❌ Failed to send progress notification: %v", err)
				// 使用日志作为后备
				log.Printf("📊 Progress (fallback): %.0f/%.0f - %s",
					notification.Params.Progress,
					notification.Params.Total,
					notification.Params.Message)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Progress notification sent to current client")
			return nil
		})

		// 设置当前请求的日志发送函数
		w.toolHandlers.SetLogSendFunc(func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Sending log notification: [%s] %s",
				string(notification.Params.Level),
				notification.Params.Data)

			// 检查客户端会话是否已初始化
			session := server.ClientSessionFromContext(ctx)
			if session == nil {
				log.Printf("⚠️ No client session in context, using fallback logging")
				log.Printf("📝 Log [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil
			}

			if !session.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				session.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.mcpServer.SendNotificationToClient(ctx, "notifications/message", map[string]any{
				"level":  string(notification.Params.Level),
				"logger": notification.Params.Logger,
				"data":   notification.Params.Data,
			})

			if err != nil {
				log.Printf("❌ Failed to send log notification: %v", err)
				// 使用日志作为后备
				log.Printf("📝 Log (fallback) [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Log notification sent to current client")
			return nil
		})

		var result map[string]interface{}
		var err error

		// Convert arguments to map[string]interface{}
		args, ok := request.Params.Arguments.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid arguments format")
		}

		// 路由到相应的工具处理器
		switch toolName {
		// 同步工具
		case "generate_card_from_text":
			result, err = w.toolHandlers.GenerateCardFromTextWithProgress(ctx, request, args)
		case "generate_card_from_url":
			result, err = w.toolHandlers.GenerateCardFromURLWithProgress(ctx, request, args)

		// 异步工具
		case "create_task_from_text":
			result, err = w.toolHandlers.CreateTaskFromText(args)
		case "create_task_from_url":
			result, err = w.toolHandlers.CreateTaskFromURL(args)
		case "get_task_status":
			result, err = w.toolHandlers.GetTaskStatus(args)
		case "get_task_result":
			result, err = w.toolHandlers.GetTaskResultWithContext(ctx, args)

		default:
			return nil, fmt.Errorf("unknown tool: %s", toolName)
		}

		if err != nil {
			log.Printf("❌ Tool execution failed: %v", err)
			return &mcp.CallToolResult{
				Content: []mcp.Content{
					mcp.NewTextContent(fmt.Sprintf("Error: %v", err)),
				},
				IsError: true,
			}, nil
		}

		log.Printf("✅ Tool executed successfully: %s", toolName)

		// Convert result to MCP content
		content := w.convertResultToContent(result)

		return &mcp.CallToolResult{
			Content: content,
			IsError: false,
		}, nil
	}
}

// convertResultToContent converts tool result to MCP content
func (w *MCPServerWrapper) convertResultToContent(result map[string]interface{}) []mcp.Content {
	var content []mcp.Content

	// Check if result already contains properly formatted content
	if contentArray, exists := result["content"]; exists {
		if contentSlice, ok := contentArray.([]map[string]interface{}); ok {
			// Convert each content item to mcp.Content
			for _, item := range contentSlice {
				if itemType, hasType := item["type"]; hasType {
					switch itemType {
					case "text":
						if text, hasText := item["text"]; hasText {
							if textStr, isString := text.(string); isString {
								content = append(content, mcp.NewTextContent(textStr))
							}
						}
					case "image":
						if data, hasData := item["data"]; hasData {
							if dataStr, isString := data.(string); isString {
								if mimeType, hasMime := item["mimeType"]; hasMime {
									if mimeStr, isMimeString := mimeType.(string); isMimeString {
										content = append(content, mcp.NewImageContent(dataStr, mimeStr))
									}
								}
							}
						}
					}
				}
			}
			return content
		}
	}

	// Fallback: Convert result to JSON-like text representation
	var parts []string
	for key, value := range result {
		switch v := value.(type) {
		case string:
			parts = append(parts, fmt.Sprintf("%s: %s", key, v))
		case []string:
			parts = append(parts, fmt.Sprintf("%s: [%s]", key, strings.Join(v, ", ")))
		case int:
			parts = append(parts, fmt.Sprintf("%s: %d", key, v))
		case bool:
			parts = append(parts, fmt.Sprintf("%s: %t", key, v))
		default:
			// Skip the content field to avoid double processing
			if key != "content" {
				parts = append(parts, fmt.Sprintf("%s: %v", key, v))
			}
		}
	}

	content = append(content, mcp.NewTextContent(strings.Join(parts, "\n")))

	return content
}

// ServeStdio starts the server in stdio mode
func (w *MCPServerWrapper) ServeStdio() error {
	log.Println("🚀 Starting Smart Card MCP Server in stdio mode...")
	return server.ServeStdio(w.mcpServer)
}

// ServeSSE starts the server in SSE mode with authentication
func (w *MCPServerWrapper) ServeSSE() error {
	if len(w.config.Ports) == 0 {
		return fmt.Errorf("no ports configured for SSE mode")
	}

	port := w.config.Ports[0]
	log.Printf("🚀 Starting Smart Card MCP Server in SSE mode on port %s...", port)

	// Create authenticated SSE server
	sseServer := w.createAuthenticatedSSEServer(port)
	return sseServer.Start(":" + port)
}

// ServeStreamableHTTP starts the server in streamable HTTP mode with authentication
func (w *MCPServerWrapper) ServeStreamableHTTP() error {
	if len(w.config.Ports) < 2 {
		return fmt.Errorf("need at least 2 ports configured for streamable HTTP mode")
	}

	port := w.config.Ports[1]
	log.Printf("🚀 Starting Smart Card MCP Server in streamable HTTP mode on port %s...", port)

	// Create authenticated HTTP server with API routes
	httpServer := w.createAuthenticatedHTTPServerWithAPI(port)
	return httpServer.ListenAndServe()
}

// ServeMixed starts the server in mixed mode (multiple transports) with authentication
func (w *MCPServerWrapper) ServeMixed() error {
	log.Println("🚀 Starting Smart Card MCP Server in mixed mode...")

	// Start SSE server in a goroutine
	if len(w.config.Ports) > 0 {
		go func() {
			port := w.config.Ports[0]
			log.Printf("📡 SSE server starting on port %s", port)
			sseServer := w.createAuthenticatedSSEServer(port)
			if err := sseServer.Start(":" + port); err != nil {
				log.Printf("❌ SSE server error: %v", err)
			}
		}()
	}

	// Start HTTP server with API routes in a goroutine
	if len(w.config.Ports) > 1 {
		go func() {
			port := w.config.Ports[1]
			log.Printf("🌐 HTTP server starting on port %s", port)
			httpServer := w.createAuthenticatedHTTPServerWithAPI(port)
			if err := httpServer.ListenAndServe(); err != nil {
				log.Printf("❌ HTTP server error: %v", err)
			}
		}()
	}

	// Add health check endpoint
	w.setupHealthCheck()

	// Keep the main goroutine alive
	select {}
}

// setupHealthCheck sets up health check endpoints
func (w *MCPServerWrapper) setupHealthCheck() {
	http.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"smart-card-mcp","version":"1.0.0"}`))
	})

	// Start health check server on the first available port
	if len(w.config.Ports) > 0 {
		port := w.config.Ports[0]
		portInt, _ := strconv.Atoi(port)
		healthPort := portInt + 1000 // Use a different port for health check

		go func() {
			log.Printf("🏥 Health check server starting on port %d", healthPort)
			if err := http.ListenAndServe(fmt.Sprintf(":%d", healthPort), nil); err != nil {
				log.Printf("❌ Health check server error: %v", err)
			}
		}()
	}
}

// createAuthenticatedSSEServer creates an SSE server with authentication middleware (with DStaff support)
func (w *MCPServerWrapper) createAuthenticatedSSEServer(port string) *server.SSEServer {
	return w.createAuthenticatedSSEServerWithDStaff(port)
}

// createAuthenticatedHTTPServer creates an HTTP server with authentication middleware
func (w *MCPServerWrapper) createAuthenticatedHTTPServer(port string) *http.Server {
	mux := http.NewServeMux()

	// Add health check endpoint
	mux.HandleFunc("/health", w.healthCheckHandler)

	// Add main MCP endpoint with authentication middleware
	mux.HandleFunc("/mcp", w.authenticatedMCPHandler)

	return &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}
}

// healthCheckHandler handles health check requests
func (w *MCPServerWrapper) healthCheckHandler(rw http.ResponseWriter, r *http.Request) {
	rw.Header().Set("Content-Type", "application/json; charset=utf-8")
	rw.WriteHeader(http.StatusOK)
	rw.Write([]byte(`{"status":"healthy","service":"smart-card-mcp","version":"1.0.0"}`))
}

// authenticatedMCPHandler handles MCP requests with authentication (with DStaff support)
func (w *MCPServerWrapper) authenticatedMCPHandler(rw http.ResponseWriter, r *http.Request) {
	// Validate authentication
	if !w.validateAuthentication(r) {
		log.Printf("❌ MCP connection rejected due to authentication failure from %s", r.RemoteAddr)
		http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Add DStaff auth context if enabled
	ctx := r.Context()
	if w.config.DStaff.Enabled {
		log.Printf("🌐 === DStaff MCP Request Processing ===")
		log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		log.Printf("🔍 Request Headers:")

		// Log relevant headers
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
		} else {
			log.Printf("   - Authorization: Not provided")
		}

		taskIDHeader := r.Header.Get("X-Task-ID")
		if taskIDHeader != "" {
			log.Printf("   - X-Task-ID: %s", taskIDHeader)
		} else {
			log.Printf("   - X-Task-ID: Not provided")
		}

		contentType := r.Header.Get("Content-Type")
		if contentType != "" {
			log.Printf("   - Content-Type: %s", contentType)
		}

		// Log query parameters
		log.Printf("🔍 Query Parameters:")
		if len(r.URL.Query()) > 0 {
			for key, values := range r.URL.Query() {
				log.Printf("   - %s: %v", key, values)
			}
		} else {
			log.Printf("   - No query parameters")
		}

		// Extract token from Authorization header and add to context
		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			ctx = context.WithValue(ctx, "authorization_token", token)
			log.Printf("🔐 Bearer token extracted and added to context")
		}

		// Extract task_id and add to context
		taskID := auth.ExtractTaskIDFromContext(ctx, r)
		if taskID != "" {
			ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
			log.Printf("📋 Task ID extracted and added to context: %s", taskID)
		} else {
			log.Printf("⚠️ No task ID found in request")
		}

		log.Printf("✅ DStaff MCP request processing completed")
	}

	// Update request with new context
	r = r.WithContext(ctx)

	// Create streamable HTTP server and handle the request
	httpServer := server.NewStreamableHTTPServer(w.mcpServer)
	httpServer.ServeHTTP(rw, r)
}

// validateAuthentication validates authentication for SSE and MCP endpoints (supports both key-based and DStaff auth)
func (w *MCPServerWrapper) validateAuthentication(r *http.Request) bool {
	// Check if DStaff official auth is enabled (highest priority)
	if w.config.DStaff.Enabled && w.config.DStaff.UseOfficialAuth {
		// Extract Bearer token from Authorization header
		authHeader := r.Header.Get("Authorization")
		log.Printf("DStaff Auth Check: %s %s from %s, Auth header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)

		if authHeader == "" {
			log.Printf("Access denied: No Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		// Extract token from Bearer header
		if !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("Access denied: Invalid Authorization header format for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			log.Printf("Access denied: Empty token in Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		// Validate token with DStaff service
		if auth.ValidateTokenWithDStaff(w.config.DStaff, token) {
			log.Printf("Access granted (DStaff auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return true
		}

		log.Printf("Access denied: DStaff token validation failed for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return false
	}

	// Fallback to key-based authentication if DStaff is not enabled or not using official auth
	if w.config.SSEAccessKey == "" {
		log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// Check for key-based authentication on /sse and /mcp endpoints
	if r.URL.Path == "/sse" || r.URL.Path == "/mcp" {
		providedKey := r.URL.Query().Get("key")
		log.Printf("Key Auth Check: %s %s from %s, query key: '%s', expected key: '%s'", r.Method, r.URL.Path, r.RemoteAddr, providedKey, w.config.SSEAccessKey)

		if providedKey == "" {
			log.Printf("Access denied: No key provided for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		if providedKey != w.config.SSEAccessKey {
			log.Printf("Access denied: Invalid key '%s' (expected: '%s') for %s %s from %s", providedKey, w.config.SSEAccessKey, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted (key auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// No authentication required for other endpoints (like /health)
	log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	return true
}

// createAuthenticatedHTTPServerWithAPI creates an HTTP server with API routes (with DStaff support)
func (w *MCPServerWrapper) createAuthenticatedHTTPServerWithAPI(port string) *http.Server {
	// Create a new HTTP mux
	mux := http.NewServeMux()

	// Add MCP streamable HTTP handler with DStaff auth context support
	mux.HandleFunc("/mcp", func(rw http.ResponseWriter, r *http.Request) {
		// Add DStaff auth context if enabled
		ctx := r.Context()
		if w.config.DStaff.Enabled {
			log.Printf("🌐 === DStaff HTTP API Request Processing ===")
			log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			log.Printf("🔍 Request Headers:")

			// Log relevant headers
			authHeader := r.Header.Get("Authorization")
			if authHeader != "" {
				log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
			} else {
				log.Printf("   - Authorization: Not provided")
			}

			taskIDHeader := r.Header.Get("X-Task-ID")
			if taskIDHeader != "" {
				log.Printf("   - X-Task-ID: %s", taskIDHeader)
			} else {
				log.Printf("   - X-Task-ID: Not provided")
			}

			contentType := r.Header.Get("Content-Type")
			if contentType != "" {
				log.Printf("   - Content-Type: %s", contentType)
			}

			// Log query parameters
			log.Printf("🔍 Query Parameters:")
			if len(r.URL.Query()) > 0 {
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}
			} else {
				log.Printf("   - No query parameters")
			}

			// Extract token from Authorization header and add to context
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				ctx = context.WithValue(ctx, "authorization_token", token)
				log.Printf("🔐 Bearer token extracted and added to context")
			}

			// Extract task_id and add to context
			taskID := auth.ExtractTaskIDFromContext(ctx, r)
			if taskID != "" {
				ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
				log.Printf("📋 Task ID extracted and added to context: %s", taskID)
			} else {
				log.Printf("⚠️ No task ID found in request")
			}

			log.Printf("✅ DStaff HTTP API request processing completed")
		}

		// Update request with new context
		r = r.WithContext(ctx)

		// Handle with streamable server
		streamableServer := server.NewStreamableHTTPServer(w.mcpServer)
		streamableServer.ServeHTTP(rw, r)
	})

	// Add API routes
	apiRouter := w.apiServer.GetRouter()
	mux.Handle("/api/", apiRouter)

	// Add health check
	mux.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"smart-card-mcp","version":"1.0.0"}`))
	})

	// Add root redirect to docs
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/api/v1/docs", http.StatusFound)
			return
		}
		http.NotFound(w, r)
	})

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	log.Printf("🌐 HTTP API server will be available at http://localhost:%s", port)
	log.Printf("📚 API documentation: http://localhost:%s/api/v1/docs", port)
	log.Printf("🔗 MCP endpoint: http://localhost:%s/mcp", port)

	return server
}

// addDStaffAuthContext adds DStaff authentication context to the request context
func (w *MCPServerWrapper) addDStaffAuthContext(ctx context.Context, request mcp.CallToolRequest) context.Context {
	log.Printf("🔍 === DStaff Auth Context Setup ===")
	log.Printf("🔧 DStaff Config Enabled: %v", w.config.DStaff.Enabled)
	log.Printf("🛠️ Tool Name: %s", request.Params.Name)

	if !w.config.DStaff.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping auth context setup")
		return ctx
	}

	// Log current context values
	log.Printf("🔍 Current Context Values:")
	if authToken := ctx.Value("authorization_token"); authToken != nil {
		if tokenStr, ok := authToken.(string); ok {
			log.Printf("   - Authorization Token: %s", maskTokenForLog(tokenStr))
		}
	} else {
		log.Printf("   - Authorization Token: Not found")
	}

	if taskID := ctx.Value("dstaff_task_id"); taskID != nil {
		if taskIDStr, ok := taskID.(string); ok {
			log.Printf("   - DStaff Task ID: %s", taskIDStr)
		}
	} else {
		log.Printf("   - DStaff Task ID: Not found")
	}

	// Log request parameters for debugging
	log.Printf("🔍 Request Parameters:")
	if args, ok := request.Params.Arguments.(map[string]interface{}); ok {
		for key, value := range args {
			if key == "task_id" {
				log.Printf("   - %s: %v", key, value)
			} else {
				// Truncate long values for readability
				valueStr := fmt.Sprintf("%v", value)
				if len(valueStr) > 100 {
					valueStr = valueStr[:100] + "..."
				}
				log.Printf("   - %s: %s", key, valueStr)
			}
		}
	}

	log.Printf("✅ DStaff auth context setup completed")
	return ctx
}

// createAuthenticatedSSEServerWithDStaff creates an SSE server with DStaff authentication support
func (w *MCPServerWrapper) createAuthenticatedSSEServerWithDStaff(port string) *server.SSEServer {
	baseURL := w.config.BaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("http://localhost:%s", port)
	}

	// Create authentication middleware with DStaff support
	authMiddleware := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(rw http.ResponseWriter, r *http.Request) {
			// Skip authentication for health check endpoint
			if r.URL.Path == "/health" {
				w.healthCheckHandler(rw, r)
				return
			}

			// Validate authentication (supports both key-based and DStaff)
			if !w.validateAuthentication(r) {
				log.Printf("❌ SSE connection rejected due to authentication failure from %s", r.RemoteAddr)
				http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
				return
			}

			// Add DStaff auth context if enabled
			ctx := r.Context()
			if w.config.DStaff.Enabled {
				log.Printf("🌐 === DStaff SSE Request Processing ===")
				log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
				log.Printf("🔍 Request Headers:")

				// Log relevant headers
				authHeader := r.Header.Get("Authorization")
				if authHeader != "" {
					log.Printf("   - Authorization: %s", maskAuthHeaderForLog(authHeader))
				} else {
					log.Printf("   - Authorization: Not provided")
				}

				taskIDHeader := r.Header.Get("X-Task-ID")
				if taskIDHeader != "" {
					log.Printf("   - X-Task-ID: %s", taskIDHeader)
				} else {
					log.Printf("   - X-Task-ID: Not provided")
				}

				// Log query parameters
				log.Printf("🔍 Query Parameters:")
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}

				// Extract token from Authorization header and add to context
				if strings.HasPrefix(authHeader, "Bearer ") {
					token := strings.TrimPrefix(authHeader, "Bearer ")
					ctx = context.WithValue(ctx, "authorization_token", token)
					log.Printf("🔐 Bearer token extracted and added to context")
				}

				// Extract task_id and add to context
				taskID := auth.ExtractTaskIDFromContext(ctx, r)
				if taskID != "" {
					ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
					log.Printf("📋 Task ID extracted and added to context: %s", taskID)
				} else {
					log.Printf("⚠️ No task ID found in request")
				}

				log.Printf("✅ DStaff SSE request processing completed")
			}

			// Update request with new context
			r = r.WithContext(ctx)

			// Authentication passed, continue to SSE server
			next.ServeHTTP(rw, r)
		})
	}

	// Create SSE server
	sseServer := server.NewSSEServer(w.mcpServer)

	// Create a custom HTTP server with authentication middleware
	customServer := &http.Server{
		Handler: authMiddleware(sseServer),
	}

	// Return SSE server with custom HTTP server
	return server.NewSSEServer(
		w.mcpServer,
		server.WithHTTPServer(customServer),
	)
}

// maskTokenForLog masks a token for safe logging
func maskTokenForLog(token string) string {
	if token == "" {
		return "Not set"
	}
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskAuthHeaderForLog masks an Authorization header for safe logging
func maskAuthHeaderForLog(authHeader string) string {
	if authHeader == "" {
		return "Not provided"
	}

	if strings.HasPrefix(authHeader, "Bearer ") {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		return "Bearer " + maskTokenForLog(token)
	}

	// For other auth types, just show the type
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) == 2 {
		return parts[0] + " ***"
	}

	return "***"
}
