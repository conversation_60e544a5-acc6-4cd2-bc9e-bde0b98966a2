package api

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/mark3labs/mcp-go/mcp"
	"smart-card-mcp/internal/config"
	"smart-card-mcp/internal/tasks"
	"smart-card-mcp/internal/tools"
)

// APIServer represents the HTTP API server
type APIServer struct {
	config       *config.Config
	taskManager  *tasks.TaskManager
	toolHandlers *tools.ToolHandlers
	router       *mux.Router
}

// NewAPIServer creates a new API server
func NewAPIServer(cfg *config.Config, taskManager *tasks.TaskManager, toolHandlers *tools.ToolHandlers) *APIServer {
	server := &APIServer{
		config:       cfg,
		taskManager:  taskManager,
		toolHandlers: toolHandlers,
		router:       mux.NewRouter(),
	}

	server.setupRoutes()
	return server
}

// getValidTools returns a map of valid tool names
func (s *APIServer) getValidTools() map[string]bool {
	validTools := make(map[string]bool)
	allTools := tools.GetAllTools(s.config)
	for _, tool := range allTools {
		validTools[tool.Name] = true
	}
	return validTools
}

// GetRouter returns the HTTP router
func (s *APIServer) GetRouter() *mux.Router {
	return s.router
}

// setupRoutes sets up all API routes
func (s *APIServer) setupRoutes() {
	// API v1 routes
	v1 := s.router.PathPrefix("/api/v1").Subrouter()

	// Middleware
	v1.Use(s.corsMiddleware)
	v1.Use(s.utf8Middleware)
	v1.Use(s.loggingMiddleware)
	v1.Use(s.authMiddleware)

	// Tool routes
	v1.HandleFunc("/tools", s.handleListTools).Methods("GET")
	v1.HandleFunc("/tools/{tool_name}", s.handleGetTool).Methods("GET")
	v1.HandleFunc("/tools/{tool_name}/sync", s.handleSyncToolCall).Methods("POST")
	v1.HandleFunc("/tools/{tool_name}/async", s.handleAsyncToolCall).Methods("POST")

	// Task routes
	v1.HandleFunc("/tasks", s.handleListTasks).Methods("GET")
	v1.HandleFunc("/tasks/{task_id}", s.handleGetTask).Methods("GET")
	v1.HandleFunc("/tasks/{task_id}/status", s.handleGetTaskStatus).Methods("GET")
	v1.HandleFunc("/tasks/{task_id}", s.handleCancelTask).Methods("DELETE")
	v1.HandleFunc("/tasks/{task_id}/result", s.handleGetTaskResult).Methods("GET")
	v1.HandleFunc("/tasks/{task_id}/progress", s.handleGetTaskProgress).Methods("GET")

	// System routes
	v1.HandleFunc("/health", s.handleHealth).Methods("GET")
	v1.HandleFunc("/stats", s.handleStats).Methods("GET")

	// Documentation routes
	v1.HandleFunc("/docs", s.handleDocs).Methods("GET")
	v1.HandleFunc("/docs/openapi.json", s.handleOpenAPI).Methods("GET")

	// Debug routes disabled in production
	// v1.HandleFunc("/debug/public", s.handleDebugPublic).Methods("GET")
	// v1.HandleFunc("/debug/static-test", s.handleStaticTest).Methods("GET")

	// Public file serving under /api/v1
	publicDir := filepath.Join(s.config.OutputDir, "public")
	log.Printf("🌐 配置静态文件服务 - 路径: /api/v1/public/output/, 目录: %s", publicDir)

	// Create public directory if it doesn't exist
	if err := os.MkdirAll(publicDir, 0755); err != nil {
		log.Printf("❌ 创建公共目录失败: %v", err)
	} else {
		log.Printf("✅ 公共目录已准备: %s", publicDir)
	}

	// Create a logging file server with proper path handling
	fileServer := http.FileServer(http.Dir(publicDir))

	// Create a wrapper that logs before stripping the prefix
	loggingWrapper := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		originalURL := r.URL.String()
		originalPath := r.URL.Path

		log.Printf("🔍 静态文件请求 - 原始URL: %s", originalURL)
		log.Printf("📁 原始路径: %s", originalPath)

		// Strip the prefix manually to get the actual file path
		if strings.HasPrefix(originalPath, "/api/v1/public/output/") {
			fileName := strings.TrimPrefix(originalPath, "/api/v1/public/output/")
			fullPath := filepath.Join(publicDir, fileName)

			log.Printf("📄 请求的文件名: %s", fileName)
			log.Printf("📂 完整文件路径: %s", fullPath)

			// Check if file exists
			if stat, err := os.Stat(fullPath); err == nil {
				log.Printf("✅ 文件存在 - 大小: %d 字节, 修改时间: %v", stat.Size(), stat.ModTime())
			} else {
				log.Printf("❌ 文件不存在: %v", err)

				// List directory contents for debugging
				if files, err := os.ReadDir(publicDir); err == nil {
					log.Printf("📋 目录内容 (%s):", publicDir)
					for _, file := range files {
						info, _ := file.Info()
						log.Printf("  - %s (大小: %d, 目录: %t)", file.Name(), info.Size(), file.IsDir())
					}
				} else {
					log.Printf("❌ 无法读取目录: %v", err)
				}
			}
		}

		// Now strip the prefix and serve the file
		http.StripPrefix("/api/v1/public/output/", fileServer).ServeHTTP(w, r)
	})

	// Add static file serving to v1 router
	v1.PathPrefix("/public/output/").Handler(loggingWrapper)
}

// Standard API response structures
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
}

type APIError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// Tool call request structures
type ToolCallRequest struct {
	Arguments   map[string]interface{} `json:"arguments"`
	CallbackURL string                 `json:"callback_url,omitempty"`
}

type AsyncToolResponse struct {
	TaskID            string    `json:"task_id"`
	Status            string    `json:"status"`
	CreatedAt         time.Time `json:"created_at"`
	EstimatedDuration int       `json:"estimated_duration"`
}

// Middleware functions
func (s *APIServer) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-API-Key")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// utf8Middleware ensures all responses explicitly declare UTF-8 encoding
func (s *APIServer) utf8Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Wrap the ResponseWriter to intercept header setting
		wrapper := &utf8ResponseWriter{ResponseWriter: w}
		next.ServeHTTP(wrapper, r)
	})
}

// utf8ResponseWriter wraps http.ResponseWriter to ensure UTF-8 encoding
type utf8ResponseWriter struct {
	http.ResponseWriter
	headerWritten bool
}

func (w *utf8ResponseWriter) Header() http.Header {
	return w.ResponseWriter.Header()
}

func (w *utf8ResponseWriter) WriteHeader(statusCode int) {
	if !w.headerWritten {
		// Ensure JSON responses have UTF-8 charset
		contentType := w.Header().Get("Content-Type")
		if contentType == "application/json" {
			w.Header().Set("Content-Type", "application/json; charset=utf-8")
		}
		w.headerWritten = true
	}
	w.ResponseWriter.WriteHeader(statusCode)
}

func (w *utf8ResponseWriter) Write(data []byte) (int, error) {
	if !w.headerWritten {
		w.WriteHeader(http.StatusOK)
	}
	return w.ResponseWriter.Write(data)
}

func (s *APIServer) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		next.ServeHTTP(w, r)

		log.Printf("🌐 %s %s - %v", r.Method, r.URL.Path, time.Since(start))
	})
}

func (s *APIServer) authMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip auth for health check, docs, and public static files
		if strings.HasSuffix(r.URL.Path, "/health") ||
		   strings.HasSuffix(r.URL.Path, "/docs") ||
		   strings.HasSuffix(r.URL.Path, "/openapi.json") ||
		   strings.HasPrefix(r.URL.Path, "/api/v1/public/output/") {
			log.Printf("🌐 Public access granted for %s %s", r.Method, r.URL.Path)
			next.ServeHTTP(w, r)
			return
		}

		// Determine which API key to use (prefer API_KEY over SSE_ACCESS_KEY)
		expectedAPIKey := s.config.APIKey
		if expectedAPIKey == "" {
			expectedAPIKey = s.config.SSEAccessKey
		}

		// Only require authentication if API key is explicitly configured and not a placeholder
		requireAuth := expectedAPIKey != "" &&
			expectedAPIKey != "your_sse_access_key_here" &&
			expectedAPIKey != "your_api_key_here"


		// Check for API key if authentication is required
		if requireAuth {
			apiKey := r.Header.Get("X-API-Key")
			authHeader := r.Header.Get("Authorization")

			// Support both X-API-Key header and Authorization Bearer token
			validKey := false
			if apiKey == expectedAPIKey {
				validKey = true
			} else if strings.HasPrefix(authHeader, "Bearer ") && strings.TrimPrefix(authHeader, "Bearer ") == expectedAPIKey {
				validKey = true
			}

			if !validKey {
				s.writeErrorResponse(w, http.StatusUnauthorized, "UNAUTHORIZED", "Invalid or missing API key", map[string]interface{}{
					"hint": "Provide API key via 'X-API-Key' header or 'Authorization: Bearer <key>' header",
				})
				return
			}

			log.Printf("🔐 API access granted for %s %s", r.Method, r.URL.Path)
		} else {
			log.Printf("⚠️  API authentication disabled - no API key configured")
		}

		next.ServeHTTP(w, r)
	})
}

// Helper functions
func (s *APIServer) writeResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	response := APIResponse{
		Success: statusCode < 400,
		Data:    data,
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

func (s *APIServer) writeErrorResponse(w http.ResponseWriter, statusCode int, code, message string, details interface{}) {
	response := APIResponse{
		Success: false,
		Error: &APIError{
			Code:    code,
			Message: message,
			Details: details,
		},
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// Tool handlers
func (s *APIServer) handleListTools(w http.ResponseWriter, r *http.Request) {
	toolsData := []map[string]interface{}{
		{
			"name":        "generate_card_from_text",
			"description": "从文本生成智能卡片",
			"parameters": map[string]interface{}{
				"text": map[string]interface{}{
					"type":        "string",
					"required":    true,
					"description": "完整的原始文本内容",
				},
			},
			"estimated_duration": 20,
		},
		{
			"name":        "generate_card_from_url",
			"description": "从网页链接生成智能卡片",
			"parameters": map[string]interface{}{
				"url": map[string]interface{}{
					"type":        "string",
					"required":    true,
					"description": "完整的网页URL地址",
				},
			},
			"estimated_duration": 30,
		},
		{
			"name":        "generate_direct_card_from_text",
			"description": "从文本直接生成HTML智能卡片（无总结）",
			"parameters": map[string]interface{}{
				"text": map[string]interface{}{
					"type":        "string",
					"required":    true,
					"description": "完整的原始文本内容，将完整保留不进行总结",
				},
			},
			"estimated_duration": 15,
		},
		{
			"name":        "generate_direct_card_from_url",
			"description": "从网页链接直接生成HTML智能卡片（无总结）",
			"parameters": map[string]interface{}{
				"url": map[string]interface{}{
					"type":        "string",
					"required":    true,
					"description": "完整的网页URL地址，内容将完整保留不进行总结",
				},
			},
			"estimated_duration": 25,
		},
	}

	s.writeResponse(w, http.StatusOK, map[string]interface{}{
		"tools": toolsData,
	})
}

func (s *APIServer) handleGetTool(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	toolName := vars["tool_name"]

	// Check if tool exists
	validTools := s.getValidTools()

	if !validTools[toolName] {
		s.writeErrorResponse(w, http.StatusNotFound, "TOOL_NOT_FOUND", fmt.Sprintf("Tool '%s' not found", toolName), nil)
		return
	}

	// Return tool details (simplified for now)
	toolData := map[string]interface{}{
		"name":        toolName,
		"description": "Tool description",
		"parameters":  map[string]interface{}{},
	}

	s.writeResponse(w, http.StatusOK, toolData)
}

func (s *APIServer) handleSyncToolCall(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	toolName := vars["tool_name"]

	// Parse request body
	var req ToolCallRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.writeErrorResponse(w, http.StatusBadRequest, "INVALID_JSON", "Invalid JSON in request body", nil)
		return
	}

	// Validate tool name
	validTools := s.getValidTools()
	if !validTools[toolName] {
		s.writeErrorResponse(w, http.StatusNotFound, "TOOL_NOT_FOUND", fmt.Sprintf("Tool '%s' not found", toolName), nil)
		return
	}

	// Execute tool synchronously
	start := time.Now()
	var result map[string]interface{}
	var err error

	switch toolName {
	case "generate_card_from_text":
		result, err = s.toolHandlers.GenerateCardFromText(req.Arguments)
	case "generate_card_from_url":
		result, err = s.toolHandlers.GenerateCardFromURL(req.Arguments)
	case "generate_direct_card_from_text":
		result, err = s.toolHandlers.GenerateDirectCardFromText(req.Arguments)
	case "generate_direct_card_from_url":
		result, err = s.toolHandlers.GenerateDirectCardFromURL(req.Arguments)
	}

	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "EXECUTION_FAILED", err.Error(), nil)
		return
	}

	executionTime := time.Since(start).Seconds()

	response := map[string]interface{}{
		"data":           result,
		"execution_time": executionTime,
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleAsyncToolCall(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	toolName := vars["tool_name"]

	// Parse request body
	var req ToolCallRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.writeErrorResponse(w, http.StatusBadRequest, "INVALID_JSON", "Invalid JSON in request body", nil)
		return
	}

	// Validate tool name
	validTools := s.getValidTools()
	if !validTools[toolName] {
		s.writeErrorResponse(w, http.StatusNotFound, "TOOL_NOT_FOUND", fmt.Sprintf("Tool '%s' not found", toolName), nil)
		return
	}

	// Map async tool names to actual tool names
	actualToolName := toolName
	switch toolName {
	case "create_task_from_text":
		actualToolName = "generate_card_from_text"
	case "create_task_from_url":
		actualToolName = "generate_card_from_url"
	}

	// Create task
	task, err := s.taskManager.CreateTask(actualToolName, req.Arguments, req.CallbackURL)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "TASK_CREATION_FAILED", err.Error(), nil)
		return
	}

	// Convert task interface to map for field access
	taskMap, ok := task.(map[string]interface{})
	if !ok {
		s.writeErrorResponse(w, http.StatusInternalServerError, "TASK_CONVERSION_ERROR", "Failed to convert task to map", nil)
		return
	}

	// Start task execution in background - we need to get the actual task from TaskManager
	taskID, _ := taskMap["task_id"].(string)
	actualTask, err := s.taskManager.GetTaskByID(taskID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "TASK_RETRIEVAL_ERROR", "Failed to retrieve task for execution", nil)
		return
	}
	go s.executeTaskAsync(actualTask)

	// Estimate duration based on tool
	estimatedDuration := 20
	if toolName == "generate_card_from_url" {
		estimatedDuration = 30
	}

	taskIDStr, _ := taskMap["task_id"].(string)
	statusStr, _ := taskMap["status"].(string)
	createdAt, _ := taskMap["created_at"].(time.Time)

	response := AsyncToolResponse{
		TaskID:            taskIDStr,
		Status:            statusStr,
		CreatedAt:         createdAt,
		EstimatedDuration: estimatedDuration,
	}

	s.writeResponse(w, http.StatusOK, response)
}

// executeTaskAsync executes a task asynchronously
func (s *APIServer) executeTaskAsync(task *tasks.Task) {
	// Update task status to running
	s.taskManager.UpdateTaskStatus(task.ID, tasks.TaskStatusRunning)

	// Get task context
	ctx, err := s.taskManager.GetTaskContext(task.ID)
	if err != nil {
		log.Printf("❌ Failed to get task context for %s: %v", task.ID, err)
		s.taskManager.SetTaskError(task.ID, err)
		s.taskManager.UpdateTaskStatus(task.ID, tasks.TaskStatusFailed)
		return
	}

	// Execute the tool
	var result map[string]interface{}

	switch task.ToolName {
	case "generate_card_from_text":
		mockReq := createMockMCPRequest(task.ID).(mcp.CallToolRequest)
		result, err = s.toolHandlers.GenerateCardFromTextWithProgress(ctx, mockReq, task.Arguments)
	case "generate_card_from_url":
		mockReq := createMockMCPRequest(task.ID).(mcp.CallToolRequest)
		result, err = s.toolHandlers.GenerateCardFromURLWithProgress(ctx, mockReq, task.Arguments)
	case "generate_direct_card_from_text":
		mockReq := createMockMCPRequest(task.ID).(mcp.CallToolRequest)
		result, err = s.toolHandlers.GenerateDirectCardFromTextWithProgress(ctx, mockReq, task.Arguments)
	case "generate_direct_card_from_url":
		mockReq := createMockMCPRequest(task.ID).(mcp.CallToolRequest)
		result, err = s.toolHandlers.GenerateDirectCardFromURLWithProgress(ctx, mockReq, task.Arguments)
	default:
		err = fmt.Errorf("unknown tool: %s", task.ToolName)
	}

	if err != nil {
		log.Printf("❌ Task %s execution failed: %v", task.ID, err)
		s.taskManager.SetTaskError(task.ID, err)
		s.taskManager.UpdateTaskStatus(task.ID, tasks.TaskStatusFailed)
		return
	}

	// Set result and mark as completed
	s.taskManager.SetTaskResult(task.ID, result)
	s.taskManager.UpdateTaskStatus(task.ID, tasks.TaskStatusCompleted)

	log.Printf("✅ Task %s completed successfully", task.ID)

	// TODO: Send webhook callback if configured
}

// Task handlers
func (s *APIServer) handleListTasks(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	status := r.URL.Query().Get("status")
	limitStr := r.URL.Query().Get("limit")
	offsetStr := r.URL.Query().Get("offset")

	limit := 10 // default
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := 0 // default
	if offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	// Get task summaries
	summaries, total, err := s.taskManager.GetTaskSummaries(tasks.TaskStatus(status), limit, offset)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "INTERNAL_ERROR", err.Error(), nil)
		return
	}

	response := map[string]interface{}{
		"tasks": summaries,
		"pagination": map[string]interface{}{
			"total":    total,
			"limit":    limit,
			"offset":   offset,
			"has_more": offset+limit < total,
		},
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleGetTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["task_id"]

	task, err := s.taskManager.GetTaskCopy(taskID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusNotFound, "TASK_NOT_FOUND", err.Error(), nil)
		return
	}

	s.writeResponse(w, http.StatusOK, task)
}
func (s *APIServer) handleGetTaskStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["task_id"]

	// Use the get_task_status tool handler
	result, err := s.toolHandlers.GetTaskStatus(map[string]interface{}{
		"task_id": taskID,
	})
	if err != nil {
		s.writeErrorResponse(w, http.StatusNotFound, "TASK_NOT_FOUND", err.Error(), nil)
		return
	}

	s.writeResponse(w, http.StatusOK, result)
}

func (s *APIServer) handleGetTaskResult(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["task_id"]

	log.Printf("📥 收到获取任务结果请求 - 任务ID: %s", taskID)
	log.Printf("🌐 请求来源: %s %s", r.Method, r.URL.String())
	log.Printf("📍 客户端IP: %s", r.RemoteAddr)

	task, err := s.taskManager.GetTaskCopy(taskID)
	if err != nil {
		log.Printf("❌ 任务不存在 - 任务ID: %s, 错误: %v", taskID, err)
		s.writeErrorResponse(w, http.StatusNotFound, "TASK_NOT_FOUND", err.Error(), nil)
		return
	}

	log.Printf("✅ 任务找到 - 任务ID: %s, 状态: %s", taskID, task.Status)

	if task.Status != tasks.TaskStatusCompleted {
		log.Printf("⚠️ 任务未完成 - 任务ID: %s, 当前状态: %s", taskID, task.Status)
		s.writeErrorResponse(w, http.StatusBadRequest, "TASK_NOT_COMPLETED", "Task is not completed yet", map[string]interface{}{
			"current_status": task.Status,
		})
		return
	}

	log.Printf("✅ 任务已完成 - 任务ID: %s", taskID)

	executionTime := 0.0
	if task.StartedAt != nil && task.CompletedAt != nil {
		executionTime = task.CompletedAt.Sub(*task.StartedAt).Seconds()
		log.Printf("⏱️ 任务执行时间: %.2f 秒 - 任务ID: %s", executionTime, taskID)
	} else {
		log.Printf("⚠️ 无法计算执行时间 - 任务ID: %s, StartedAt: %v, CompletedAt: %v", taskID, task.StartedAt, task.CompletedAt)
	}

	// Check if public_url parameter is set
	publicURL := r.URL.Query().Get("public_url")
	usePublicURL := publicURL == "1"

	log.Printf("🔍 检查公共URL参数 - 任务ID: %s", taskID)
	log.Printf("📋 查询参数 public_url: '%s' - 任务ID: %s", publicURL, taskID)
	log.Printf("🎯 使用公共URL模式: %t - 任务ID: %s", usePublicURL, taskID)

	result := task.Result
	if usePublicURL {
		log.Printf("🔄 启用公共URL模式，开始转换图片 - 任务ID: %s", taskID)
		// Convert base64 images to public URLs
		result = s.convertImagesToPublicURLs(task.ID, task.Result)
		log.Printf("✅ 图片转换完成 - 任务ID: %s", taskID)
	} else {
		log.Printf("📝 使用默认模式，返回原始结果 - 任务ID: %s", taskID)
	}

	// Process result to convert content array to single object
	processedResult := s.processResultForAPI(result)

	response := map[string]interface{}{
		"task_id":        task.ID,
		"status":         task.Status,
		"result":         processedResult,
		"execution_time": executionTime,
		"completed_at":   task.CompletedAt,
	}

	log.Printf("📤 准备发送响应 - 任务ID: %s", taskID)
	log.Printf("📊 响应数据: task_id=%s, status=%s, execution_time=%.2f", task.ID, task.Status, executionTime)

	s.writeResponse(w, http.StatusOK, response)

	log.Printf("✅ 任务结果响应发送完成 - 任务ID: %s", taskID)
}

// processResultForAPI converts content array to single object for API response
func (s *APIServer) processResultForAPI(result interface{}) interface{} {
	if result == nil {
		return result
	}

	// Convert result to map for processing
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return result
	}

	// Process content array
	content, exists := resultMap["content"]
	if !exists {
		return result
	}

	// Handle different slice types
	var contentArray []interface{}

	switch v := content.(type) {
	case []interface{}:
		contentArray = v
	case []map[string]interface{}:
		// Convert []map[string]interface{} to []interface{}
		contentArray = make([]interface{}, len(v))
		for i, item := range v {
			contentArray[i] = item
		}
	default:
		return result
	}

	// If content array has items, use the first one as single content object
	if len(contentArray) > 0 {
		// Create a new result map with content as single object instead of array
		newResult := make(map[string]interface{})
		for k, v := range resultMap {
			if k == "content" {
				newResult[k] = contentArray[0] // Use first item as single object
			} else {
				newResult[k] = v
			}
		}
		return newResult
	}

	return result
}

// convertImagesToPublicURLs converts base64 image data to public URLs
func (s *APIServer) convertImagesToPublicURLs(taskID string, result interface{}) interface{} {
	log.Printf("🔄 开始转换图片为公共URL - 任务ID: %s", taskID)

	if result == nil {
		log.Printf("⚠️ 结果为空，跳过转换 - 任务ID: %s", taskID)
		return result
	}

	// Convert result to map for processing
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		log.Printf("⚠️ 结果不是map类型，跳过转换 - 任务ID: %s, 类型: %T", taskID, result)
		return result
	}
	log.Printf("✅ 结果类型验证通过 - 任务ID: %s", taskID)

	// Process content array
	content, exists := resultMap["content"]
	if !exists {
		log.Printf("⚠️ 结果中不存在content字段，跳过转换 - 任务ID: %s", taskID)
		return result
	}

	// Try to convert content to []interface{} first
	var contentArray []interface{}

	// Handle different slice types
	switch v := content.(type) {
	case []interface{}:
		contentArray = v
	case []map[string]interface{}:
		// Convert []map[string]interface{} to []interface{}
		contentArray = make([]interface{}, len(v))
		for i, item := range v {
			contentArray[i] = item
		}
		log.Printf("🔄 转换content类型从 []map[string]interface{} 到 []interface{} - 任务ID: %s", taskID)
	default:
		log.Printf("⚠️ content不是支持的数组类型，跳过转换 - 任务ID: %s, content类型: %T", taskID, content)
		return result
	}
	log.Printf("📋 找到content数组，包含 %d 个项目 - 任务ID: %s", len(contentArray), taskID)

	// Create public directory if it doesn't exist
	publicDir := filepath.Join(s.config.OutputDir, "public")
	log.Printf("📁 准备创建公共目录: %s - 任务ID: %s", publicDir, taskID)

	if err := os.MkdirAll(publicDir, 0755); err != nil {
		log.Printf("❌ 创建公共目录失败: %v - 任务ID: %s, 目录: %s", err, taskID, publicDir)
		return result
	}
	log.Printf("✅ 公共目录创建成功 - 任务ID: %s, 目录: %s", taskID, publicDir)

	imageCount := 0
	convertedCount := 0

	// Process each content item
	for i, item := range contentArray {
		log.Printf("🔍 处理第 %d 个内容项 - 任务ID: %s", i, taskID)

		itemMap, ok := item.(map[string]interface{})
		if !ok {
			log.Printf("⚠️ 第 %d 个项目不是map类型，跳过 - 任务ID: %s, 类型: %T", i, taskID, item)
			continue
		}

		// Check if it's an image
		itemType, exists := itemMap["type"]
		if !exists {
			log.Printf("⚠️ 第 %d 个项目缺少type字段，跳过 - 任务ID: %s", i, taskID)
			continue
		}

		log.Printf("📝 第 %d 个项目类型: %v - 任务ID: %s", i, itemType, taskID)

		if itemType == "image" {
			imageCount++
			log.Printf("🖼️ 发现图片项目 (第 %d 个，总第 %d 张图片) - 任务ID: %s", i, imageCount, taskID)

			imageData, exists := itemMap["data"]
			if !exists {
				log.Printf("⚠️ 图片项目缺少data字段 - 任务ID: %s, 项目索引: %d", taskID, i)
				continue
			}

			imageDataStr, ok := imageData.(string)
			if !ok {
				log.Printf("⚠️ 图片data不是字符串类型 - 任务ID: %s, 项目索引: %d, data类型: %T", taskID, i, imageData)
				continue
			}

			dataLength := len(imageDataStr)
			log.Printf("📊 图片数据长度: %d 字符 - 任务ID: %s, 项目索引: %d", dataLength, taskID, i)

			// Save image to public directory and get URL
			log.Printf("💾 开始保存图片到公共目录 - 任务ID: %s, 项目索引: %d", taskID, i)

			if publicURL := s.saveImageToPublic(taskID, imageDataStr, i); publicURL != "" {
				// Replace base64 data with public URL
				itemMap["data"] = publicURL
				itemMap["public_url"] = true
				convertedCount++

				log.Printf("✅ 图片转换成功 - 任务ID: %s, 项目索引: %d", taskID, i)
				log.Printf("🌐 公共URL: %s", publicURL)
				log.Printf("📈 已转换图片数量: %d/%d", convertedCount, imageCount)
			} else {
				log.Printf("❌ 图片保存失败 - 任务ID: %s, 项目索引: %d", taskID, i)
			}
		} else {
			log.Printf("ℹ️ 第 %d 个项目不是图片，跳过 - 任务ID: %s, 类型: %v", i, taskID, itemType)
		}
	}

	log.Printf("🎉 图片转换完成 - 任务ID: %s", taskID)
	log.Printf("📊 转换统计: 总项目数=%d, 图片数=%d, 成功转换=%d", len(contentArray), imageCount, convertedCount)

	return result
}

// saveImageToPublic saves base64 image data to public directory and returns the public URL
func (s *APIServer) saveImageToPublic(taskID, imageData string, index int) string {
	log.Printf("🔧 开始保存图片到公共目录 - 任务ID: %s, 索引: %d", taskID, index)

	// Validate input parameters
	if taskID == "" {
		log.Printf("❌ 任务ID为空，无法保存图片 - 索引: %d", index)
		return ""
	}

	if imageData == "" {
		log.Printf("❌ 图片数据为空，无法保存 - 任务ID: %s, 索引: %d", taskID, index)
		return ""
	}

	dataLength := len(imageData)
	log.Printf("📏 图片数据长度: %d 字符 - 任务ID: %s, 索引: %d", dataLength, taskID, index)

	// Decode base64 data
	log.Printf("🔓 开始解码base64数据 - 任务ID: %s, 索引: %d", taskID, index)
	imageBytes, err := base64.StdEncoding.DecodeString(imageData)
	if err != nil {
		log.Printf("❌ base64解码失败: %v - 任务ID: %s, 索引: %d", err, taskID, index)
		return ""
	}

	imageSizeBytes := len(imageBytes)
	imageSizeKB := float64(imageSizeBytes) / 1024
	log.Printf("✅ base64解码成功 - 任务ID: %s, 索引: %d", taskID, index)
	log.Printf("📊 解码后图片大小: %d 字节 (%.2f KB) - 任务ID: %s, 索引: %d", imageSizeBytes, imageSizeKB, taskID, index)

	// Generate filename
	filename := fmt.Sprintf("%s_%d.png", taskID, index)
	filePath := filepath.Join(s.config.OutputDir, "public", filename)
	log.Printf("📝 生成文件名: %s - 任务ID: %s, 索引: %d", filename, taskID, index)
	log.Printf("📂 完整文件路径: %s - 任务ID: %s, 索引: %d", filePath, taskID, index)

	// Check if file already exists
	if _, err := os.Stat(filePath); err == nil {
		log.Printf("⚠️ 文件已存在，将覆盖 - 任务ID: %s, 索引: %d, 路径: %s", taskID, index, filePath)
	} else if !os.IsNotExist(err) {
		log.Printf("⚠️ 检查文件状态时出错: %v - 任务ID: %s, 索引: %d, 路径: %s", err, taskID, index, filePath)
	}

	// Save image file
	log.Printf("💾 开始写入图片文件 - 任务ID: %s, 索引: %d, 路径: %s", taskID, index, filePath)
	if err := os.WriteFile(filePath, imageBytes, 0644); err != nil {
		log.Printf("❌ 保存图片文件失败: %v - 任务ID: %s, 索引: %d, 路径: %s", err, taskID, index, filePath)
		return ""
	}

	log.Printf("✅ 图片文件保存成功 - 任务ID: %s, 索引: %d, 路径: %s", taskID, index, filePath)

	// Verify file was saved correctly
	if stat, err := os.Stat(filePath); err == nil {
		savedSize := stat.Size()
		savedSizeKB := float64(savedSize) / 1024
		log.Printf("🔍 文件验证成功 - 任务ID: %s, 索引: %d", taskID, index)
		log.Printf("📊 保存的文件大小: %d 字节 (%.2f KB) - 任务ID: %s, 索引: %d", savedSize, savedSizeKB, taskID, index)

		if savedSize != int64(imageSizeBytes) {
			log.Printf("⚠️ 文件大小不匹配! 原始: %d, 保存: %d - 任务ID: %s, 索引: %d", imageSizeBytes, savedSize, taskID, index)
		} else {
			log.Printf("✅ 文件大小验证通过 - 任务ID: %s, 索引: %d", taskID, index)
		}
	} else {
		log.Printf("⚠️ 文件验证失败: %v - 任务ID: %s, 索引: %d, 路径: %s", err, taskID, index, filePath)
	}

	// Generate public URL
	publicURL := fmt.Sprintf("%s/api/v1/public/output/%s", s.config.PublicURLBase, filename)
	log.Printf("🌐 生成公共URL - 任务ID: %s, 索引: %d", taskID, index)
	log.Printf("🔗 公共URL: %s", publicURL)
	log.Printf("⚙️ URL基础地址: %s", s.config.PublicURLBase)
	log.Printf("📁 文件名: %s", filename)
	log.Printf("📂 完整文件路径: %s", filePath)

	// Test file accessibility
	if _, err := os.Stat(filePath); err == nil {
		log.Printf("✅ 文件可访问性验证通过 - 任务ID: %s, 索引: %d", taskID, index)
	} else {
		log.Printf("❌ 文件可访问性验证失败: %v - 任务ID: %s, 索引: %d", err, taskID, index)
	}

	log.Printf("🎉 图片保存到公共目录完成 - 任务ID: %s, 索引: %d", taskID, index)

	return publicURL
}

func (s *APIServer) handleDebugPublic(w http.ResponseWriter, r *http.Request) {
	publicDir := filepath.Join(s.config.OutputDir, "public")

	log.Printf("🔍 调试公共文件目录: %s", publicDir)

	// Check if directory exists
	if _, err := os.Stat(publicDir); os.IsNotExist(err) {
		response := map[string]interface{}{
			"error": "Public directory does not exist",
			"path":  publicDir,
		}
		s.writeResponse(w, http.StatusNotFound, response)
		return
	}

	// List files in public directory
	files, err := os.ReadDir(publicDir)
	if err != nil {
		response := map[string]interface{}{
			"error": "Failed to read public directory",
			"path":  publicDir,
			"details": err.Error(),
		}
		s.writeResponse(w, http.StatusInternalServerError, response)
		return
	}

	fileList := make([]map[string]interface{}, 0)
	for _, file := range files {
		info, _ := file.Info()
		fileList = append(fileList, map[string]interface{}{
			"name": file.Name(),
			"size": info.Size(),
			"is_dir": file.IsDir(),
			"mod_time": info.ModTime(),
		})
	}

	response := map[string]interface{}{
		"public_dir": publicDir,
		"files": fileList,
		"total_files": len(files),
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleStaticTest(w http.ResponseWriter, r *http.Request) {
	publicDir := filepath.Join(s.config.OutputDir, "public")

	log.Printf("🧪 静态文件服务测试")
	log.Printf("📂 公共目录: %s", publicDir)
	log.Printf("🌐 PUBLIC_URL_BASE: %s", s.config.PublicURLBase)

	// List all files in public directory
	files, err := os.ReadDir(publicDir)
	if err != nil {
		response := map[string]interface{}{
			"error": "Failed to read public directory",
			"path":  publicDir,
			"details": err.Error(),
		}
		s.writeResponse(w, http.StatusInternalServerError, response)
		return
	}

	testResults := make([]map[string]interface{}, 0)

	for _, file := range files {
		if !file.IsDir() {
			fileName := file.Name()
			publicURL := fmt.Sprintf("%s/api/v1/public/output/%s", s.config.PublicURLBase, fileName)
			filePath := filepath.Join(publicDir, fileName)

			info, _ := file.Info()

			testResults = append(testResults, map[string]interface{}{
				"filename": fileName,
				"public_url": publicURL,
				"file_path": filePath,
				"size": info.Size(),
				"mod_time": info.ModTime(),
			})
		}
	}

	response := map[string]interface{}{
		"public_dir": publicDir,
		"public_url_base": s.config.PublicURLBase,
		"total_files": len(files),
		"test_results": testResults,
		"instructions": "Try accessing the public_url for each file to test static serving",
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleCancelTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["task_id"]

	// Check if task exists
	taskInterface, err := s.taskManager.GetTask(taskID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusNotFound, "TASK_NOT_FOUND", err.Error(), nil)
		return
	}

	// Convert to map for field access
	taskMap, ok := taskInterface.(map[string]interface{})
	if !ok {
		s.writeErrorResponse(w, http.StatusInternalServerError, "TASK_CONVERSION_ERROR", "Failed to convert task to map", nil)
		return
	}

	// Check if task can be cancelled
	status, _ := taskMap["status"].(string)
	if status == "completed" || status == "failed" || status == "cancelled" {
		s.writeErrorResponse(w, http.StatusBadRequest, "TASK_ALREADY_COMPLETED", "Task cannot be cancelled", map[string]interface{}{
			"current_status": status,
		})
		return
	}

	// Cancel the task
	err = s.taskManager.CancelTask(taskID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "INTERNAL_ERROR", err.Error(), nil)
		return
	}

	response := map[string]interface{}{
		"task_id": taskID,
		"status":  "cancelled",
		"message": "Task cancelled successfully",
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleGetTaskProgress(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["task_id"]

	// Check if client wants SSE
	if r.Header.Get("Accept") == "text/event-stream" {
		s.handleTaskProgressSSE(w, r, taskID)
		return
	}

	// Regular JSON response
	task, err := s.taskManager.GetTaskCopy(taskID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusNotFound, "TASK_NOT_FOUND", err.Error(), nil)
		return
	}

	response := map[string]interface{}{
		"task_id":   task.ID,
		"status":    task.Status,
		"progress":  task.Progress,
		"timestamp": time.Now(),
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleTaskProgressSSE(w http.ResponseWriter, r *http.Request, taskID string) {
	// Set SSE headers
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	flusher, ok := w.(http.Flusher)
	if !ok {
		s.writeErrorResponse(w, http.StatusInternalServerError, "INTERNAL_ERROR", "SSE not supported", nil)
		return
	}

	// Check if task exists
	_, err := s.taskManager.GetTask(taskID)
	if err != nil {
		fmt.Fprintf(w, "event: error\ndata: {\"error\":\"Task not found\"}\n\n")
		flusher.Flush()
		return
	}

	// Send progress updates
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-r.Context().Done():
			return
		case <-ticker.C:
			task, err := s.taskManager.GetTaskCopy(taskID)
			if err != nil {
				fmt.Fprintf(w, "event: error\ndata: {\"error\":\"Task not found\"}\n\n")
				flusher.Flush()
				return
			}

			data := map[string]interface{}{
				"task_id":   task.ID,
				"status":    task.Status,
				"progress":  task.Progress,
				"timestamp": time.Now(),
			}

			// If task is completed, include result
			if task.Status == tasks.TaskStatusCompleted {
				data["result"] = task.Result
			} else if task.Status == tasks.TaskStatusFailed {
				data["error"] = task.Error
			}

			jsonData, _ := json.Marshal(data)
			fmt.Fprintf(w, "data: %s\n\n", jsonData)
			flusher.Flush()

			// Stop if task is finished
			if task.Status == tasks.TaskStatusCompleted || task.Status == tasks.TaskStatusFailed || task.Status == tasks.TaskStatusCancelled {
				return
			}
		}
	}
}

// System handlers
func (s *APIServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	stats := s.taskManager.GetStats()

	response := map[string]interface{}{
		"status":  "healthy",
		"service": "smart-card-mcp",
		"version": "1.0.0",
		"uptime":  time.Since(time.Now().Add(-time.Hour)).Seconds(), // TODO: track actual uptime
		"tasks":   stats,
	}

	s.writeResponse(w, http.StatusOK, response)
}

func (s *APIServer) handleStats(w http.ResponseWriter, r *http.Request) {
	stats := s.taskManager.GetStats()

	response := map[string]interface{}{
		"stats": map[string]interface{}{
			"total_tasks":            stats["total_tasks"],
			"active_tasks":           stats["active_tasks"],
			"completed_tasks":        stats["completed_tasks"],
			"failed_tasks":           stats["failed_tasks"],
			"average_execution_time": 18.5, // TODO: calculate actual average
			"uptime":                 time.Since(time.Now().Add(-time.Hour)).Seconds(),
			"memory_usage":           "45.2MB", // TODO: get actual memory usage
			"tools": map[string]interface{}{
				"generate_card_from_text": map[string]interface{}{
					"total_calls":   89,  // TODO: track actual stats
					"success_rate":  98.9,
					"avg_duration":  16.2,
				},
				"generate_card_from_url": map[string]interface{}{
					"total_calls":   43,
					"success_rate":  95.3,
					"avg_duration":  22.8,
				},
			},
		},
	}

	s.writeResponse(w, http.StatusOK, response)
}

// Documentation handlers
func (s *APIServer) handleDocs(w http.ResponseWriter, r *http.Request) {
	// Serve Swagger UI HTML
	html := `<!DOCTYPE html>
<html>
<head>
    <title>Smart Card MCP API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/api/v1/docs/openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(html))
}

func (s *APIServer) handleOpenAPI(w http.ResponseWriter, r *http.Request) {
	// Generate OpenAPI specification
	spec := map[string]interface{}{
		"openapi": "3.0.0",
		"info": map[string]interface{}{
			"title":       "Smart Card MCP API",
			"description": "HTTP API for Smart Card MCP Server with authentication support",
			"version":     "1.0.0",
		},
		"servers": []map[string]interface{}{
			{
				"url":         fmt.Sprintf("http://localhost:%s/api/v1", s.config.Ports[1]),
				"description": "Local development server",
			},
		},
		"paths": map[string]interface{}{
			"/tools": map[string]interface{}{
				"get": map[string]interface{}{
					"summary":     "List available tools",
					"description": "Get a list of all available tools",
					"security": []map[string]interface{}{
						{"ApiKeyAuth": []string{}},
						{"BearerAuth": []string{}},
					},
					"responses": map[string]interface{}{
						"200": map[string]interface{}{
							"description": "List of tools",
							"content": map[string]interface{}{
								"application/json": map[string]interface{}{
									"schema": map[string]interface{}{
										"type": "object",
										"properties": map[string]interface{}{
											"success": map[string]interface{}{"type": "boolean"},
											"data": map[string]interface{}{
												"type": "object",
												"properties": map[string]interface{}{
													"tools": map[string]interface{}{
														"type": "array",
														"items": map[string]interface{}{
															"type": "object",
														},
													},
												},
											},
										},
									},
								},
							},
						},
						"401": map[string]interface{}{
							"description": "Unauthorized - Invalid or missing API key",
						},
					},
				},
			},
			"/tools/{tool_name}/sync": map[string]interface{}{
				"post": map[string]interface{}{
					"summary":     "Execute tool synchronously",
					"description": "Execute a tool and wait for the result",
					"security": []map[string]interface{}{
						{"ApiKeyAuth": []string{}},
						{"BearerAuth": []string{}},
					},
					"parameters": []map[string]interface{}{
						{
							"name":        "tool_name",
							"in":          "path",
							"required":    true,
							"description": "Name of the tool to execute",
							"schema":      map[string]interface{}{"type": "string"},
						},
					},
					"requestBody": map[string]interface{}{
						"required": true,
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"arguments": map[string]interface{}{
											"type":        "object",
											"description": "Tool arguments",
										},
									},
									"required": []string{"arguments"},
								},
							},
						},
					},
					"responses": map[string]interface{}{
						"200": map[string]interface{}{
							"description": "Tool execution result",
						},
						"400": map[string]interface{}{
							"description": "Bad request",
						},
						"404": map[string]interface{}{
							"description": "Tool not found",
						},
						"500": map[string]interface{}{
							"description": "Execution failed",
						},
					},
				},
			},
			"/tools/{tool_name}/async": map[string]interface{}{
				"post": map[string]interface{}{
					"summary":     "Execute tool asynchronously",
					"description": "Start tool execution and return task ID",
					"parameters": []map[string]interface{}{
						{
							"name":        "tool_name",
							"in":          "path",
							"required":    true,
							"description": "Name of the tool to execute",
							"schema":      map[string]interface{}{"type": "string"},
						},
					},
					"requestBody": map[string]interface{}{
						"required": true,
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"arguments": map[string]interface{}{
											"type":        "object",
											"description": "Tool arguments",
										},
										"callback_url": map[string]interface{}{
											"type":        "string",
											"description": "Optional webhook URL for completion notification",
										},
									},
									"required": []string{"arguments"},
								},
							},
						},
					},
					"responses": map[string]interface{}{
						"200": map[string]interface{}{
							"description": "Task created successfully",
						},
					},
				},
			},
			"/tasks/{task_id}": map[string]interface{}{
				"get": map[string]interface{}{
					"summary":     "Get task status",
					"description": "Get the current status and details of a task",
					"parameters": []map[string]interface{}{
						{
							"name":        "task_id",
							"in":          "path",
							"required":    true,
							"description": "Task ID",
							"schema":      map[string]interface{}{"type": "string"},
						},
					},
					"responses": map[string]interface{}{
						"200": map[string]interface{}{
							"description": "Task details",
						},
						"404": map[string]interface{}{
							"description": "Task not found",
						},
					},
				},
				"delete": map[string]interface{}{
					"summary":     "Cancel task",
					"description": "Cancel a running task",
					"parameters": []map[string]interface{}{
						{
							"name":        "task_id",
							"in":          "path",
							"required":    true,
							"description": "Task ID",
							"schema":      map[string]interface{}{"type": "string"},
						},
					},
					"responses": map[string]interface{}{
						"200": map[string]interface{}{
							"description": "Task cancelled successfully",
						},
						"404": map[string]interface{}{
							"description": "Task not found",
						},
						"400": map[string]interface{}{
							"description": "Task cannot be cancelled",
						},
					},
				},
			},
		},
		"components": map[string]interface{}{
			"securitySchemes": map[string]interface{}{
				"ApiKeyAuth": map[string]interface{}{
					"type": "apiKey",
					"in":   "header",
					"name": "X-API-Key",
				},
				"BearerAuth": map[string]interface{}{
					"type":   "http",
					"scheme": "bearer",
				},
			},
		},
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(spec)
}

// Helper functions
func createMockMCPRequest(taskID string) interface{} {
	// Create a mock MCP request with progress token for task tracking
	return mcp.CallToolRequest{
		Request: mcp.Request{
			Method: "tools/call",
		},
		Params: mcp.CallToolParams{
			Name: "mock_tool",
			Meta: &mcp.Meta{
				ProgressToken: taskID,
			},
		},
	}
}