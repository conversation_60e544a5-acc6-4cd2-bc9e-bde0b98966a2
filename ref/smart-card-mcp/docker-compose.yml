version: '3.8'

services:
  smart-card-mcp:
    image: smart-card-mcp:latest
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smart-card-mcp-server
    restart: unless-stopped
    ports:
      - "48088:48088"  # SSE port
      - "48089:48089"  # HTTP port
      - "49083:49083"  # Health check port
    environment:
      # Preferred: ARK API configuration (DeepSeek V3)
      - ARK_API_KEY=${ARK_API_KEY}
      - ARK_BASE_URL=${ARK_BASE_URL:-https://ark.cn-beijing.volces.com/api/v3}
      - ARK_MODEL=${ARK_MODEL:-deepseek-v3-250324}

      # Fallback: OpenAI API configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-3.5-turbo}

      # Required: Jina API for web fetching
      - JINA_API_KEY=${JINA_API_KEY}
      - JINA_API_URL=${JINA_API_URL:-https://r.jina.ai/}

      # Optional: Log level (DEBUG, INFO, WARNING, ERROR)
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}

      # Optional: SSE Access Key for authentication
      - SSE_ACCESS_KEY=${SSE_ACCESS_KEY}

      # MCP Server Configuration
      - MCP_PORTS=${MCP_PORTS:-48088,48089}
      - MCP_BASE_URL=${MCP_BASE_URL:-http://localhost:48088}
      - MCP_TRANSPORT=${MCP_TRANSPORT:-mixed}

      # Optional: Enable MCP debug mode for detailed logging
      - MCP_DEBUG=${MCP_DEBUG:-false}

      # Output and templates directories
      - OUTPUT_DIR=/app/output
      - TEMPLATES_DIR=/app/templates

      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-false}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-http://*********:8800}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-false}
    volumes:
      # Optional: Mount logs directory for persistent logging
      - ./logs:/app/logs
      # Mount data directory for any persistent data
      - ./data:/app/data
      # Mount output directory for generated cards
      - ./output:/app/output
      # Optional: Mount custom templates
      - ./templates:/app/templates:ro
    networks:
      - smart-card-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:49083/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.smart-card.service=mcp-server"
      - "com.smart-card.version=1.0.0"
      - "com.smart-card.ports=48083,48084"
      - "com.smart-card.description=Smart Card MCP Server for text summarization and card generation"

networks:
  smart-card-network:
    driver: bridge
    name: smart-card-network

volumes:
  logs:
    driver: local
  data:
    driver: local
  output:
    driver: local
