#!/usr/bin/env python3
"""
简单的调试测试
"""

import json
import requests

def test_simple_call():
    """测试简单的工具调用"""
    
    base_url = "http://localhost:48089/mcp?key=your_sse_access_key_here"
    
    # 工具调用请求 - 带有进度令牌
    call_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "generate_card_from_text",
            "arguments": {
                "text": "简单测试文本"
            },
            "meta": {
                "progressToken": "debug-token-123"
            }
        }
    }
    
    print("🧪 Testing with Progress Token...")
    print(f"📝 Request: {json.dumps(call_request, indent=2)}")
    print()
    
    try:
        response = requests.post(
            base_url,
            json=call_request,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text[:500]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple_call()
    print("\n💡 Check server logs for debug information!")
