# Branch Debug Test Script
# This script tests the branch debugging output in GetTaskResult

Write-Host "🧪 Testing Branch Debug Output in GetTaskResult..." -ForegroundColor Cyan

# Configuration
$BaseURL = "http://localhost:48089"
$MCPEndpoint = "$BaseURL/mcp"

Write-Host ""
Write-Host "📝 Step 1: Create and complete an async task" -ForegroundColor Yellow

$headers = @{
    "Authorization" = "Bearer test_branch_debug_token_12345678"
    "Content-Type" = "application/json"
}

# Create async task
$createTaskData = @{
    method = "tools/call"
    params = @{
        name = "create_task_from_text"
        arguments = @{
            text = "这是一个测试分支调试输出的异步任务。我们将观察 GetTaskResult 函数中的分支执行情况。"
            Context = @{
                task_id = "branch_debug_test_task_12345"
            }
        }
    }
}
$createTaskBody = $createTaskData | ConvertTo-Json -Depth 4

Write-Host "📤 Creating async task..." -ForegroundColor White

try {
    $createResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $createTaskBody -TimeoutSec 10
    $createResult = $createResponse.Content | ConvertFrom-Json
    $taskId = $createResult.result.task_id
    Write-Host "✅ Task created: $taskId" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Task creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📝 Step 2: Wait for task completion" -ForegroundColor Yellow

# Wait for task to complete
$maxWaitTime = 60
$waitInterval = 3
$elapsedTime = 0

do {
    Start-Sleep -Seconds $waitInterval
    $elapsedTime += $waitInterval
    
    # Check task status
    $statusData = @{
        method = "tools/call"
        params = @{
            name = "get_task_status"
            arguments = @{
                task_id = $taskId
            }
        }
    }
    $statusBody = $statusData | ConvertTo-Json -Depth 4
    
    try {
        $statusResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $statusBody -TimeoutSec 10
        $statusResult = $statusResponse.Content | ConvertFrom-Json
        $taskStatus = $statusResult.result.status
        
        Write-Host "⏳ Task status: $taskStatus (waited ${elapsedTime}s)" -ForegroundColor Gray
        
        if ($taskStatus -eq "completed") {
            Write-Host "✅ Task completed!" -ForegroundColor Green
            break
        } elseif ($taskStatus -eq "failed") {
            Write-Host "❌ Task failed!" -ForegroundColor Red
            exit 1
        }
        
    } catch {
        Write-Host "⚠️ Status check failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} while ($elapsedTime -lt $maxWaitTime)

Write-Host ""
Write-Host "📝 Step 3: Retrieve task result (this will show branch debug output)" -ForegroundColor Yellow

# Get task result with Context parameter
$resultData = @{
    method = "tools/call"
    params = @{
        name = "get_task_result"
        arguments = @{
            task_id = $taskId
            Context = @{
                task_id = "branch_debug_test_task_12345"
            }
        }
    }
}
$resultBody = $resultData | ConvertTo-Json -Depth 4

Write-Host "📤 Retrieving task result (watch for branch debug output)..." -ForegroundColor White

try {
    $resultResponse = Invoke-WebRequest -Uri $MCPEndpoint -Method POST -Headers $headers -Body $resultBody -TimeoutSec 15
    Write-Host "✅ Result retrieval successful - Status: $($resultResponse.StatusCode)" -ForegroundColor Green
    
    $result = $resultResponse.Content | ConvertFrom-Json
    if ($result.result.content -and $result.result.content[0].type -eq "image") {
        Write-Host "🖼️ Image result received successfully" -ForegroundColor Green
    }
    
} catch {
    Write-Host "⚠️ Result retrieval failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📝 Step 4: Check server logs for branch debug output" -ForegroundColor Yellow

Write-Host ""
Write-Host "🔍 Expected branch debug log entries:" -ForegroundColor Cyan
Write-Host "   🔍 === GetTaskResult Branch Analysis ===" -ForegroundColor Gray
Write-Host "   ✅ Branch 1: Result is a map[string]interface{}" -ForegroundColor Gray
Write-Host "   🔧 Result map keys: [content success] or [image]" -ForegroundColor Gray
Write-Host ""
Write-Host "   Then one of these branches:" -ForegroundColor White
Write-Host "   📋 Old Format (Branch 1.1):" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.1: Found direct 'image' field (old format)" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.1.1: Image data is string, length: [number]" -ForegroundColor Gray
Write-Host "      📤 Returning image in MCP format (converted from old format)" -ForegroundColor Gray
Write-Host ""
Write-Host "   📋 New Format (Branch 1.3):" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.3: Found 'content' field (new format)" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.3.1: Content is array with 1 items" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.3.2: First content item is map" -ForegroundColor Gray
Write-Host "      🔧 Content item keys: [type data mimeType]" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.3.3: Content type is 'image'" -ForegroundColor Gray
Write-Host "      ✅ Branch 1.3.4: Image data found, length: [number]" -ForegroundColor Gray
Write-Host "      📤 Returning result in original MCP format (new format)" -ForegroundColor Gray

Write-Host ""
Write-Host "🎉 Branch Debug Test Summary:" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 What we can learn from the logs:" -ForegroundColor White
Write-Host "   🔍 Which branch the code execution follows" -ForegroundColor Green
Write-Host "   📊 The structure of the result data" -ForegroundColor Green
Write-Host "   🔧 The keys available in result maps" -ForegroundColor Green
Write-Host "   📋 Whether it's old format (direct 'image' field) or new format ('content' array)" -ForegroundColor Green
Write-Host "   🖼️ The length of image data" -ForegroundColor Green
Write-Host "   📤 Which return path is taken" -ForegroundColor Green

Write-Host ""
Write-Host "🔍 To view the actual server logs with branch debug output, run:" -ForegroundColor Cyan
Write-Host "   docker compose logs smart-card-mcp --tail=50" -ForegroundColor Gray

Write-Host ""
Write-Host "✨ Branch debug output is now active!" -ForegroundColor Green
Write-Host "   - Detailed branch execution tracking" -ForegroundColor White
Write-Host "   - Result structure analysis" -ForegroundColor White
Write-Host "   - Data format detection" -ForegroundColor White
Write-Host "   - Return path identification" -ForegroundColor White
