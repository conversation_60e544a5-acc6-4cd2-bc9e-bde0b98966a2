# Smart Card MCP 异步API使用指南

## 📋 概述

本指南详细介绍如何使用HTTP API异步调用Smart Card MCP服务，实现从URL生成智能卡片的完整流程。

## 🚀 异步流程概述

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as Smart Card API
    participant Worker as 后台任务
    
    Client->>API: 1. 创建异步任务
    API->>Worker: 启动后台处理
    API-->>Client: 返回任务ID
    
    loop 轮询状态
        Client->>API: 2. 查询任务状态
        API-->>Client: 返回状态和进度
    end
    
    Worker->>API: 任务完成
    Client->>API: 3. 获取最终结果
    API-->>Client: 返回卡片数据
```

## 🔧 API端点详解

### 1. 创建异步任务

**端点**: `POST /api/v1/tools/create_task_from_url/async`

**请求头**:
```http
Content-Type: application/json
X-API-Key: your-api-key-here
```

**请求体**:
```json
{
  "arguments": {
    "url": "https://example.com/article",
    "summary_prompt": "重点关注技术细节",
    "style_prompt": "简约现代风格"
  }
}
```

**响应** (HTTP 200):
```json
{
  "success": true,
  "data": {
    "task_id": "task_abc12345",
    "status": "pending",
    "created_at": "2025-08-30T14:30:00Z",
    "message": "Task created successfully"
  }
}
```

### 2. 查询任务状态

**端点**: `GET /api/v1/tasks/{task_id}/status`

**请求头**:
```http
X-API-Key: your-api-key-here
```

**响应**:
```json
{
  "task_id": "task_abc12345",
  "status": "running",
  "progress": {
    "current_step": 2,
    "total_steps": 4,
    "message": "正在生成HTML卡片...",
    "percentage": 50
  },
  "created_at": "2025-08-30T14:30:00Z",
  "started_at": "2025-08-30T14:30:05Z"
}
```

**任务状态说明**:
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 执行完成
- `failed`: 执行失败
- `cancelled`: 已取消

### 3. 获取任务结果

**端点**: `GET /api/v1/tasks/{task_id}/result`

**请求头**:
```http
X-API-Key: your-api-key-here
```

**查询参数**:
- `public_url=1` (可选): 返回公共URL而不是base64数据

**响应（默认模式）**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "iVBORw0KGgoAAAANSUhEUgAA...",
        "mimeType": "image/png"
      }
    ],
    "execution_time": 15.2
  }
}
```

**响应（公共URL模式）**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "http://localhost:48089/api/v1/public/output/task_abc123_0.png",
        "mimeType": "image/png",
        "public_url": true
      }
    ],
    "execution_time": 15.2
  }
}
```

## 💡 最佳实践

### 1. 错误处理

```python
try:
    task_id = client.create_task_from_url(url)
    result = client.wait_for_completion(task_id)
except requests.exceptions.Timeout:
    print("请求超时，请检查网络连接")
except requests.exceptions.HTTPError as e:
    if e.response.status_code == 401:
        print("API密钥无效")
    elif e.response.status_code == 429:
        print("请求频率过高，请稍后重试")
    else:
        print(f"HTTP错误: {e.response.status_code}")
except Exception as e:
    print(f"未知错误: {e}")
```

### 2. 超时和重试机制

```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(delay * (2 ** attempt))  # 指数退避
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3)
def get_task_status_with_retry(client, task_id):
    return client.get_task_status(task_id)
```

### 3. 进度监控

```python
def monitor_progress(client, task_id):
    """监控任务进度并显示详细信息"""
    last_step = 0
    
    while True:
        status_info = client.get_task_status(task_id)
        status = status_info.get('status')
        progress = status_info.get('progress', {})
        
        # 显示进度变化
        current_step = progress.get('current_step', 0)
        if current_step > last_step:
            total_steps = progress.get('total_steps', 0)
            message = progress.get('message', '')
            percentage = progress.get('percentage', 0)
            
            print(f"📊 步骤 {current_step}/{total_steps} ({percentage}%): {message}")
            last_step = current_step
        
        if status in ['completed', 'failed', 'cancelled']:
            break
            
        time.sleep(2)
```

### 4. 批量处理

```python
import asyncio
import aiohttp

async def process_urls_batch(urls, max_concurrent=5):
    """批量处理多个URL"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_single_url(session, url):
        async with semaphore:
            # 创建任务
            task_id = await create_async_task(session, url)
            # 等待完成
            result = await wait_for_completion(session, task_id)
            return url, result
    
    async with aiohttp.ClientSession() as session:
        tasks = [process_single_url(session, url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
    return results
```

## 🔍 故障排查

### 常见错误及解决方案

1. **401 Unauthorized**
   - 检查API密钥是否正确
   - 确认API密钥格式：`X-API-Key: your-key-here`

2. **404 Task Not Found**
   - 检查任务ID是否正确
   - 任务可能已过期（默认TTL为1小时）

3. **429 Too Many Requests**
   - 降低请求频率
   - 实现指数退避重试机制

4. **500 Internal Server Error**
   - 检查服务器日志
   - 验证请求参数格式

5. **任务长时间处于pending状态**
   - 检查服务器负载
   - 可能达到最大并发任务数限制

### 调试技巧

1. **启用详细日志**:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **检查网络连接**:
```bash
curl -I http://localhost:48089/api/v1/health
```

3. **验证API密钥**:
```bash
curl -H "X-API-Key: your-key" http://localhost:48089/api/v1/tools
```

## 📊 性能优化

### 1. 连接池配置

```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

session = requests.Session()

# 配置重试策略
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504],
)

# 配置连接池
adapter = HTTPAdapter(
    pool_connections=10,
    pool_maxsize=20,
    max_retries=retry_strategy
)

session.mount("http://", adapter)
session.mount("https://", adapter)
```

### 2. 异步并发处理

```python
import asyncio
import aiohttp

async def async_generate_cards(urls):
    """异步并发生成多个卡片"""
    connector = aiohttp.TCPConnector(limit=10)
    timeout = aiohttp.ClientTimeout(total=300)
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout
    ) as session:
        tasks = []
        for url in urls:
            task = asyncio.create_task(
                process_url_async(session, url)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return results
```

## 🎯 使用示例

查看 `examples/` 目录下的完整示例代码：

- `async_client_python.py` - Python实现
- `async_client_javascript.js` - JavaScript/Node.js实现  
- `async_client_bash.sh` - Bash/curl实现

每个示例都包含完整的错误处理、进度监控和结果保存功能。
