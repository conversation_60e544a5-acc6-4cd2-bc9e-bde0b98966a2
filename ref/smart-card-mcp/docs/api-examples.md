# Smart Card MCP HTTP API 使用示例

## 概述

本文档提供了 Smart Card MCP HTTP API 的详细使用示例，包括同步调用、异步调用、任务管理等功能。

## 基础配置

### 服务器地址
- **Base URL**: `http://localhost:48089/api/v1`
- **API 文档**: `http://localhost:48089/api/v1/docs`

### 认证配置

API支持两种认证方式，通过.env文件配置：

```bash
# 在.env文件中设置API密钥
API_KEY=smart-card-mcp-your-secure-key-here
```

**生成安全的API密钥：**
```bash
# 使用内置工具生成API密钥
go run scripts/generate-api-key.go
```

**在请求中使用API密钥：**
```bash
# 方式1：使用 X-API-Key 头（推荐）
curl -H "X-API-Key: smart-card-mcp-your-secure-key-here" ...

# 方式2：使用 Authorization Bearer 头
curl -H "Authorization: Bearer smart-card-mcp-your-secure-key-here" ...
```

**注意：** 如果未配置API_KEY，则API访问无需认证（仅用于开发环境）。

## 1. 同步工具调用

### 1.1 从文本生成卡片（同步）

```bash
curl -X POST http://localhost:48089/api/v1/tools/generate_card_from_text/sync \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "text": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。AI技术正在快速发展，并在各个行业中找到应用，从医疗保健到金融，从交通到娱乐。"
    }
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "iVBORw0KGgoAAAANSUhEUgAA...",
        "mimeType": "image/png"
      }
    ]
  },
  "execution_time": 15.2
}
```

### 1.2 从URL生成卡片（同步）

```bash
curl -X POST http://localhost:48089/api/v1/tools/generate_card_from_url/sync \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "url": "https://example.com/article"
    }
  }'
```

## 2. 异步工具调用

### 2.1 启动异步任务

```bash
curl -X POST http://localhost:48089/api/v1/tools/generate_card_from_text/async \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "text": "这是一个长文本，需要异步处理..."
    },
    "callback_url": "https://your-app.com/webhook"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_a1b2c3d4",
    "status": "pending",
    "created_at": "2025-01-20T10:30:00Z",
    "estimated_duration": 20
  }
}
```

### 2.2 查询任务状态

```bash
curl http://localhost:48089/api/v1/tasks/task_a1b2c3d4
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_a1b2c3d4",
    "tool_name": "generate_card_from_text",
    "status": "running",
    "progress": {
      "current": 2,
      "total": 3,
      "message": "正在生成HTML卡片设计...",
      "percentage": 66.67
    },
    "created_at": "2025-01-20T10:30:00Z",
    "started_at": "2025-01-20T10:30:05Z",
    "completed_at": null,
    "result": null,
    "error": null
  }
}
```

### 2.3 获取任务结果

```bash
curl http://localhost:48089/api/v1/tasks/task_a1b2c3d4/result
```

**响应示例**（任务完成后）:
```json
{
  "success": true,
  "data": {
    "task_id": "task_a1b2c3d4",
    "status": "completed",
    "result": {
      "content": [
        {
          "type": "image",
          "data": "iVBORw0KGgoAAAANSUhEUgAA...",
          "mimeType": "image/png"
        }
      ]
    },
    "execution_time": 18.5,
    "completed_at": "2025-01-20T10:30:23Z"
  }
}
```

## 3. 实时进度监控

### 3.1 使用 Server-Sent Events (SSE)

```bash
curl -H "Accept: text/event-stream" \
  http://localhost:48089/api/v1/tasks/task_a1b2c3d4/progress
```

**响应流示例**:
```
data: {"task_id":"task_a1b2c3d4","progress":{"current":1,"total":3,"message":"正在分析和总结文本内容...","percentage":33.33},"timestamp":"2025-01-20T10:30:10Z"}

data: {"task_id":"task_a1b2c3d4","progress":{"current":2,"total":3,"message":"正在生成HTML卡片设计...","percentage":66.67},"timestamp":"2025-01-20T10:30:15Z"}

data: {"task_id":"task_a1b2c3d4","status":"completed","result":{"content":[{"type":"image","data":"..."}]},"timestamp":"2025-01-20T10:30:23Z"}
```

### 3.2 JavaScript 客户端示例

```javascript
const eventSource = new EventSource('http://localhost:48089/api/v1/tasks/task_a1b2c3d4/progress');

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Progress update:', data);

  if (data.status === 'completed') {
    console.log('Task completed:', data.result);
    eventSource.close();
  } else if (data.status === 'failed') {
    console.error('Task failed:', data.error);
    eventSource.close();
  }
};

eventSource.onerror = function(event) {
  console.error('SSE error:', event);
};
```

## 4. 任务管理

### 4.1 获取任务列表

```bash
# 获取所有任务
curl http://localhost:48089/api/v1/tasks

# 按状态筛选
curl http://localhost:48089/api/v1/tasks?status=running

# 分页
curl http://localhost:48089/api/v1/tasks?limit=5&offset=10
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "task_id": "task_a1b2c3d4",
        "tool_name": "generate_card_from_text",
        "status": "running",
        "progress": {
          "current": 2,
          "total": 3,
          "percentage": 66.67
        },
        "created_at": "2025-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 10,
      "offset": 0,
      "has_more": true
    }
  }
}
```

### 4.2 取消任务

```bash
curl -X DELETE http://localhost:48089/api/v1/tasks/task_a1b2c3d4
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_a1b2c3d4",
    "status": "cancelled",
    "message": "Task cancelled successfully"
  }
}
```

## 5. 系统信息

### 5.1 健康检查

```bash
curl http://localhost:48089/api/v1/health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "smart-card-mcp",
    "version": "1.0.0",
    "uptime": 3600,
    "tasks": {
      "active": 3,
      "completed": 127,
      "failed": 2
    }
  }
}
```

### 5.2 系统统计

```bash
curl http://localhost:48089/api/v1/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "stats": {
      "total_tasks": 132,
      "active_tasks": 3,
      "completed_tasks": 127,
      "failed_tasks": 2,
      "average_execution_time": 18.5,
      "uptime": 3600,
      "memory_usage": "45.2MB",
      "tools": {
        "generate_card_from_text": {
          "total_calls": 89,
          "success_rate": 98.9,
          "avg_duration": 16.2
        },
        "generate_card_from_url": {
          "total_calls": 43,
          "success_rate": 95.3,
          "avg_duration": 22.8
        }
      }
    }
  }
}
```

### 5.3 获取可用工具列表

```bash
curl http://localhost:48089/api/v1/tools
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "name": "generate_card_from_text",
        "description": "从文本生成智能卡片",
        "parameters": {
          "text": {
            "type": "string",
            "required": true,
            "description": "完整的原始文本内容"
          }
        },
        "estimated_duration": 20
      },
      {
        "name": "generate_card_from_url",
        "description": "从网页链接生成智能卡片",
        "parameters": {
          "url": {
            "type": "string",
            "required": true,
            "description": "完整的网页URL地址"
          }
        },
        "estimated_duration": 30
      }
    ]
  }
}
```

## 6. 错误处理

### 6.1 常见错误响应

所有错误响应都遵循统一格式：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数 'text' 是必需的",
    "details": {
      "parameter": "text",
      "expected": "string",
      "received": "null"
    }
  },
  "request_id": "req_123456789"
}
```

### 6.2 错误代码说明

- `INVALID_PARAMETER`: 参数错误
- `TOOL_NOT_FOUND`: 工具不存在
- `TASK_NOT_FOUND`: 任务不存在
- `TASK_ALREADY_COMPLETED`: 任务已完成
- `EXECUTION_FAILED`: 执行失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_ERROR`: 内部错误
- `UNAUTHORIZED`: 认证失败

## 7. Python 客户端示例

### 7.1 同步调用示例

```python
import requests
import json

def generate_card_sync(text):
    url = "http://localhost:48089/api/v1/tools/generate_card_from_text/sync"
    payload = {
        "arguments": {
            "text": text
        }
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        result = response.json()
        if result["success"]:
            return result["data"]
        else:
            print(f"Error: {result['error']['message']}")
    else:
        print(f"HTTP Error: {response.status_code}")

    return None

# 使用示例
text = "人工智能正在改变世界..."
result = generate_card_sync(text)
if result:
    print(f"生成成功，执行时间: {result.get('execution_time', 0)}秒")
```

### 7.2 异步调用示例

```python
import requests
import time

def generate_card_async(text):
    # 启动异步任务
    url = "http://localhost:48089/api/v1/tools/generate_card_from_text/async"
    payload = {
        "arguments": {
            "text": text
        }
    }

    response = requests.post(url, json=payload)
    if response.status_code != 202:
        print(f"Failed to start task: {response.status_code}")
        return None

    result = response.json()
    task_id = result["data"]["task_id"]
    print(f"Task started: {task_id}")

    # 轮询任务状态
    while True:
        status_url = f"http://localhost:48089/api/v1/tasks/{task_id}"
        status_response = requests.get(status_url)

        if status_response.status_code == 200:
            status_data = status_response.json()["data"]
            status = status_data["status"]

            if status == "completed":
                # 获取结果
                result_url = f"http://localhost:48089/api/v1/tasks/{task_id}/result"
                result_response = requests.get(result_url)
                if result_response.status_code == 200:
                    return result_response.json()["data"]
                break
            elif status == "failed":
                print(f"Task failed: {status_data.get('error', 'Unknown error')}")
                break
            else:
                # 显示进度
                progress = status_data.get("progress")
                if progress:
                    print(f"Progress: {progress['percentage']:.1f}% - {progress['message']}")

                time.sleep(2)  # 等待2秒后再次检查
        else:
            print(f"Failed to get task status: {status_response.status_code}")
            break

    return None

# 使用示例
text = "这是一个需要异步处理的长文本..."
result = generate_card_async(text)
if result:
    print(f"异步生成成功，执行时间: {result.get('execution_time', 0)}秒")
```

## 8. 最佳实践

### 8.1 选择同步还是异步

- **同步调用**：适用于简短文本（<1000字符）或需要立即获得结果的场景
- **异步调用**：适用于长文本、URL抓取或需要处理多个任务的场景

### 8.2 错误重试策略

```python
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session
```

### 8.3 批量处理

```python
def batch_generate_cards(texts, max_concurrent=5):
    import concurrent.futures

    def process_single(text):
        return generate_card_async(text)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
        futures = [executor.submit(process_single, text) for text in texts]
        results = []

        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"Error processing text: {e}")
                results.append(None)

        return results
```

## 9. 故障排除

### 9.1 常见问题

1. **连接被拒绝**
   - 检查服务是否正在运行：`docker-compose ps`
   - 检查端口是否正确：默认为48089

2. **任务一直处于pending状态**
   - 检查是否达到最大并发任务数限制（默认10个）
   - 查看服务器日志：`docker-compose logs smart-card-mcp`

3. **API密钥认证失败**
   - 确认环境变量`SSE_ACCESS_KEY`已正确设置
   - 检查请求头格式是否正确

### 9.2 调试技巧

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 查看实时日志
docker-compose logs -f smart-card-mcp

# 检查API健康状态
curl http://localhost:48089/api/v1/health
```

## 10. 性能优化

### 10.1 并发控制

- 默认最大并发任务数：10个
- 可通过环境变量调整：`MAX_CONCURRENT_TASKS=20`

### 10.2 缓存策略

- 任务结果默认保留1小时
- 可通过环境变量调整：`TASK_TTL=3600`

### 10.3 监控指标

定期检查系统统计信息：
```bash
curl http://localhost:48089/api/v1/stats | jq '.data.stats'
```

关注以下指标：
- 成功率（success_rate）
- 平均执行时间（avg_duration）
- 活跃任务数（active_tasks）
- 内存使用量（memory_usage）
```