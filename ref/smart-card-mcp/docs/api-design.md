# Smart Card MCP HTTP API 设计文档

## 概述

Smart Card MCP 服务器提供 RESTful HTTP API 接口，支持同步和异步调用两种模式，方便外部系统集成。

## API 端点设计

### 基础信息

- **Base URL**: `http://localhost:48089/api/v1`
- **Content-Type**: `application/json`
- **Authentication**: <PERSON><PERSON> (可选)

### 1. 工具调用接口

#### 1.1 同步调用工具
```
POST /api/v1/tools/{tool_name}/sync
```

**请求体**:
```json
{
  "arguments": {
    "text": "要生成卡片的文本内容"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "type": "image",
        "data": "base64_encoded_image",
        "mimeType": "image/png"
      }
    ]
  },
  "execution_time": 15.2
}
```

#### 1.2 异步调用工具
```
POST /api/v1/tools/{tool_name}/async
```

**请求体**:
```json
{
  "arguments": {
    "text": "要生成卡片的文本内容"
  },
  "callback_url": "https://your-app.com/webhook"
}
```

**响应**:
```json
{
  "success": true,
  "task_id": "task_123456789",
  "status": "pending",
  "created_at": "2025-01-20T10:30:00Z",
  "estimated_duration": 20
}
```

### 2. 任务管理接口

#### 2.1 查询任务状态
```
GET /api/v1/tasks/{task_id}
```

**响应**:
```json
{
  "task_id": "task_123456789",
  "status": "running",
  "progress": {
    "current": 2,
    "total": 3,
    "message": "正在生成HTML卡片设计...",
    "percentage": 66.67
  },
  "created_at": "2025-01-20T10:30:00Z",
  "started_at": "2025-01-20T10:30:05Z",
  "completed_at": null,
  "result": null,
  "error": null
}
```

#### 2.2 获取任务结果
```
GET /api/v1/tasks/{task_id}/result
```

**响应** (成功时):
```json
{
  "success": true,
  "task_id": "task_123456789",
  "status": "completed",
  "result": {
    "content": [
      {
        "type": "image",
        "data": "base64_encoded_image",
        "mimeType": "image/png"
      }
    ]
  },
  "execution_time": 18.5,
  "completed_at": "2025-01-20T10:30:23Z"
}
```

#### 2.3 取消任务
```
DELETE /api/v1/tasks/{task_id}
```

**响应**:
```json
{
  "success": true,
  "task_id": "task_123456789",
  "status": "cancelled",
  "message": "Task cancelled successfully"
}
```

#### 2.4 获取任务列表
```
GET /api/v1/tasks?status=running&limit=10&offset=0
```

**响应**:
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "task_123456789",
      "tool_name": "generate_card_from_text",
      "status": "running",
      "progress": {
        "current": 2,
        "total": 3,
        "percentage": 66.67
      },
      "created_at": "2025-01-20T10:30:00Z"
    }
  ],
  "pagination": {
    "total": 25,
    "limit": 10,
    "offset": 0,
    "has_more": true
  }
}
```

### 3. 实时进度接口

#### 3.1 SSE 进度流
```
GET /api/v1/tasks/{task_id}/progress
Accept: text/event-stream
```

**响应流**:
```
data: {"task_id":"task_123456789","progress":{"current":1,"total":3,"message":"正在分析和总结文本内容...","percentage":33.33},"timestamp":"2025-01-20T10:30:10Z"}

data: {"task_id":"task_123456789","progress":{"current":2,"total":3,"message":"正在生成HTML卡片设计...","percentage":66.67},"timestamp":"2025-01-20T10:30:15Z"}

data: {"task_id":"task_123456789","status":"completed","result":{"content":[{"type":"image","data":"..."}]},"timestamp":"2025-01-20T10:30:23Z"}
```

### 4. 工具信息接口

#### 4.1 获取可用工具列表
```
GET /api/v1/tools
```

**响应**:
```json
{
  "success": true,
  "tools": [
    {
      "name": "generate_card_from_text",
      "description": "从文本生成智能卡片",
      "parameters": {
        "text": {
          "type": "string",
          "required": true,
          "description": "完整的原始文本内容"
        }
      },
      "estimated_duration": 20
    },
    {
      "name": "generate_card_from_url",
      "description": "从网页链接生成智能卡片",
      "parameters": {
        "url": {
          "type": "string",
          "required": true,
          "description": "完整的网页URL地址"
        }
      },
      "estimated_duration": 30
    }
  ]
}
```

#### 4.2 获取工具详细信息
```
GET /api/v1/tools/{tool_name}
```

### 5. 系统信息接口

#### 5.1 健康检查
```
GET /api/v1/health
```

**响应**:
```json
{
  "status": "healthy",
  "service": "smart-card-mcp",
  "version": "1.0.0",
  "uptime": 3600,
  "tasks": {
    "active": 3,
    "completed": 127,
    "failed": 2
  }
}
```

#### 5.2 系统统计
```
GET /api/v1/stats
```

**响应**:
```json
{
  "success": true,
  "stats": {
    "total_tasks": 132,
    "active_tasks": 3,
    "completed_tasks": 127,
    "failed_tasks": 2,
    "average_execution_time": 18.5,
    "uptime": 3600,
    "memory_usage": "45.2MB",
    "tools": {
      "generate_card_from_text": {
        "total_calls": 89,
        "success_rate": 98.9,
        "avg_duration": 16.2
      },
      "generate_card_from_url": {
        "total_calls": 43,
        "success_rate": 95.3,
        "avg_duration": 22.8
      }
    }
  }
}
```

### 6. API 文档接口

#### 6.1 OpenAPI 规范
```
GET /api/v1/docs/openapi.json
```

#### 6.2 Swagger UI
```
GET /api/v1/docs
```

## 错误响应格式

所有错误响应都遵循统一格式：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数 'text' 是必需的",
    "details": {
      "parameter": "text",
      "expected": "string",
      "received": "null"
    }
  },
  "request_id": "req_123456789"
}
```

### 错误代码

- `INVALID_PARAMETER`: 参数错误
- `TOOL_NOT_FOUND`: 工具不存在
- `TASK_NOT_FOUND`: 任务不存在
- `TASK_ALREADY_COMPLETED`: 任务已完成
- `EXECUTION_FAILED`: 执行失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_ERROR`: 内部错误

## 认证和授权

### Bearer Token 认证
```
Authorization: Bearer your_api_token_here
```

### API Key 认证
```
X-API-Key: your_api_key_here
```

## 限流和配额

- 每个 API Key 每分钟最多 60 次请求
- 同时运行的异步任务最多 10 个
- 单个任务最长执行时间 5 分钟

## Webhook 回调

异步任务完成后，如果提供了 `callback_url`，系统会发送 POST 请求：

```json
{
  "task_id": "task_123456789",
  "status": "completed",
  "result": {
    "content": [...]
  },
  "execution_time": 18.5,
  "completed_at": "2025-01-20T10:30:23Z"
}
```