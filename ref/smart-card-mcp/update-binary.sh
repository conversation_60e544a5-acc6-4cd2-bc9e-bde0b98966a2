#!/bin/bash

# Smart Card MCP 二进制更新脚本
# 用于从 Docker 容器中提取最新的二进制文件

set -e

# 默认参数
CONTAINER_NAME="smart-card-mcp-server"
BINARY_PATH="/app/smart-card-mcp"
OUTPUT_DIR="bin"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔄 开始更新 Smart Card MCP 二进制文件...${NC}"

# 检查容器是否运行
if ! docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q "$CONTAINER_NAME"; then
    echo -e "${RED}❌ 容器 '$CONTAINER_NAME' 未运行，请先启动容器${NC}"
    echo -e "${YELLOW}💡 运行: docker compose up -d${NC}"
    exit 1
fi

CONTAINER_STATUS=$(docker ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}")
echo -e "${GREEN}✅ 容器状态: $CONTAINER_STATUS${NC}"

# 创建输出目录
if [ ! -d "$OUTPUT_DIR" ]; then
    mkdir -p "$OUTPUT_DIR"
    echo -e "${BLUE}📁 创建目录: $OUTPUT_DIR${NC}"
fi

# 生成时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TIMESTAMPED_FILE="$OUTPUT_DIR/smart-card-mcp-linux-$TIMESTAMP"
LATEST_FILE="$OUTPUT_DIR/smart-card-mcp-linux-latest"

# 从容器拷贝二进制文件
echo -e "${BLUE}📦 从容器拷贝二进制文件...${NC}"
if ! docker cp "${CONTAINER_NAME}:${BINARY_PATH}" "$TIMESTAMPED_FILE"; then
    echo -e "${RED}❌ 拷贝失败${NC}"
    exit 1
fi

# 创建最新版本的符号链接
if [ -f "$LATEST_FILE" ]; then
    rm -f "$LATEST_FILE"
fi
cp "$TIMESTAMPED_FILE" "$LATEST_FILE"

# 设置执行权限
chmod +x "$TIMESTAMPED_FILE"
chmod +x "$LATEST_FILE"

# 获取文件大小
FILE_SIZE=$(stat -f%z "$TIMESTAMPED_FILE" 2>/dev/null || stat -c%s "$TIMESTAMPED_FILE" 2>/dev/null)
FILE_SIZE_MB=$(echo "scale=2; $FILE_SIZE / 1024 / 1024" | bc)

echo -e "${GREEN}✅ 二进制文件更新完成!${NC}"
echo -e "${CYAN}📄 时间戳版本: $TIMESTAMPED_FILE (${FILE_SIZE_MB} MB)${NC}"
echo -e "${CYAN}🔗 最新版本: $LATEST_FILE (${FILE_SIZE_MB} MB)${NC}"

# 显示目录内容
echo -e "\n${YELLOW}📋 $OUTPUT_DIR 目录内容:${NC}"
ls -lh "$OUTPUT_DIR"

echo -e "\n${YELLOW}💡 使用方法:${NC}"
echo -e "${WHITE}   - 直接运行: ./$LATEST_FILE${NC}"
echo -e "${WHITE}   - 或使用时间戳版本: ./$TIMESTAMPED_FILE${NC}"
echo -e "${WHITE}   - 查看版本: ./$LATEST_FILE --version${NC}"

echo -e "\n${GREEN}🎉 更新完成!${NC}"
