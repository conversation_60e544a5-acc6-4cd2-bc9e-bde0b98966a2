# Smart Card MCP Server Environment Configuration
# Copy this file to .env and fill in your actual values

# ================================
# Required Configuration
# ================================

# ARK API Configuration (Preferred - DeepSeek V3)
ARK_API_KEY=576a03b3-c58c-4f73-9725-7d3ba37945ce
ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
ARK_MODEL=deepseek-v3-250324

# OpenAI API Configuration (Fallback)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# Jina API Configuration (Required for web fetching)
JINA_API_KEY=your_jina_api_key_here
JINA_API_URL=https://r.jina.ai/

# ================================
# Optional Configuration
# ================================

# Server Configuration
LOG_LEVEL=INFO
MCP_TRANSPORT=mixed
MCP_PORTS=48083,48084
MCP_BASE_URL=http://localhost:48083
MCP_DEBUG=false

# Authentication (Optional)
SSE_ACCESS_KEY=your_sse_access_key_here

# Directory Configuration
OUTPUT_DIR=output
TEMPLATES_DIR=templates

# File Management Configuration
AUTO_CLEANUP_FILES=false
SAVE_GENERATED_CONTENT=true
PUBLIC_URL_BASE=http://localhost:48089

# DStaff Integration Configuration
DSTAFF_ENABLED=false
DSTAFF_ENDPOINT_URL=http://*********:8800
DSTAFF_USE_OFFICIAL_AUTH=true

# ================================
# Alternative API Providers
# ================================

# ARK API Setup Instructions:
# 1. Visit https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint
# 2. Create a model inference endpoint
# 3. Select deepseek-v3-250324 model
# 4. Get your API key and endpoint URL

# If using Azure OpenAI instead of OpenAI
# OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment-name
# OPENAI_API_KEY=your_azure_openai_key
# OPENAI_MODEL=gpt-4

# If using other OpenAI-compatible APIs (like Ollama, LocalAI, etc.)
# OPENAI_BASE_URL=http://localhost:11434/v1
# OPENAI_API_KEY=not-needed-for-local
# OPENAI_MODEL=llama2

# Popular model options:
# ARK Models:
# - deepseek-v3-250324 (recommended, most capable)
# OpenAI Models:
# - gpt-4o-mini (recommended, cost-effective)
# - gpt-4o (most capable)
# - gpt-4-turbo
# - gpt-3.5-turbo (legacy)
# - For local models: llama2, codellama, mistral, etc.

# ================================
# File Management
# ================================

# File Cleanup Configuration:
# AUTO_CLEANUP_FILES=false (default) - Keep generated files in output directory
# AUTO_CLEANUP_FILES=true - Automatically delete files after processing
# Note: This only affects files saved to the output directory, not system temp files

# Content Saving Configuration:
# SAVE_GENERATED_CONTENT=true (default) - Save all generated content to output/saved directory
# SAVE_GENERATED_CONTENT=false - Disable content saving feature
# Saved content includes: summary.md, card.html, card.png, and metadata.json

# Public URL Configuration:
# PUBLIC_URL_BASE=http://localhost:48089 (default) - Base URL for public file access
# Used when ?public_url=1 parameter is provided in result API
# Images will be accessible at: {PUBLIC_URL_BASE}/api/v1/public/output/{filename}

# ================================
# DStaff Integration
# ================================

# DStaff Platform Integration Setup:
# 1. Set DSTAFF_ENABLED=true to enable integration
# 2. Configure DSTAFF_ENDPOINT_URL to your DStaff server
# 3. Set DSTAFF_USE_OFFICIAL_AUTH=true for token validation
# 4. When enabled, generated cards will be automatically uploaded to DStaff
# 5. Requires Authorization header with Bearer token and task_id parameter

# Example DStaff usage:
# curl -X POST http://localhost:48084/api/v1/tools/generate_card_from_text \
#   -H "Authorization: Bearer your_dstaff_token" \
#   -H "Content-Type: application/json" \
#   -d '{"text": "Your content", "task_id": "your_task_id"}'

# ================================
# Docker Compose Overrides
# ================================

# Uncomment and modify these if you want to override Docker Compose settings
# COMPOSE_PROJECT_NAME=smart-card-mcp
# COMPOSE_FILE=docker-compose.yml
