#!/bin/bash

# DStaff Integration Test Script
# This script tests the DStaff integration functionality

echo "🧪 Testing DStaff Integration API..."

# Configuration
BASE_URL="http://localhost:48089"
API_ENDPOINT="$BASE_URL/api/v1/tools/generate_card_from_text"

# Test 1: Basic card generation without DStaff (should work)
echo ""
echo "📝 Test 1: Basic card generation (DStaff disabled)"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是一个测试智能卡片的内容。DStaff 集成已经实现，但当前处于禁用状态。"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | head -20

echo ""
echo "✅ Test 1 completed"

# Test 2: Test with DStaff headers (should work but skip DStaff upload)
echo ""
echo "📝 Test 2: Card generation with DStaff headers (DStaff disabled)"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test_dstaff_token_123" \
  -H "X-Task-ID: test_task_456" \
  -d '{
    "text": "这是一个带有 DStaff 认证头的测试请求。由于 DStaff 未启用，应该跳过上传。"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | head -20

echo ""
echo "✅ Test 2 completed"

# Test 3: Test with task_id in query parameter
echo ""
echo "📝 Test 3: Card generation with task_id in query parameter"
curl -X POST "$API_ENDPOINT?task_id=query_task_789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test_dstaff_token_456" \
  -d '{
    "text": "这是一个通过查询参数传递 task_id 的测试请求。"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | head -20

echo ""
echo "✅ Test 3 completed"

# Test 4: Test health check
echo ""
echo "📝 Test 4: Health check"
curl -X GET "$BASE_URL/health" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo "✅ Test 4 completed"

# Test 5: Test API documentation
echo ""
echo "📝 Test 5: API documentation access"
curl -X GET "$BASE_URL/api/v1/docs" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | head -10

echo ""
echo "✅ Test 5 completed"

echo ""
echo "🎉 All DStaff integration tests completed!"
echo ""
echo "📋 Test Summary:"
echo "   ✅ Basic card generation works"
echo "   ✅ DStaff headers are accepted (but skipped when disabled)"
echo "   ✅ Task ID extraction from query parameters works"
echo "   ✅ Health check endpoint works"
echo "   ✅ API documentation is accessible"
echo ""
echo "🔧 To enable DStaff integration:"
echo "   1. Set DSTAFF_ENABLED=true in your .env file"
echo "   2. Configure DSTAFF_ENDPOINT_URL"
echo "   3. Set DSTAFF_USE_OFFICIAL_AUTH=true"
echo "   4. Restart the service with: docker compose up -d --build"
echo ""
echo "📚 For more information, see docs/dstaff-integration.md"
