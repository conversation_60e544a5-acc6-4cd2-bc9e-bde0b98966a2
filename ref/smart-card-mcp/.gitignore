# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
smart-card-mcp

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Output directories
output/
data/
temp/

# Docker
.dockerignore

# Build artifacts
dist/
build/

# Temporary files
tmp/
temp/

# Coverage reports
coverage.out
coverage.html

# Air (live reload tool) temporary files
tmp/

# Local development files
local/
dev/

# Backup files
*.bak
*.backup

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Generated documentation
docs/_build/

# Test coverage
*.cover
*.coverprofile

# Profiling data
*.prof
*.pprof

# Memory dumps
*.mem

# Crash logs
crash.log

# Local configuration overrides
config.local.yaml
config.local.json

# Generated files
*.generated.go

# Certificates and keys
*.pem
*.key
*.crt
*.p12

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
cache/
