# Smart Card MCP 二进制更新脚本 - 简化版本

param(
    [string]$ContainerName = "smart-card-mcp-server",
    [string]$BinaryPath = "/app/smart-card-mcp",
    [string]$OutputDir = "bin"
)

Write-Host "🔄 开始更新 Smart Card MCP 二进制文件..." -ForegroundColor Green

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "📁 创建目录: $OutputDir" -ForegroundColor Blue
}

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$timestampedFile = "$OutputDir/smart-card-mcp-linux-$timestamp"
$latestFile = "$OutputDir/smart-card-mcp-linux-latest"

# 从容器拷贝二进制文件
Write-Host "📦 从容器拷贝二进制文件..." -ForegroundColor Blue
try {
    docker cp "${ContainerName}:${BinaryPath}" $timestampedFile
    if ($LASTEXITCODE -ne 0) {
        throw "Docker copy failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "❌ 拷贝失败: $_" -ForegroundColor Red
    Write-Host "💡 请确保容器正在运行: docker compose up -d" -ForegroundColor Yellow
    exit 1
}

# 创建最新版本的拷贝
if (Test-Path $latestFile) {
    Remove-Item $latestFile -Force
}
Copy-Item $timestampedFile $latestFile

# 获取文件大小
$fileSize = (Get-Item $timestampedFile).Length
$fileSizeMB = [math]::Round($fileSize / 1MB, 2)

Write-Host "✅ 二进制文件更新完成!" -ForegroundColor Green
Write-Host "📄 时间戳版本: $timestampedFile ($fileSizeMB MB)" -ForegroundColor Cyan
Write-Host "🔗 最新版本: $latestFile ($fileSizeMB MB)" -ForegroundColor Cyan

# 显示目录内容
Write-Host "`n📋 $OutputDir 目录内容:" -ForegroundColor Yellow
Get-ChildItem $OutputDir | Format-Table Name, Length, LastWriteTime -AutoSize

Write-Host "`n💡 使用方法:" -ForegroundColor Yellow
Write-Host "   - 在 Linux 环境中运行: ./$latestFile" -ForegroundColor White
Write-Host "   - 或使用时间戳版本: ./$timestampedFile" -ForegroundColor White

Write-Host "`n🎉 更新完成!" -ForegroundColor Green
