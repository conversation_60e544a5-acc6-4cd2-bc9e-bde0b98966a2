## Description
<!-- Provide a concise description of the changes in this PR -->

Fixes #<issue_number> (if applicable)

## Type of Change
<!-- Please select all the relevant options by replacing [ ] with [x] -->

- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] MCP spec compatibility implementation
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Code refactoring (no functional changes)
- [ ] Performance improvement
- [ ] Tests only (no functional changes)
- [ ] Other (please describe):

## Checklist
<!-- Please select all that apply by replacing [ ] with [x] -->

- [ ] My code follows the code style of this project
- [ ] I have performed a self-review of my own code
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] I have updated the documentation accordingly

## MCP Spec Compliance
<!-- If this PR implements a feature from the MCP specification, please answer the following -->
<!-- If not applicable, remove this section -->

- [ ] This PR implements a feature defined in the MCP specification
- [ ] Link to relevant spec section: [Link text](https://modelcontextprotocol.io/specification/path-to-section)
- [ ] Implementation follows the specification exactly

## Additional Information
<!-- Any additional information that might be useful for reviewers -->
<!-- If not applicable, remove this section -->
