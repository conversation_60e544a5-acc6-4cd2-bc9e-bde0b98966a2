// Code generated by `go generate`. DO NOT EDIT.
// source: server/internal/gen/request_handler.go.tmpl
package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
)

// HandleMessage processes an incoming JSON-RPC message and returns an appropriate response
func (s *MCPServer) HandleMessage(
	ctx context.Context,
	message json.RawMessage,
) mcp.JSONRPCMessage {
	// Add server to context
	ctx = context.WithValue(ctx, serverKey{}, s)
	var err *requestError

	var baseMessage struct {
		JSONRPC string      `json:"jsonrpc"`
		Method  mcp.MCPMethod `json:"method"`
		ID      any           `json:"id,omitempty"`
		Result  any           `json:"result,omitempty"`
	}

	if err := json.Unmarshal(message, &baseMessage); err != nil {
		return createErrorResponse(
			nil,
			mcp.PARSE_ERROR,
			"Failed to parse message",
		)
	}

	// Check for valid JSONRPC version
	if baseMessage.JSONRPC != mcp.JSONRPC_VERSION {
		return createErrorResponse(
			baseMessage.ID,
			mcp.INVALID_REQUEST,
			"Invalid JSON-RPC version",
		)
	}

	if baseMessage.ID == nil {
		var notification mcp.JSONRPCNotification
		if err := json.Unmarshal(message, &notification); err != nil {
			return createErrorResponse(
				nil,
				mcp.PARSE_ERROR,
				"Failed to parse notification",
			)
		}
		s.handleNotification(ctx, notification)
		return nil // Return nil for notifications
	}

	if baseMessage.Result != nil {
		// this is a response to a request sent by the server (e.g. from a ping
		// sent due to WithKeepAlive option)
		return nil
	}

	handleErr := s.hooks.onRequestInitialization(ctx, baseMessage.ID, message)
    if handleErr != nil {
    	return createErrorResponse(
    		baseMessage.ID,
    		mcp.INVALID_REQUEST,
    		handleErr.Error(),
    	)
    }

    // Get request header from ctx
    h := ctx.Value(requestHeader)
	headers, ok := h.(http.Header)

	if headers == nil || !ok {
		headers = make(http.Header)
	}

	switch baseMessage.Method {
	{{- range .}}
	case mcp.{{.MethodName}}:
		var request mcp.{{.ParamType}}
		var result *mcp.{{.ResultType}}
		{{ if .Group }}if s.capabilities.{{.Group}} == nil {
			err = &requestError{
				id:   baseMessage.ID,
				code: mcp.METHOD_NOT_FOUND,
				err:  fmt.Errorf("{{toLower .GroupName}} %w", ErrUnsupported),
			}
		} else{{ end }} if unmarshalErr := json.Unmarshal(message, &request); unmarshalErr != nil {
			err = &requestError{
				id:   baseMessage.ID,
				code: mcp.INVALID_REQUEST,
				err:  &UnparsableMessageError{message: message, err: unmarshalErr, method: baseMessage.Method},
			}
		} else {
            request.Header = headers
			s.hooks.before{{.HookName}}(ctx, baseMessage.ID, &request)
			result, err = s.{{.HandlerFunc}}(ctx, baseMessage.ID, request)
		}
		if err != nil {
			s.hooks.onError(ctx, baseMessage.ID, baseMessage.Method, &request, err)
			return err.ToJSONRPCError()
		}
		s.hooks.after{{.HookName}}(ctx, baseMessage.ID, &request, result)
		return createResponse(baseMessage.ID, *result)
	{{- end }}
	default:
		return createErrorResponse(
			baseMessage.ID,
			mcp.METHOD_NOT_FOUND,
			fmt.Sprintf("Method %s not found", baseMessage.Method),
		)
	}
}
