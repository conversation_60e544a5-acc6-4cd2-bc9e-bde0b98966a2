# Environment variables and secrets
.env
.env.local
.env.production
.env.staging

# Go build artifacts
*.exe
*.exe~
*.dll
*.so
*.dylib
chatppt-mcp-server
main

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Docker
.dockerignore

# Test files
test_*.py
test_*.go

# Temporary files
*.tmp
*.temp

# Node modules (if any)
node_modules/

# Python cache (if any)
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/

# Coverage reports
coverage.txt
coverage.html

# Dependency directories
vendor/

# Binary distribution
dist/
build/
