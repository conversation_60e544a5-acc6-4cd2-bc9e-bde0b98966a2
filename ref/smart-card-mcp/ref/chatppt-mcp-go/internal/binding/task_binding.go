package binding

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TaskBinding represents the binding between task_id and project_id
type TaskBinding struct {
	TaskID    string    `json:"task_id"`
	ProjectID string    `json:"project_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TaskBindingManager manages the binding relationships between task_id and project_id
type TaskBindingManager struct {
	bindings map[string]*TaskBinding
	mutex    sync.RWMutex
	filePath string
}

// NewTaskBindingManager creates a new TaskBindingManager instance
func NewTaskBindingManager(dataDir string) (*TaskBindingManager, error) {
	// Ensure data directory exists
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	filePath := filepath.Join(dataDir, "task_bindings.json")
	
	manager := &TaskBindingManager{
		bindings: make(map[string]*TaskBinding),
		filePath: filePath,
	}

	// Load existing bindings from file
	if err := manager.loadFromFile(); err != nil {
		log.Printf("⚠️ Failed to load existing bindings: %v", err)
		// Continue with empty bindings - this is not a fatal error
	}

	return manager, nil
}

// BindTaskToProject creates or updates a binding between task_id and project_id
func (m *TaskBindingManager) BindTaskToProject(taskID, projectID string) error {
	if taskID == "" || projectID == "" {
		return fmt.Errorf("task_id and project_id cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	
	// Check if binding already exists
	if existing, exists := m.bindings[taskID]; exists {
		// Update existing binding
		existing.ProjectID = projectID
		existing.UpdatedAt = now
		log.Printf("🔄 Updated binding: task_id=%s -> project_id=%s", taskID, projectID)
	} else {
		// Create new binding
		m.bindings[taskID] = &TaskBinding{
			TaskID:    taskID,
			ProjectID: projectID,
			CreatedAt: now,
			UpdatedAt: now,
		}
		log.Printf("✅ Created binding: task_id=%s -> project_id=%s", taskID, projectID)
	}

	// Save to file
	return m.saveToFile()
}

// GetProjectID retrieves the project_id for a given task_id
func (m *TaskBindingManager) GetProjectID(taskID string) (string, bool) {
	if taskID == "" {
		return "", false
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if binding, exists := m.bindings[taskID]; exists {
		log.Printf("🔍 Found binding: task_id=%s -> project_id=%s", taskID, binding.ProjectID)
		return binding.ProjectID, true
	}

	log.Printf("❌ No binding found for task_id=%s", taskID)
	return "", false
}

// GetBinding retrieves the complete binding information for a given task_id
func (m *TaskBindingManager) GetBinding(taskID string) (*TaskBinding, bool) {
	if taskID == "" {
		return nil, false
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if binding, exists := m.bindings[taskID]; exists {
		// Return a copy to prevent external modification
		bindingCopy := *binding
		return &bindingCopy, true
	}

	return nil, false
}

// RemoveBinding removes a binding for a given task_id
func (m *TaskBindingManager) RemoveBinding(taskID string) error {
	if taskID == "" {
		return fmt.Errorf("task_id cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.bindings[taskID]; exists {
		delete(m.bindings, taskID)
		log.Printf("🗑️ Removed binding for task_id=%s", taskID)
		return m.saveToFile()
	}

	return fmt.Errorf("binding not found for task_id=%s", taskID)
}

// ListBindings returns all current bindings
func (m *TaskBindingManager) ListBindings() map[string]*TaskBinding {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// Return a copy to prevent external modification
	result := make(map[string]*TaskBinding)
	for k, v := range m.bindings {
		bindingCopy := *v
		result[k] = &bindingCopy
	}

	return result
}

// CleanupOldBindings removes bindings older than the specified duration
func (m *TaskBindingManager) CleanupOldBindings(maxAge time.Duration) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	cutoff := time.Now().Add(-maxAge)
	removed := 0

	for taskID, binding := range m.bindings {
		if binding.UpdatedAt.Before(cutoff) {
			delete(m.bindings, taskID)
			removed++
		}
	}

	if removed > 0 {
		log.Printf("🧹 Cleaned up %d old bindings (older than %v)", removed, maxAge)
		return m.saveToFile()
	}

	return nil
}

// loadFromFile loads bindings from the JSON file
func (m *TaskBindingManager) loadFromFile() error {
	if _, err := os.Stat(m.filePath); os.IsNotExist(err) {
		// File doesn't exist, start with empty bindings
		log.Printf("📁 Binding file doesn't exist, starting with empty bindings")
		return nil
	}

	data, err := os.ReadFile(m.filePath)
	if err != nil {
		return fmt.Errorf("failed to read bindings file: %w", err)
	}

	if len(data) == 0 {
		// Empty file, start with empty bindings
		return nil
	}

	var bindings map[string]*TaskBinding
	if err := json.Unmarshal(data, &bindings); err != nil {
		return fmt.Errorf("failed to unmarshal bindings: %w", err)
	}

	m.bindings = bindings
	log.Printf("📂 Loaded %d bindings from file", len(m.bindings))
	return nil
}

// saveToFile saves current bindings to the JSON file
func (m *TaskBindingManager) saveToFile() error {
	data, err := json.MarshalIndent(m.bindings, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal bindings: %w", err)
	}

	if err := os.WriteFile(m.filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write bindings file: %w", err)
	}

	return nil
}

// GetStats returns statistics about the current bindings
func (m *TaskBindingManager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_bindings": len(m.bindings),
		"file_path":      m.filePath,
	}

	if len(m.bindings) > 0 {
		var oldest, newest time.Time
		first := true
		
		for _, binding := range m.bindings {
			if first {
				oldest = binding.CreatedAt
				newest = binding.UpdatedAt
				first = false
			} else {
				if binding.CreatedAt.Before(oldest) {
					oldest = binding.CreatedAt
				}
				if binding.UpdatedAt.After(newest) {
					newest = binding.UpdatedAt
				}
			}
		}
		
		stats["oldest_binding"] = oldest
		stats["newest_update"] = newest
	}

	return stats
}
