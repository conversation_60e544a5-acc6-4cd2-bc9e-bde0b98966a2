package auth

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"
)

// AuthKey is a custom context key for storing authentication information
type AuthKey struct{}

// AuthInfo holds authentication information
type AuthInfo struct {
	APIPPTKey    string
	SSEAccessKey string
}

// WithAuthInfo adds authentication info to the context
func WithAuthInfo(ctx context.Context, authInfo *AuthInfo) context.Context {
	return context.WithValue(ctx, AuthKey{}, authInfo)
}

// FromContext extracts authentication info from the context
func FromContext(ctx context.Context) (*AuthInfo, error) {
	authInfo, ok := ctx.Value(AuthKey{}).(*AuthInfo)
	if !ok {
		return nil, fmt.Errorf("missing authentication info in context")
	}
	return authInfo, nil
}

// ValidateSSEAccess validates SSE access using query parameters
func ValidateSSEAccess(queryString, sseAccessKey string) bool {
	// If no access key is configured, allow open access
	if sseAccessKey == "" {
		log.Println("🔓 SSE access validation disabled (no access key configured)")
		return true
	}

	// Parse query string
	values, err := url.ParseQuery(queryString)
	if err != nil {
		log.Printf("❌ SSE access validation error: failed to parse query string: %v", err)
		return false
	}

	providedKey := values.Get("key")
	if providedKey == sseAccessKey {
		log.Println("✅ SSE access validation successful")
		return true
	}

	log.Println("❌ SSE access validation failed: invalid key provided")
	return false
}

// HTTPContextFunc extracts auth info from HTTP request headers
func HTTPContextFunc(apiKey, sseAccessKey string) func(context.Context, *http.Request) context.Context {
	return func(ctx context.Context, r *http.Request) context.Context {
		authInfo := &AuthInfo{
			APIPPTKey:    apiKey,
			SSEAccessKey: sseAccessKey,
		}
		return WithAuthInfo(ctx, authInfo)
	}
}

// StdioContextFunc extracts auth info from environment variables
func StdioContextFunc(apiKey, sseAccessKey string) func(context.Context) context.Context {
	return func(ctx context.Context) context.Context {
		authInfo := &AuthInfo{
			APIPPTKey:    apiKey,
			SSEAccessKey: sseAccessKey,
		}
		return WithAuthInfo(ctx, authInfo)
	}
}

// GetAPIKey extracts the API key from context for making API calls
func GetAPIKey(ctx context.Context) (string, error) {
	authInfo, err := FromContext(ctx)
	if err != nil {
		return "", err
	}
	if authInfo.APIPPTKey == "" {
		return "", fmt.Errorf("API key not configured")
	}
	return authInfo.APIPPTKey, nil
}

// CreateAuthHeader creates the Authorization header for API calls
func CreateAuthHeader(apiKey string) string {
	return fmt.Sprintf("Bearer %s", apiKey)
}

// ValidateAuthHeader validates the Authorization header format
func ValidateAuthHeader(header string) (string, error) {
	if header == "" {
		return "", fmt.Errorf("authorization header is empty")
	}

	parts := strings.SplitN(header, " ", 2)
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return "", fmt.Errorf("invalid authorization header format")
	}

	return parts[1], nil
}
