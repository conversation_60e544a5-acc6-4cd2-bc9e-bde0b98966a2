# ChatPPT MCP Server Environment Variables

# Required: Your ChatPPT API Key from www.yoo-ai.com
API_PPT_KEY=your_api_key_here

# Optional: Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Optional: SSE Access Key for authentication (if not set, allows open access)
# Generate a secure random key, e.g.: openssl rand -hex 32
SSE_ACCESS_KEY=your_sse_access_key_here

# Optional: MCP Server Port (default: 8000)
MCP_PORT=8000

# Optional: API Base URL (usually no need to change)
API_BASE=https://saas.api.yoo-ai.com

# Optional: Transport mode (stdio, sse, streamable_http, mixed)
MCP_TRANSPORT=mixed

# Optional: Enable MCP debug mode for detailed logging
MCP_DEBUG=false

# ============================================================================
# DSTAFF INTEGRATION (for Task Binding Feature)
# ============================================================================

# Enable DStaff integration (true/false)
DSTAFF_ENABLED=true

# DStaff endpoint URL
DSTAFF_ENDPOINT_URL=http://10.50.5.3:8800

# Use official DStaff authentication
DSTAFF_USE_OFFICIAL_AUTH=false

# DStaff authentication token (optional)
DSTAFF_TOKEN=your_dstaff_token

# Default DStaff task ID (optional, can be overridden per request)
DSTAFF_TASK_ID=your_default_task_id

# ============================================================================
# TASK BINDING FEATURE NOTES
# ============================================================================
#
# The new Task Binding feature automatically binds project_id (ppt_id) with
# dstaff task_id to avoid AI context passing issues in MCP calls.
#
# How it works:
# 1. When generating PPT (build_ppt, text_build_ppt, etc.), the system
#    automatically saves the binding between task_id and returned project_id
# 2. For subsequent operations (query_ppt, download_ppt, etc.), you can:
#    - Provide ppt_id directly (backward compatible)
#    - Or just provide task_id, system will auto-resolve project_id
#
# Data persistence:
# - Bindings are stored in ./data/task_bindings.json
# - This directory is mounted as a volume in Docker
# - Bindings persist across container restarts
