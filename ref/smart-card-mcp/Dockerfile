# Build stage
FROM golang:1.23-alpine AS builder

# Install git and ca-certificates (needed for fetching dependencies and HTTPS)
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o smart-card-mcp .

# Final stage
FROM alpine:latest

# Install ca-certificates, tzdata, and Chinese fonts
RUN apk --no-cache add ca-certificates tzdata fontconfig ttf-dejavu

# Install Chinese fonts and emoji fonts
RUN apk --no-cache add --repository http://dl-cdn.alpinelinux.org/alpine/edge/community \
    font-wqy-zenhei font-noto-cjk font-noto-emoji

# Install Chromium for HTML to image conversion
RUN apk --no-cache add chromium

# Set Chromium environment variables for better emoji support
ENV CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu --font-render-hinting=none"

# Install Node.js and npm for Puppeteer (alternative method)
RUN apk --no-cache add nodejs npm

# Install Puppeteer globally
RUN npm install -g puppeteer

# Update font cache
RUN fc-cache -fv

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/smart-card-mcp .

# Create necessary directories
RUN mkdir -p output templates logs data && \
    chown -R appuser:appgroup /app

# Templates directory will be created by the application if needed

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 48083 48084

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:49083/health || exit 1

# Default command
CMD ["./smart-card-mcp"]
