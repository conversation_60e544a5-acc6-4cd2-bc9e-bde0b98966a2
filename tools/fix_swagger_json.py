#!/usr/bin/env python3
"""
修复swagger.json文件，确保包含schemes字段
"""

import json
import os

def fix_swagger_json():
    swagger_file = "docs/swagger.json"
    
    if not os.path.exists(swagger_file):
        print(f"❌ 文件不存在: {swagger_file}")
        return False
    
    try:
        # 读取swagger.json
        with open(swagger_file, 'r', encoding='utf-8') as f:
            swagger_data = json.load(f)
        
        print(f"📖 读取swagger.json成功")
        
        # 检查并修复schemes
        if 'schemes' not in swagger_data or not swagger_data['schemes']:
            swagger_data['schemes'] = ['http']
            print("✅ 添加schemes: ['http']")
        
        # 确保host存在
        if 'host' not in swagger_data:
            swagger_data['host'] = 'localhost:8080'
            print("✅ 添加host: localhost:8080")
        
        # 确保basePath存在
        if 'basePath' not in swagger_data:
            swagger_data['basePath'] = '/api/v1'
            print("✅ 添加basePath: /api/v1")
        
        # 检查paths是否为空
        if 'paths' not in swagger_data or not swagger_data['paths']:
            print("⚠️  警告: paths为空或不存在")
        else:
            path_count = len(swagger_data['paths'])
            print(f"✅ 找到 {path_count} 个API路径")
        
        # 写回文件
        with open(swagger_file, 'w', encoding='utf-8') as f:
            json.dump(swagger_data, f, indent=4, ensure_ascii=False)
        
        print(f"✅ swagger.json修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始修复swagger.json...")
    if fix_swagger_json():
        print("🎉 修复成功！现在可以启动服务器测试Swagger UI")
        print("📖 访问: http://localhost:8080/swagger/index.html")
        print("🔍 检查: http://localhost:8080/swagger/doc.json")
    else:
        print("❌ 修复失败")
