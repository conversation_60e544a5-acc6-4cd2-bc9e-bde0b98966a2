@echo off
REM PPT Narrator Build Script for Windows
REM This script ensures Swagger docs are generated before building

echo 🚀 Starting PPT Narrator build process...

REM Check if we're in the right directory
if not exist "go.mod" (
    echo ❌ Error: go.mod not found. Please run this script from the project root.
    exit /b 1
)

REM Create docs directory if it doesn't exist
if not exist "docs" (
    echo 📁 Creating docs directory...
    mkdir docs
)

REM Check if swag is installed
swag version >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing swag tool...
    go install github.com/swaggo/swag/cmd/swag@latest
)

REM Generate Swagger documentation
echo 📚 Generating Swagger documentation...
swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal

REM Verify docs were generated
if not exist "docs\docs.go" (
    echo ❌ Error: Failed to generate Swagger docs
    exit /b 1
)

echo ✅ Swagger documentation generated successfully

REM Build the application
echo 🔨 Building application...
go build -o main.exe cmd/server/main.go

echo ✅ Build completed successfully!
echo 🎉 You can now run the application with: main.exe
echo 📖 Swagger UI will be available at: http://localhost:8080/swagger/index.html

pause
