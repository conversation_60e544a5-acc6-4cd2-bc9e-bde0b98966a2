@echo off
echo === PPT Narrator with DStaff Integration ===
echo.

echo Setting up environment variables for DStaff integration...

REM 设置数字员工平台集成环境变量
set DSTAFF_ENABLED=true
set DSTAFF_USE_OFFICIAL_AUTH=true
set DSTAFF_ENDPOINT_URL=http://*********:8800
set MCP_DEBUG=true

echo Configuration:
echo   DSTAFF_ENABLED=%DSTAFF_ENABLED%
echo   DSTAFF_USE_OFFICIAL_AUTH=%DSTAFF_USE_OFFICIAL_AUTH%
echo   DSTAFF_ENDPOINT_URL=%DSTAFF_ENDPOINT_URL%
echo   MCP_DEBUG=%MCP_DEBUG%
echo.

echo Starting PPT Narrator with DStaff integration...
echo.
echo Note: All requests will require Bearer Token authentication
echo Example usage:
echo   curl -H "Authorization: Bearer your_token_here" http://localhost:48080/
echo.

REM 启动服务
docker-compose up mcp-pipeline-server

pause
