@echo off
echo Starting PPT Narrator Server...
echo.

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found. Please copy .env.example to .env and configure it.
    echo.
    pause
    exit /b 1
)

REM Load environment variables from .env file (basic implementation)
for /f "usebackq tokens=1,2 delims==" %%a in (.env) do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM Check if OPENAI_API_KEY is set
if "%OPENAI_API_KEY%"=="" (
    echo Error: OPENAI_API_KEY not set in .env file
    echo Please add your OpenAI API key to the .env file
    echo.
    pause
    exit /b 1
)

REM Create directories
if not exist uploads mkdir uploads
if not exist screenshots mkdir screenshots
if not exist videos mkdir videos
if not exist temp mkdir temp

echo Configuration loaded successfully
echo Starting server on port %PORT%...
echo.

REM Run the server
go run cmd/server/main.go

pause
