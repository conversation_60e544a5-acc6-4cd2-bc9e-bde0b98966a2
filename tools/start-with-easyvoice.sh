#!/bin/bash

# PPT Narrator - EasyVoice TTS 启动脚本
# 使用 EasyVoice 作为主要的 TTS 提供商

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "=== PPT Narrator - EasyVoice TTS 启动脚本 ==="
echo

# 检查 Docker 和 Docker Compose
print_message $YELLOW "检查系统依赖..."

if ! command -v docker &> /dev/null; then
    print_message $RED "错误: Docker 未安装或不在 PATH 中"
    print_message $YELLOW "请安装 Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_message $RED "错误: Docker Compose 未安装或不在 PATH 中"
    print_message $YELLOW "请安装 Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
fi

print_message $GREEN "✓ Docker 和 Docker Compose 已安装"

# 检查配置文件
print_message $YELLOW "检查配置文件..."

if [ ! -f ".env" ]; then
    if [ -f ".env.easyvoice.example" ]; then
        print_message $YELLOW "未找到 .env 文件，正在从 .env.easyvoice.example 创建..."
        cp .env.easyvoice.example .env
        print_message $GREEN "✓ 已创建 .env 文件"
        print_message $YELLOW "请编辑 .env 文件，填入您的 EasyVoice 凭据："
        print_message $CYAN "  - EASYVOICE_USERNAME=your_username"
        print_message $CYAN "  - EASYVOICE_PASSWORD=your_password"
        print_message $CYAN "  - OPENAI_API_KEY=your_openai_key (用于AI讲稿生成)"
        echo
        read -p "按 Enter 键继续，或按 Ctrl+C 退出编辑配置文件..."
    else
        print_message $RED "错误: 未找到 .env.easyvoice.example 文件"
        exit 1
    fi
fi

# 检查必要的环境变量
print_message $YELLOW "检查 EasyVoice 配置..."

source .env

if [ -z "$EASYVOICE_USERNAME" ] || [ "$EASYVOICE_USERNAME" = "your_username_here" ]; then
    print_message $RED "错误: 请在 .env 文件中设置 EASYVOICE_USERNAME"
    exit 1
fi

if [ -z "$EASYVOICE_PASSWORD" ] || [ "$EASYVOICE_PASSWORD" = "your_password_here" ]; then
    print_message $RED "错误: 请在 .env 文件中设置 EASYVOICE_PASSWORD"
    exit 1
fi

if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
    print_message $YELLOW "警告: 未设置 OPENAI_API_KEY，AI 讲稿生成功能可能不可用"
    print_message $YELLOW "如果您有 MiniMax API Key，可以设置 AI_PROVIDER=minimax"
fi

print_message $GREEN "✓ EasyVoice 配置检查完成"

# 显示配置信息
print_message $BLUE "当前配置："
print_message $CYAN "  TTS 提供商: ${TTS_PROVIDER:-easyvoice}"
print_message $CYAN "  EasyVoice 语音: ${EASYVOICE_VOICE:-zh-CN-YunxiNeural}"
print_message $CYAN "  EasyVoice 用户: ${EASYVOICE_USERNAME}"
print_message $CYAN "  AI 提供商: ${AI_PROVIDER:-openai}"
echo

# 停止现有容器
print_message $YELLOW "停止现有容器..."
docker-compose -f docker-compose.easyvoice.yml down --remove-orphans 2>/dev/null || true
print_message $GREEN "✓ 现有容器已停止"

# 构建和启动服务
print_message $YELLOW "构建和启动 PPT Narrator (EasyVoice TTS)..."
docker-compose -f docker-compose.easyvoice.yml up -d --build

# 等待服务启动
print_message $YELLOW "等待服务启动..."
sleep 10

# 检查服务状态
print_message $YELLOW "检查服务状态..."
if curl -s http://localhost:8080/health > /dev/null; then
    print_message $GREEN "✓ PPT Narrator 服务已启动"
else
    print_message $RED "✗ PPT Narrator 服务启动失败"
    print_message $YELLOW "查看日志："
    docker-compose -f docker-compose.easyvoice.yml logs ppt-narrator
    exit 1
fi

# 测试 EasyVoice TTS
print_message $YELLOW "测试 EasyVoice TTS 连接..."
response=$(curl -s http://localhost:8080/api/v1/tts/test/easyvoice)
if echo "$response" | grep -q '"success":true'; then
    print_message $GREEN "✓ EasyVoice TTS 连接成功"
else
    print_message $YELLOW "⚠ EasyVoice TTS 连接测试失败，请检查凭据"
    print_message $CYAN "响应: $response"
fi

# 显示可用的语音模型
print_message $YELLOW "获取可用的 TTS 提供商..."
providers_response=$(curl -s http://localhost:8080/api/v1/tts/providers)
if echo "$providers_response" | grep -q '"success":true'; then
    print_message $GREEN "✓ TTS 提供商信息获取成功"
    
    # 提取 EasyVoice 可用状态
    easyvoice_available=$(echo "$providers_response" | grep -o '"name":"easyvoice"[^}]*"available":[^,}]*' | grep -o 'true\|false')
    if [ "$easyvoice_available" = "true" ]; then
        print_message $GREEN "✓ EasyVoice 提供商可用"
    else
        print_message $YELLOW "⚠ EasyVoice 提供商不可用，请检查配置"
    fi
else
    print_message $YELLOW "⚠ 无法获取 TTS 提供商信息"
fi

print_message $GREEN "=== 启动完成 ==="
echo
print_message $BLUE "服务信息："
print_message $CYAN "  Web 界面: http://localhost:8080"
print_message $CYAN "  API 文档: http://localhost:8080/docs"
print_message $CYAN "  健康检查: http://localhost:8080/health"
print_message $CYAN "  TTS 提供商: http://localhost:8080/api/v1/tts/providers"
echo

print_message $BLUE "EasyVoice TTS 功能："
print_message $CYAN "  • 支持 21 种高质量中文语音"
print_message $CYAN "  • 可调节语速、音调、音量"
print_message $CYAN "  • 支持动态切换语音模型"
print_message $CYAN "  • 与 OpenAI、MiniMax 兼容"
echo

print_message $BLUE "推荐的语音模型："
print_message $CYAN "  男声: zh-CN-YunxiNeural (云希，自然亲切)"
print_message $CYAN "  女声: zh-CN-XiaoxiaoNeural (晓晓，甜美可爱)"
print_message $CYAN "  商务: zh-CN-YunjieNeural (云杰，专业商务)"
print_message $CYAN "  知性: zh-CN-XiaohanNeural (晓涵，知性优雅)"
echo

print_message $BLUE "常用命令："
print_message $CYAN "  查看日志: docker-compose -f docker-compose.easyvoice.yml logs -f"
print_message $CYAN "  停止服务: docker-compose -f docker-compose.easyvoice.yml down"
print_message $CYAN "  重启服务: docker-compose -f docker-compose.easyvoice.yml restart"
print_message $CYAN "  测试 API: ./tests/test_tts_api.sh"
echo

print_message $GREEN "PPT Narrator (EasyVoice TTS) 已成功启动！"
