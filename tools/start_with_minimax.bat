@echo off
chcp 65001 >nul
echo === PPT Narrator - MiniMax TTS 启动脚本 ===
echo.

REM 检查必要的环境变量
if "%MINIMAX_TTS_API_KEY%"=="" (
    echo ❌ 请先设置 MiniMax TTS API Key
    echo.
    echo 设置方法：
    echo 1. 临时设置（本次会话有效）：
    echo    set MINIMAX_TTS_API_KEY=your_api_key_here
    echo    set MINIMAX_TTS_GROUP_ID=your_group_id_here
    echo.
    echo 2. 永久设置（推荐）：
    echo    创建 .env 文件，参考 .env.minimax.example
    echo.
    echo 3. 获取 API Key：
    echo    访问 https://platform.minimaxi.com/
    echo.
    pause
    exit /b 1
)

if "%MINIMAX_TTS_GROUP_ID%"=="" (
    echo ❌ 请先设置 MiniMax TTS Group ID
    echo.
    echo 设置方法：
    echo    set MINIMAX_TTS_GROUP_ID=your_group_id_here
    echo.
    pause
    exit /b 1
)

REM 设置 MiniMax TTS 相关环境变量
echo 配置 MiniMax TTS 环境变量...

REM AI 和 TTS 都使用 MiniMax
set AI_PROVIDER=minimax
set TTS_PROVIDER=minimax

REM MiniMax AI 配置（如果没有设置的话，使用 TTS 的配置）
if "%MINIMAX_API_KEY%"=="" set MINIMAX_API_KEY=%MINIMAX_TTS_API_KEY%
if "%MINIMAX_GROUP_ID%"=="" set MINIMAX_GROUP_ID=%MINIMAX_TTS_GROUP_ID%
set MINIMAX_MODEL=abab6.5s-chat
set MINIMAX_BASE_URL=https://api.minimaxi.com/v1/text/chatcompletion_v2

REM MiniMax TTS 配置
set MINIMAX_TTS_MODEL=speech-02-hd
set MINIMAX_TTS_VOICE_ID=male-qn-qingse
set MINIMAX_TTS_EMOTION=happy

REM 通用配置
set TTS_SPEED=1.0
set MAX_TOKENS=8000
set TEMPERATURE=0.7

REM 讲稿风格配置
set NARRATOR_ROLE=专业讲师
set NARRATOR_STYLE=亲切自然
set TARGET_AUDIENCE=大学生
set SPEAKING_TONE=轻松友好
set SPEECH_NATURALNESS=高度口语化

REM 工具路径
set LIBREOFFICE_PATH=libreoffice
set FFMPEG_PATH=ffmpeg

REM 目录配置
set PORT=8080
set UPLOAD_DIR=./uploads
set SCREENSHOT_DIR=./screenshots
set VIDEO_DIR=./videos
set TEMP_DIR=./temp

echo ✅ 环境变量配置完成
echo.

REM 显示当前配置
echo 当前配置:
echo - AI 提供商: %AI_PROVIDER%
echo - TTS 提供商: %TTS_PROVIDER%
echo - TTS 模型: %MINIMAX_TTS_MODEL%
echo - 音色: %MINIMAX_TTS_VOICE_ID%
echo - 情感: %MINIMAX_TTS_EMOTION%
echo - 语速: %TTS_SPEED%
echo - 讲师角色: %NARRATOR_ROLE%
echo - 讲解风格: %NARRATOR_STYLE%
echo.

REM 检查依赖
echo 检查系统依赖...

REM 检查 Go
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go 未安装，请先安装 Go
    echo 下载地址: https://golang.org/dl/
    pause
    exit /b 1
)
echo ✅ Go 已安装

REM 检查 FFmpeg
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ FFmpeg 未安装，请先安装 FFmpeg
    echo 下载地址: https://ffmpeg.org/download.html
    pause
    exit /b 1
)
echo ✅ FFmpeg 已安装

REM 检查 LibreOffice
libreoffice --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  LibreOffice 未安装，PPT 转换功能将不可用
    echo 下载地址: https://www.libreoffice.org/download/
) else (
    echo ✅ LibreOffice 已安装
)

echo.
echo 依赖检查完成，正在启动服务...
echo.

REM 创建必要的目录
if not exist "uploads" mkdir uploads
if not exist "screenshots" mkdir screenshots
if not exist "videos" mkdir videos
if not exist "temp" mkdir temp

REM 启动服务
echo 启动 PPT Narrator 服务...
echo 服务地址: http://localhost:%PORT%
echo.
echo 按 Ctrl+C 停止服务
echo.

go run cmd/server/main.go

if %errorlevel% neq 0 (
    echo.
    echo ❌ 服务启动失败
    echo.
    echo 可能的原因：
    echo 1. 端口 %PORT% 已被占用
    echo 2. Go 模块依赖问题，尝试运行: go mod tidy
    echo 3. 配置错误，请检查环境变量
    echo.
    pause
)

echo.
echo 服务已停止
pause
