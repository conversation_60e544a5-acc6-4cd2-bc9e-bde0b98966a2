#!/usr/bin/env python3
"""
音频诊断工具 - 检查音频文件的问题
"""

import os
import subprocess
import json
import requests
import sys
from pathlib import Path

def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg 可用")
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"   版本: {version_line}")
            return True
        else:
            print("❌ FFmpeg 不可用")
            return False
    except Exception as e:
        print(f"❌ FFmpeg 检查失败: {e}")
        return False

def analyze_audio_file(file_path):
    """分析音频文件的详细信息"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        # 使用ffprobe获取音频信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', file_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"❌ 无法分析文件: {file_path}")
            print(f"   错误: {result.stderr}")
            return None
        
        data = json.loads(result.stdout)
        
        # 提取音频流信息
        audio_streams = [s for s in data.get('streams', []) if s.get('codec_type') == 'audio']
        
        if not audio_streams:
            print(f"❌ 文件中没有音频流: {file_path}")
            return None
        
        stream = audio_streams[0]
        format_info = data.get('format', {})
        
        file_size = os.path.getsize(file_path)
        duration = float(format_info.get('duration', 0))
        
        info = {
            'file_path': file_path,
            'file_size': file_size,
            'duration': duration,
            'codec': stream.get('codec_name'),
            'sample_rate': stream.get('sample_rate'),
            'channels': stream.get('channels'),
            'bit_rate': stream.get('bit_rate'),
            'format': format_info.get('format_name'),
        }
        
        print(f"📊 音频文件分析: {os.path.basename(file_path)}")
        print(f"   文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        print(f"   时长: {duration:.2f} 秒")
        print(f"   编解码器: {info['codec']}")
        print(f"   采样率: {info['sample_rate']} Hz")
        print(f"   声道数: {info['channels']}")
        print(f"   比特率: {info['bit_rate']} bps" if info['bit_rate'] else "   比特率: 未知")
        print(f"   格式: {info['format']}")
        
        # 检查是否有声音（通过比特率和文件大小判断）
        if file_size < 1000:  # 小于1KB
            print("   ⚠️  文件太小，可能没有实际音频内容")
        elif duration > 0 and file_size / duration < 1000:  # 每秒小于1KB
            print("   ⚠️  比特率过低，可能音频质量有问题")
        else:
            print("   ✅ 文件大小和时长正常")
        
        return info
        
    except Exception as e:
        print(f"❌ 分析文件时出错: {e}")
        return None

def test_audio_generation():
    """测试音频生成功能"""
    print("\n🧪 测试音频生成...")
    
    # 创建测试目录
    test_dir = "audio_test"
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # 生成测试音频（1秒的正弦波）
        test_file = os.path.join(test_dir, "test_sine.mp3")
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'sine=frequency=440:duration=1',
            '-c:a', 'mp3', '-b:a', '128k', '-y', test_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and os.path.exists(test_file):
            print("✅ 测试音频生成成功")
            analyze_audio_file(test_file)
            
            # 清理测试文件
            os.remove(test_file)
            return True
        else:
            print("❌ 测试音频生成失败")
            print(f"   错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试音频生成异常: {e}")
        return False
    finally:
        # 清理测试目录
        try:
            os.rmdir(test_dir)
        except:
            pass

def test_audio_concat():
    """测试音频合并功能"""
    print("\n🔗 测试音频合并...")
    
    test_dir = "concat_test"
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # 生成两个测试音频文件
        test_files = []
        for i, freq in enumerate([440, 880]):  # A4 和 A5 音符
            test_file = os.path.join(test_dir, f"test_{i+1}.mp3")
            cmd = [
                'ffmpeg', '-f', 'lavfi', '-i', f'sine=frequency={freq}:duration=0.5',
                '-c:a', 'mp3', '-b:a', '128k', '-y', test_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                test_files.append(test_file)
            else:
                print(f"❌ 生成测试文件 {i+1} 失败")
                return False
        
        # 创建合并列表文件
        list_file = os.path.join(test_dir, "concat_list.txt")
        with open(list_file, 'w') as f:
            for test_file in test_files:
                f.write(f"file '{os.path.abspath(test_file)}'\n")
        
        # 合并音频文件
        output_file = os.path.join(test_dir, "combined.mp3")
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', '-i', list_file,
            '-c', 'copy', '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and os.path.exists(output_file):
            print("✅ 音频合并测试成功")
            analyze_audio_file(output_file)
            return True
        else:
            print("❌ 音频合并测试失败")
            print(f"   错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 音频合并测试异常: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            import shutil
            shutil.rmtree(test_dir)
        except:
            pass

def check_project_audio_files(project_id):
    """检查项目的音频文件"""
    print(f"\n🔍 检查项目音频文件 (ID: {project_id})...")
    
    # 假设的项目目录结构
    project_dir = f"uploads/{project_id}"
    audio_dir = os.path.join(project_dir, "audio")
    
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return False
    
    print(f"📁 音频目录: {audio_dir}")
    
    # 查找所有音频文件
    audio_files = []
    for ext in ['.mp3', '.wav', '.m4a']:
        audio_files.extend(Path(audio_dir).glob(f"*{ext}"))
    
    if not audio_files:
        print("❌ 没有找到音频文件")
        return False
    
    print(f"📄 找到 {len(audio_files)} 个音频文件:")
    
    all_good = True
    for audio_file in sorted(audio_files):
        print(f"\n--- {audio_file.name} ---")
        info = analyze_audio_file(str(audio_file))
        if not info:
            all_good = False
        elif info['duration'] == 0:
            print("   ❌ 音频时长为0，可能是空文件")
            all_good = False
        elif info['file_size'] < 100:
            print("   ❌ 文件太小，可能没有实际内容")
            all_good = False
    
    return all_good

def test_tts_api(project_id=None):
    """测试TTS API"""
    print(f"\n🎤 测试TTS API...")
    
    base_url = "http://localhost:8080"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务不可用")
            return False
        print("✅ 服务可用")
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False
    
    if project_id:
        # 检查项目进度
        try:
            response = requests.get(f"{base_url}/api/v1/pipeline/{project_id}/progress", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    progress = data.get('progress', {})
                    print(f"📊 项目状态: {progress.get('current_stage')}")
                    print(f"📊 进度: {progress.get('progress')}%")
                    if progress.get('error'):
                        print(f"❌ 错误: {progress.get('error')}")
                else:
                    print(f"❌ 获取进度失败: {data.get('error')}")
            else:
                print(f"❌ 无法获取项目进度: {response.status_code}")
        except Exception as e:
            print(f"❌ 检查项目进度时出错: {e}")
    
    return True

def main():
    """主函数"""
    print("🔧 PPT Narrator 音频诊断工具")
    print("=" * 50)
    
    # 基础检查
    print("1️⃣ 基础环境检查")
    ffmpeg_ok = check_ffmpeg()
    
    if not ffmpeg_ok:
        print("\n❌ FFmpeg 不可用，无法进行音频处理")
        print("请确保 FFmpeg 已正确安装并在 PATH 中")
        return
    
    # 功能测试
    print("\n2️⃣ 功能测试")
    test_audio_generation()
    test_audio_concat()
    
    # API测试
    print("\n3️⃣ API测试")
    test_tts_api()
    
    # 项目文件检查
    if len(sys.argv) > 1:
        project_id = sys.argv[1]
        print(f"\n4️⃣ 项目文件检查")
        check_project_audio_files(project_id)
        test_tts_api(project_id)
    else:
        print("\n💡 提示: 运行 'python audio_diagnostic_tool.py <project_id>' 来检查特定项目的音频文件")
    
    print("\n" + "=" * 50)
    print("🏁 诊断完成")

if __name__ == "__main__":
    main()
