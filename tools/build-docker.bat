@echo off
setlocal enabledelayedexpansion

REM PPT Narrator Docker Build Script for Windows
REM Usage: build-docker.bat [OPTIONS]

REM Default values
set IMAGE_NAME=ppt-narrator
set TAG=latest
set DOCKERFILE=Dockerfile
set PUSH=false
set PLATFORM=linux/amd64
set BUILD_ARGS=

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_build
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-n" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--name" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-t" (
    set TAG=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--tag" (
    set TAG=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-f" (
    set DOCKERFILE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--file" (
    set DOCKERFILE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set PUSH=true
    shift
    goto :parse_args
)
if "%~1"=="--push" (
    set PUSH=true
    shift
    goto :parse_args
)
if "%~1"=="--platform" (
    set PLATFORM=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--alpine" (
    set DOCKERFILE=Dockerfile.alpine
    shift
    goto :parse_args
)
if "%~1"=="--dev" (
    set DOCKERFILE=Dockerfile.dev
    shift
    goto :parse_args
)
if "%~1"=="--no-cache" (
    set BUILD_ARGS=!BUILD_ARGS! --no-cache
    shift
    goto :parse_args
)
echo Unknown option: %~1
goto :show_help

:show_help
echo PPT Narrator Docker Build Script for Windows
echo.
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo     -h, --help              Show this help message
echo     -n, --name NAME         Docker image name (default: ppt-narrator)
echo     -t, --tag TAG           Docker image tag (default: latest)
echo     -f, --file DOCKERFILE   Dockerfile to use (default: Dockerfile)
echo     -p, --push              Push image to registry after build
echo     --platform PLATFORM    Target platform (default: linux/amd64)
echo     --alpine                Use Alpine-based Dockerfile
echo     --dev                   Use development Dockerfile
echo     --no-cache              Build without using cache
echo.
echo Examples:
echo     %0                                          # Basic build
echo     %0 --tag v1.0.0 --push                    # Build and push with version tag
echo     %0 --alpine --tag alpine-latest           # Build Alpine version
echo     %0 --dev --name ppt-narrator-dev          # Build development version
echo.
goto :eof

:start_build
REM Validate inputs
if not exist "%DOCKERFILE%" (
    echo [ERROR] Dockerfile not found: %DOCKERFILE%
    exit /b 1
)

REM Check if Docker is available
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    exit /b 1
)

REM Check if Docker daemon is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker daemon is not running
    exit /b 1
)

REM Build information
echo [INFO] Building Docker image with the following configuration:
echo   Image Name: %IMAGE_NAME%
echo   Tag: %TAG%
echo   Dockerfile: %DOCKERFILE%
echo   Platform: %PLATFORM%
echo   Push: %PUSH%
if not "%BUILD_ARGS%"=="" (
    echo   Build Args: %BUILD_ARGS%
)
echo.

REM Confirm build
set /p CONFIRM="Continue with build? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo [INFO] Build cancelled
    exit /b 0
)

REM Start build
echo [INFO] Starting Docker build...

REM Build command
set BUILD_CMD=docker build --platform %PLATFORM% -f %DOCKERFILE% -t %IMAGE_NAME%:%TAG% %BUILD_ARGS% .

echo [INFO] Running: %BUILD_CMD%
echo.

REM Execute build
%BUILD_CMD%
if errorlevel 1 (
    echo [ERROR] Docker build failed
    exit /b 1
)

echo [SUCCESS] Docker image built successfully: %IMAGE_NAME%:%TAG%

REM Show image info
echo [INFO] Image information:
docker images %IMAGE_NAME%:%TAG%

REM Push if requested
if "%PUSH%"=="true" (
    echo [INFO] Pushing image to registry...
    
    docker push %IMAGE_NAME%:%TAG%
    if errorlevel 1 (
        echo [ERROR] Failed to push image
        exit /b 1
    )
    
    echo [SUCCESS] Image pushed successfully: %IMAGE_NAME%:%TAG%
)

REM Final summary
echo.
echo [SUCCESS] Build completed successfully!
echo.
echo Image: %IMAGE_NAME%:%TAG%
echo.
echo To run the container:
echo   docker run -p 8080:8080 --env-file .env %IMAGE_NAME%:%TAG%
echo.
echo To run with Docker Compose:
echo   docker-compose up
echo.

pause
