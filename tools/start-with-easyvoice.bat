@echo off
setlocal enabledelayedexpansion

REM PPT Narrator - EasyVoice TTS 启动脚本 (Windows)
REM 使用 EasyVoice 作为主要的 TTS 提供商

echo === PPT Narrator - EasyVoice TTS 启动脚本 ===
echo.

REM 检查 Docker 和 Docker Compose
echo 检查系统依赖...

docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker 未安装或不在 PATH 中
    echo 请安装 Docker Desktop: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose 未安装或不在 PATH 中
    echo 请安装 Docker Compose: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

echo ✓ Docker 和 Docker Compose 已安装

REM 检查配置文件
echo 检查配置文件...

if not exist ".env" (
    if exist ".env.easyvoice.example" (
        echo 未找到 .env 文件，正在从 .env.easyvoice.example 创建...
        copy ".env.easyvoice.example" ".env" >nul
        echo ✓ 已创建 .env 文件
        echo 请编辑 .env 文件，填入您的 EasyVoice 凭据：
        echo   - EASYVOICE_USERNAME=your_username
        echo   - EASYVOICE_PASSWORD=your_password
        echo   - OPENAI_API_KEY=your_openai_key (用于AI讲稿生成)
        echo.
        pause
    ) else (
        echo 错误: 未找到 .env.easyvoice.example 文件
        pause
        exit /b 1
    )
)

REM 检查必要的环境变量
echo 检查 EasyVoice 配置...

REM 读取 .env 文件中的配置
for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
    if "%%a"=="EASYVOICE_USERNAME" set EASYVOICE_USERNAME=%%b
    if "%%a"=="EASYVOICE_PASSWORD" set EASYVOICE_PASSWORD=%%b
    if "%%a"=="OPENAI_API_KEY" set OPENAI_API_KEY=%%b
    if "%%a"=="TTS_PROVIDER" set TTS_PROVIDER=%%b
    if "%%a"=="EASYVOICE_VOICE" set EASYVOICE_VOICE=%%b
    if "%%a"=="AI_PROVIDER" set AI_PROVIDER=%%b
)

if "%EASYVOICE_USERNAME%"=="" (
    echo 错误: 请在 .env 文件中设置 EASYVOICE_USERNAME
    pause
    exit /b 1
)

if "%EASYVOICE_USERNAME%"=="your_username_here" (
    echo 错误: 请在 .env 文件中设置正确的 EASYVOICE_USERNAME
    pause
    exit /b 1
)

if "%EASYVOICE_PASSWORD%"=="" (
    echo 错误: 请在 .env 文件中设置 EASYVOICE_PASSWORD
    pause
    exit /b 1
)

if "%EASYVOICE_PASSWORD%"=="your_password_here" (
    echo 错误: 请在 .env 文件中设置正确的 EASYVOICE_PASSWORD
    pause
    exit /b 1
)

if "%OPENAI_API_KEY%"=="" (
    echo 警告: 未设置 OPENAI_API_KEY，AI 讲稿生成功能可能不可用
    echo 如果您有 MiniMax API Key，可以设置 AI_PROVIDER=minimax
)

if "%OPENAI_API_KEY%"=="your_openai_api_key_here" (
    echo 警告: 请设置正确的 OPENAI_API_KEY
)

echo ✓ EasyVoice 配置检查完成

REM 显示配置信息
echo.
echo 当前配置：
if "%TTS_PROVIDER%"=="" set TTS_PROVIDER=easyvoice
if "%EASYVOICE_VOICE%"=="" set EASYVOICE_VOICE=zh-CN-YunxiNeural
if "%AI_PROVIDER%"=="" set AI_PROVIDER=openai

echo   TTS 提供商: %TTS_PROVIDER%
echo   EasyVoice 语音: %EASYVOICE_VOICE%
echo   EasyVoice 用户: %EASYVOICE_USERNAME%
echo   AI 提供商: %AI_PROVIDER%
echo.

REM 停止现有容器
echo 停止现有容器...
docker-compose -f docker-compose.easyvoice.yml down --remove-orphans >nul 2>&1
echo ✓ 现有容器已停止

REM 构建和启动服务
echo 构建和启动 PPT Narrator (EasyVoice TTS)...
docker-compose -f docker-compose.easyvoice.yml up -d --build

if errorlevel 1 (
    echo 错误: 服务启动失败
    echo 查看日志:
    docker-compose -f docker-compose.easyvoice.yml logs ppt-narrator
    pause
    exit /b 1
)

REM 等待服务启动
echo 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
curl -s http://localhost:8080/health >nul 2>&1
if errorlevel 1 (
    echo ✗ PPT Narrator 服务启动失败
    echo 查看日志:
    docker-compose -f docker-compose.easyvoice.yml logs ppt-narrator
    pause
    exit /b 1
) else (
    echo ✓ PPT Narrator 服务已启动
)

REM 测试 EasyVoice TTS
echo 测试 EasyVoice TTS 连接...
curl -s http://localhost:8080/api/v1/tts/test/easyvoice > temp_response.json 2>nul
if exist temp_response.json (
    findstr /c:"\"success\":true" temp_response.json >nul
    if !errorlevel! equ 0 (
        echo ✓ EasyVoice TTS 连接成功
    ) else (
        echo ⚠ EasyVoice TTS 连接测试失败，请检查凭据
        type temp_response.json
    )
    del temp_response.json >nul 2>&1
) else (
    echo ⚠ 无法测试 EasyVoice TTS 连接
)

REM 获取可用的 TTS 提供商
echo 获取可用的 TTS 提供商...
curl -s http://localhost:8080/api/v1/tts/providers > temp_providers.json 2>nul
if exist temp_providers.json (
    findstr /c:"\"success\":true" temp_providers.json >nul
    if !errorlevel! equ 0 (
        echo ✓ TTS 提供商信息获取成功
        
        findstr /c:"\"name\":\"easyvoice\"" temp_providers.json >nul
        if !errorlevel! equ 0 (
            findstr /c:"\"available\":true" temp_providers.json >nul
            if !errorlevel! equ 0 (
                echo ✓ EasyVoice 提供商可用
            ) else (
                echo ⚠ EasyVoice 提供商不可用，请检查配置
            )
        )
    ) else (
        echo ⚠ 无法获取 TTS 提供商信息
    )
    del temp_providers.json >nul 2>&1
) else (
    echo ⚠ 无法获取 TTS 提供商信息
)

echo.
echo === 启动完成 ===
echo.
echo 服务信息：
echo   Web 界面: http://localhost:8080
echo   API 文档: http://localhost:8080/docs
echo   健康检查: http://localhost:8080/health
echo   TTS 提供商: http://localhost:8080/api/v1/tts/providers
echo.
echo EasyVoice TTS 功能：
echo   • 支持 21 种高质量中文语音
echo   • 可调节语速、音调、音量
echo   • 支持动态切换语音模型
echo   • 与 OpenAI、MiniMax 兼容
echo.
echo 推荐的语音模型：
echo   男声: zh-CN-YunxiNeural (云希，自然亲切)
echo   女声: zh-CN-XiaoxiaoNeural (晓晓，甜美可爱)
echo   商务: zh-CN-YunjieNeural (云杰，专业商务)
echo   知性: zh-CN-XiaohanNeural (晓涵，知性优雅)
echo.
echo 常用命令：
echo   查看日志: docker-compose -f docker-compose.easyvoice.yml logs -f
echo   停止服务: docker-compose -f docker-compose.easyvoice.yml down
echo   重启服务: docker-compose -f docker-compose.easyvoice.yml restart
echo   测试 API: tests\test_tts_api.sh (需要 Git Bash 或 WSL)
echo.
echo PPT Narrator (EasyVoice TTS) 已成功启动！
echo.
pause
