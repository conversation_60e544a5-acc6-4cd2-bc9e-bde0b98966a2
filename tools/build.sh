#!/bin/bash

# PPT Narrator Build Script
# This script ensures Swagger docs are generated before building

set -e

echo "🚀 Starting PPT Narrator build process..."

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: go.mod not found. Please run this script from the project root."
    exit 1
fi

# Create docs directory if it doesn't exist
if [ ! -d "docs" ]; then
    echo "📁 Creating docs directory..."
    mkdir -p docs
fi

# Check if swag is installed
if ! command -v swag &> /dev/null; then
    echo "📦 Installing swag tool..."
    go install github.com/swaggo/swag/cmd/swag@latest
fi

# Generate Swagger documentation
echo "📚 Generating Swagger documentation..."
swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal

# Verify docs were generated
if [ ! -f "docs/docs.go" ]; then
    echo "❌ Error: Failed to generate Swagger docs"
    exit 1
fi

echo "✅ Swagger documentation generated successfully"

# Build the application
echo "🔨 Building application..."
go build -o main cmd/server/main.go

echo "✅ Build completed successfully!"
echo "🎉 You can now run the application with: ./main"
echo "📖 Swagger UI will be available at: http://localhost:8080/swagger/index.html"
