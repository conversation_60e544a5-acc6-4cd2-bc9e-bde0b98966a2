#!/bin/bash

# PPT Narrator Docker Build Script
# Usage: ./build-docker.sh [OPTIONS]

set -e

# Default values
IMAGE_NAME="ppt-narrator"
TAG="latest"
DOCKERFILE="Dockerfile"
PUSH=false
PLATFORM="linux/amd64"
BUILD_ARGS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
PPT Narrator Docker Build Script

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -n, --name NAME         Docker image name (default: ppt-narrator)
    -t, --tag TAG           Docker image tag (default: latest)
    -f, --file DOCKERFILE   Dockerfile to use (default: Dockerfile)
    -p, --push              Push image to registry after build
    --platform PLATFORM    Target platform (default: linux/amd64)
    --build-arg ARG=VALUE   Pass build argument to docker build
    --alpine                Use Alpine-based Dockerfile
    --dev                   Use development Dockerfile
    --no-cache              Build without using cache

Examples:
    $0                                          # Basic build
    $0 --tag v1.0.0 --push                    # Build and push with version tag
    $0 --alpine --tag alpine-latest           # Build Alpine version
    $0 --dev --name ppt-narrator-dev          # Build development version
    $0 --build-arg VERSION=1.0.0              # Build with custom build arg

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --build-arg)
            BUILD_ARGS="$BUILD_ARGS --build-arg $2"
            shift 2
            ;;
        --alpine)
            DOCKERFILE="Dockerfile.alpine"
            shift
            ;;
        --dev)
            DOCKERFILE="Dockerfile.dev"
            shift
            ;;
        --no-cache)
            BUILD_ARGS="$BUILD_ARGS --no-cache"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate inputs
if [ ! -f "$DOCKERFILE" ]; then
    log_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    log_error "Docker daemon is not running"
    exit 1
fi

# Build information
log_info "Building Docker image with the following configuration:"
echo "  Image Name: $IMAGE_NAME"
echo "  Tag: $TAG"
echo "  Dockerfile: $DOCKERFILE"
echo "  Platform: $PLATFORM"
echo "  Push: $PUSH"
if [ -n "$BUILD_ARGS" ]; then
    echo "  Build Args: $BUILD_ARGS"
fi

# Confirm build
read -p "Continue with build? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Build cancelled"
    exit 0
fi

# Start build
log_info "Starting Docker build..."

# Build command
BUILD_CMD="docker build \
    --platform $PLATFORM \
    -f $DOCKERFILE \
    -t $IMAGE_NAME:$TAG \
    $BUILD_ARGS \
    ."

log_info "Running: $BUILD_CMD"

# Execute build
if eval $BUILD_CMD; then
    log_success "Docker image built successfully: $IMAGE_NAME:$TAG"
else
    log_error "Docker build failed"
    exit 1
fi

# Show image info
log_info "Image information:"
docker images $IMAGE_NAME:$TAG

# Push if requested
if [ "$PUSH" = true ]; then
    log_info "Pushing image to registry..."
    
    if docker push $IMAGE_NAME:$TAG; then
        log_success "Image pushed successfully: $IMAGE_NAME:$TAG"
    else
        log_error "Failed to push image"
        exit 1
    fi
fi

# Security scan (if available)
if command -v docker &> /dev/null && docker --help | grep -q "scan"; then
    log_info "Running security scan..."
    docker scan $IMAGE_NAME:$TAG || log_warning "Security scan failed or not available"
fi

# Final summary
log_success "Build completed successfully!"
echo
echo "Image: $IMAGE_NAME:$TAG"
echo "Size: $(docker images $IMAGE_NAME:$TAG --format "table {{.Size}}" | tail -n 1)"
echo
echo "To run the container:"
echo "  docker run -p 8080:8080 --env-file .env $IMAGE_NAME:$TAG"
echo
echo "To run with Docker Compose:"
echo "  docker-compose up"
