@echo off
setlocal enabledelayedexpansion

REM PPT Narrator 开发环境启动脚本
REM 用于启动开发环境所需的基础服务

echo ==========================================
echo     PPT Narrator 开发环境启动
echo ==========================================
echo.

REM 检查Docker是否安装
echo [INFO] 检查系统依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 系统依赖检查通过

REM 启动基础服务 (数据库、Redis、原PPT生成器)
echo [INFO] 启动基础服务...
docker-compose up -d postgres redis ppt-narrator

if errorlevel 1 (
    echo [ERROR] 基础服务启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 基础服务启动完成

REM 等待服务就绪
echo [INFO] 等待服务就绪...
timeout /t 15 /nobreak >nul

echo [SUCCESS] 开发环境基础服务已启动！
echo.
echo ==========================================
echo          开发环境信息
echo ==========================================
echo PostgreSQL:   localhost:5432
echo Redis:        localhost:6379
echo PPT生成器:    localhost:8080
echo ==========================================
echo.
echo 现在可以启动开发服务:
echo.
echo 1. 业务API (Go):
echo    cd web/business-api
echo    go run cmd/main.go
echo.
echo 2. 用户前端 (React):
echo    cd web/user-frontend
echo    npm start
echo.
echo 3. 管理员后台 (React):
echo    cd web/admin-dashboard
echo    npm start
echo.
echo 停止基础服务: docker-compose down
echo.

pause
