package tests

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"
)

// TestSubtitleIntegration tests the complete subtitle generation and video embedding workflow
func TestSubtitleIntegration(t *testing.T) {
	// Skip if FFmpeg is not available
	if !isFFmpegAvailable() {
		t.Skip("FFmpeg not available, skipping integration test")
	}

	// Create temporary directory for test
	tempDir, err := os.MkdirTemp("", "subtitle_integration_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Setup test configuration
	cfg := &config.Config{
		TempDir:                 tempDir,
		VideoDir:                tempDir,
		FFmpegPath:              "ffmpeg",
		SubtitleEnabled:         true,
		SubtitleFontSize:        24,
		SubtitleFontColor:       "#FFFFFF",
		SubtitleBackgroundColor: "",
		SubtitlePosition:        "bottom",
		SubtitleFontFamily:      "Arial",
		SubtitleOutline:         2,
		SubtitleShadow:          true,
	}

	// Create mock database store
	store := &MockDatabaseStoreService{
		projects: make(map[string]*models.PPTProject),
		slides:   make(map[string][]*models.PPTSlide),
	}

	// Create test project
	project := &models.PPTProject{
		ID:       "test-project-subtitle",
		Filename: "test_subtitle.pptx",
		Status:   "audio_ready",
	}
	store.projects[project.ID] = project

	// Create test slides with narration text
	slides := []*models.PPTSlide{
		{
			ID:             "slide1",
			ProjectID:      project.ID,
			SlideNumber:    1,
			ScreenshotPath: createTestImage(t, tempDir, "slide1.png"),
			NarrationText:  "欢迎来到第一张幻灯片。[停顿1秒]这里包含重要信息。",
			NarrationAudio: createTestAudio(t, tempDir, "slide1.mp3", 5.0),
			Duration:       5.0,
		},
		{
			ID:             "slide2",
			ProjectID:      project.ID,
			SlideNumber:    2,
			ScreenshotPath: createTestImage(t, tempDir, "slide2.png"),
			NarrationText:  "这是第二张幻灯片。[停顿0.5秒]包含更多详细内容！",
			NarrationAudio: createTestAudio(t, tempDir, "slide2.mp3", 4.0),
			Duration:       4.0,
		},
	}
	store.slides[project.ID] = slides

	// Initialize services
	videoService := services.NewVideoService(cfg, store)

	t.Run("Video generation without subtitles", func(t *testing.T) {
		// Test video generation without subtitles
		request := &models.VideoGenerationRequest{
			ProjectID:       project.ID,
			OutputFormat:    "mp4",
			Quality:         "high",
			FPS:             1,
			Resolution:      "1280x720",
			EnableSubtitles: false,
		}

		outputPath := filepath.Join(tempDir, "video_no_subtitles.mp4")
		err := videoService.GenerateVideo(project.ID, request)
		if err != nil {
			t.Fatalf("Video generation without subtitles failed: %v", err)
		}

		// Verify video file was created
		if !fileExists(outputPath) {
			// Check if video was created in project video path
			if project.VideoPath != "" && fileExists(project.VideoPath) {
				outputPath = project.VideoPath
			} else {
				t.Errorf("Video file was not created")
				return
			}
		}

		// Verify video file is not empty
		fileInfo, err := os.Stat(outputPath)
		if err != nil {
			t.Errorf("Failed to stat video file: %v", err)
		} else if fileInfo.Size() == 0 {
			t.Errorf("Video file is empty")
		}
	})

	t.Run("Video generation with subtitles", func(t *testing.T) {
		// Test video generation with subtitles
		request := &models.VideoGenerationRequest{
			ProjectID:       project.ID,
			OutputFormat:    "mp4",
			Quality:         "high",
			FPS:             1,
			Resolution:      "1280x720",
			EnableSubtitles: true,
			SubtitleStyle: &models.SubtitleStyle{
				FontSize:   28,
				FontColor:  "#FFFF00", // Yellow
				FontFamily: "Arial",
				Outline:    3,
				Shadow:     true,
			},
		}

		outputPath := filepath.Join(tempDir, "video_with_subtitles.mp4")
		err := videoService.GenerateVideo(project.ID, request)
		if err != nil {
			t.Fatalf("Video generation with subtitles failed: %v", err)
		}

		// Verify video file was created
		if !fileExists(outputPath) {
			// Check if video was created in project video path
			if project.VideoPath != "" && fileExists(project.VideoPath) {
				outputPath = project.VideoPath
			} else {
				t.Errorf("Video file with subtitles was not created")
				return
			}
		}

		// Verify video file is not empty
		fileInfo, err := os.Stat(outputPath)
		if err != nil {
			t.Errorf("Failed to stat video file with subtitles: %v", err)
		} else if fileInfo.Size() == 0 {
			t.Errorf("Video file with subtitles is empty")
		}

		// Verify subtitle file was generated during the process
		subtitlePath := filepath.Join(tempDir, "video_"+project.ID, "subtitles.srt")
		if fileExists(subtitlePath) {
			// Read and verify subtitle content
			content, err := os.ReadFile(subtitlePath)
			if err != nil {
				t.Errorf("Failed to read subtitle file: %v", err)
			} else {
				contentStr := string(content)
				if !strings.Contains(contentStr, "欢迎来到第一张幻灯片") {
					t.Errorf("Subtitle file should contain slide text")
				}
				if !strings.Contains(contentStr, "-->") {
					t.Errorf("Subtitle file should contain SRT time format")
				}
			}
		}
	})

	t.Run("Subtitle style customization", func(t *testing.T) {
		// Test different subtitle styles
		customStyles := []*models.SubtitleStyle{
			{
				FontSize:        32,
				FontColor:       "#FF0000", // Red
				FontFamily:      "Arial",
				Outline:         1,
				Shadow:          false,
				Position:        "top",
				BackgroundColor: "#000000", // Black background
			},
			{
				FontSize:   20,
				FontColor:  "#00FF00", // Green
				FontFamily: "Arial",
				Outline:    4,
				Shadow:     true,
				Position:   "center",
			},
		}

		for i, style := range customStyles {
			request := &models.VideoGenerationRequest{
				ProjectID:       project.ID,
				OutputFormat:    "mp4",
				Quality:         "medium",
				FPS:             1,
				Resolution:      "1280x720",
				EnableSubtitles: true,
				SubtitleStyle:   style,
			}

			outputPath := filepath.Join(tempDir, "video_custom_style_"+string(rune('1'+i))+".mp4")
			err := videoService.GenerateVideo(project.ID, request)
			if err != nil {
				t.Errorf("Video generation with custom style %d failed: %v", i+1, err)
				continue
			}

			// Verify video file was created
			if !fileExists(outputPath) && (project.VideoPath == "" || !fileExists(project.VideoPath)) {
				t.Errorf("Video file with custom style %d was not created", i+1)
			}
		}
	})
}

// MockDatabaseStoreService for integration testing
type MockDatabaseStoreService struct {
	projects map[string]*models.PPTProject
	slides   map[string][]*models.PPTSlide
}

func (m *MockDatabaseStoreService) GetProject(projectID string) (*models.PPTProject, error) {
	if project, exists := m.projects[projectID]; exists {
		return project, nil
	}
	return nil, nil
}

func (m *MockDatabaseStoreService) GetSlidesByProject(projectID string) ([]*models.PPTSlide, error) {
	if slides, exists := m.slides[projectID]; exists {
		return slides, nil
	}
	return []*models.PPTSlide{}, nil
}

func (m *MockDatabaseStoreService) UpdateProject(project *models.PPTProject) error {
	m.projects[project.ID] = project
	return nil
}

// Helper functions for creating test files
func createTestImage(t *testing.T, dir, filename string) string {
	// Create a simple test image file (placeholder)
	imagePath := filepath.Join(dir, filename)
	
	// Create a minimal PNG file (1x1 pixel black image)
	pngData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
		0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
		0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
		0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
		0x54, 0x08, 0xD7, 0x63, 0x00, 0x00, 0x00, 0x02, // compressed image data
		0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, // checksum
		0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, // IEND chunk
		0x60, 0x82,
	}
	
	err := os.WriteFile(imagePath, pngData, 0644)
	if err != nil {
		t.Fatalf("Failed to create test image: %v", err)
	}
	
	return imagePath
}

func createTestAudio(t *testing.T, dir, filename string, duration float64) string {
	// Create a placeholder audio file
	audioPath := filepath.Join(dir, filename)
	
	// Create a simple placeholder file (not a real audio file)
	// In a real test, you might want to create a proper audio file
	content := []byte("MOCK_AUDIO_FILE_DURATION_" + string(rune(int(duration))))
	err := os.WriteFile(audioPath, content, 0644)
	if err != nil {
		t.Fatalf("Failed to create test audio: %v", err)
	}
	
	return audioPath
}

func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

func isFFmpegAvailable() bool {
	// Check if FFmpeg is available in the system
	_, err := os.Stat("ffmpeg")
	if err == nil {
		return true
	}
	
	// Try to run ffmpeg command
	// This is a simple check - in production you might want a more robust check
	return false // For now, assume FFmpeg is not available in test environment
}
