package services

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/utils"
	"strings"
	"time"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
)

// AIService handles AI-related operations for narration generation
type AIService struct {
	config        *config.Config
	openaiClient  *openai.Client
	minimaxClient *MinimaxClient
	store         *DatabaseStoreService
	retryManager  *utils.RetryManager
}

// NewAIService creates a new AI service
func NewAIService(cfg *config.Config, store *DatabaseStoreService) *AIService {
	var openaiClient *openai.Client
	var minimaxClient *MinimaxClient

	// Initialize OpenAI client if configured
	if cfg.AIProvider == "openai" || cfg.OpenAIAPIKey != "" {
		clientConfig := openai.DefaultConfig(cfg.OpenAIAPIKey)
		if cfg.OpenAIBaseURL != "" {
			clientConfig.BaseURL = cfg.OpenAIBaseURL
		}
		openaiClient = openai.NewClientWithConfig(clientConfig)
	}

	// Initialize MiniMax client if configured
	if cfg.AIProvider == "minimax" || cfg.MinimaxAPIKey != "" {
		minimaxClient = NewMinimaxClient(cfg)
	}

	// Create retry configuration from config
	retryConfig := &utils.RetryConfig{
		MaxRetries:    cfg.AIMaxRetries,
		BaseDelay:     time.Duration(cfg.AIBaseDelay) * time.Second,
		MaxDelay:      time.Duration(cfg.AIMaxDelay) * time.Second,
		BackoffFactor: cfg.AIBackoffFactor,
		JitterEnabled: true,
		RetryableErrors: []string{
			"rate limit", "too many requests", "timeout", "connection",
			"network", "temporary", "503", "502", "504", "429",
			"openai", "minimax", "1002", "context_length_exceeded",
		},
	}

	return &AIService{
		config:        cfg,
		openaiClient:  openaiClient,
		minimaxClient: minimaxClient,
		store:         store,
		retryManager:  utils.NewRetryManager(retryConfig),
	}
}

// GenerateNarration generates narration script for all slides in a project
func (s *AIService) GenerateNarration(projectID string, userRequirements string) error {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Check if project is in a valid state for narration generation
	validStates := []string{"screenshots_ready", "completed", "narration_failed", "audio_failed", "video_failed"}
	isValidState := false
	for _, state := range validStates {
		if project.Status == state {
			isValidState = true
			break
		}
	}

	if !isValidState {
		// Check if we have screenshots even if status is not ideal
		slides, err := s.store.GetSlidesByProject(projectID)
		if err != nil {
			return fmt.Errorf("failed to get slides: %w", err)
		}

		hasScreenshots := false
		for _, slide := range slides {
			if slide.ScreenshotPath != "" {
				hasScreenshots = true
				break
			}
		}

		if !hasScreenshots {
			return fmt.Errorf("no screenshots found. Please generate screenshots first")
		}

		fmt.Printf("Warning: Project status '%s' is not ideal for narration generation, but proceeding due to available screenshots\n", project.Status)
	}

	// Update project status
	project.Status = "generating_narration"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}

	// Create job for tracking
	job, err := s.store.CreateJob(projectID, "narration")
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	job.Status = "running"
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Get slides
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	if len(slides) == 0 {
		return fmt.Errorf("no slides found for project %s", projectID)
	}

	// Store user requirements in memory
	if userRequirements != "" {
		_, err = s.store.AddMemory(projectID, "user_requirements", userRequirements, "requirement")
		if err != nil {
			return fmt.Errorf("failed to store user requirements: %w", err)
		}
	}

	// Generate narration for each slide with resume capability
	var fullNarration strings.Builder
	totalSlides := len(slides)

	// Find the starting point for resume
	startSlideIndex := s.findResumePoint(slides)
	if startSlideIndex > 0 {
		fmt.Printf("Resuming narration generation from slide %d\n", startSlideIndex+1)

		// Rebuild full narration from existing slides
		for i := 0; i < startSlideIndex; i++ {
			if slides[i].NarrationText != "" {
				fullNarration.WriteString(fmt.Sprintf("## 第%d页\n\n%s\n\n", slides[i].SlideNumber, slides[i].NarrationText))
			}
		}
	}

	for i := startSlideIndex; i < len(slides); i++ {
		slide := slides[i]

		// Update progress
		job.Progress = (i * 100) / totalSlides
		s.store.UpdateJob(job)

		// Skip if narration already exists (resume case)
		if slide.NarrationText != "" {
			fmt.Printf("Slide %d already has narration, skipping\n", slide.SlideNumber)
			fullNarration.WriteString(fmt.Sprintf("## 第%d页\n\n%s\n\n", slide.SlideNumber, slide.NarrationText))
			continue
		}

		fmt.Printf("Generating narration for slide %d/%d\n", i+1, totalSlides)

		// Generate narration for this slide
		narration, err := s.generateSlideNarration(projectID, slide, i+1, totalSlides)
		if err != nil {
			job.Status = "failed"
			job.ErrorMsg = err.Error()
			s.store.UpdateJob(job)
			// Don't set project to "failed", use "narration_failed" to preserve screenshot state
			project.Status = "narration_failed"
			project.ErrorMessage = fmt.Sprintf("Failed at slide %d: %s", slide.SlideNumber, err.Error())
			s.store.UpdateProject(project)
			return fmt.Errorf("failed to generate narration for slide %d: %w", slide.SlideNumber, err)
		}

		// Update slide with narration
		slide.NarrationText = narration
		if err := s.store.UpdateSlide(slide); err != nil {
			return fmt.Errorf("failed to update slide with narration: %w", err)
		}

		// Add to full narration
		fullNarration.WriteString(fmt.Sprintf("## 第%d页\n\n%s\n\n", slide.SlideNumber, narration))

		// Store slide-specific memory if needed
		s.storeSlideContext(projectID, slide, narration)

		fmt.Printf("✅ Completed slide %d/%d\n", i+1, totalSlides)
	}

	// Update project with full narration
	project.NarrationScript = fullNarration.String()
	project.Status = "narration_ready"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project with narration: %w", err)
	}

	// Complete job
	job.Status = "completed"
	job.Progress = 100
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to complete job: %w", err)
	}

	return nil
}

// generateSlideNarration generates narration for a single slide
func (s *AIService) generateSlideNarration(projectID string, slide *models.PPTSlide, currentSlide, totalSlides int) (string, error) {
	// Build context from memories
	context, err := s.buildContextFromMemories(projectID)
	if err != nil {
		return "", fmt.Errorf("failed to build context: %w", err)
	}

	// Build prompt
	prompt := s.buildNarrationPrompt(slide, currentSlide, totalSlides, context)

	// 获取前三页的讲稿作为上下文
	previousNarrations := s.getPreviousNarrations(projectID, currentSlide, 3)

	// Generate narration based on configured AI provider
	var narration string
	switch s.config.AIProvider {
	case "minimax":
		// 如果有截图，使用MiniMax视觉AI；否则使用文本AI
		if slide.ScreenshotPath != "" {
			narration, err = s.generateWithMinimaxVisionWithContext(prompt, slide.ScreenshotPath, projectID, previousNarrations)
		} else {
			narration, err = s.generateWithMinimaxWithContext(prompt, projectID, previousNarrations)
		}
	case "openai":
		fallthrough
	default:
		// 如果有截图，使用视觉AI；否则使用文本AI
		if slide.ScreenshotPath != "" {
			narration, err = s.generateWithOpenAIVisionWithTools(prompt, slide.ScreenshotPath, projectID, previousNarrations)
		} else {
			narration, err = s.generateWithOpenAIWithTools(prompt, projectID, previousNarrations)
		}
	}

	if err != nil {
		return "", fmt.Errorf("failed to generate narration: %w", err)
	}

	narration = strings.TrimSpace(narration)

	// Store AI's insights as memory for future slides
	s.storeAIInsights(projectID, slide, narration)

	return narration, nil
}

// generateWithOpenAI generates narration using OpenAI API with retry mechanism
func (s *AIService) generateWithOpenAI(prompt string) (string, error) {
	if s.openaiClient == nil {
		return "", fmt.Errorf("OpenAI client not initialized")
	}

	return s.openaiWithRetry(func() (string, error) {
		resp, err := s.openaiClient.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model: s.config.OpenAIModel,
				Messages: []openai.ChatCompletionMessage{
					{
						Role:    openai.ChatMessageRoleSystem,
						Content: s.config.SystemPrompt,
					},
					{
						Role:    openai.ChatMessageRoleUser,
						Content: prompt,
					},
				},
				MaxTokens:   s.config.MaxTokens,
				Temperature: float32(s.config.Temperature),
			},
		)

		if err != nil {
			return "", fmt.Errorf("failed to call OpenAI API: %w", err)
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("no response from OpenAI API")
		}

		return resp.Choices[0].Message.Content, nil
	})
}

// generateWithMinimax generates narration using MiniMax API (legacy method)
func (s *AIService) generateWithMinimax(prompt string) (string, error) {
	if s.minimaxClient == nil {
		return "", fmt.Errorf("MiniMax client not initialized")
	}

	// Validate MiniMax configuration
	if err := s.minimaxClient.ValidateConfig(); err != nil {
		return "", fmt.Errorf("MiniMax configuration error: %w", err)
	}

	response, err := s.minimaxClient.ChatCompletion(
		context.Background(),
		s.config.SystemPrompt,
		prompt,
	)

	if err != nil {
		return "", fmt.Errorf("failed to call MiniMax API: %w", err)
	}

	return response, nil
}

// generateWithMinimaxWithContext generates narration using MiniMax API with previous narrations context
func (s *AIService) generateWithMinimaxWithContext(prompt string, projectID string, previousNarrations []string) (string, error) {
	if s.minimaxClient == nil {
		return "", fmt.Errorf("MiniMax client not initialized")
	}

	// Validate MiniMax configuration
	if err := s.minimaxClient.ValidateConfig(); err != nil {
		return "", fmt.Errorf("MiniMax configuration error: %w", err)
	}

	// 构建包含前置讲稿的完整提示词
	fullPrompt := prompt

	// 添加前置讲稿作为上下文
	if len(previousNarrations) > 0 {
		contextContent := "\n\n## 前面页面的讲稿内容\n\n"
		for _, narration := range previousNarrations {
			contextContent += narration + "\n\n"
		}
		contextContent += "请基于以上内容，确保当前页面的讲稿与前面内容自然衔接。\n\n"
		contextContent += "---\n\n"

		// 将上下文添加到提示词前面
		fullPrompt = contextContent + prompt
	}

	response, err := s.minimaxClient.ChatCompletion(
		context.Background(),
		s.config.SystemPrompt,
		fullPrompt,
	)

	if err != nil {
		return "", fmt.Errorf("failed to call MiniMax API: %w", err)
	}

	return response, nil
}

// generateWithMinimaxVisionWithContext generates narration using MiniMax Vision API with previous narrations context
func (s *AIService) generateWithMinimaxVisionWithContext(prompt string, imagePath string, projectID string, previousNarrations []string) (string, error) {
	if s.minimaxClient == nil {
		return "", fmt.Errorf("MiniMax client not initialized")
	}

	// Validate MiniMax configuration
	if err := s.minimaxClient.ValidateConfig(); err != nil {
		return "", fmt.Errorf("MiniMax configuration error: %w", err)
	}

	// 构建包含前置讲稿的完整提示词
	fullPrompt := prompt

	// 添加前置讲稿作为上下文
	if len(previousNarrations) > 0 {
		contextContent := "\n\n## 前面页面的讲稿内容\n\n"
		for _, narration := range previousNarrations {
			contextContent += narration + "\n\n"
		}
		contextContent += "请基于以上内容，确保当前页面的讲稿与前面内容自然衔接。\n\n"
		contextContent += "---\n\n"

		// 将上下文添加到提示词前面
		fullPrompt = contextContent + prompt
	}

	// 添加图像分析指导
	fullPrompt += "\n\n## 图像分析要求\n\n"
	fullPrompt += "请仔细分析提供的PPT截图，重点关注：\n"
	fullPrompt += "- 页面标题和主要文字内容\n"
	fullPrompt += "- 图表、表格、流程图等视觉元素\n"
	fullPrompt += "- 数据趋势和关键信息点\n"
	fullPrompt += "- 页面布局和逻辑结构\n"
	fullPrompt += "基于图像内容生成自然流畅的讲解。\n"

	response, err := s.minimaxClient.ChatCompletionWithImage(
		context.Background(),
		s.config.SystemPrompt,
		fullPrompt,
		imagePath,
	)

	if err != nil {
		return "", fmt.Errorf("failed to call MiniMax Vision API: %w", err)
	}

	return response, nil
}

// openaiWithRetry performs OpenAI API calls with unified retry mechanism
func (s *AIService) openaiWithRetry(apiCall func() (string, error)) (string, error) {
	fmt.Printf("🔄 Executing OpenAI API call with retry mechanism...\n")

	result, retryResult := utils.ExecuteWithResult(s.retryManager, apiCall)
	utils.LogRetryResult("OpenAI API call", retryResult)

	if !retryResult.Success {
		return "", fmt.Errorf("OpenAI API call failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result, nil
}

// isOpenAIRetryableError determines if an OpenAI error is retryable
func (s *AIService) isOpenAIRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// Network-related errors
	if strings.Contains(errStr, "timeout") || strings.Contains(errStr, "connection") ||
	   strings.Contains(errStr, "network") || strings.Contains(errStr, "EOF") {
		return true
	}

	// HTTP status codes that are retryable
	if strings.Contains(errStr, "status code: 429") || // Rate limit
	   strings.Contains(errStr, "status code: 500") || // Internal server error
	   strings.Contains(errStr, "status code: 502") || // Bad gateway
	   strings.Contains(errStr, "status code: 503") || // Service unavailable
	   strings.Contains(errStr, "status code: 504") {  // Gateway timeout
		return true
	}

	// OpenAI specific errors that might be retryable
	if strings.Contains(errStr, "rate limit") || strings.Contains(errStr, "quota") ||
	   strings.Contains(errStr, "overloaded") || strings.Contains(errStr, "server_error") {
		return true
	}

	return false
}

// buildContextFromMemories builds context string from stored memories
func (s *AIService) buildContextFromMemories(projectID string) (string, error) {
	memories, err := s.store.GetMemoriesByProject(projectID)
	if err != nil {
		return "", err
	}

	if len(memories) == 0 {
		return "", nil
	}

	var contextBuilder strings.Builder
	contextBuilder.WriteString("## 上下文信息\n\n")

	for _, memory := range memories {
		contextBuilder.WriteString(fmt.Sprintf("**%s**: %s\n", memory.MemoryKey, memory.MemoryValue))
	}

	return contextBuilder.String(), nil
}

// buildNarrationPrompt builds the prompt for narration generation
func (s *AIService) buildNarrationPrompt(slide *models.PPTSlide, currentSlide, totalSlides int, context string) string {
	var promptBuilder strings.Builder

	// 动态角色设定
	promptBuilder.WriteString("## 角色设定\n\n")
	promptBuilder.WriteString(fmt.Sprintf("你是一位%s，正在为%s讲解一个完整的PPT演示。\n", s.config.NarratorRole, s.config.TargetAudience))
	promptBuilder.WriteString(fmt.Sprintf("你的讲解风格：%s，语调：%s，自然度：%s\n\n", s.config.NarratorStyle, s.config.SpeakingTone, s.config.SpeechNaturalness))

	promptBuilder.WriteString("## 重要理解\n")
	promptBuilder.WriteString("**这是一个连贯的PPT演示，不是独立的课程！**\n")
	promptBuilder.WriteString("- 每张幻灯片都是整体内容的一部分，需要自然衔接\n")
	promptBuilder.WriteString("- 避免每页都用\"大家好\"、\"今天我们来讲\"等开场白\n")
	promptBuilder.WriteString("- 根据页面位置调整语调：开头介绍、中间展开、结尾总结\n")
	promptBuilder.WriteString("- 用承上启下的语言连接前后内容\n\n")

	promptBuilder.WriteString("## 讲解目标\n")
	promptBuilder.WriteString("- 用自然的语言解释复杂概念，避免生硬的过渡语\n")
	promptBuilder.WriteString("- 将新知识与已有知识自然联系\n")
	promptBuilder.WriteString("- 适当引导思考，但不要过度使用套话\n")
	promptBuilder.WriteString("- 保持真实的对话感，就像面对面交流\n\n")

	if context != "" {
		promptBuilder.WriteString("## 前置知识背景\n\n")
		promptBuilder.WriteString(context)
		promptBuilder.WriteString("\n\n")
	}

	promptBuilder.WriteString(fmt.Sprintf("## 当前任务\n\n"))
	promptBuilder.WriteString(fmt.Sprintf("为第%d页（共%d页）PPT生成讲述稿。\n\n", currentSlide, totalSlides))

	// 根据页面位置提供不同的指导
	if currentSlide == 1 {
		promptBuilder.WriteString("**这是第一页**：适合用开场白介绍主题，设定整体基调\n")
	} else if currentSlide == totalSlides {
		promptBuilder.WriteString("**这是最后一页**：适合总结要点，给出结论或展望\n")
	} else {
		promptBuilder.WriteString("**这是中间页面**：承接上文，展开当前要点，为下文做铺垫\n")
	}
	promptBuilder.WriteString("\n")

	// 添加截图分析指导
	if slide.ScreenshotPath != "" {
		promptBuilder.WriteString("## 内容分析重点\n\n")
		promptBuilder.WriteString("**重要**: 请仔细分析PPT截图，重点关注：\n")
		promptBuilder.WriteString("- **核心知识点**: 这页要传达的主要概念是什么？\n")
		promptBuilder.WriteString("- **文字信息**: 标题、正文、列表、公式等文字内容\n")
		promptBuilder.WriteString("- **图表数据**: 图表、表格、流程图传达的具体信息和趋势\n")
		promptBuilder.WriteString("- **逻辑关系**: 各元素之间的因果关系、层次结构\n")
		promptBuilder.WriteString("- **实际应用**: 这些知识在现实中如何应用\n\n")
		promptBuilder.WriteString("**避免**: 不要过度描述颜色、字体、装饰等设计元素，除非它们对理解内容有帮助。\n\n")
	}

	if slide.Title != "" {
		promptBuilder.WriteString(fmt.Sprintf("**页面标题**: %s\n", slide.Title))
	}
	if slide.Content != "" {
		promptBuilder.WriteString(fmt.Sprintf("**提取文字**: %s\n", slide.Content))
	}

	promptBuilder.WriteString("\n## 讲述要求\n\n")
	promptBuilder.WriteString("1. **自然衔接**: 根据前面内容自然过渡，不要每页都重新开始\n")
	promptBuilder.WriteString("   - 第一页：自然引入主题，如\"咱们来看看SQL注入这个话题\"\n")
	promptBuilder.WriteString("   - 中间页：承接上文，如\"刚才说到了基础概念，现在我们深入一点\"\n")
	promptBuilder.WriteString("   - 最后页：自然总结，如\"通过这些内容，我们可以看出...\"\n")
	promptBuilder.WriteString("2. **极度口语化**: 大量使用\"嗯\"、\"呃\"、\"那个\"、\"这样吧\"、\"怎么说呢\"等口语词\n")
	promptBuilder.WriteString("3. **多样连接词**: 每次使用不同的连接词，避免重复\n")
	promptBuilder.WriteString("   - 转折: \"不过呢\"→\"但是话说回来\"→\"换个角度看\"→\"当然了\"\n")
	promptBuilder.WriteString("   - 递进: \"而且啊\"→\"另外呢\"→\"还有就是\"→\"更重要的是\"\n")
	promptBuilder.WriteString("   - 解释: \"也就是说\"→\"换句话说\"→\"简单来讲\"→\"打个比方\"\n")
	promptBuilder.WriteString("4. **停顿标记**: 必须在适当位置插入 [停顿X秒] 标记（使用较短停顿保持节奏）\n")
	promptBuilder.WriteString("   - 重要概念后: [停顿1秒]\n")
	promptBuilder.WriteString("   - 转折前: [停顿0.5秒]\n")
	promptBuilder.WriteString("   - 举例前: [停顿0.5秒]\n")
	promptBuilder.WriteString("   - 强调时: [停顿0.8秒]\n")
	promptBuilder.WriteString("   - 提问后: [停顿1.2秒]\n")
	promptBuilder.WriteString("5. **自然节奏**: 让讲解有起伏，像真人说话的节奏\n")
	promptBuilder.WriteString("6. **聚焦内容**: 重点讲解知识点，不描述页面设计\n")
	promptBuilder.WriteString("7. **时长控制**: 每页1-2分钟的讲述内容（约200-400字）\n")

	if currentSlide == 1 {
		promptBuilder.WriteString("9. **开场风格**: 这是开篇，用自然的方式引入主题，避免\"欢迎来到\"等套话\n")
	} else if currentSlide == totalSlides {
		promptBuilder.WriteString("9. **结尾风格**: 这是结尾，自然总结要点，避免\"今天我们学习了\"等套话\n")
	} else {
		promptBuilder.WriteString("9. **中间衔接**: 与前面内容自然过渡，不要刻意提及页码或顺序\n")
	}

	promptBuilder.WriteString("\n## 工具使用\n\n")
	promptBuilder.WriteString("你可以使用以下工具来增强讲解效果：\n")
	promptBuilder.WriteString("- **add_memory**: 当遇到重要概念、公式、定义时，将其添加到记忆中供后续页面参考\n")
	promptBuilder.WriteString("- **query_memory**: 查询之前记忆的相关概念，建立知识点之间的联系\n\n")
	promptBuilder.WriteString("**使用建议**：\n")
	promptBuilder.WriteString("- 遇到核心概念时，使用add_memory保存\n")
	promptBuilder.WriteString("- 讲解新内容前，使用query_memory查找相关的已学概念\n")
	promptBuilder.WriteString("- 这样可以帮助学生建立完整的知识体系\n\n")

	promptBuilder.WriteString("## 🎯 表达要求\n\n")
	promptBuilder.WriteString("**核心原则**: 用自然、生动、多样化的方式讲解，避免任何机械化的教学模式\n\n")
	promptBuilder.WriteString("**表达风格**:\n")
	promptBuilder.WriteString("- 像与朋友对话一样自然流畅\n")
	promptBuilder.WriteString("- 根据内容调整情感色彩（好奇、惊喜、思考等）\n")
	promptBuilder.WriteString("- 句式长短搭配，有节奏感\n")
	promptBuilder.WriteString("- 适当使用反问、感叹、假设等多种句式\n\n")
	promptBuilder.WriteString("**绝对避免**:\n")
	promptBuilder.WriteString("- 机械化教学用词：\"重要\"、\"关键\"、\"注意\"、\"掌握\"、\"学习\"\n")
	promptBuilder.WriteString("- 固定开场白：\"大家好\"、\"同学们\"、\"今天我们\"、\"现在让我们\"\n")
	promptBuilder.WriteString("- 序列化表达：\"首先\"、\"其次\"、\"然后\"、\"接下来\"、\"最后\"\n")
	promptBuilder.WriteString("- 重复的句式结构和连接词\n")
	promptBuilder.WriteString("- 同一段中相同词汇超过2次使用\n\n")

	promptBuilder.WriteString("## ✨ 推荐表达方式\n\n")
	promptBuilder.WriteString("**引入话题**:\n")
	promptBuilder.WriteString("- \"想象一下...\"、\"有没有想过...\"、\"这让我想到...\"、\"换个角度思考...\"\n\n")
	promptBuilder.WriteString("**解释概念**:\n")
	promptBuilder.WriteString("- \"简单来说...\"、\"用大白话讲...\"、\"通俗点说...\"、\"换个说法...\"\n\n")
	promptBuilder.WriteString("**举例说明**:\n")
	promptBuilder.WriteString("- \"比如说...\"、\"拿...来说...\"、\"就好比...\"、\"类似于...\"\n\n")
	promptBuilder.WriteString("**强调重点**:\n")
	promptBuilder.WriteString("- \"值得注意的是...\"、\"特别有意思的是...\"、\"令人惊讶的是...\"、\"不得不提的是...\"\n\n")
	promptBuilder.WriteString("**转换话题**:\n")
	promptBuilder.WriteString("- \"话说回来...\"、\"另一方面...\"、\"与此同时...\"、\"顺便提一下...\"\n\n")
	promptBuilder.WriteString("**情感表达示例**:\n")
	promptBuilder.WriteString("- \"这个现象挺有意思的 [停顿1秒]，你有没有发现...\"\n")
	promptBuilder.WriteString("- \"等等，这里有个细节值得琢磨一下 [停顿1.5秒]\"\n")
	promptBuilder.WriteString("- \"说到这儿，我想起一个很形象的比喻...\"\n")
	promptBuilder.WriteString("- \"你可能会疑惑 [停顿1秒]，为什么会出现这种情况呢？\"\n\n")
	promptBuilder.WriteString("## 🎨 个性化讲解指导\n\n")

	if currentSlide == 1 {
		promptBuilder.WriteString("**开场建议**: 用自然的方式引入主题，可以从听众的角度、实际应用或有趣现象开始\n\n")
	} else if currentSlide == totalSlides {
		promptBuilder.WriteString("**结尾建议**: 自然地总结要点或给出展望，避免生硬的\"总结\"模式\n\n")
	} else {
		promptBuilder.WriteString("**承接建议**: 自然地从前面的内容过渡到当前话题，保持逻辑连贯性\n\n")
	}

	promptBuilder.WriteString("**表达多样化技巧**:\n")
	promptBuilder.WriteString("- 根据内容性质调整语调（严肃、轻松、好奇、惊喜等）\n")
	promptBuilder.WriteString("- 使用不同的句式结构（疑问、感叹、假设、比喻等）\n")
	promptBuilder.WriteString("- 适当加入个人思考和见解\n")
	promptBuilder.WriteString("- 用生活化的例子解释抽象概念\n\n")

	promptBuilder.WriteString("**质量要求**:\n")
	promptBuilder.WriteString("- 每句话都要有独特的表达方式\n")
	promptBuilder.WriteString("- 避免任何形式的重复和套路\n")
	promptBuilder.WriteString("- 保持自然的对话感和真实的情感\n")
	promptBuilder.WriteString("- 让听众感受到真诚的分享而非机械的灌输\n")
	promptBuilder.WriteString("- **不要限制讲稿长度**：根据内容重要性和复杂程度自由发挥\n")
	promptBuilder.WriteString("- **深度优于简洁**：重要概念可以详细阐述，多角度分析\n\n")

	promptBuilder.WriteString("## 🎯 最终输出要求\n\n")
	promptBuilder.WriteString("**核心目标**: 生成自然、生动、多样化的讲解内容，让听众感受到真诚的分享\n\n")
	promptBuilder.WriteString("**质量标准**:\n")
	promptBuilder.WriteString("1. **表达自然**: 像朋友间对话一样流畅自然\n")
	promptBuilder.WriteString("2. **内容丰富**: 深入浅出地讲解核心概念，不怕内容多\n")
	promptBuilder.WriteString("3. **情感真实**: 根据内容自然流露相应情感\n")
	promptBuilder.WriteString("4. **逻辑清晰**: 思路连贯，过渡自然\n")
	promptBuilder.WriteString("5. **个性鲜明**: 有独特的讲解风格和视角\n")
	promptBuilder.WriteString("6. **充分展开**: 重要内容可以详细讲解，给听众充分的理解时间\n\n")
	promptBuilder.WriteString("**长度指导**: \n")
	promptBuilder.WriteString("- 不要自我限制讲稿长度，根据内容重要性自由发挥\n")
	promptBuilder.WriteString("- 简单内容可以简洁明了，复杂内容应该详细阐述\n")
	promptBuilder.WriteString("- 重要概念可以从多个角度分析，举多个例子\n")
	promptBuilder.WriteString("- 让听众充分理解比简洁更重要\n\n")
	promptBuilder.WriteString("**输出格式**: 直接输出讲稿内容，包含适当的停顿标记 [停顿X秒]\n\n")
	promptBuilder.WriteString("**最终检验**: 讲稿应该听起来像一个有经验的讲师在真诚地分享知识，内容充实而不匆忙！")

	return promptBuilder.String()
}

// storeSlideContext stores slide-specific context for future reference
func (s *AIService) storeSlideContext(projectID string, slide *models.PPTSlide, narration string) {
	// Store key themes or topics from this slide
	if slide.Title != "" {
		key := fmt.Sprintf("slide_%d_title", slide.SlideNumber)
		cleanTitle := utils.CleanUTF8String(slide.Title)
		s.store.AddMemory(projectID, key, cleanTitle, "context")
	}

	// Store a summary of the narration for context
	if len(narration) > 200 {
		summary := narration[:200] + "..."
		key := fmt.Sprintf("slide_%d_summary", slide.SlideNumber)
		cleanSummary := utils.CleanUTF8String(summary)
		s.store.AddMemory(projectID, key, cleanSummary, "context")
	}
}

// storeAIInsights stores AI-generated insights for future use
func (s *AIService) storeAIInsights(projectID string, slide *models.PPTSlide, narration string) {
	// This could be enhanced to extract key insights from the narration
	// For now, we'll store basic information
	
	// Estimate speaking duration (rough calculation: ~150 words per minute)
	wordCount := len(strings.Fields(narration))
	duration := float64(wordCount) / 150.0 * 60.0 // in seconds
	
	slide.Duration = duration
	s.store.UpdateSlide(slide)
}

// AddMemory allows external services to add memory entries
func (s *AIService) AddMemory(projectID, key, value, memoryType string) error {
	_, err := s.store.AddMemory(projectID, key, value, memoryType)
	return err
}

// GetMemories retrieves all memories for a project
func (s *AIService) GetMemories(projectID string) ([]*models.AIMemory, error) {
	return s.store.GetMemoriesByProject(projectID)
}

// UpdateMemory updates an existing memory entry
func (s *AIService) UpdateMemory(projectID, key, newValue string) error {
	memory, err := s.store.GetMemory(projectID, key)
	if err != nil {
		// If memory doesn't exist, create it
		_, err = s.store.AddMemory(projectID, key, newValue, "context")
		return err
	}

	memory.MemoryValue = newValue
	memory.UpdatedAt = time.Now()
	return s.store.UpdateMemory(memory)
}

// ClearMemories clears all memories for a project
func (s *AIService) ClearMemories(projectID string) error {
	memories, err := s.store.GetMemoriesByProject(projectID)
	if err != nil {
		return err
	}

	for _, memory := range memories {
		if err := s.store.DeleteMemory(memory.ID); err != nil {
			return fmt.Errorf("failed to delete memory %s: %w", memory.ID, err)
		}
	}

	return nil
}

// GetNarrationProgress returns the current narration generation progress
func (s *AIService) GetNarrationProgress(projectID string) (int, error) {
	// Find the narration job
	// This is a simplified implementation - in a real system you might want to store job IDs
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return 0, err
	}

	if project.Status == "narration_ready" || project.Status == "completed" {
		return 100, nil
	}

	if project.Status == "generating_narration" {
		// Count slides with narration
		slides, err := s.store.GetSlidesByProject(projectID)
		if err != nil {
			return 0, err
		}

		if len(slides) == 0 {
			return 0, nil
		}

		completedSlides := 0
		for _, slide := range slides {
			if slide.NarrationText != "" {
				completedSlides++
			}
		}

		return (completedSlides * 100) / len(slides), nil
	}

	return 0, nil
}

// ValidateAIProvider validates the configured AI provider
func (s *AIService) ValidateAIProvider() error {
	switch s.config.AIProvider {
	case "openai":
		if s.openaiClient == nil {
			return fmt.Errorf("OpenAI client not initialized")
		}
		if s.config.OpenAIAPIKey == "" {
			return fmt.Errorf("OpenAI API key is required")
		}
		return nil
	case "minimax":
		if s.minimaxClient == nil {
			return fmt.Errorf("MiniMax client not initialized")
		}
		return s.minimaxClient.ValidateConfig()
	default:
		return fmt.Errorf("unsupported AI provider: %s", s.config.AIProvider)
	}
}

// GetAIProviderInfo returns information about the current AI provider
func (s *AIService) GetAIProviderInfo() map[string]interface{} {
	info := map[string]interface{}{
		"provider": s.config.AIProvider,
	}

	switch s.config.AIProvider {
	case "openai":
		info["model"] = s.config.OpenAIModel
		info["base_url"] = s.config.OpenAIBaseURL
		info["available"] = s.openaiClient != nil && s.config.OpenAIAPIKey != ""
	case "minimax":
		info["model"] = s.config.MinimaxModel
		info["base_url"] = s.config.MinimaxBaseURL
		info["group_id"] = s.config.MinimaxGroupID
		info["available"] = s.minimaxClient != nil && s.config.MinimaxAPIKey != ""
	}

	info["max_tokens"] = s.config.MaxTokens
	info["temperature"] = s.config.Temperature

	return info
}

// generateWithOpenAIVision generates narration using OpenAI Vision API with image input and retry mechanism
func (s *AIService) generateWithOpenAIVision(prompt string, imagePath string) (string, error) {
	if s.openaiClient == nil {
		return "", fmt.Errorf("OpenAI client not initialized")
	}

	// Read and encode the image
	imageData, err := s.encodeImageToBase64(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to encode image: %w", err)
	}

	return s.openaiWithRetry(func() (string, error) {
		// Create messages with both text and image
		messages := []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: s.config.SystemPrompt,
			},
			{
				Role: openai.ChatMessageRoleUser,
				MultiContent: []openai.ChatMessagePart{
					{
						Type: openai.ChatMessagePartTypeText,
						Text: prompt,
					},
					{
						Type: openai.ChatMessagePartTypeImageURL,
						ImageURL: &openai.ChatMessageImageURL{
							URL:    fmt.Sprintf("data:image/png;base64,%s", imageData),
							Detail: openai.ImageURLDetailHigh, // 高质量分析
						},
					},
				},
			},
		}

		resp, err := s.openaiClient.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:       "gpt-4o", // 使用支持视觉的模型
				Messages:    messages,
				MaxTokens:   s.config.MaxTokens,
				Temperature: float32(s.config.Temperature),
			},
		)

		if err != nil {
			return "", fmt.Errorf("OpenAI Vision API error: %w", err)
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("no response from OpenAI Vision API")
		}

		return resp.Choices[0].Message.Content, nil
	})
}

// encodeImageToBase64 reads an image file and encodes it to base64
func (s *AIService) encodeImageToBase64(imagePath string) (string, error) {
	// Open the image file
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	// Read the file content
	imageBytes, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read image file: %w", err)
	}

	// Encode to base64
	encoded := base64.StdEncoding.EncodeToString(imageBytes)
	return encoded, nil
}

// getPreviousNarrations 获取前N页的讲稿作为上下文
func (s *AIService) getPreviousNarrations(projectID string, currentSlide, count int) []string {
	var narrations []string

	// 获取前count页的讲稿
	startSlide := max(1, currentSlide-count)
	for i := startSlide; i < currentSlide; i++ {
		// 从数据库获取第i页的讲稿
		slides, err := s.store.GetSlidesByProject(projectID)
		if err != nil {
			continue
		}

		for _, slide := range slides {
			if slide.SlideNumber == i && slide.NarrationText != "" {
				narrations = append(narrations, fmt.Sprintf("第%d页讲稿：%s", i, slide.NarrationText))
				break
			}
		}
	}

	return narrations
}

// generateWithOpenAIWithTools 使用OpenAI API生成讲稿，支持工具调用
func (s *AIService) generateWithOpenAIWithTools(prompt string, projectID string, previousNarrations []string) (string, error) {
	if s.openaiClient == nil {
		return "", fmt.Errorf("OpenAI client not initialized")
	}

	// 构建消息，包含前三页讲稿
	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleSystem,
			Content: s.config.SystemPrompt,
		},
	}

	// 添加前三页讲稿作为上下文
	if len(previousNarrations) > 0 {
		contextContent := "## 前面页面的讲稿内容\n\n"
		for _, narration := range previousNarrations {
			contextContent += narration + "\n\n"
		}
		contextContent += "请基于以上内容，确保当前页面的讲稿与前面内容自然衔接。\n\n"

		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: contextContent,
		})
	}

	// 添加当前页面的提示词
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: prompt,
	})

	// 定义工具函数
	tools := []openai.Tool{
		{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        "add_memory",
				Description: "添加重要的知识点或概念到记忆中，用于后续页面参考",
				Parameters: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"key": map[string]interface{}{
							"type":        "string",
							"description": "记忆的键名，用于后续查询",
						},
						"content": map[string]interface{}{
							"type":        "string",
							"description": "要记忆的内容",
						},
						"category": map[string]interface{}{
							"type":        "string",
							"description": "记忆的分类，如：concept, example, formula等",
						},
					},
					"required": []string{"key", "content", "category"},
				},
			},
		},
		{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        "query_memory",
				Description: "查询之前记忆的知识点或概念",
				Parameters: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"query": map[string]interface{}{
							"type":        "string",
							"description": "查询的关键词或概念",
						},
					},
					"required": []string{"query"},
				},
			},
		},
	}

	return s.openaiWithRetry(func() (string, error) {
		resp, err := s.openaiClient.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:       s.config.OpenAIModel,
				Messages:    messages,
				Tools:       tools,
				ToolChoice:  "auto",
				MaxTokens:   s.config.MaxTokens,
				Temperature: float32(s.config.Temperature),
			},
		)

		if err != nil {
			return "", fmt.Errorf("OpenAI API error: %w", err)
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("no response from OpenAI API")
		}

		choice := resp.Choices[0]

		// 处理工具调用
		if len(choice.Message.ToolCalls) > 0 {
			return s.handleToolCalls(choice.Message.ToolCalls, projectID, messages, tools)
		}

		return choice.Message.Content, nil
	})
}

// generateWithOpenAIVisionWithTools 使用OpenAI Vision API生成讲稿，支持工具调用
func (s *AIService) generateWithOpenAIVisionWithTools(prompt string, imagePath string, projectID string, previousNarrations []string) (string, error) {
	if s.openaiClient == nil {
		return "", fmt.Errorf("OpenAI client not initialized")
	}

	// Read and encode the image
	imageData, err := s.encodeImageToBase64(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to encode image: %w", err)
	}

	// 构建消息，包含前三页讲稿
	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleSystem,
			Content: s.config.SystemPrompt,
		},
	}

	// 添加前三页讲稿作为上下文
	if len(previousNarrations) > 0 {
		contextContent := "## 前面页面的讲稿内容\n\n"
		for _, narration := range previousNarrations {
			contextContent += narration + "\n\n"
		}
		contextContent += "请基于以上内容，确保当前页面的讲稿与前面内容自然衔接。\n\n"

		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: contextContent,
		})
	}

	// 添加当前页面的提示词和图片
	messages = append(messages, openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleUser,
		MultiContent: []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeText,
				Text: prompt,
			},
			{
				Type: openai.ChatMessagePartTypeImageURL,
				ImageURL: &openai.ChatMessageImageURL{
					URL:    fmt.Sprintf("data:image/png;base64,%s", imageData),
					Detail: openai.ImageURLDetailHigh,
				},
			},
		},
	})

	// 定义工具函数（与文本版本相同）
	tools := []openai.Tool{
		{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        "add_memory",
				Description: "添加重要的知识点或概念到记忆中，用于后续页面参考",
				Parameters: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"key": map[string]interface{}{
							"type":        "string",
							"description": "记忆的键名，用于后续查询",
						},
						"content": map[string]interface{}{
							"type":        "string",
							"description": "要记忆的内容",
						},
						"category": map[string]interface{}{
							"type":        "string",
							"description": "记忆的分类，如：concept, example, formula等",
						},
					},
					"required": []string{"key", "content", "category"},
				},
			},
		},
		{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        "query_memory",
				Description: "查询之前记忆的知识点或概念",
				Parameters: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"query": map[string]interface{}{
							"type":        "string",
							"description": "查询的关键词或概念",
						},
					},
					"required": []string{"query"},
				},
			},
		},
	}

	return s.openaiWithRetry(func() (string, error) {
		resp, err := s.openaiClient.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:       "gpt-4o", // 使用支持视觉的模型
				Messages:    messages,
				Tools:       tools,
				ToolChoice:  "auto",
				MaxTokens:   s.config.MaxTokens,
				Temperature: float32(s.config.Temperature),
			},
		)

		if err != nil {
			return "", fmt.Errorf("OpenAI Vision API error: %w", err)
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("no response from OpenAI Vision API")
		}

		choice := resp.Choices[0]

		// 处理工具调用
		if len(choice.Message.ToolCalls) > 0 {
			return s.handleToolCalls(choice.Message.ToolCalls, projectID, messages, tools)
		}

		return choice.Message.Content, nil
	})
}

// handleToolCalls 处理AI的工具调用
func (s *AIService) handleToolCalls(toolCalls []openai.ToolCall, projectID string, messages []openai.ChatCompletionMessage, tools []openai.Tool) (string, error) {
	// 添加AI的工具调用消息
	messages = append(messages, openai.ChatCompletionMessage{
		Role:      openai.ChatMessageRoleAssistant,
		ToolCalls: toolCalls,
	})

	// 处理每个工具调用
	for _, toolCall := range toolCalls {
		var result string
		var err error

		switch toolCall.Function.Name {
		case "add_memory":
			result, err = s.handleAddMemory(toolCall.Function.Arguments, projectID)
		case "query_memory":
			result, err = s.handleQueryMemory(toolCall.Function.Arguments, projectID)
		default:
			result = fmt.Sprintf("Unknown function: %s", toolCall.Function.Name)
		}

		if err != nil {
			result = fmt.Sprintf("Error: %s", err.Error())
		}

		// 添加工具调用结果
		messages = append(messages, openai.ChatCompletionMessage{
			Role:       openai.ChatMessageRoleTool,
			Content:    result,
			ToolCallID: toolCall.ID,
		})
	}

	// 再次调用API获取最终回复（使用重试机制）
	return s.openaiWithRetry(func() (string, error) {
		resp, err := s.openaiClient.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:       s.config.OpenAIModel,
				Messages:    messages,
				Tools:       tools,
				ToolChoice:  "auto",
				MaxTokens:   s.config.MaxTokens,
				Temperature: float32(s.config.Temperature),
			},
		)

		if err != nil {
			return "", fmt.Errorf("OpenAI API error in tool call follow-up: %w", err)
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("no response from OpenAI API in tool call follow-up")
		}

		return resp.Choices[0].Message.Content, nil
	})
}

// handleAddMemory 处理添加记忆的工具调用
func (s *AIService) handleAddMemory(arguments string, projectID string) (string, error) {
	var params struct {
		Key      string `json:"key"`
		Content  string `json:"content"`
		Category string `json:"category"`
	}

	if err := json.Unmarshal([]byte(arguments), &params); err != nil {
		return "", fmt.Errorf("failed to parse add_memory arguments: %w", err)
	}

	// 添加记忆到存储
	_, err := s.store.AddMemory(projectID, params.Key, params.Content, params.Category)
	if err != nil {
		return "", fmt.Errorf("failed to add memory: %w", err)
	}

	return fmt.Sprintf("已成功添加记忆：%s (%s)", params.Key, params.Category), nil
}

// handleQueryMemory 处理查询记忆的工具调用
func (s *AIService) handleQueryMemory(arguments string, projectID string) (string, error) {
	var params struct {
		Query string `json:"query"`
	}

	if err := json.Unmarshal([]byte(arguments), &params); err != nil {
		return "", fmt.Errorf("failed to parse query_memory arguments: %w", err)
	}

	// 查询记忆
	memories, err := s.store.GetMemoriesByProject(projectID)
	if err != nil {
		return "", fmt.Errorf("failed to query memories: %w", err)
	}

	// 简单的关键词匹配查询
	var results []string
	query := strings.ToLower(params.Query)

	for _, memory := range memories {
		if strings.Contains(strings.ToLower(memory.MemoryKey), query) ||
		   strings.Contains(strings.ToLower(memory.MemoryValue), query) {
			results = append(results, fmt.Sprintf("%s (%s): %s", memory.MemoryKey, memory.MemoryType, memory.MemoryValue))
		}
	}

	if len(results) == 0 {
		return "未找到相关记忆", nil
	}

	return fmt.Sprintf("找到相关记忆：\n%s", strings.Join(results, "\n")), nil
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// findResumePoint finds the starting point for resume based on existing narrations
func (s *AIService) findResumePoint(slides []*models.PPTSlide) int {
	for i, slide := range slides {
		if slide.NarrationText == "" {
			return i // Resume from first slide without narration
		}
	}
	return len(slides) // All slides have narration
}

// GetDetailedNarrationProgress returns detailed progress information for narration generation
func (s *AIService) GetDetailedNarrationProgress(projectID string) (*NarrationProgress, error) {
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get slides: %w", err)
	}

	progress := &NarrationProgress{
		TotalSlides:     len(slides),
		CompletedSlides: 0,
		CurrentSlide:    0,
		CanResume:       false,
		SlideDetails:    make([]SlideProgress, len(slides)),
	}

	for i, slide := range slides {
		hasNarration := slide.NarrationText != ""
		progress.SlideDetails[i] = SlideProgress{
			SlideNumber:   slide.SlideNumber,
			Title:         slide.Title,
			HasNarration:  hasNarration,
			NarrationLength: len(slide.NarrationText),
		}

		if hasNarration {
			progress.CompletedSlides++
		} else if progress.CurrentSlide == 0 {
			progress.CurrentSlide = i + 1
		}
	}

	// Can resume if some slides are completed but not all
	progress.CanResume = progress.CompletedSlides > 0 && progress.CompletedSlides < progress.TotalSlides

	if progress.CurrentSlide == 0 && progress.CompletedSlides == progress.TotalSlides {
		progress.CurrentSlide = progress.TotalSlides
	}

	return progress, nil
}

// NarrationProgress represents the progress of narration generation
type NarrationProgress struct {
	TotalSlides     int             `json:"total_slides"`
	CompletedSlides int             `json:"completed_slides"`
	CurrentSlide    int             `json:"current_slide"`
	CanResume       bool            `json:"can_resume"`
	SlideDetails    []SlideProgress `json:"slide_details"`
}

// SlideProgress represents the progress of a single slide
type SlideProgress struct {
	SlideNumber     int    `json:"slide_number"`
	Title           string `json:"title"`
	HasNarration    bool   `json:"has_narration"`
	NarrationLength int    `json:"narration_length"`
}
