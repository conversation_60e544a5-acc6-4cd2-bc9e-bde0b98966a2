package services

import (
	"context"
	"fmt"
	"log"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/utils"
	"runtime"
	"sync"
	"time"
)

// PipelineStage represents a stage in the processing pipeline
type PipelineStage string

const (
	StageUpload     PipelineStage = "upload"
	StageScreenshot PipelineStage = "screenshot"
	StageNarration  PipelineStage = "narration"
	StageAudio      PipelineStage = "audio"
	StageVideo      PipelineStage = "video"
	StageCompleted  PipelineStage = "completed"
	StageFailed     PipelineStage = "failed"
)

// DetailedError represents detailed error information for debugging
type DetailedError struct {
	Message     string            `json:"message"`
	Stage       PipelineStage     `json:"stage"`
	Timestamp   time.Time         `json:"timestamp"`
	StackTrace  string            `json:"stack_trace,omitempty"`
	Context     map[string]string `json:"context,omitempty"`
	Retryable   bool              `json:"retryable"`
	RetryCount  int               `json:"retry_count"`
	LastRetry   *time.Time        `json:"last_retry,omitempty"`
}

// PipelineProgress represents the progress of the entire pipeline
type PipelineProgress struct {
	ProjectID     string                 `json:"project_id"`
	CurrentStage  PipelineStage          `json:"current_stage"`
	Progress      float64                `json:"progress"`
	Message       string                 `json:"message"`
	StartTime     time.Time              `json:"start_time"`
	LastUpdate    time.Time              `json:"last_update"`
	EstimatedTime *time.Duration         `json:"estimated_time,omitempty"`
	StageDetails  map[string]interface{} `json:"stage_details"`
	Error         string                 `json:"error,omitempty"`
	DetailedError *DetailedError         `json:"detailed_error,omitempty"`
	CanRetry      bool                   `json:"can_retry"`
	RetryFrom     PipelineStage          `json:"retry_from,omitempty"`
}

// PipelineConfig represents configuration for pipeline execution
type PipelineConfig struct {
	UserRequirements       string `json:"user_requirements"`
	EnableSubtitles        bool   `json:"enable_subtitles"`
	SubtitleStyleTemplate  string `json:"subtitle_style_template"`
}

// PipelineService handles the complete PPT processing pipeline
type PipelineService struct {
	cfg            *config.Config
	store          *DatabaseStoreService
	pptProcessor   *PPTProcessorService
	aiService      *AIService
	ttsService     *TTSService
	videoService   *VideoService
	progressMap    map[string]*PipelineProgress
	progressMutex  sync.RWMutex
	retryManager   *utils.RetryManager
	activeJobs     map[string]context.CancelFunc
	jobsMutex      sync.RWMutex
}

// NewPipelineService creates a new pipeline service
func NewPipelineService(
	cfg *config.Config,
	store *DatabaseStoreService,
	pptProcessor *PPTProcessorService,
	aiService *AIService,
	ttsService *TTSService,
	videoService *VideoService,
) *PipelineService {
	// Create global retry configuration
	globalRetryConfig := &utils.RetryConfig{
		MaxRetries:    cfg.GlobalMaxRetries,
		BaseDelay:     5 * time.Second,
		MaxDelay:      300 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []string{
			"timeout", "temporary", "connection", "network",
			"resource busy", "rate limit", "503", "502", "504",
		},
	}

	return &PipelineService{
		cfg:          cfg,
		store:        store,
		pptProcessor: pptProcessor,
		aiService:    aiService,
		ttsService:   ttsService,
		videoService: videoService,
		progressMap:  make(map[string]*PipelineProgress),
		activeJobs:   make(map[string]context.CancelFunc),
		retryManager: utils.NewRetryManager(globalRetryConfig),
	}
}

// StartFullPipeline starts the complete processing pipeline for a project
func (ps *PipelineService) StartFullPipeline(projectID string, config *PipelineConfig) error {
	// Check if project exists
	_, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("project not found: %w", err)
	}

	// Check if pipeline is already running
	ps.progressMutex.Lock()
	if _, exists := ps.progressMap[projectID]; exists {
		ps.progressMutex.Unlock()
		return fmt.Errorf("pipeline already running for project %s", projectID)
	}

	// Initialize progress
	progress := &PipelineProgress{
		ProjectID:    projectID,
		CurrentStage: StageScreenshot,
		Progress:     0.0,
		Message:      "Starting full pipeline processing...",
		StartTime:    time.Now(),
		LastUpdate:   time.Now(),
		StageDetails: make(map[string]interface{}),
		CanRetry:     false,
	}
	ps.progressMap[projectID] = progress
	ps.progressMutex.Unlock()

	// Create cancellable context
	ctx, cancel := context.WithCancel(context.Background())
	ps.jobsMutex.Lock()
	ps.activeJobs[projectID] = cancel
	ps.jobsMutex.Unlock()

	// Start pipeline in goroutine
	go ps.runPipeline(ctx, projectID, config)

	return nil
}

// runPipeline executes the complete pipeline
func (ps *PipelineService) runPipeline(ctx context.Context, projectID string, config *PipelineConfig) {
	defer func() {
		ps.jobsMutex.Lock()
		delete(ps.activeJobs, projectID)
		ps.jobsMutex.Unlock()
	}()

	// Stage 1: Generate Screenshots
	if err := ps.runScreenshotStage(ctx, projectID); err != nil {
		context := map[string]string{
			"stage": "screenshot",
			"project_id": projectID,
		}
		ps.updateProgressWithDetailedError(projectID, StageFailed, 0, err, true, StageScreenshot, context)
		return
	}

	// Check if cancelled
	if ctx.Err() != nil {
		ps.updateProgress(projectID, StageFailed, 0, "Pipeline cancelled", true, StageScreenshot)
		return
	}

	// Stage 2: Generate Narration
	if err := ps.runNarrationStage(ctx, projectID, config.UserRequirements); err != nil {
		context := map[string]string{
			"stage": "narration",
			"project_id": projectID,
			"user_requirements": config.UserRequirements,
		}
		ps.updateProgressWithDetailedError(projectID, StageFailed, 40, err, true, StageNarration, context)
		return
	}

	// Check if cancelled
	if ctx.Err() != nil {
		ps.updateProgress(projectID, StageFailed, 40, "Pipeline cancelled", true, StageNarration)
		return
	}

	// Stage 3: Generate Audio
	if err := ps.runAudioStage(ctx, projectID); err != nil {
		context := map[string]string{
			"stage": "audio",
			"project_id": projectID,
		}
		ps.updateProgressWithDetailedError(projectID, StageFailed, 70, err, true, StageAudio, context)
		return
	}

	// Check if cancelled
	if ctx.Err() != nil {
		ps.updateProgress(projectID, StageFailed, 70, "Pipeline cancelled", true, StageAudio)
		return
	}

	// Stage 4: Generate Video
	if err := ps.runVideoStageWithConfig(ctx, projectID, config); err != nil {
		context := map[string]string{
			"stage": "video",
			"project_id": projectID,
		}
		ps.updateProgressWithDetailedError(projectID, StageFailed, 90, err, true, StageVideo, context)
		return
	}

	// Pipeline completed successfully
	ps.updateProgress(projectID, StageCompleted, 100, "Pipeline completed successfully!", false, "")

	// Update project status
	project, err := ps.store.GetProject(projectID)
	if err == nil {
		project.Status = "completed"
		if err := ps.store.UpdateProject(project); err != nil {
			log.Printf("Warning: Failed to update project status: %v", err)
		}
	}
}

// runScreenshotStage executes the screenshot generation stage
func (ps *PipelineService) runScreenshotStage(ctx context.Context, projectID string) error {
	return ps.runScreenshotStageWithForce(ctx, projectID, false)
}

// runScreenshotStageWithForce executes the screenshot generation stage with optional force regeneration
func (ps *PipelineService) runScreenshotStageWithForce(ctx context.Context, projectID string, forceRegenerate bool) error {
	ps.updateProgress(projectID, StageScreenshot, 5, "Generating screenshots...", false, "")

	// Check if screenshots already exist (only skip if not forcing regeneration)
	slides, err := ps.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	hasScreenshots := false
	for _, slide := range slides {
		if slide.ScreenshotPath != "" {
			hasScreenshots = true
			break
		}
	}

	if hasScreenshots && !forceRegenerate {
		ps.updateProgress(projectID, StageScreenshot, 20, "Screenshots already exist, skipping...", false, "")
		return nil
	}

	// Clear existing screenshots if forcing regeneration
	if forceRegenerate && hasScreenshots {
		log.Printf("🔄 Force regenerating screenshots for project %s", projectID)
		for i := range slides {
			if slides[i].ScreenshotPath != "" {
				slides[i].ScreenshotPath = ""
				if err := ps.store.UpdateSlide(slides[i]); err != nil {
					log.Printf("Warning: Failed to clear screenshot path for slide %d: %v", slides[i].SlideNumber, err)
				}
			}
		}
	}

	// Generate screenshots
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Process PPT with retry mechanism
	fmt.Printf("🔄 Processing PPT with retry mechanism...\n")
	result := ps.retryManager.ExecuteWithContext(ctx, func() error {
		return ps.pptProcessor.ProcessPPTFile(projectID, project.FilePath)
	})

	utils.LogRetryResult("PPT processing", result)

	if !result.Success {
		return fmt.Errorf("failed to process PPT after %d attempts: %w", result.Attempts, result.LastError)
	}

	ps.updateProgress(projectID, StageScreenshot, 20, "Screenshots generated successfully", false, "")
	return nil
}

// runNarrationStage executes the narration generation stage
func (ps *PipelineService) runNarrationStage(ctx context.Context, projectID string, userRequirements string) error {
	return ps.runNarrationStageWithForce(ctx, projectID, userRequirements, false)
}

// runNarrationStageWithForce executes the narration generation stage with optional force regeneration
func (ps *PipelineService) runNarrationStageWithForce(ctx context.Context, projectID string, userRequirements string, forceRegenerate bool) error {
	ps.updateProgress(projectID, StageNarration, 25, "Generating narration...", false, "")

	// Check if narration already exists (only skip if not forcing regeneration)
	slides, err := ps.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	hasNarration := true
	for _, slide := range slides {
		if slide.NarrationText == "" {
			hasNarration = false
			break
		}
	}

	if hasNarration && !forceRegenerate {
		ps.updateProgress(projectID, StageNarration, 40, "Narration already exists, skipping...", false, "")
		return nil
	}

	// Clear existing narration if forcing regeneration
	if forceRegenerate && hasNarration {
		log.Printf("🔄 Force regenerating narration for project %s", projectID)
		for i := range slides {
			if slides[i].NarrationText != "" {
				slides[i].NarrationText = ""
				if err := ps.store.UpdateSlide(slides[i]); err != nil {
					log.Printf("Warning: Failed to clear narration for slide %d: %v", slides[i].SlideNumber, err)
				}
			}
		}
		// Also clear project narration script
		project, err := ps.store.GetProject(projectID)
		if err == nil && project.NarrationScript != "" {
			project.NarrationScript = ""
			if err := ps.store.UpdateProject(project); err != nil {
				log.Printf("Warning: Failed to clear project narration script: %v", err)
			}
		}
	}

	// Generate narration with retry mechanism
	fmt.Printf("🔄 Generating narration with retry mechanism...\n")
	result := ps.retryManager.ExecuteWithContext(ctx, func() error {
		return ps.aiService.GenerateNarration(projectID, userRequirements)
	})

	utils.LogRetryResult("Narration generation", result)

	if !result.Success {
		return fmt.Errorf("failed to generate narration after %d attempts: %w", result.Attempts, result.LastError)
	}

	ps.updateProgress(projectID, StageNarration, 40, "Narration generated successfully", false, "")
	return nil
}

// runAudioStage executes the audio generation stage
func (ps *PipelineService) runAudioStage(ctx context.Context, projectID string) error {
	return ps.runAudioStageWithForce(ctx, projectID, false)
}

// runAudioStageWithForce executes the audio generation stage with optional force regeneration
func (ps *PipelineService) runAudioStageWithForce(ctx context.Context, projectID string, forceRegenerate bool) error {
	ps.updateProgress(projectID, StageAudio, 45, "Generating audio...", false, "")

	// Check if audio already exists (only skip if not forcing regeneration)
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	if project.AudioPath != "" && !forceRegenerate {
		ps.updateProgress(projectID, StageAudio, 70, "Audio already exists, skipping...", false, "")
		return nil
	}

	// Clear existing audio path if forcing regeneration
	if forceRegenerate && project.AudioPath != "" {
		log.Printf("🔄 Force regenerating audio for project %s", projectID)
		project.AudioPath = ""
		if err := ps.store.UpdateProject(project); err != nil {
			log.Printf("Warning: Failed to clear audio path: %v", err)
		}
	}

	// Generate audio with retry mechanism
	fmt.Printf("🔄 Generating audio with retry mechanism...\n")
	result := ps.retryManager.ExecuteWithContext(ctx, func() error {
		return ps.ttsService.GenerateAudio(projectID)
	})

	utils.LogRetryResult("Audio generation", result)

	if !result.Success {
		return fmt.Errorf("failed to generate audio after %d attempts: %w", result.Attempts, result.LastError)
	}

	ps.updateProgress(projectID, StageAudio, 70, "Audio generated successfully", false, "")
	return nil
}

// runVideoStage executes the video generation stage
func (ps *PipelineService) runVideoStage(ctx context.Context, projectID string) error {
	return ps.runVideoStageWithForce(ctx, projectID, false)
}

// runVideoStageWithConfig executes the video generation stage with configuration
func (ps *PipelineService) runVideoStageWithConfig(ctx context.Context, projectID string, config *PipelineConfig) error {
	return ps.runVideoStageWithConfigAndForce(ctx, projectID, config, false)
}

// runVideoStageWithForce executes the video generation stage with optional force regeneration
func (ps *PipelineService) runVideoStageWithForce(ctx context.Context, projectID string, forceRegenerate bool) error {
	ps.updateProgress(projectID, StageVideo, 75, "Generating video...", false, "")

	// Check if video already exists (only skip if not forcing regeneration)
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	if project.VideoPath != "" && !forceRegenerate {
		ps.updateProgress(projectID, StageVideo, 100, "Video already exists, skipping...", false, "")
		return nil
	}

	// Clear existing video path if forcing regeneration
	if forceRegenerate && project.VideoPath != "" {
		log.Printf("🔄 Force regenerating video for project %s", projectID)
		project.VideoPath = ""
		if err := ps.store.UpdateProject(project); err != nil {
			log.Printf("Warning: Failed to clear video path: %v", err)
		}
	}

	// Generate video with retry mechanism
	fmt.Printf("🔄 Generating video with retry mechanism...\n")

	// Create default video generation request
	videoRequest := &models.VideoGenerationRequest{
		ProjectID:    projectID,
		OutputFormat: "mp4",
		Quality:      "high",
		FPS:          1,
		Resolution:   "1920x1080",
	}

	result := ps.retryManager.ExecuteWithContext(ctx, func() error {
		return ps.videoService.GenerateVideo(projectID, videoRequest)
	})

	utils.LogRetryResult("Video generation", result)

	if !result.Success {
		return fmt.Errorf("failed to generate video after %d attempts: %w", result.Attempts, result.LastError)
	}

	ps.updateProgress(projectID, StageVideo, 100, "Video generated successfully", false, "")
	return nil
}

// runVideoStageWithConfigAndForce executes the video generation stage with configuration and optional force regeneration
func (ps *PipelineService) runVideoStageWithConfigAndForce(ctx context.Context, projectID string, config *PipelineConfig, forceRegenerate bool) error {
	ps.updateProgress(projectID, StageVideo, 75, "Generating video...", false, "")

	// Check if video already exists (only skip if not forcing regeneration)
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	if project.VideoPath != "" && !forceRegenerate {
		ps.updateProgress(projectID, StageVideo, 100, "Video already exists, skipping...", false, "")
		return nil
	}

	// Clear existing video path if forcing regeneration
	if forceRegenerate && project.VideoPath != "" {
		log.Printf("🔄 Force regenerating video for project %s", projectID)
		project.VideoPath = ""
		if err := ps.store.UpdateProject(project); err != nil {
			log.Printf("Warning: Failed to clear video path: %v", err)
		}
	}

	// Generate video with retry mechanism
	fmt.Printf("🔄 Generating video with retry mechanism...\n")

	// Create video generation request with subtitle configuration
	videoRequest := &models.VideoGenerationRequest{
		ProjectID:             projectID,
		OutputFormat:          "mp4",
		Quality:               "high",
		FPS:                   1,
		Resolution:            "1920x1080",
		EnableSubtitles:       config.EnableSubtitles,
		SubtitleStyleTemplate: config.SubtitleStyleTemplate,
	}

	result := ps.retryManager.ExecuteWithContext(ctx, func() error {
		return ps.videoService.GenerateVideo(projectID, videoRequest)
	})

	utils.LogRetryResult("Video generation", result)

	if !result.Success {
		return fmt.Errorf("failed to generate video after %d attempts: %w", result.Attempts, result.LastError)
	}

	ps.updateProgress(projectID, StageVideo, 100, "Video generated successfully", false, "")
	return nil
}

// updateProgress updates the pipeline progress
func (ps *PipelineService) updateProgress(projectID string, stage PipelineStage, progress float64, message string, canRetry bool, retryFrom PipelineStage) {
	ps.progressMutex.Lock()
	defer ps.progressMutex.Unlock()

	if p, exists := ps.progressMap[projectID]; exists {
		p.CurrentStage = stage
		p.Progress = progress
		p.Message = message
		p.LastUpdate = time.Now()
		p.CanRetry = canRetry
		if retryFrom != "" {
			p.RetryFrom = retryFrom
		}
		if stage == StageFailed {
			p.Error = message
		}
	}
}

// updateProgressWithDetailedError updates the pipeline progress with detailed error information
func (ps *PipelineService) updateProgressWithDetailedError(projectID string, stage PipelineStage, progress float64, err error, canRetry bool, retryFrom PipelineStage, context map[string]string) {
	ps.progressMutex.Lock()
	defer ps.progressMutex.Unlock()

	if p, exists := ps.progressMap[projectID]; exists {
		p.CurrentStage = stage
		p.Progress = progress
		p.Message = fmt.Sprintf("%s failed: %v", stage, err)
		p.LastUpdate = time.Now()
		p.CanRetry = canRetry
		if retryFrom != "" {
			p.RetryFrom = retryFrom
		}

		if stage == StageFailed {
			p.Error = err.Error()

			// Create detailed error information
			detailedError := &DetailedError{
				Message:    err.Error(),
				Stage:      stage,
				Timestamp:  time.Now(),
				StackTrace: ps.getStackTrace(),
				Context:    context,
				Retryable:  canRetry,
				RetryCount: 0,
			}

			// If there was a previous detailed error, increment retry count
			if p.DetailedError != nil && p.DetailedError.Stage == stage {
				detailedError.RetryCount = p.DetailedError.RetryCount + 1
				now := time.Now()
				detailedError.LastRetry = &now
			}

			p.DetailedError = detailedError

			// Log detailed error for debugging
			ps.logDetailedError(projectID, detailedError)
		}
	}
}

// getStackTrace captures the current stack trace for debugging
func (ps *PipelineService) getStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// logDetailedError logs detailed error information
func (ps *PipelineService) logDetailedError(projectID string, detailedError *DetailedError) {
	log.Printf("=== DETAILED ERROR REPORT ===")
	log.Printf("Project ID: %s", projectID)
	log.Printf("Stage: %s", detailedError.Stage)
	log.Printf("Message: %s", detailedError.Message)
	log.Printf("Timestamp: %s", detailedError.Timestamp.Format(time.RFC3339))
	log.Printf("Retryable: %t", detailedError.Retryable)
	log.Printf("Retry Count: %d", detailedError.RetryCount)

	if detailedError.Context != nil && len(detailedError.Context) > 0 {
		log.Printf("Context:")
		for key, value := range detailedError.Context {
			log.Printf("  %s: %s", key, value)
		}
	}

	if detailedError.StackTrace != "" {
		log.Printf("Stack Trace:")
		log.Printf("%s", detailedError.StackTrace)
	}

	log.Printf("=== END ERROR REPORT ===")
}

// GetProgress returns the current pipeline progress
func (ps *PipelineService) GetProgress(projectID string) (*PipelineProgress, error) {
	ps.progressMutex.RLock()
	defer ps.progressMutex.RUnlock()

	if progress, exists := ps.progressMap[projectID]; exists {
		// Create a copy to avoid race conditions
		progressCopy := *progress
		return &progressCopy, nil
	}

	return nil, fmt.Errorf("no pipeline progress found for project %s", projectID)
}

// RetryFromStage retries the pipeline from a specific stage
func (ps *PipelineService) RetryFromStage(projectID string, stage PipelineStage, userRequirements string) error {
	// Get project info
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("project not found: %w", err)
	}

	// Cancel existing job if running
	ps.CancelPipeline(projectID)

	// Wait a moment for cleanup
	time.Sleep(100 * time.Millisecond)

	// Update project status to indicate retry/regeneration
	if project.Status == "completed" {
		project.Status = "regenerating"
		project.ErrorMessage = ""
		ps.store.UpdateProject(project)
		log.Printf("Starting regeneration for completed project %s from %s stage", projectID, stage)
	} else {
		project.Status = "retrying"
		project.ErrorMessage = ""
		ps.store.UpdateProject(project)
		log.Printf("Retrying failed project %s from %s stage", projectID, stage)
	}

	// Clear previous progress
	ps.progressMutex.Lock()
	delete(ps.progressMap, projectID)
	ps.progressMutex.Unlock()

	// Start from specific stage
	return ps.startFromStage(projectID, stage, userRequirements)
}

// startFromStage starts the pipeline from a specific stage
func (ps *PipelineService) startFromStage(projectID string, stage PipelineStage, userRequirements string) error {
	// Get project to determine message
	project, err := ps.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Determine appropriate message
	var message string
	if project.Status == "regenerating" {
		message = fmt.Sprintf("Regenerating from %s stage...", stage)
	} else {
		message = fmt.Sprintf("Retrying from %s stage...", stage)
	}

	// Initialize progress
	ps.progressMutex.Lock()
	progress := &PipelineProgress{
		ProjectID:    projectID,
		CurrentStage: stage,
		Progress:     ps.getStageStartProgress(stage),
		Message:      message,
		StartTime:    time.Now(),
		LastUpdate:   time.Now(),
		StageDetails: make(map[string]interface{}),
		CanRetry:     false,
	}
	ps.progressMap[projectID] = progress
	ps.progressMutex.Unlock()

	// Create cancellable context
	ctx, cancel := context.WithCancel(context.Background())
	ps.jobsMutex.Lock()
	ps.activeJobs[projectID] = cancel
	ps.jobsMutex.Unlock()

	// Start pipeline from specific stage (with force regeneration for retry)
	go ps.runFromStageWithRetry(ctx, projectID, stage, userRequirements, true)

	return nil
}

// getStageStartProgress returns the starting progress for each stage
func (ps *PipelineService) getStageStartProgress(stage PipelineStage) float64 {
	switch stage {
	case StageScreenshot:
		return 0
	case StageNarration:
		return 20
	case StageAudio:
		return 40
	case StageVideo:
		return 70
	default:
		return 0
	}
}

// runFromStage runs the pipeline from a specific stage
func (ps *PipelineService) runFromStage(ctx context.Context, projectID string, startStage PipelineStage, userRequirements string) {
	ps.runFromStageWithRetry(ctx, projectID, startStage, userRequirements, false)
}

// runFromStageWithRetry runs the pipeline from a specific stage with retry option
func (ps *PipelineService) runFromStageWithRetry(ctx context.Context, projectID string, startStage PipelineStage, userRequirements string, isRetry bool) {
	defer func() {
		ps.jobsMutex.Lock()
		delete(ps.activeJobs, projectID)
		ps.jobsMutex.Unlock()
	}()

	log.Printf("🔄 Starting pipeline from %s stage for project %s", startStage, projectID)

	// Execute stages based on starting point
	switch startStage {
	case StageScreenshot:
		log.Printf("📸 Starting screenshot stage for project %s (retry: %v)", projectID, isRetry)
		if err := ps.runScreenshotStageWithForce(ctx, projectID, isRetry); err != nil {
			context := map[string]string{
				"stage": "screenshot",
				"project_id": projectID,
				"retry": "true",
			}
			ps.updateProgressWithDetailedError(projectID, StageFailed, 0, err, true, StageScreenshot, context)
			return
		}
		fallthrough
	case StageNarration:
		if ctx.Err() != nil {
			return
		}
		log.Printf("📝 Starting narration stage for project %s (retry: %v)", projectID, isRetry)
		if err := ps.runNarrationStageWithForce(ctx, projectID, userRequirements, isRetry); err != nil {
			context := map[string]string{
				"stage": "narration",
				"project_id": projectID,
				"user_requirements": userRequirements,
				"retry": "true",
			}
			ps.updateProgressWithDetailedError(projectID, StageFailed, 40, err, true, StageNarration, context)
			return
		}
		fallthrough
	case StageAudio:
		if ctx.Err() != nil {
			return
		}
		log.Printf("🎵 Starting audio stage for project %s (retry: %v)", projectID, isRetry)
		if err := ps.runAudioStageWithForce(ctx, projectID, isRetry); err != nil {
			context := map[string]string{
				"stage": "audio",
				"project_id": projectID,
				"retry": "true",
			}
			ps.updateProgressWithDetailedError(projectID, StageFailed, 70, err, true, StageAudio, context)
			return
		}
		fallthrough
	case StageVideo:
		if ctx.Err() != nil {
			return
		}
		log.Printf("🎬 Starting video stage for project %s (retry: %v)", projectID, isRetry)
		if err := ps.runVideoStageWithForce(ctx, projectID, isRetry); err != nil {
			context := map[string]string{
				"stage": "video",
				"project_id": projectID,
				"retry": "true",
			}
			ps.updateProgressWithDetailedError(projectID, StageFailed, 90, err, true, StageVideo, context)
			return
		}
	}

	// Pipeline completed successfully
	ps.updateProgress(projectID, StageCompleted, 100, "Pipeline completed successfully!", false, "")

	// Update project status
	project, err := ps.store.GetProject(projectID)
	if err == nil {
		project.Status = "completed"
		if err := ps.store.UpdateProject(project); err != nil {
			log.Printf("Warning: Failed to update project status: %v", err)
		}
	}
}

// CancelPipeline cancels a running pipeline
func (ps *PipelineService) CancelPipeline(projectID string) error {
	ps.jobsMutex.Lock()
	defer ps.jobsMutex.Unlock()

	if cancel, exists := ps.activeJobs[projectID]; exists {
		cancel()
		delete(ps.activeJobs, projectID)
		ps.updateProgress(projectID, StageFailed, 0, "Pipeline cancelled by user", true, StageScreenshot)
		return nil
	}

	return fmt.Errorf("no active pipeline found for project %s", projectID)
}

// IsRunning checks if a pipeline is currently running
func (ps *PipelineService) IsRunning(projectID string) bool {
	ps.jobsMutex.RLock()
	defer ps.jobsMutex.RUnlock()

	_, exists := ps.activeJobs[projectID]
	return exists
}

// CleanupProgress removes old progress entries
func (ps *PipelineService) CleanupProgress(projectID string) {
	ps.progressMutex.Lock()
	defer ps.progressMutex.Unlock()

	delete(ps.progressMap, projectID)
}
