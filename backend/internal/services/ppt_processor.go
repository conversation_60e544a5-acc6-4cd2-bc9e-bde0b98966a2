package services

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/utils"
	"sort"
	"strconv"
	"strings"
	"time"
)

// PPTProcessorService handles PPT file processing and screenshot generation
type PPTProcessorService struct {
	config       *config.Config
	store        *DatabaseStoreService
	retryManager *utils.RetryManager
}

// NewPPTProcessorService creates a new PPT processor service
func NewPPTProcessorService(cfg *config.Config, store *DatabaseStoreService) *PPTProcessorService {
	// Create retry configuration from config
	retryConfig := &utils.RetryConfig{
		MaxRetries:    cfg.PPTMaxRetries,
		BaseDelay:     time.Duration(cfg.PPTBaseDelay) * time.Second,
		MaxDelay:      time.Duration(cfg.PPTMaxDelay) * time.Second,
		BackoffFactor: cfg.PPTBackoffFactor,
		JitterEnabled: true,
		RetryableErrors: []string{
			"libreoffice", "conversion failed", "timeout", "temporary",
			"file locked", "permission denied", "resource busy",
			"failed to convert", "command failed", "exit status",
		},
	}

	return &PPTProcessorService{
		config:       cfg,
		store:        store,
		retryManager: utils.NewRetryManager(retryConfig),
	}
}

// ProcessPPTFile processes a PPT file and generates screenshots
func (s *PPTProcessorService) ProcessPPTFile(projectID, filePath string) error {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Update project status
	project.Status = "processing"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}

	// Create job for tracking
	job, err := s.store.CreateJob(projectID, "screenshot")
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	// Start processing
	job.Status = "running"
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Convert PPT to images using LibreOffice
	screenshotDir := filepath.Join(s.config.ScreenshotDir, projectID)
	if err := os.MkdirAll(screenshotDir, 0755); err != nil {
		return fmt.Errorf("failed to create screenshot directory: %w", err)
	}

	// Use LibreOffice to convert PPT to images with retry
	fmt.Printf("🔄 Converting PPT to images with retry mechanism...\n")
	result := s.retryManager.Execute(func() error {
		return s.convertPPTToImages(filePath, screenshotDir)
	})

	utils.LogRetryResult("PPT conversion", result)

	if !result.Success {
		job.Status = "failed"
		job.ErrorMsg = result.LastError.Error()
		s.store.UpdateJob(job)
		project.Status = "failed"
		project.ErrorMessage = result.LastError.Error()
		s.store.UpdateProject(project)
		return fmt.Errorf("failed to convert PPT to images after %d attempts: %w", result.Attempts, result.LastError)
	}

	// Count generated images and create slide records
	imageFiles, err := s.getImageFiles(screenshotDir)
	if err != nil {
		return fmt.Errorf("failed to get image files: %w", err)
	}

	project.SlideCount = len(imageFiles)
	project.ScreenshotsPath = screenshotDir

	// Debug: Print image files order
	fmt.Printf("📸 Creating slide records for %d image files:\n", len(imageFiles))
	for i, imageFile := range imageFiles {
		fmt.Printf("  File %d: %s\n", i, filepath.Base(imageFile))
	}

	// Create slide records
	for i, imageFile := range imageFiles {
		slideNumber := i + 1
		title := fmt.Sprintf("Slide %d", slideNumber)

		fmt.Printf("Creating slide record: SlideNumber=%d, ImageFile=%s\n", slideNumber, filepath.Base(imageFile))

		slide, err := s.store.CreateSlide(projectID, slideNumber, title, "")
		if err != nil {
			return fmt.Errorf("failed to create slide record: %w", err)
		}

		slide.ScreenshotPath = imageFile
		if err := s.store.UpdateSlide(slide); err != nil {
			return fmt.Errorf("failed to update slide with screenshot path: %w", err)
		}
	}

	// Update job and project status
	job.Status = "completed"
	job.Progress = 100
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	project.Status = "screenshots_ready"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project: %w", err)
	}

	return nil
}

// convertPPTToImages converts PPT file to images using multiple methods
func (s *PPTProcessorService) convertPPTToImages(pptPath, outputDir string) error {
	fmt.Printf("Converting PPT to images: %s -> %s\n", pptPath, outputDir)

	// Method 1: Try LibreOffice PDF + ImageMagick conversion (most reliable for multi-page)
	if err := s.convertPPTViaPDF(pptPath, outputDir); err == nil {
		fmt.Println("Successfully converted using LibreOffice PDF + ImageMagick")
		return nil
	} else {
		fmt.Printf("LibreOffice PDF + ImageMagick conversion failed: %v\n", err)
	}

	// Method 2: Try LibreOffice direct PNG conversion
	if err := s.convertPPTDirectToPNG(pptPath, outputDir); err == nil {
		fmt.Println("Successfully converted using LibreOffice direct PNG conversion")
		return nil
	} else {
		fmt.Printf("LibreOffice direct PNG conversion failed: %v\n", err)
	}

	// Method 3: Try alternative LibreOffice conversion
	if err := s.convertPPTToImagesAlternative(pptPath, outputDir); err == nil {
		fmt.Println("Successfully converted using alternative method")
		return nil
	} else {
		fmt.Printf("Alternative conversion failed: %v\n", err)
	}

	return fmt.Errorf("all conversion methods failed")
}

// convertPPTDirectToPNG converts PPT directly to PNG using LibreOffice with page range
func (s *PPTProcessorService) convertPPTDirectToPNG(pptPath, outputDir string) error {
	fmt.Printf("Trying LibreOffice direct PNG conversion with page range...\n")

	// First, try to get the number of slides by converting to PDF
	slideCount, err := s.getSlideCount(pptPath)
	if err != nil {
		fmt.Printf("Failed to get slide count, trying single conversion: %v\n", err)
		return s.convertSinglePNG(pptPath, outputDir)
	}

	fmt.Printf("Detected %d slides, converting each page separately...\n", slideCount)

	// Convert each slide separately
	for i := 1; i <= slideCount; i++ {
		if err := s.convertSingleSlide(pptPath, outputDir, i); err != nil {
			fmt.Printf("Failed to convert slide %d: %v\n", i, err)
			// Continue with other slides instead of failing completely
		}
	}

	// Check if we have any PNG files
	files, err := filepath.Glob(filepath.Join(outputDir, "*.png"))
	if err != nil || len(files) == 0 {
		return fmt.Errorf("no PNG files generated")
	}

	fmt.Printf("Successfully generated %d PNG files\n", len(files))
	return nil
}

// getSlideCount gets the number of slides by converting to PDF and checking pages
func (s *PPTProcessorService) getSlideCount(pptPath string) (int, error) {
	// Create temporary directory
	tempDir := filepath.Join(s.config.TempDir, "slide_count")
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return 0, fmt.Errorf("failed to create temp directory: %w", err)
	}
	// 保留临时文件用于调试
	// defer os.RemoveAll(tempDir)

	// Convert to PDF to count pages
	cmd := exec.Command(s.config.LibreOfficePath,
		"--headless",
		"--convert-to", "pdf",
		"--outdir", tempDir,
		pptPath)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return 0, fmt.Errorf("failed to convert to PDF: %w, output: %s", err, string(output))
	}

	// Find the generated PDF
	files, err := filepath.Glob(filepath.Join(tempDir, "*.pdf"))
	if err != nil || len(files) == 0 {
		return 0, fmt.Errorf("no PDF file generated")
	}
	pdfPath := files[0]

	// Use ImageMagick identify to count pages
	cmd = exec.Command("identify", pdfPath)
	output, err = cmd.CombinedOutput()
	if err != nil {
		return 0, fmt.Errorf("failed to identify PDF pages: %w", err)
	}

	// Count lines in output (each line represents a page)
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	return len(lines), nil
}

// convertSingleSlide converts a single slide to PNG
func (s *PPTProcessorService) convertSingleSlide(pptPath, outputDir string, slideNumber int) error {
	// Create temporary directory for this slide
	tempDir := filepath.Join(s.config.TempDir, fmt.Sprintf("slide_%d", slideNumber))
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	// 保留临时文件用于调试
	// defer os.RemoveAll(tempDir)

	// Convert specific slide using LibreOffice with page range
	// Note: LibreOffice uses 1-based page numbering
	cmd := exec.Command(s.config.LibreOfficePath,
		"--headless",
		"--convert-to", "png",
		"--outdir", tempDir,
		fmt.Sprintf("--print-to-file"),
		fmt.Sprintf("--printer-name=slide_%d", slideNumber),
		pptPath)

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Single slide conversion failed: %v, output: %s\n", err, string(output))
		// Try alternative method with macro
		return s.convertSlideWithMacro(pptPath, outputDir, slideNumber)
	}

	// Move the generated file to output directory with correct name
	files, err := filepath.Glob(filepath.Join(tempDir, "*.png"))
	if err != nil || len(files) == 0 {
		return fmt.Errorf("no PNG file generated for slide %d", slideNumber)
	}

	// Rename to slide-XXX.png format (0-based, zero-padded for correct sorting)
	destPath := filepath.Join(outputDir, fmt.Sprintf("slide-%03d.png", slideNumber-1))
	if err := os.Rename(files[0], destPath); err != nil {
		return s.copyFile(files[0], destPath)
	}

	fmt.Printf("Converted slide %d -> %s\n", slideNumber, destPath)
	return nil
}

// convertSlideWithMacro uses LibreOffice macro to export specific slide
func (s *PPTProcessorService) convertSlideWithMacro(pptPath, outputDir string, slideNumber int) error {
	// This is a fallback method - for now, we'll skip individual slide conversion
	// and rely on the PDF method instead
	return fmt.Errorf("macro conversion not implemented for slide %d", slideNumber)
}

// convertSinglePNG fallback method for single PNG conversion
func (s *PPTProcessorService) convertSinglePNG(pptPath, outputDir string) error {
	fmt.Printf("Trying single PNG conversion as fallback...\n")

	// Create temporary directory for conversion
	tempDir := filepath.Join(s.config.TempDir, "single_conversion")
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	// 保留临时文件用于调试
	// defer os.RemoveAll(tempDir)

	// Convert PPT to PNG directly using LibreOffice
	cmd := exec.Command(s.config.LibreOfficePath,
		"--headless",
		"--convert-to", "png",
		"--outdir", tempDir,
		pptPath)

	output, err := cmd.CombinedOutput()
	fmt.Printf("LibreOffice single PNG output: %s\n", string(output))

	if err != nil {
		return fmt.Errorf("failed to convert PPT to PNG: %w", err)
	}

	// Move generated PNG files to output directory and rename them
	return s.moveAndRenameImages(tempDir, outputDir)
}

// convertPPTViaPDF converts PPT to PDF then to PNG using ImageMagick
func (s *PPTProcessorService) convertPPTViaPDF(pptPath, outputDir string) error {
	fmt.Printf("Trying LibreOffice PDF + ImageMagick conversion...\n")

	// Create unique temporary directory for this conversion
	tempDir := filepath.Join(s.config.TempDir, "pdf_conversion", fmt.Sprintf("conv_%d", time.Now().UnixNano()))
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	// Clean up temporary directory after conversion
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			fmt.Printf("Warning: failed to clean up temp directory %s: %v\n", tempDir, err)
		}
	}()

	// Step 1: Convert PPT to PDF using LibreOffice
	cmd := exec.Command(s.config.LibreOfficePath,
		"--headless",
		"--convert-to", "pdf",
		"--outdir", tempDir,
		pptPath)

	output, err := cmd.CombinedOutput()
	fmt.Printf("LibreOffice PDF output: %s\n", string(output))

	if err != nil {
		return fmt.Errorf("failed to convert PPT to PDF: %w", err)
	}

	// Generate expected PDF filename based on input file
	baseName := strings.TrimSuffix(filepath.Base(pptPath), filepath.Ext(pptPath))
	expectedPDFPath := filepath.Join(tempDir, baseName+".pdf")

	var pdfPath string
	// Check if the expected PDF file exists
	if _, err := os.Stat(expectedPDFPath); err == nil {
		pdfPath = expectedPDFPath
		fmt.Printf("Generated PDF: %s\n", pdfPath)
	} else {
		// Fallback: Find any PDF file in the directory
		files, err := filepath.Glob(filepath.Join(tempDir, "*.pdf"))
		if err != nil || len(files) == 0 {
			return fmt.Errorf("no PDF file generated, expected: %s", expectedPDFPath)
		}
		pdfPath = files[0]
		fmt.Printf("Generated PDF (fallback): %s\n", pdfPath)
	}

	// First check if ImageMagick is available
	cmd = exec.Command("convert", "-version")
	versionOutput, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ImageMagick not available: %w", err)
	}
	fmt.Printf("ImageMagick version: %s\n", string(versionOutput))

	// Check if PDF file exists and is not empty
	pdfInfo, err := os.Stat(pdfPath)
	if err != nil {
		return fmt.Errorf("PDF file not found: %w", err)
	}
	if pdfInfo.Size() == 0 {
		return fmt.Errorf("PDF file is empty")
	}
	fmt.Printf("PDF file size: %d bytes\n", pdfInfo.Size())

	// Check PDF page count first
	cmd = exec.Command("identify", "-ping", pdfPath)
	identifyOutput, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Warning: failed to identify PDF: %v, output: %s\n", err, string(identifyOutput))
		// Try alternative method to check PDF
		cmd = exec.Command("pdfinfo", pdfPath)
		pdfInfoOutput, err2 := cmd.CombinedOutput()
		if err2 != nil {
			fmt.Printf("Warning: pdfinfo also failed: %v\n", err2)
		} else {
			fmt.Printf("PDF info: %s\n", string(pdfInfoOutput))
		}
	} else {
		lines := strings.Split(strings.TrimSpace(string(identifyOutput)), "\n")
		fmt.Printf("PDF has %d pages (identified)\n", len(lines))
	}

	// Step 2: Convert PDF to PNG images using ImageMagick
	// Try different approaches (use zero-padded format for correct sorting)
	outputPattern := filepath.Join(outputDir, "slide-%03d.png")

	// Method 1: Standard convert command with proper syntax
	cmd = exec.Command("convert",
		"-density", "150",
		"-quality", "90",
		"-background", "white",
		"-alpha", "remove",
		"-colorspace", "RGB",
		pdfPath+"[0-999]",  // Specify page range to force multi-page output
		outputPattern)

	output, err = cmd.CombinedOutput()
	fmt.Printf("ImageMagick convert (method 1) output: %s\n", string(output))

	if err != nil {
		fmt.Printf("Method 1 failed: %v\n", err)
		// Method 2: Try with different parameters and explicit page range
		cmd = exec.Command("convert",
			"-density", "72",
			pdfPath+"[0-999]",  // Explicit page range
			outputPattern)

		output, err = cmd.CombinedOutput()
		fmt.Printf("ImageMagick convert (method 2) output: %s\n", string(output))

		if err != nil {
			fmt.Printf("Method 2 failed: %v\n", err)
			// Method 3: Try page by page conversion
			return s.convertPDFPageByPage(pdfPath, outputDir)
		}
	}

	// Verify that PNG files were created
	generatedFiles, err := filepath.Glob(filepath.Join(outputDir, "slide-*.png"))
	if err != nil {
		return fmt.Errorf("failed to check generated PNG files: %w", err)
	}

	if len(generatedFiles) == 0 {
		fmt.Printf("No PNG files generated, trying page-by-page conversion\n")
		return s.convertPDFPageByPage(pdfPath, outputDir)
	}

	fmt.Printf("ImageMagick generated %d PNG files: %v\n", len(generatedFiles), generatedFiles)
	return nil
}

// convertPDFPageByPage converts PDF to PNG page by page
func (s *PPTProcessorService) convertPDFPageByPage(pdfPath, outputDir string) error {
	fmt.Printf("Trying page-by-page PDF conversion...\n")

	// First, try to determine the number of pages using different methods
	pageCount := s.getPDFPageCount(pdfPath)
	if pageCount <= 0 {
		fmt.Printf("Could not determine page count, assuming 10 pages max\n")
		pageCount = 10 // Fallback: try up to 10 pages
	}

	fmt.Printf("Attempting to convert %d pages\n", pageCount)

	successCount := 0
	for i := 0; i < pageCount; i++ {
		outputFile := filepath.Join(outputDir, fmt.Sprintf("slide-%03d.png", i))

		// Convert specific page (ImageMagick uses 0-based indexing for pages)
		// First, let's try to verify the PDF file exists and is readable
		if _, err := os.Stat(pdfPath); err != nil {
			fmt.Printf("PDF file not accessible: %v\n", err)
			continue
		}

		// Try different ImageMagick syntax approaches
		var cmd *exec.Cmd

		// Method 1: Standard page specification
		cmd = exec.Command("convert",
			"-density", "150",
			"-quality", "90",
			"-background", "white",
			"-alpha", "remove",
			fmt.Sprintf("%s[%d]", pdfPath, i),
			outputFile)

		output, err := cmd.CombinedOutput()
		if err != nil {
			fmt.Printf("Method 1 failed for page %d: %v, output: %s\n", i, err, string(output))

			// Method 2: Try with simpler syntax
			cmd = exec.Command("convert",
				"-density", "72",
				fmt.Sprintf("%s[%d]", pdfPath, i),
				outputFile)

			output, err = cmd.CombinedOutput()
			if err != nil {
				fmt.Printf("Method 2 failed for page %d: %v, output: %s\n", i, err, string(output))

				// Method 3: Try with pdftoppm + convert
				if err := s.convertPageWithPdftoppm(pdfPath, outputFile, i); err != nil {
					fmt.Printf("Method 3 (pdftoppm) failed for page %d: %v\n", i, err)

					// If we can't convert this page, maybe we've reached the end
					if i == 0 {
						// If we can't even convert the first page, it's a real error
						continue
					} else {
						// If we've converted some pages already, we might have reached the end
						break
					}
				} else {
					fmt.Printf("Successfully converted page %d using pdftoppm -> %s\n", i, outputFile)
					successCount++
				}
			} else {
				fmt.Printf("Successfully converted page %d using method 2 -> %s\n", i, outputFile)
				successCount++
			}
		} else {
			fmt.Printf("Successfully converted page %d using method 1 -> %s\n", i, outputFile)
			successCount++
		}
	}

	if successCount == 0 {
		return fmt.Errorf("failed to convert any pages from PDF")
	}

	fmt.Printf("Successfully converted %d pages\n", successCount)
	return nil
}

// getPDFPageCount tries to determine the number of pages in a PDF
func (s *PPTProcessorService) getPDFPageCount(pdfPath string) int {
	// Method 1: Try pdfinfo
	cmd := exec.Command("pdfinfo", pdfPath)
	output, err := cmd.CombinedOutput()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "Pages:") {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					if count, err := strconv.Atoi(parts[1]); err == nil {
						fmt.Printf("PDF has %d pages (via pdfinfo)\n", count)
						return count
					}
				}
			}
		}
	}

	// Method 2: Try identify with specific format
	cmd = exec.Command("identify", "-format", "%n\n", pdfPath)
	output, err = cmd.CombinedOutput()
	if err == nil {
		lines := strings.Split(strings.TrimSpace(string(output)), "\n")
		if len(lines) > 0 {
			if count, err := strconv.Atoi(lines[0]); err == nil {
				fmt.Printf("PDF has %d pages (via identify)\n", count)
				return count
			}
		}
	}

	fmt.Printf("Could not determine PDF page count\n")
	return -1
}

// convertPageWithPdftoppm converts a single PDF page using pdftoppm
func (s *PPTProcessorService) convertPageWithPdftoppm(pdfPath, outputFile string, pageIndex int) error {
	// pdftoppm uses 1-based page numbering
	pageNum := pageIndex + 1

	// Create temporary file for PNG output (pdftoppm can output PNG directly)
	tempDir := filepath.Dir(outputFile)
	tempPrefix := filepath.Join(tempDir, fmt.Sprintf("temp_page_%d", pageIndex))

	// Convert PDF page to PNG using pdftoppm (direct PNG output)
	cmd := exec.Command("pdftoppm",
		"-f", fmt.Sprintf("%d", pageNum),  // First page (1-based)
		"-l", fmt.Sprintf("%d", pageNum),  // Last page (same as first for single page)
		"-r", "150",                       // Resolution
		"-png",                            // Output PNG format
		"-singlefile",                     // Single file output
		pdfPath,
		tempPrefix)                        // Output prefix

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("pdftoppm failed: %w, output: %s", err, string(output))
	}

	// pdftoppm generates file with .png extension
	tempPNGFile := tempPrefix + ".png"

	// Check if the temporary PNG file was created
	if _, err := os.Stat(tempPNGFile); err != nil {
		return fmt.Errorf("pdftoppm did not generate expected file %s: %w", tempPNGFile, err)
	}

	// Move the temporary PNG file to the final location
	if err := os.Rename(tempPNGFile, outputFile); err != nil {
		// If rename fails, try copy
		if copyErr := s.copyFile(tempPNGFile, outputFile); copyErr != nil {
			os.Remove(tempPNGFile) // Clean up temp file
			return fmt.Errorf("failed to move PNG file: rename failed: %w, copy failed: %w", err, copyErr)
		}
		os.Remove(tempPNGFile) // Clean up temp file after successful copy
	}

	fmt.Printf("Successfully converted page %d using pdftoppm: %s -> %s\n", pageIndex, pdfPath, outputFile)
	return nil
}

// moveAndRenameImages moves PNG files from temp dir to output dir with standard naming
func (s *PPTProcessorService) moveAndRenameImages(tempDir, outputDir string) error {
	// Find all PNG files in temp directory
	files, err := filepath.Glob(filepath.Join(tempDir, "*.png"))
	if err != nil {
		return fmt.Errorf("failed to find PNG files: %w", err)
	}

	if len(files) == 0 {
		return fmt.Errorf("no PNG files generated")
	}

	// Sort files to ensure consistent ordering
	sort.Strings(files)
	fmt.Printf("Found %d PNG files: %v\n", len(files), files)

	// Move and rename files to slide-000.png, slide-001.png, etc. (zero-padded for correct sorting)
	for i, file := range files {
		destPath := filepath.Join(outputDir, fmt.Sprintf("slide-%03d.png", i))
		if err := os.Rename(file, destPath); err != nil {
			// If rename fails, try copy + delete
			if err := s.copyFile(file, destPath); err != nil {
				return fmt.Errorf("failed to move %s to %s: %w", file, destPath, err)
			}
			os.Remove(file) // Clean up original file
		}
		fmt.Printf("Moved %s -> %s\n", file, destPath)
	}

	return nil
}

// copyFile copies a file from src to dst
func (s *PPTProcessorService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// convertPPTToImagesAlternative uses LibreOffice direct conversion as fallback
func (s *PPTProcessorService) convertPPTToImagesAlternative(pptPath, outputDir string) error {
	// Try LibreOffice direct conversion to PNG
	cmd := exec.Command(s.config.LibreOfficePath,
		"--headless",
		"--convert-to", "png",
		"--outdir", outputDir,
		pptPath)

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to convert PPT to images: %w", err)
	}

	// LibreOffice might generate files with different naming patterns
	// Try to rename them to our standard format
	return s.standardizeImageNames(outputDir)
}

// standardizeImageNames renames generated images to our standard format
func (s *PPTProcessorService) standardizeImageNames(outputDir string) error {
	// Find all PNG files in the output directory
	files, err := filepath.Glob(filepath.Join(outputDir, "*.png"))
	if err != nil {
		return fmt.Errorf("failed to find PNG files: %w", err)
	}

	// Sort files to ensure consistent ordering
	sort.Strings(files)

	// Rename files to slide-000.png, slide-001.png, etc. (zero-padded for correct sorting)
	for i, file := range files {
		newName := filepath.Join(outputDir, fmt.Sprintf("slide-%03d.png", i))
		if file != newName {
			if err := os.Rename(file, newName); err != nil {
				return fmt.Errorf("failed to rename %s to %s: %w", file, newName, err)
			}
		}
	}

	return nil
}

// createDummyImages creates dummy images for testing purposes
func (s *PPTProcessorService) createDummyImages(outputDir string, count int) error {
	for i := 1; i <= count; i++ {
		filename := fmt.Sprintf("slide-%03d.png", i)
		filepath := filepath.Join(outputDir, filename)

		// Create a simple dummy image file (just an empty file for now)
		file, err := os.Create(filepath)
		if err != nil {
			return fmt.Errorf("failed to create dummy image %s: %w", filename, err)
		}
		file.Close()
	}
	return nil
}

// getImageFiles returns a list of image files in the directory, sorted by name
func (s *PPTProcessorService) getImageFiles(dir string) ([]string, error) {
	var imageFiles []string

	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		name := entry.Name()
		ext := strings.ToLower(filepath.Ext(name))

		if ext == ".png" || ext == ".jpg" || ext == ".jpeg" {
			imageFiles = append(imageFiles, filepath.Join(dir, name))
		}
	}

	// Sort files to ensure consistent ordering (now works correctly with zero-padded filenames)
	sort.Strings(imageFiles)

	return imageFiles, nil
}

// GetSlideScreenshot returns the screenshot path for a specific slide
func (s *PPTProcessorService) GetSlideScreenshot(projectID string, slideNumber int) (string, error) {
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return "", fmt.Errorf("failed to get slides: %w", err)
	}

	for _, slide := range slides {
		if slide.SlideNumber == slideNumber {
			return slide.ScreenshotPath, nil
		}
	}

	return "", fmt.Errorf("slide %d not found in project %s", slideNumber, projectID)
}

// GetProjectScreenshots returns all screenshot paths for a project
func (s *PPTProcessorService) GetProjectScreenshots(projectID string) ([]string, error) {
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get slides: %w", err)
	}

	var screenshots []string
	for _, slide := range slides {
		if slide.ScreenshotPath != "" {
			screenshots = append(screenshots, slide.ScreenshotPath)
		}
	}

	return screenshots, nil
}

// ValidatePPTFile validates if the uploaded file is a valid PPT file
func (s *PPTProcessorService) ValidatePPTFile(filePath string) error {
	// Check file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != ".ppt" && ext != ".pptx" {
		return fmt.Errorf("invalid file type: %s. Only .ppt and .pptx files are supported", ext)
	}

	// Check if file exists and is readable
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", filePath)
	}

	// Check file size (optional)
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Limit file size to 100MB
	maxSize := int64(100 * 1024 * 1024)
	if fileInfo.Size() > maxSize {
		return fmt.Errorf("file too large: %d bytes. Maximum size is %d bytes", fileInfo.Size(), maxSize)
	}

	return nil
}

// GetProcessingProgress returns the current processing progress for a project
func (s *PPTProcessorService) GetProcessingProgress(projectID string) (*models.ScreenshotProgress, error) {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	progress := &models.ScreenshotProgress{
		ProjectID:   projectID,
		TotalSlides: project.SlideCount,
		Status:      project.Status,
		Message:     "Processing screenshots...",
	}

	// Count processed slides
	slides, err := s.store.GetSlidesByProject(projectID)
	if err == nil {
		processedCount := 0
		for _, slide := range slides {
			if slide.ScreenshotPath != "" {
				processedCount++
			}
		}
		progress.ProcessedSlides = processedCount
	}

	return progress, nil
}
