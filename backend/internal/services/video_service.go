package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/utils"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// VideoService handles video generation and composition
type VideoService struct {
	config          *config.Config
	store           *DatabaseStoreService
	retryManager    *utils.RetryManager
	subtitleService *SubtitleService
}

// NewVideoService creates a new video service
func NewVideoService(cfg *config.Config, store *DatabaseStoreService) *VideoService {
	// Create retry configuration from config
	retryConfig := &utils.RetryConfig{
		MaxRetries:    cfg.VideoMaxRetries,
		BaseDelay:     time.Duration(cfg.VideoBaseDelay) * time.Second,
		MaxDelay:      time.Duration(cfg.VideoMaxDelay) * time.Second,
		BackoffFactor: cfg.VideoBackoffFactor,
		JitterEnabled: true,
		RetryableErrors: []string{
			"ffmpeg", "encoding failed", "timeout", "temporary",
			"resource busy", "disk space", "memory", "command failed",
			"exit status", "conversion failed", "no space left",
		},
	}

	// Initialize subtitle service
	subtitleService := NewSubtitleService(cfg, store)

	return &VideoService{
		config:          cfg,
		store:           store,
		retryManager:    utils.NewRetryManager(retryConfig),
		subtitleService: subtitleService,
	}
}

// GenerateVideo generates the final video by combining slides and audio
func (s *VideoService) GenerateVideo(projectID string, request *models.VideoGenerationRequest) error {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Check if audio is ready - allow more states for recovery
	validStates := []string{"audio_ready", "video_failed", "failed", "generating_video"}
	isValidState := false
	for _, state := range validStates {
		if project.Status == state {
			isValidState = true
			break
		}
	}

	if !isValidState {
		return fmt.Errorf("project status '%s' is not valid for video generation. Valid states: %v", project.Status, validStates)
	}

	// Check if audio path exists
	if project.AudioPath == "" {
		// Try to find audio files from slides as fallback
		slides, err := s.store.GetSlidesByProject(projectID)
		if err != nil {
			return fmt.Errorf("audio path is empty and failed to check slides: %w", err)
		}

		audioCount := 0
		for _, slide := range slides {
			if slide.NarrationAudio != "" && s.fileExists(slide.NarrationAudio) {
				audioCount++
			}
		}

		if audioCount == 0 {
			return fmt.Errorf("no audio files found for project %s. Please generate audio first", projectID)
		}

		fmt.Printf("⚠️  Project audio path is empty but found %d slide audio files, proceeding with video generation\n", audioCount)
	}

	// Update project status
	project.Status = "generating_video"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}

	// Create job for tracking
	job, err := s.store.CreateJob(projectID, "video")
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	job.Status = "running"
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Create video directory
	videoDir := filepath.Join(s.config.VideoDir, projectID)
	if err := os.MkdirAll(videoDir, 0755); err != nil {
		return fmt.Errorf("failed to create video directory: %w", err)
	}

	// Get slides
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	if len(slides) == 0 {
		return fmt.Errorf("no slides found for project %s", projectID)
	}

	// Generate video
	outputPath := filepath.Join(videoDir, "presentation.mp4")
	if request.OutputFormat != "" && request.OutputFormat != "mp4" {
		outputPath = filepath.Join(videoDir, fmt.Sprintf("presentation.%s", request.OutputFormat))
	}

	// Create video with retry mechanism
	fmt.Printf("🔄 Creating video with retry mechanism...\n")
	result := s.retryManager.Execute(func() error {
		return s.createVideoWithFFmpeg(project, slides, outputPath, request)
	})

	utils.LogRetryResult("Video generation", result)

	if !result.Success {
		job.Status = "failed"
		job.ErrorMsg = result.LastError.Error()
		s.store.UpdateJob(job)
		// Don't set project to "failed", use "video_failed" to preserve audio_ready state
		project.Status = "video_failed"
		project.ErrorMessage = result.LastError.Error()
		s.store.UpdateProject(project)
		return fmt.Errorf("failed to create video after %d attempts: %w", result.Attempts, result.LastError)
	}

	// Update project with video path
	project.VideoPath = outputPath
	project.Status = "completed"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project with video path: %w", err)
	}

	// Complete job
	job.Status = "completed"
	job.Progress = 100
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to complete job: %w", err)
	}

	return nil
}

// createVideoWithFFmpeg creates video using FFmpeg
func (s *VideoService) createVideoWithFFmpeg(project *models.PPTProject, slides []*models.PPTSlide, outputPath string, request *models.VideoGenerationRequest) error {
	// Set default values
	fps := 1 // 1 FPS for slide show
	if request.FPS > 0 {
		fps = request.FPS
	}

	resolution := "1920x1080"
	if request.Resolution != "" {
		resolution = request.Resolution
	}

	// Create temporary directory for processing
	tempDir := filepath.Join(s.config.TempDir, "video_"+project.ID)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// Generate subtitle file if requested
	var subtitlePath string
	if request.EnableSubtitles {
		subtitlePath = filepath.Join(tempDir, "subtitles.srt")
		if err := s.subtitleService.GenerateSubtitleFile(project.ID, subtitlePath); err != nil {
			fmt.Printf("Warning: Failed to generate subtitles: %v\n", err)
			// Continue without subtitles rather than failing
			subtitlePath = ""
		}
	}

	// Method 1: Create video from individual slide videos
	if len(slides) > 1 {
		return s.createVideoFromSlides(project, slides, outputPath, tempDir, fps, resolution, subtitlePath, request)
	}

	// Method 2: Simple single slide video
	if len(slides) == 1 {
		return s.createSingleSlideVideo(project, slides[0], outputPath, fps, resolution, subtitlePath, request)
	}

	return fmt.Errorf("no slides to process")
}

// createVideoFromSlides creates video by combining multiple slides
func (s *VideoService) createVideoFromSlides(project *models.PPTProject, slides []*models.PPTSlide, outputPath, tempDir string, fps int, resolution string, subtitlePath string, request *models.VideoGenerationRequest) error {
	var videoSegments []string

	// Debug: Print slide order
	fmt.Printf("🎬 Creating video segments for %d slides:\n", len(slides))
	for i, slide := range slides {
		fmt.Printf("  Slide %d: SlideNumber=%d, ScreenshotPath=%s, AudioPath=%s\n",
			i+1, slide.SlideNumber, slide.ScreenshotPath, slide.NarrationAudio)
	}

	// Create video segment for each slide
	segmentIndex := 1 // Use sequential numbering for file names
	for _, slide := range slides {
		if slide.ScreenshotPath == "" {
			continue
		}

		// Calculate duration for this slide based on audio
		duration := slide.Duration
		if slide.NarrationAudio != "" && s.fileExists(slide.NarrationAudio) {
			// Get duration from audio file
			audioDuration, err := s.getAudioDuration(slide.NarrationAudio)
			if err == nil && audioDuration > 0 {
				duration = audioDuration
			}
		}
		if duration <= 0 {
			duration = 3.0 // Default 3 seconds per slide
		}

		// Use sequential numbering for consistent ordering
		segmentPath := filepath.Join(tempDir, fmt.Sprintf("segment_%03d.mp4", segmentIndex))

		// Create video segment for this slide
		err := s.createSlideVideoSegment(slide.ScreenshotPath, slide.NarrationAudio, segmentPath, duration, fps, resolution)
		if err != nil {
			return fmt.Errorf("failed to create video segment for slide %d: %w", slide.SlideNumber, err)
		}

		videoSegments = append(videoSegments, segmentPath)
		segmentIndex++
	}

	// Combine all segments
	return s.combineVideoSegments(videoSegments, outputPath, subtitlePath, request)
}

// createSlideVideoSegment creates a video segment for a single slide
func (s *VideoService) createSlideVideoSegment(imagePath, audioPath, outputPath string, duration float64, fps int, resolution string) error {
	args := []string{
		"-y",         // Overwrite output file
		"-loop", "1", // Loop the image
		"-i", imagePath, // Input image
	}

	// Add audio if available
	if audioPath != "" && s.fileExists(audioPath) {
		// Get exact audio duration
		audioDuration, err := s.getAudioDuration(audioPath)
		if err != nil {
			// Fallback to provided duration if audio duration detection fails
			audioDuration = duration
		}

		args = append(args, "-i", audioPath)

		// Video settings with audio - use exact audio duration
		args = append(args,
			"-map", "0:v:0",       // Map video from first input (image)
			"-map", "1:a:0",       // Map audio from second input (audio file)
			"-c:v", "libx264",     // Video codec
			"-c:a", "aac",         // Audio codec
			"-r", strconv.Itoa(fps), // Frame rate
			"-s", resolution,      // Resolution
			"-pix_fmt", "yuv420p", // Pixel format for compatibility
			"-t", fmt.Sprintf("%.3f", audioDuration), // Exact duration from audio
			"-avoid_negative_ts", "make_zero", // Handle timestamp issues
			"-fflags", "+genpts",  // Generate presentation timestamps
			outputPath,
		)
	} else {
		// No audio, use specified duration
		args = append(args,
			"-t", fmt.Sprintf("%.3f", duration), // Duration with more precision
			"-c:v", "libx264",     // Video codec
			"-r", strconv.Itoa(fps), // Frame rate
			"-s", resolution,      // Resolution
			"-pix_fmt", "yuv420p", // Pixel format for compatibility
			"-an",                 // No audio
			outputPath,
		)
	}

	cmd := exec.Command(s.config.FFmpegPath, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg error creating slide video segment: %w, output: %s", err, string(output))
	}

	// Verify the output video duration matches expected duration
	if audioPath != "" && s.fileExists(audioPath) {
		if err := s.verifyVideoDuration(outputPath, audioPath); err != nil {
			// Log warning but don't fail - the video is probably still usable
			fmt.Printf("Warning: Video duration verification failed: %v\n", err)
		}
	}

	return nil
}

// createSingleSlideVideo creates video for a single slide
func (s *VideoService) createSingleSlideVideo(project *models.PPTProject, slide *models.PPTSlide, outputPath string, fps int, resolution string, subtitlePath string, request *models.VideoGenerationRequest) error {
	if slide.ScreenshotPath == "" {
		return fmt.Errorf("no screenshot available for slide %d", slide.SlideNumber)
	}

	duration := slide.Duration
	if slide.NarrationAudio != "" && s.fileExists(slide.NarrationAudio) {
		// Get duration from audio file
		audioDuration, err := s.getAudioDuration(slide.NarrationAudio)
		if err == nil && audioDuration > 0 {
			duration = audioDuration
		}
	}
	if duration <= 0 {
		duration = 10.0 // Default 10 seconds for single slide
	}

	// Create video segment first
	tempVideoPath := outputPath + ".temp.mp4"
	err := s.createSlideVideoSegment(slide.ScreenshotPath, slide.NarrationAudio, tempVideoPath, duration, fps, resolution)
	if err != nil {
		return err
	}

	// Add subtitles if requested
	if subtitlePath != "" && s.fileExists(subtitlePath) {
		err = s.addSubtitlesToVideo(tempVideoPath, subtitlePath, outputPath, request)
		os.Remove(tempVideoPath) // Clean up temp file
		return err
	}

	// No subtitles, just rename temp file to final output
	return os.Rename(tempVideoPath, outputPath)
}

// combineVideoSegments combines multiple video segments into one
func (s *VideoService) combineVideoSegments(segments []string, outputPath string, subtitlePath string, request *models.VideoGenerationRequest) error {
	if len(segments) == 0 {
		return fmt.Errorf("no video segments to combine")
	}

	if len(segments) == 1 {
		// Only one segment, handle subtitles if needed
		if subtitlePath != "" && s.fileExists(subtitlePath) {
			return s.addSubtitlesToVideo(segments[0], subtitlePath, outputPath, request)
		}
		// No subtitles, just copy it
		return s.copyFile(segments[0], outputPath)
	}

	// Create file list for FFmpeg concat
	tempDir := filepath.Dir(outputPath)
	listFile := filepath.Join(tempDir, "video_list.txt")

	file, err := os.Create(listFile)
	if err != nil {
		return fmt.Errorf("failed to create video list file: %w", err)
	}
	defer file.Close()
	defer os.Remove(listFile)

	// Write segment list
	for _, segment := range segments {
		_, err := file.WriteString(fmt.Sprintf("file '%s'\n", segment))
		if err != nil {
			return fmt.Errorf("failed to write to video list: %w", err)
		}
	}
	file.Close()

	// Create temporary output path for video without subtitles
	tempOutputPath := outputPath + ".temp.mp4"

	// Combine videos using FFmpeg concat
	// Use re-encoding to ensure compatibility between segments
	args := []string{
		"-y", // Overwrite output
		"-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-c:v", "libx264",     // Re-encode video for consistency
		"-c:a", "aac",         // Re-encode audio for consistency
		"-preset", "medium",   // Encoding speed vs quality balance
		"-crf", "23",          // Quality setting
		"-pix_fmt", "yuv420p", // Pixel format for compatibility
		tempOutputPath,
	}

	cmd := exec.Command(s.config.FFmpegPath, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg concat error: %w, output: %s", err, string(output))
	}

	// Add subtitles if requested
	if subtitlePath != "" && s.fileExists(subtitlePath) {
		err = s.addSubtitlesToVideo(tempOutputPath, subtitlePath, outputPath, request)
		os.Remove(tempOutputPath) // Clean up temp file
		return err
	}

	// No subtitles, just rename temp file to final output
	return os.Rename(tempOutputPath, outputPath)
}

// copyFile copies a file from src to dst
func (s *VideoService) copyFile(src, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return fmt.Errorf("failed to read source file: %w", err)
	}

	err = os.WriteFile(dst, input, 0644)
	if err != nil {
		return fmt.Errorf("failed to write destination file: %w", err)
	}

	return nil
}

// fileExists checks if a file exists
func (s *VideoService) fileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// getAudioDuration gets the duration of an audio file using FFprobe
func (s *VideoService) getAudioDuration(audioPath string) (float64, error) {
	// Try FFprobe first (more reliable)
	duration, err := s.getAudioDurationWithFFprobe(audioPath)
	if err == nil {
		return duration, nil
	}

	// Fallback to FFmpeg
	return s.getAudioDurationWithFFmpeg(audioPath)
}

// getAudioDurationWithFFprobe gets audio duration using FFprobe
func (s *VideoService) getAudioDurationWithFFprobe(audioPath string) (float64, error) {
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		audioPath,
	)

	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("failed to get audio duration with ffprobe: %w", err)
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse duration: %w", err)
	}

	return duration, nil
}

// getAudioDurationWithFFmpeg gets audio duration using FFmpeg (fallback method)
func (s *VideoService) getAudioDurationWithFFmpeg(audioPath string) (float64, error) {
	cmd := exec.Command(s.config.FFmpegPath,
		"-i", audioPath,
		"-f", "null",
		"-",
	)

	output, _ := cmd.CombinedOutput()
	// FFmpeg returns error when outputting to null, but we can still parse the output

	// Parse duration from FFmpeg output
	// Look for pattern like "Duration: 00:00:05.23"
	re := regexp.MustCompile(`Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})`)
	matches := re.FindStringSubmatch(string(output))

	if len(matches) != 5 {
		return 0, fmt.Errorf("could not parse duration from FFmpeg output: %s", string(output))
	}

	hours, _ := strconv.Atoi(matches[1])
	minutes, _ := strconv.Atoi(matches[2])
	seconds, _ := strconv.Atoi(matches[3])
	centiseconds, _ := strconv.Atoi(matches[4])

	totalSeconds := float64(hours*3600 + minutes*60 + seconds) + float64(centiseconds)/100.0
	return totalSeconds, nil
}

// verifyVideoDuration verifies that the video duration matches the audio duration
func (s *VideoService) verifyVideoDuration(videoPath, audioPath string) error {
	// Get audio duration
	audioDuration, err := s.getAudioDuration(audioPath)
	if err != nil {
		return fmt.Errorf("failed to get audio duration: %w", err)
	}

	// Get video duration
	videoDuration, err := s.getVideoDuration(videoPath)
	if err != nil {
		return fmt.Errorf("failed to get video duration: %w", err)
	}

	// Allow small tolerance (0.1 seconds)
	tolerance := 0.1
	diff := videoDuration - audioDuration
	if diff < -tolerance || diff > tolerance {
		return fmt.Errorf("video duration (%.3fs) differs from audio duration (%.3fs) by %.3fs",
			videoDuration, audioDuration, diff)
	}

	return nil
}

// getVideoDuration gets the duration of a video file using FFprobe
func (s *VideoService) getVideoDuration(videoPath string) (float64, error) {
	// Use FFprobe to get video duration
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		videoPath,
	)

	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("failed to get video duration with ffprobe: %w", err)
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse video duration: %w", err)
	}

	return duration, nil
}

// GetVideoProgress returns the current video generation progress
func (s *VideoService) GetVideoProgress(projectID string) (int, error) {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return 0, err
	}

	switch project.Status {
	case "completed":
		return 100, nil
	case "generating_video":
		return 50, nil // Simplified progress
	default:
		return 0, nil
	}
}

// ValidateFFmpeg checks if FFmpeg is available
func (s *VideoService) ValidateFFmpeg() error {
	cmd := exec.Command(s.config.FFmpegPath, "-version")
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("FFmpeg not found or not working: %w", err)
	}
	return nil
}

// GetVideoInfo returns information about the generated video
func (s *VideoService) GetVideoInfo(projectID string) (*VideoInfo, error) {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	if project.VideoPath == "" {
		return nil, fmt.Errorf("no video generated for project %s", projectID)
	}

	// Get file info
	fileInfo, err := os.Stat(project.VideoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get video file info: %w", err)
	}

	// Calculate total duration from slides
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get slides: %w", err)
	}

	totalDuration := 0.0
	for _, slide := range slides {
		totalDuration += slide.Duration
	}

	return &VideoInfo{
		FilePath:   project.VideoPath,
		FileSize:   fileInfo.Size(),
		Duration:   totalDuration,
		SlideCount: len(slides),
		CreatedAt:  fileInfo.ModTime(),
	}, nil
}

// VideoInfo represents information about a generated video
type VideoInfo struct {
	FilePath   string      `json:"file_path"`
	FileSize   int64       `json:"file_size"`
	Duration   float64     `json:"duration"`
	SlideCount int         `json:"slide_count"`
	CreatedAt  interface{} `json:"created_at"`
}

// addSubtitlesToVideo adds subtitles to a video using FFmpeg
func (s *VideoService) addSubtitlesToVideo(inputVideoPath, subtitlePath, outputVideoPath string, request *models.VideoGenerationRequest) error {
	if !s.fileExists(inputVideoPath) {
		return fmt.Errorf("input video file not found: %s", inputVideoPath)
	}

	if !s.fileExists(subtitlePath) {
		return fmt.Errorf("subtitle file not found: %s", subtitlePath)
	}

	// Build subtitle style from config and request
	subtitleStyle := s.buildSubtitleStyle(request)

	// Build FFmpeg command with subtitle filter
	args := []string{
		"-y", // Overwrite output file
		"-i", inputVideoPath, // Input video
		"-vf", fmt.Sprintf("subtitles=%s%s", subtitlePath, subtitleStyle), // Video filter with subtitles
		"-c:a", "copy", // Copy audio stream without re-encoding
		"-preset", "medium", // Encoding speed vs quality balance
		"-crf", "23", // Quality setting
		outputVideoPath,
	}

	cmd := exec.Command(s.config.FFmpegPath, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg subtitle embedding error: %w, output: %s", err, string(output))
	}

	return nil
}

// buildSubtitleStyle builds FFmpeg subtitle style string from config and request
func (s *VideoService) buildSubtitleStyle(request *models.VideoGenerationRequest) string {
	var styleOptions []string

	// Start with config defaults
	fontSize := s.config.SubtitleFontSize
	fontColor := s.config.SubtitleFontColor
	fontFamily := s.config.SubtitleFontFamily
	outline := s.config.SubtitleOutline
	shadow := s.config.SubtitleShadow
	backgroundColor := s.config.SubtitleBackgroundColor

	// Apply predefined template if specified
	if request.SubtitleStyleTemplate != "" {
		if template, exists := s.config.SubtitleStyleTemplates[request.SubtitleStyleTemplate]; exists {
			fontSize = template.FontSize
			fontColor = template.FontColor
			fontFamily = template.FontFamily
			outline = template.Outline
			shadow = template.Shadow
			backgroundColor = template.BackgroundColor
		}
	}

	// Override with custom style if provided
	if request.SubtitleStyle != nil {
		if request.SubtitleStyle.FontSize > 0 {
			fontSize = request.SubtitleStyle.FontSize
		}
		if request.SubtitleStyle.FontColor != "" {
			fontColor = request.SubtitleStyle.FontColor
		}
		if request.SubtitleStyle.FontFamily != "" {
			fontFamily = request.SubtitleStyle.FontFamily
		}
		if request.SubtitleStyle.Outline > 0 {
			outline = request.SubtitleStyle.Outline
		}
		if request.SubtitleStyle.BackgroundColor != "" {
			backgroundColor = request.SubtitleStyle.BackgroundColor
		}
		shadow = request.SubtitleStyle.Shadow
	}

	// Build style options with improved layout and readability
	if fontSize > 0 {
		styleOptions = append(styleOptions, fmt.Sprintf("FontSize=%d", fontSize))
	}

	if fontColor != "" {
		// Convert hex color to FFmpeg format (remove # and add &H prefix)
		color := strings.TrimPrefix(fontColor, "#")
		// Ensure proper color format for FFmpeg (BGR format)
		if len(color) == 6 {
			// Convert RGB to BGR for FFmpeg
			r := color[0:2]
			g := color[2:4]
			b := color[4:6]
			color = b + g + r
		}
		styleOptions = append(styleOptions, fmt.Sprintf("PrimaryColour=&H%s", color))
	}

	if fontFamily != "" {
		styleOptions = append(styleOptions, fmt.Sprintf("FontName=%s", fontFamily))
	}

	if outline > 0 {
		styleOptions = append(styleOptions, fmt.Sprintf("Outline=%d", outline))
		// Add outline color (black) for better contrast
		styleOptions = append(styleOptions, "OutlineColour=&H000000")
	}

	// Handle background color
	if backgroundColor != "" {
		// Convert hex color to FFmpeg format
		bgColor := strings.TrimPrefix(backgroundColor, "#")
		if len(bgColor) == 8 {
			// ARGB format - keep as is
			styleOptions = append(styleOptions, fmt.Sprintf("BackColour=&H%s", bgColor))
		} else if len(bgColor) == 6 {
			// RGB format - add alpha for semi-transparency
			styleOptions = append(styleOptions, fmt.Sprintf("BackColour=&H80%s", bgColor))
		}
		styleOptions = append(styleOptions, "BorderStyle=4") // Background box
	}

	if shadow {
		styleOptions = append(styleOptions, "Shadow=2") // Increased shadow for better visibility
		// Only add default background if no custom background is set
		if backgroundColor == "" {
			styleOptions = append(styleOptions, "BackColour=&*********") // Semi-transparent black background
			styleOptions = append(styleOptions, "BorderStyle=4")         // Background box
		}
	} else {
		styleOptions = append(styleOptions, "Shadow=0")
	}

	// Add alignment and margin settings for better positioning
	styleOptions = append(styleOptions, "Alignment=2") // Bottom center alignment
	styleOptions = append(styleOptions, "MarginL=50")  // Left margin (increased)
	styleOptions = append(styleOptions, "MarginR=50")  // Right margin (increased)
	styleOptions = append(styleOptions, "MarginV=40")  // Vertical margin from bottom (increased)

	// Add spacing and layout improvements
	styleOptions = append(styleOptions, "Spacing=1")     // Character spacing
	styleOptions = append(styleOptions, "BorderStyle=1") // Outline border style (will be overridden by background box if set)

	if len(styleOptions) > 0 {
		return fmt.Sprintf(":force_style='%s'", strings.Join(styleOptions, ","))
	}

	return ""
}
