package services

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"
)

// TTSService handles text-to-speech conversion
type TTSService struct {
	config             *config.Config
	openaiClient       *openai.Client
	minimaxTTSClient   *MinimaxTTSClient
	easyvoiceTTSClient *EasyVoiceTTSClient
	audioProcessor     *AudioProcessor
	store              *DatabaseStoreService
}

// NewTTSService creates a new TTS service
func NewTTSService(cfg *config.Config, store *DatabaseStoreService) *TTSService {
	var openaiClient *openai.Client
	var minimaxTTSClient *MinimaxTTSClient
	var easyvoiceTTSClient *EasyVoiceTTSClient

	// Initialize OpenAI client if configured
	if cfg.TTSProvider == "openai" || cfg.OpenAIAPIKey != "" {
		clientConfig := openai.DefaultConfig(cfg.OpenAIAPIKey)
		if cfg.OpenAIBaseURL != "" {
			clientConfig.BaseURL = cfg.OpenAIBaseURL
		}
		openaiClient = openai.NewClientWithConfig(clientConfig)
	}

	// Initialize MiniMax TTS client if configured
	if cfg.TTSProvider == "minimax" || cfg.MinimaxTTSAPIKey != "" {
		minimaxTTSClient = NewMinimaxTTSClient(cfg)
	}

	// Initialize EasyVoice TTS client if configured
	if cfg.TTSProvider == "easyvoice" || cfg.EasyVoiceUsername != "" {
		easyvoiceTTSClient = NewEasyVoiceTTSClient(cfg)
	}

	// Initialize audio processor with configuration
	audioProcessor := NewAudioProcessorWithConfig(cfg.FFmpegPath, cfg.TempDir, cfg.PauseSpeedFactor)

	return &TTSService{
		config:             cfg,
		openaiClient:       openaiClient,
		minimaxTTSClient:   minimaxTTSClient,
		easyvoiceTTSClient: easyvoiceTTSClient,
		audioProcessor:     audioProcessor,
		store:              store,
	}
}

// GenerateAudio generates audio files for all slides in a project
func (s *TTSService) GenerateAudio(projectID string) error {
	project, err := s.store.GetProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Check if narration is ready
	if project.Status != "narration_ready" && project.Status != "audio_failed" && project.NarrationScript == "" {
		return fmt.Errorf("narration not ready for project %s", projectID)
	}

	// If project status is not narration_ready or audio_failed, check if we have narration content
	if project.Status != "narration_ready" && project.Status != "audio_failed" {
		// Check if we have slides with narration
		slides, err := s.store.GetSlidesByProject(projectID)
		if err != nil {
			return fmt.Errorf("failed to get slides: %w", err)
		}

		hasNarration := false
		for _, slide := range slides {
			if slide.NarrationText != "" {
				hasNarration = true
				break
			}
		}

		if !hasNarration {
			return fmt.Errorf("narration not ready. Please generate narration first")
		}
	}

	// Update project status
	project.Status = "generating_audio"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}

	// Create job for tracking
	job, err := s.store.CreateJob(projectID, "tts")
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	job.Status = "running"
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Create audio directory
	audioDir := filepath.Join(s.config.VideoDir, projectID, "audio")
	if err := os.MkdirAll(audioDir, 0755); err != nil {
		return fmt.Errorf("failed to create audio directory: %w", err)
	}

	// Get slides with narration
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	totalSlides := len(slides)
	var audioFiles []string

	// Find resume point for audio generation
	startSlideIndex := s.findAudioResumePoint(slides, audioDir)
	if startSlideIndex > 0 {
		fmt.Printf("Resuming audio generation from slide %d\n", startSlideIndex+1)

		// Collect existing audio files
		for i := 0; i < startSlideIndex; i++ {
			slide := slides[i]
			if slide.NarrationText != "" && slide.NarrationAudio != "" {
				if s.fileExists(slide.NarrationAudio) {
					audioFiles = append(audioFiles, slide.NarrationAudio)
				}
			}
		}
	}

	// Debug: Print slide order
	fmt.Printf("🎵 Generating audio for %d slides (starting from index %d):\n", len(slides), startSlideIndex)
	for i, slide := range slides {
		fmt.Printf("  Slide %d: SlideNumber=%d, Title=%s, HasNarration=%t\n",
			i+1, slide.SlideNumber, slide.Title, slide.NarrationText != "")
	}

	// Generate audio for each slide starting from resume point
	for i := startSlideIndex; i < len(slides); i++ {
		slide := slides[i]

		// Update progress
		job.Progress = (i * 100) / totalSlides
		s.store.UpdateJob(job)

		if slide.NarrationText == "" {
			fmt.Printf("Slide %d has no narration, skipping\n", slide.SlideNumber)
			continue // Skip slides without narration
		}

		// Check if audio already exists (resume case)
		if slide.NarrationAudio != "" && s.fileExists(slide.NarrationAudio) {
			fmt.Printf("Slide %d already has audio, skipping\n", slide.SlideNumber)
			audioFiles = append(audioFiles, slide.NarrationAudio)
			continue
		}

		fmt.Printf("Generating audio for slide %d/%d\n", i+1, totalSlides)

		// Generate audio file with pause processing
		audioPath, err := s.generateSlideAudioWithPauses(projectID, slide, audioDir)
		if err != nil {
			job.Status = "failed"
			job.ErrorMsg = err.Error()
			s.store.UpdateJob(job)
			// Don't set project to "failed", use "audio_failed" to preserve narration_ready state
			project.Status = "audio_failed"
			project.ErrorMessage = fmt.Sprintf("Failed at slide %d: %s", slide.SlideNumber, err.Error())
			s.store.UpdateProject(project)
			return fmt.Errorf("failed to generate audio for slide %d: %w", slide.SlideNumber, err)
		}

		// Update slide with audio path
		slide.NarrationAudio = audioPath
		if err := s.store.UpdateSlide(slide); err != nil {
			return fmt.Errorf("failed to update slide with audio path: %w", err)
		}

		audioFiles = append(audioFiles, audioPath)
		fmt.Printf("✅ Completed audio for slide %d/%d\n", i+1, totalSlides)
	}

	// Debug: Print audio files order before combining
	fmt.Printf("🎵 Audio files to combine (%d files):\n", len(audioFiles))
	for i, audioFile := range audioFiles {
		fmt.Printf("  %d: %s\n", i+1, audioFile)
	}

	// Generate combined audio file
	combinedAudioPath := filepath.Join(audioDir, "combined.mp3")
	if err := s.audioProcessor.CombineAudioFiles(audioFiles, combinedAudioPath); err != nil {
		// Log detailed error information
		log.Printf("=== AUDIO COMBINATION ERROR ===")
		log.Printf("Project ID: %s", projectID)
		log.Printf("Audio files count: %d", len(audioFiles))
		log.Printf("Output path: %s", combinedAudioPath)
		log.Printf("Error: %v", err)

		// Log individual audio files for debugging
		for i, audioFile := range audioFiles {
			log.Printf("Audio file %d: %s", i+1, audioFile)
			if stat, statErr := os.Stat(audioFile); statErr == nil {
				log.Printf("  Size: %d bytes", stat.Size())
				log.Printf("  Modified: %s", stat.ModTime().Format(time.RFC3339))
			} else {
				log.Printf("  Error getting file info: %v", statErr)
			}
		}
		log.Printf("=== END AUDIO ERROR ===")

		// Update job and project status to indicate audio combination failure
		job.Status = "failed"
		job.ErrorMsg = fmt.Sprintf("Audio combination failed: %v", err)
		s.store.UpdateJob(job)
		project.Status = "audio_failed"
		project.ErrorMessage = fmt.Sprintf("Failed to combine %d audio files: %v", len(audioFiles), err)
		s.store.UpdateProject(project)
		return fmt.Errorf("failed to combine audio files: %w", err)
	}

	// Update project with audio path
	project.AudioPath = combinedAudioPath
	project.Status = "audio_ready"
	if err := s.store.UpdateProject(project); err != nil {
		return fmt.Errorf("failed to update project with audio path: %w", err)
	}

	// Complete job
	job.Status = "completed"
	job.Progress = 100
	if err := s.store.UpdateJob(job); err != nil {
		return fmt.Errorf("failed to complete job: %w", err)
	}

	return nil
}

// generateSlideAudioWithPauses generates audio for a single slide with pause processing
func (s *TTSService) generateSlideAudioWithPauses(projectID string, slide *models.PPTSlide, audioDir string) (string, error) {
	if slide.NarrationText == "" {
		return "", fmt.Errorf("no narration text for slide %d", slide.SlideNumber)
	}

	// Create slide-specific directory for segments
	slideDir := filepath.Join(audioDir, fmt.Sprintf("slide_%03d", slide.SlideNumber))
	if err := os.MkdirAll(slideDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create slide directory: %w", err)
	}

	// Create audio generation function based on provider
	var generateAudioFunc func(string) ([]byte, error)
	switch s.config.TTSProvider {
	case "minimax":
		generateAudioFunc = func(text string) ([]byte, error) {
			return s.minimaxTTSClient.GenerateAudio(context.Background(), text)
		}
	case "openai":
		generateAudioFunc = func(text string) ([]byte, error) {
			return s.generateOpenAIAudioData(text)
		}
	case "easyvoice":
		generateAudioFunc = func(text string) ([]byte, error) {
			return s.easyvoiceTTSClient.GenerateAudio(context.Background(), text)
		}
	default:
		return "", fmt.Errorf("unsupported TTS provider: %s", s.config.TTSProvider)
	}

	// Process text with pauses and generate audio segments with retry
	retryConfig := DefaultRetryConfig()
	// Customize retry config from configuration
	retryConfig.MaxRetries = s.config.TTSMaxRetries
	retryConfig.BaseDelay = time.Duration(s.config.TTSBaseDelay) * time.Second
	retryConfig.MaxDelay = time.Duration(s.config.TTSMaxDelay) * time.Second
	retryConfig.BackoffFactor = s.config.TTSBackoffFactor

	audioFiles, err := s.audioProcessor.ProcessTextToAudioSegmentsWithRetry(slide.NarrationText, slideDir, generateAudioFunc, retryConfig)
	if err != nil {
		return "", fmt.Errorf("failed to process text segments: %w", err)
	}

	// Combine all segments into final audio file
	finalAudioPath := filepath.Join(audioDir, fmt.Sprintf("slide_%03d.mp3", slide.SlideNumber))
	if err := s.audioProcessor.CombineAudioFiles(audioFiles, finalAudioPath); err != nil {
		// Log detailed error information for slide audio combination
		log.Printf("=== SLIDE AUDIO COMBINATION ERROR ===")
		log.Printf("Slide Number: %d", slide.SlideNumber)
		log.Printf("Audio segments count: %d", len(audioFiles))
		log.Printf("Output path: %s", finalAudioPath)
		log.Printf("Error: %v", err)

		// Log individual audio segments for debugging
		for i, audioFile := range audioFiles {
			log.Printf("Audio segment %d: %s", i+1, audioFile)
			if stat, statErr := os.Stat(audioFile); statErr == nil {
				log.Printf("  Size: %d bytes", stat.Size())
			} else {
				log.Printf("  Error getting file info: %v", statErr)
			}
		}
		log.Printf("=== END SLIDE AUDIO ERROR ===")

		return "", fmt.Errorf("failed to combine audio segments for slide %d: %w", slide.SlideNumber, err)
	}

	return finalAudioPath, nil
}

// generateSlideAudio generates audio for a single slide (legacy method for backward compatibility)
func (s *TTSService) generateSlideAudio(projectID string, slide *models.PPTSlide, audioDir string) (string, error) {
	if slide.NarrationText == "" {
		return "", fmt.Errorf("no narration text for slide %d", slide.SlideNumber)
	}

	// Create audio file path
	audioFileName := fmt.Sprintf("slide_%03d.mp3", slide.SlideNumber)
	audioPath := filepath.Join(audioDir, audioFileName)

	// Use appropriate TTS provider
	switch s.config.TTSProvider {
	case "openai":
		return s.generateOpenAIAudio(slide.NarrationText, audioPath)
	case "minimax":
		return s.generateMinimaxAudio(slide.NarrationText, audioPath)
	case "easyvoice":
		return s.generateEasyVoiceAudio(slide.NarrationText, audioPath)
	default:
		return "", fmt.Errorf("unsupported TTS provider: %s", s.config.TTSProvider)
	}
}

// generateOpenAIAudio generates audio using OpenAI TTS API
func (s *TTSService) generateOpenAIAudio(text, outputPath string) (string, error) {
	// Create TTS request
	req := openai.CreateSpeechRequest{
		Model: openai.TTSModel1,
		Input: text,
		Voice: openai.VoiceAlloy, // Default voice, can be configured
		Speed: s.config.TTSSpeed,
	}

	// Override voice if configured
	if s.config.TTSVoice != "" {
		switch s.config.TTSVoice {
		case "alloy":
			req.Voice = openai.VoiceAlloy
		case "echo":
			req.Voice = openai.VoiceEcho
		case "fable":
			req.Voice = openai.VoiceFable
		case "onyx":
			req.Voice = openai.VoiceOnyx
		case "nova":
			req.Voice = openai.VoiceNova
		case "shimmer":
			req.Voice = openai.VoiceShimmer
		}
	}

	// Call OpenAI TTS API
	response, err := s.openaiClient.CreateSpeech(context.Background(), req)
	if err != nil {
		return "", fmt.Errorf("failed to create speech: %w", err)
	}
	defer response.Close()

	// Create output file
	file, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("failed to create audio file: %w", err)
	}
	defer file.Close()

	// Copy audio data to file
	_, err = io.Copy(file, response)
	if err != nil {
		return "", fmt.Errorf("failed to write audio data: %w", err)
	}

	return outputPath, nil
}

// generateMinimaxAudio generates audio using MiniMax TTS API
func (s *TTSService) generateMinimaxAudio(text, outputPath string) (string, error) {
	if s.minimaxTTSClient == nil {
		return "", fmt.Errorf("MiniMax TTS client not initialized")
	}

	// Generate audio data
	audioData, err := s.minimaxTTSClient.GenerateAudio(context.Background(), text)
	if err != nil {
		return "", fmt.Errorf("failed to generate MiniMax audio: %w", err)
	}

	// Save audio data to file
	if err := s.audioProcessor.SaveAudioData(audioData, outputPath); err != nil {
		return "", fmt.Errorf("failed to save audio file: %w", err)
	}

	return outputPath, nil
}

// generateOpenAIAudioData generates audio data using OpenAI TTS API (returns bytes)
func (s *TTSService) generateOpenAIAudioData(text string) ([]byte, error) {
	if s.openaiClient == nil {
		return nil, fmt.Errorf("OpenAI client not initialized")
	}

	// Create TTS request
	req := openai.CreateSpeechRequest{
		Model: openai.TTSModel1,
		Input: text,
		Voice: openai.VoiceAlloy, // Default voice, can be configured
		Speed: s.config.TTSSpeed,
	}

	// Override voice if configured
	if s.config.TTSVoice != "" {
		switch s.config.TTSVoice {
		case "alloy":
			req.Voice = openai.VoiceAlloy
		case "echo":
			req.Voice = openai.VoiceEcho
		case "fable":
			req.Voice = openai.VoiceFable
		case "onyx":
			req.Voice = openai.VoiceOnyx
		case "nova":
			req.Voice = openai.VoiceNova
		case "shimmer":
			req.Voice = openai.VoiceShimmer
		}
	}

	// Call OpenAI TTS API
	response, err := s.openaiClient.CreateSpeech(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("failed to create speech: %w", err)
	}
	defer response.Close()

	// Read all audio data
	audioData, err := io.ReadAll(response)
	if err != nil {
		return nil, fmt.Errorf("failed to read audio data: %w", err)
	}

	return audioData, nil
}

// findAudioResumePoint finds the starting point for audio generation resume
func (s *TTSService) findAudioResumePoint(slides []*models.PPTSlide, audioDir string) int {
	for i, slide := range slides {
		// Skip slides without narration
		if slide.NarrationText == "" {
			continue
		}

		// Check if audio file exists and is valid
		if slide.NarrationAudio == "" || !s.fileExists(slide.NarrationAudio) {
			return i // Resume from first slide without valid audio
		}
	}
	return len(slides) // All slides have audio
}

// fileExists checks if a file exists and has content
func (s *TTSService) fileExists(path string) bool {
	if path == "" {
		return false
	}
	info, err := os.Stat(path)
	return err == nil && info.Size() > 0
}

// GetAudioProgress returns the progress of audio generation
func (s *TTSService) GetAudioProgress(projectID string) (*AudioProgress, error) {
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get slides: %w", err)
	}

	progress := &AudioProgress{
		TotalSlides:     0,
		CompletedSlides: 0,
		CurrentSlide:    0,
		CanResume:       false,
		SlideDetails:    make([]AudioSlideProgress, 0),
	}

	for i, slide := range slides {
		// Only count slides with narration
		if slide.NarrationText == "" {
			continue
		}

		progress.TotalSlides++
		hasAudio := slide.NarrationAudio != "" && s.fileExists(slide.NarrationAudio)

		slideProgress := AudioSlideProgress{
			SlideNumber:  slide.SlideNumber,
			Title:        slide.Title,
			HasNarration: true,
			HasAudio:     hasAudio,
			AudioPath:    slide.NarrationAudio,
		}

		if hasAudio {
			progress.CompletedSlides++
			// Get audio file size if available
			if info, err := os.Stat(slide.NarrationAudio); err == nil {
				slideProgress.AudioSize = info.Size()
			}
		} else if progress.CurrentSlide == 0 {
			progress.CurrentSlide = i + 1
		}

		progress.SlideDetails = append(progress.SlideDetails, slideProgress)
	}

	// Can resume if some slides are completed but not all
	progress.CanResume = progress.CompletedSlides > 0 && progress.CompletedSlides < progress.TotalSlides

	if progress.CurrentSlide == 0 && progress.CompletedSlides == progress.TotalSlides {
		progress.CurrentSlide = progress.TotalSlides
	}

	return progress, nil
}

// AudioProgress represents the progress of audio generation
type AudioProgress struct {
	TotalSlides     int                   `json:"total_slides"`
	CompletedSlides int                   `json:"completed_slides"`
	CurrentSlide    int                   `json:"current_slide"`
	CanResume       bool                  `json:"can_resume"`
	SlideDetails    []AudioSlideProgress  `json:"slide_details"`
}

// AudioSlideProgress represents the progress of audio generation for a single slide
type AudioSlideProgress struct {
	SlideNumber  int    `json:"slide_number"`
	Title        string `json:"title"`
	HasNarration bool   `json:"has_narration"`
	HasAudio     bool   `json:"has_audio"`
	AudioPath    string `json:"audio_path"`
	AudioSize    int64  `json:"audio_size"`
}




// ValidateAudioFile validates if an audio file exists and is readable
func (s *TTSService) ValidateAudioFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("audio file does not exist: %s", filePath)
	}

	// Check if file is readable
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("cannot read audio file: %w", err)
	}
	file.Close()

	return nil
}

// GetSlideAudio returns the audio file path for a specific slide
func (s *TTSService) GetSlideAudio(projectID string, slideNumber int) (string, error) {
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return "", fmt.Errorf("failed to get slides: %w", err)
	}

	for _, slide := range slides {
		if slide.SlideNumber == slideNumber {
			return slide.NarrationAudio, nil
		}
	}

	return "", fmt.Errorf("slide %d not found in project %s", slideNumber, projectID)
}

// generateEasyVoiceAudio generates audio using EasyVoice TTS API
func (s *TTSService) generateEasyVoiceAudio(text, outputPath string) (string, error) {
	// Generate audio data using EasyVoice client
	audioData, err := s.easyvoiceTTSClient.GenerateAudio(context.Background(), text)
	if err != nil {
		return "", fmt.Errorf("failed to generate audio with EasyVoice: %w", err)
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return "", fmt.Errorf("failed to create output directory: %w", err)
	}

	// Write audio data to file
	if err := os.WriteFile(outputPath, audioData, 0644); err != nil {
		return "", fmt.Errorf("failed to write audio file: %w", err)
	}

	log.Printf("Generated EasyVoice audio: %s", outputPath)
	return outputPath, nil
}
