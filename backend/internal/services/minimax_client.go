package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"ppt-narrator/internal/config"
	"time"
)

// MinimaxClient handles MiniMax API interactions
type MinimaxClient struct {
	config     *config.Config
	httpClient *http.Client
}

// NewMinimaxClient creates a new MiniMax client
func NewMinimaxClient(cfg *config.Config) *MinimaxClient {
	return &MinimaxClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// MinimaxMessage represents a message in the conversation (for text-only)
type MinimaxMessage struct {
	SenderType string `json:"sender_type"` // "USER" or "BOT"
	SenderName string `json:"sender_name"`
	Text       string `json:"text"`
}

// MinimaxOpenAIMessage represents OpenAI-compatible message format for vision API
type MinimaxOpenAIMessage struct {
	Role    string      `json:"role"`    // "system", "user", "assistant"
	Name    string      `json:"name,omitempty"`
	Content interface{} `json:"content"` // Can be string or []MinimaxMessagePart
}

// MinimaxMessagePart represents a part of a multimodal message
type MinimaxMessagePart struct {
	Type     string            `json:"type"`      // "text" or "image_url"
	Text     string            `json:"text,omitempty"`
	ImageURL *MinimaxImageURL  `json:"image_url,omitempty"`
}

// MinimaxImageURL represents an image URL in the message
type MinimaxImageURL struct {
	URL string `json:"url"`
}

// MinimaxRequest represents the request structure for MiniMax text API
type MinimaxRequest struct {
	Model               string           `json:"model"`
	Messages            []MinimaxMessage `json:"messages"`
	Stream              bool             `json:"stream"`
	UseStandardSSE      bool             `json:"use_standard_sse"`
	ContinueLastMessage bool             `json:"continue_last_message"`
	MaxTokens           int              `json:"max_tokens,omitempty"`
	Temperature         float64          `json:"temperature,omitempty"`
	TopP                float64          `json:"top_p,omitempty"`
	TokensToGenerate    int              `json:"tokens_to_generate,omitempty"`
	MaskSensitiveInfo   bool             `json:"mask_sensitive_info,omitempty"`
}

// MinimaxOpenAIRequest represents the request structure for MiniMax Vision API (OpenAI-compatible)
type MinimaxOpenAIRequest struct {
	Model       string                  `json:"model"`
	Messages    []MinimaxOpenAIMessage  `json:"messages"`
	MaxTokens   int                     `json:"max_tokens,omitempty"`
	Temperature float64                 `json:"temperature,omitempty"`
}

// MinimaxChoice represents a choice in the response
type MinimaxChoice struct {
	Messages     []MinimaxMessage `json:"messages"`
	Index        int              `json:"index"`
	FinishReason string           `json:"finish_reason"`
}

// MinimaxUsage represents token usage information
type MinimaxUsage struct {
	TotalTokens int `json:"total_tokens"`
}

// MinimaxResponse represents the response from MiniMax API
type MinimaxResponse struct {
	ID                  string          `json:"id"`
	Object              string          `json:"object"`
	Created             int64           `json:"created"`
	Model               string          `json:"model"`
	Choices             []MinimaxChoice `json:"choices"`
	Usage               MinimaxUsage    `json:"usage"`
	InputSensitive      bool            `json:"input_sensitive"`
	OutputSensitive     bool            `json:"output_sensitive"`
	InputSensitiveInfo  interface{}     `json:"input_sensitive_info"`
	OutputSensitiveInfo interface{}     `json:"output_sensitive_info"`
	BaseResp            struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
}

// ChatCompletion sends a chat completion request to MiniMax API with retry mechanism
func (c *MinimaxClient) ChatCompletion(ctx context.Context, systemPrompt, userPrompt string) (string, error) {
	// Prepare messages
	messages := []MinimaxMessage{
		{
			SenderType: "USER",
			SenderName: "用户",
			Text:       systemPrompt + "\n\n" + userPrompt,
		},
	}

	return c.chatCompletionWithRetry(ctx, messages)
}

// ChatCompletionWithImage sends a chat completion request with image to MiniMax API
func (c *MinimaxClient) ChatCompletionWithImage(ctx context.Context, systemPrompt, userPrompt, imagePath string) (string, error) {
	// Read and encode the image
	imageData, err := c.encodeImageToBase64(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to encode image: %w", err)
	}

	// Prepare messages with image using OpenAI-compatible format
	messages := []MinimaxOpenAIMessage{
		{
			Role:    "system",
			Content: systemPrompt,
		},
		{
			Role: "user",
			Name: "用户",
			Content: []MinimaxMessagePart{
				{
					Type: "text",
					Text: userPrompt,
				},
				{
					Type: "image_url",
					ImageURL: &MinimaxImageURL{
						URL: fmt.Sprintf("data:image/jpeg;base64,%s", imageData),
					},
				},
			},
		},
	}

	return c.chatCompletionOpenAIWithRetry(ctx, messages)
}

// chatCompletionWithRetry performs the actual API call with retry mechanism
func (c *MinimaxClient) chatCompletionWithRetry(ctx context.Context, messages []MinimaxMessage) (string, error) {
	maxRetries := 3
	baseDelay := time.Second

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// Exponential backoff with jitter
			delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(attempt-1)))
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(delay):
			}
		}

		result, err := c.performChatCompletion(ctx, messages)
		if err == nil {
			return result, nil
		}

		// Check if error is retryable
		if !c.isRetryableError(err) {
			return "", err
		}

		if attempt == maxRetries-1 {
			return "", fmt.Errorf("max retries exceeded, last error: %w", err)
		}
	}

	return "", fmt.Errorf("unexpected error in retry loop")
}

// chatCompletionOpenAIWithRetry performs the OpenAI-compatible API call with retry mechanism
func (c *MinimaxClient) chatCompletionOpenAIWithRetry(ctx context.Context, messages []MinimaxOpenAIMessage) (string, error) {
	maxRetries := 3
	baseDelay := time.Second

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// Exponential backoff with jitter
			delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(attempt-1)))
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(delay):
			}
		}

		result, err := c.performOpenAIChatCompletion(ctx, messages)
		if err == nil {
			return result, nil
		}

		// Check if error is retryable
		if !c.isRetryableError(err) {
			return "", err
		}

		if attempt == maxRetries-1 {
			return "", fmt.Errorf("max retries exceeded, last error: %w", err)
		}
	}

	return "", fmt.Errorf("unexpected error in retry loop")
}



// performChatCompletion performs the actual API call without retry
func (c *MinimaxClient) performChatCompletion(ctx context.Context, messages []MinimaxMessage) (string, error) {
	// Prepare request
	request := MinimaxRequest{
		Model:               c.config.MinimaxModel,
		Messages:            messages,
		Stream:              false,
		UseStandardSSE:      false,
		ContinueLastMessage: false,
		MaxTokens:           c.config.MaxTokens,
		Temperature:         c.config.Temperature,
		TopP:                0.95,
		TokensToGenerate:    c.config.MaxTokens,
		MaskSensitiveInfo:   false,
	}

	// Convert to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.MinimaxBaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.MinimaxAPIKey)
	if c.config.MinimaxGroupID != "" {
		req.Header.Set("GroupId", c.config.MinimaxGroupID)
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	// Parse response
	var minimaxResp MinimaxResponse
	if err := json.Unmarshal(responseBody, &minimaxResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Check API response status
	if minimaxResp.BaseResp.StatusCode != 0 {
		return "", fmt.Errorf("MiniMax API error: %s (code: %d)", minimaxResp.BaseResp.StatusMsg, minimaxResp.BaseResp.StatusCode)
	}

	// Extract response text
	if len(minimaxResp.Choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	choice := minimaxResp.Choices[0]
	if len(choice.Messages) == 0 {
		return "", fmt.Errorf("no messages in choice")
	}

	// Find the bot's response
	for _, message := range choice.Messages {
		if message.SenderType == "BOT" {
			return message.Text, nil
		}
	}

	return "", fmt.Errorf("no bot response found")
}

// performOpenAIChatCompletion performs the actual OpenAI-compatible API call without retry
func (c *MinimaxClient) performOpenAIChatCompletion(ctx context.Context, messages []MinimaxOpenAIMessage) (string, error) {
	// Prepare request using OpenAI-compatible format
	request := MinimaxOpenAIRequest{
		Model:       c.config.MinimaxModel,
		Messages:    messages,
		MaxTokens:   c.config.MaxTokens,
		Temperature: c.config.Temperature,
	}

	// Convert to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request - use the same endpoint as text API
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.MinimaxBaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.MinimaxAPIKey)
	if c.config.MinimaxGroupID != "" {
		req.Header.Set("GroupId", c.config.MinimaxGroupID)
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	// Parse response - try OpenAI-compatible format first
	var openaiResp struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Error *struct {
			Message string `json:"message"`
			Code    int    `json:"code"`
		} `json:"error"`
	}

	if err := json.Unmarshal(responseBody, &openaiResp); err == nil {
		if openaiResp.Error != nil {
			return "", fmt.Errorf("MiniMax API error: %s (code: %d)", openaiResp.Error.Message, openaiResp.Error.Code)
		}
		if len(openaiResp.Choices) > 0 {
			return openaiResp.Choices[0].Message.Content, nil
		}
	}

	// Fallback to MiniMax format
	var minimaxResp MinimaxResponse
	if err := json.Unmarshal(responseBody, &minimaxResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Check API response status
	if minimaxResp.BaseResp.StatusCode != 0 {
		return "", fmt.Errorf("MiniMax API error: %s (code: %d)", minimaxResp.BaseResp.StatusMsg, minimaxResp.BaseResp.StatusCode)
	}

	// Extract response text
	if len(minimaxResp.Choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	choice := minimaxResp.Choices[0]
	if len(choice.Messages) == 0 {
		return "", fmt.Errorf("no messages in choice")
	}

	// Find the bot's response
	for _, message := range choice.Messages {
		if message.SenderType == "BOT" {
			return message.Text, nil
		}
	}

	return "", fmt.Errorf("no bot response found")
}

// ChatCompletionWithMessages sends a chat completion request with multiple messages (with retry)
func (c *MinimaxClient) ChatCompletionWithMessages(ctx context.Context, messages []MinimaxMessage) (string, error) {
	return c.chatCompletionWithRetry(ctx, messages)
}

// isRetryableError determines if an error is retryable
func (c *MinimaxClient) isRetryableError(err error) bool {
	// Check for network errors, timeouts, and certain HTTP status codes
	if err == nil {
		return false
	}

	errStr := err.Error()

	// Network-related errors
	if contains(errStr, "timeout") || contains(errStr, "connection") ||
	   contains(errStr, "network") || contains(errStr, "EOF") {
		return true
	}

	// HTTP status codes that are retryable
	if contains(errStr, "status 429") || // Rate limit
	   contains(errStr, "status 500") || // Internal server error
	   contains(errStr, "status 502") || // Bad gateway
	   contains(errStr, "status 503") || // Service unavailable
	   contains(errStr, "status 504") {  // Gateway timeout
		return true
	}

	// MiniMax API specific errors that might be retryable
	if contains(errStr, "rate limit") || contains(errStr, "quota") {
		return true
	}

	return false
}

// encodeImageToBase64 reads an image file and encodes it to base64
func (c *MinimaxClient) encodeImageToBase64(imagePath string) (string, error) {
	// Open the image file
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	// Read the file content
	imageBytes, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read image file: %w", err)
	}

	// Encode to base64
	encoded := base64.StdEncoding.EncodeToString(imageBytes)
	return encoded, nil
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
		 (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
		  findSubstring(s, substr))))
}

// findSubstring performs a simple substring search
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// ValidateConfig validates MiniMax configuration
func (c *MinimaxClient) ValidateConfig() error {
	if c.config.MinimaxAPIKey == "" {
		return fmt.Errorf("MiniMax API key is required")
	}
	if c.config.MinimaxModel == "" {
		return fmt.Errorf("MiniMax model is required")
	}
	if c.config.MinimaxBaseURL == "" {
		return fmt.Errorf("MiniMax base URL is required")
	}
	return nil
}
