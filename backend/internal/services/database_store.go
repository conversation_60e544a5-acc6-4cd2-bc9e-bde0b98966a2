package services

import (
	"fmt"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/utils"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// DatabaseStoreService provides CRUD operations for database storage
type DatabaseStoreService struct {
	db *gorm.DB
}

// NewDatabaseStoreService creates a new database store service
func NewDatabaseStoreService(db *gorm.DB) *DatabaseStoreService {
	return &DatabaseStoreService{
		db: db,
	}
}

// Project operations
func (s *DatabaseStoreService) CreateProject(name, description string) (*models.PPTProject, error) {
	project := &models.PPTProject{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Status:      "created",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(project).Error; err != nil {
		return nil, fmt.Errorf("failed to create project: %w", err)
	}

	return project, nil
}

func (s *DatabaseStoreService) GetProject(id string) (*models.PPTProject, error) {
	var project models.PPTProject

	if err := s.db.Preload("Slides").Preload("Memories").First(&project, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("project not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	return &project, nil
}

func (s *DatabaseStoreService) UpdateProject(project *models.PPTProject) error {
	project.UpdatedAt = time.Now()

	if err := s.db.Save(project).Error; err != nil {
		return fmt.Errorf("failed to update project: %w", err)
	}

	return nil
}

func (s *DatabaseStoreService) ListProjects() ([]*models.PPTProject, error) {
	var projects []*models.PPTProject

	if err := s.db.Preload("Slides").Preload("Memories").Find(&projects).Error; err != nil {
		return nil, fmt.Errorf("failed to list projects: %w", err)
	}

	return projects, nil
}

func (s *DatabaseStoreService) DeleteProject(id string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete related records first
		if err := tx.Where("project_id = ?", id).Delete(&models.PPTSlide{}).Error; err != nil {
			return fmt.Errorf("failed to delete slides: %w", err)
		}

		if err := tx.Where("project_id = ?", id).Delete(&models.AIMemory{}).Error; err != nil {
			return fmt.Errorf("failed to delete memories: %w", err)
		}

		if err := tx.Where("project_id = ?", id).Delete(&models.ProcessingJob{}).Error; err != nil {
			return fmt.Errorf("failed to delete jobs: %w", err)
		}

		// Delete the project
		if err := tx.Delete(&models.PPTProject{}, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to delete project: %w", err)
		}

		return nil
	})
}

// Slide operations
func (s *DatabaseStoreService) CreateSlide(projectID string, slideNumber int, title, content string) (*models.PPTSlide, error) {
	slide := &models.PPTSlide{
		ID:          uuid.New().String(),
		ProjectID:   projectID,
		SlideNumber: slideNumber,
		Title:       title,
		Content:     content,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(slide).Error; err != nil {
		return nil, fmt.Errorf("failed to create slide: %w", err)
	}

	return slide, nil
}

func (s *DatabaseStoreService) GetSlide(id string) (*models.PPTSlide, error) {
	var slide models.PPTSlide

	if err := s.db.First(&slide, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("slide not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get slide: %w", err)
	}

	return &slide, nil
}

func (s *DatabaseStoreService) UpdateSlide(slide *models.PPTSlide) error {
	slide.UpdatedAt = time.Now()

	if err := s.db.Save(slide).Error; err != nil {
		return fmt.Errorf("failed to update slide: %w", err)
	}

	return nil
}

func (s *DatabaseStoreService) GetSlidesByProject(projectID string) ([]*models.PPTSlide, error) {
	var slides []*models.PPTSlide

	if err := s.db.Where("project_id = ?", projectID).Order("slide_number").Find(&slides).Error; err != nil {
		return nil, fmt.Errorf("failed to get slides: %w", err)
	}

	return slides, nil
}

// Memory operations
func (s *DatabaseStoreService) AddMemory(projectID, key, value, memoryType string) (*models.AIMemory, error) {
	if memoryType == "" {
		memoryType = "context"
	}

	// Clean UTF-8 strings to prevent encoding errors
	cleanKey := utils.CleanUTF8String(key)
	cleanValue := utils.CleanUTF8String(value)
	cleanMemoryType := utils.CleanUTF8String(memoryType)

	memory := &models.AIMemory{
		ID:          uuid.New().String(),
		ProjectID:   projectID,
		MemoryKey:   cleanKey,
		MemoryValue: cleanValue,
		MemoryType:  cleanMemoryType,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(memory).Error; err != nil {
		return nil, fmt.Errorf("failed to add memory: %w", err)
	}

	return memory, nil
}

func (s *DatabaseStoreService) GetMemory(projectID, key string) (*models.AIMemory, error) {
	var memory models.AIMemory

	if err := s.db.Where("project_id = ? AND memory_key = ?", projectID, key).First(&memory).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("memory not found for project %s with key %s", projectID, key)
		}
		return nil, fmt.Errorf("failed to get memory: %w", err)
	}

	return &memory, nil
}

func (s *DatabaseStoreService) GetMemoriesByProject(projectID string) ([]*models.AIMemory, error) {
	var memories []*models.AIMemory

	if err := s.db.Where("project_id = ?", projectID).Find(&memories).Error; err != nil {
		return nil, fmt.Errorf("failed to get memories: %w", err)
	}

	return memories, nil
}

func (s *DatabaseStoreService) UpdateMemory(memory *models.AIMemory) error {
	memory.UpdatedAt = time.Now()

	if err := s.db.Save(memory).Error; err != nil {
		return fmt.Errorf("failed to update memory: %w", err)
	}

	return nil
}

func (s *DatabaseStoreService) DeleteMemory(id string) error {
	if err := s.db.Delete(&models.AIMemory{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete memory: %w", err)
	}

	return nil
}

// Job operations
func (s *DatabaseStoreService) CreateJob(projectID, jobType string) (*models.ProcessingJob, error) {
	job := &models.ProcessingJob{
		ID:        uuid.New().String(),
		ProjectID: projectID,
		JobType:   jobType,
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.Create(job).Error; err != nil {
		return nil, fmt.Errorf("failed to create job: %w", err)
	}

	return job, nil
}

func (s *DatabaseStoreService) GetJob(id string) (*models.ProcessingJob, error) {
	var job models.ProcessingJob

	if err := s.db.First(&job, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("job not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	return &job, nil
}

func (s *DatabaseStoreService) UpdateJob(job *models.ProcessingJob) error {
	job.UpdatedAt = time.Now()

	if err := s.db.Save(job).Error; err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	return nil
}
