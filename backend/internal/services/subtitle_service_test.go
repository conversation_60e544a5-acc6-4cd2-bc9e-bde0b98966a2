package services

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
)

// MockStoreService for testing
type MockStoreService struct {
	projects map[string]*models.PPTProject
	slides   map[string][]*models.PPTSlide
}

func NewMockStoreService() *MockStoreService {
	return &MockStoreService{
		projects: make(map[string]*models.PPTProject),
		slides:   make(map[string][]*models.PPTSlide),
	}
}

func (m *MockStoreService) GetProject(projectID string) (*models.PPTProject, error) {
	if project, exists := m.projects[projectID]; exists {
		return project, nil
	}
	return nil, nil
}

func (m *MockStoreService) GetSlidesByProject(projectID string) ([]*models.PPTSlide, error) {
	if slides, exists := m.slides[projectID]; exists {
		return slides, nil
	}
	return []*models.PPTSlide{}, nil
}

func (m *MockStoreService) AddProject(project *models.PPTProject) {
	m.projects[project.ID] = project
}

func (m *MockStoreService) AddSlides(projectID string, slides []*models.PPTSlide) {
	m.slides[projectID] = slides
}

func TestSubtitleService_removePauseMarkers(t *testing.T) {
	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Remove single pause marker",
			input:    "Hello [停顿1秒] world",
			expected: "Hello  world",
		},
		{
			name:     "Remove multiple pause markers",
			input:    "Hello [停顿1秒] world [停顿2秒] test [停顿0.5秒] end",
			expected: "Hello  world  test  end",
		},
		{
			name:     "No pause markers",
			input:    "Hello world test",
			expected: "Hello world test",
		},
		{
			name:     "Decimal pause markers",
			input:    "Start [停顿1.5秒] middle [停顿2.3秒] end",
			expected: "Start  middle  end",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.removePauseMarkers(tt.input)
			if result != tt.expected {
				t.Errorf("removePauseMarkers() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSubtitleService_splitIntoSentences(t *testing.T) {
	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "Chinese sentences",
			input:    "这是第一句。这是第二句！这是第三句？",
			expected: []string{"这是第一句", "这是第二句", "这是第三句"},
		},
		{
			name:     "English sentences",
			input:    "This is first. This is second! This is third?",
			expected: []string{"This is first", "This is second", "This is third"},
		},
		{
			name:     "Mixed punctuation",
			input:    "Hello world. How are you? I'm fine!",
			expected: []string{"Hello world", "How are you", "I'm fine"},
		},
		{
			name:     "Single sentence",
			input:    "This is a single sentence.",
			expected: []string{"This is a single sentence"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.splitIntoSentences(tt.input)
			if len(result) != len(tt.expected) {
				t.Errorf("splitIntoSentences() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			for i, sentence := range result {
				if sentence != tt.expected[i] {
					t.Errorf("splitIntoSentences()[%d] = %v, want %v", i, sentence, tt.expected[i])
				}
			}
		})
	}
}

func TestSubtitleService_splitSentenceForSubtitle(t *testing.T) {
	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "Short sentence",
			input:    "Hello world",
			expected: []string{"Hello world"},
		},
		{
			name:     "Long sentence that needs splitting",
			input:    "This is a very long sentence that should be split into multiple lines for better readability",
			expected: []string{"This is a very long\nsentence that should be\nsplit into multiple\nlines for better\nreadability"},
		},
		{
			name:     "Chinese sentence",
			input:    "这是一个很长的中文句子需要被分割成多行来提高可读性和观看体验",
			expected: []string{"这是一个很长的中文句子需要被分割成多行来提高可读性和观看体验"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.splitSentenceForSubtitle(tt.input)
			if len(result) != len(tt.expected) {
				t.Errorf("splitSentenceForSubtitle() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			for i, line := range result {
				if line != tt.expected[i] {
					t.Errorf("splitSentenceForSubtitle()[%d] = %v, want %v", i, line, tt.expected[i])
				}
			}
		})
	}
}

func TestSubtitleService_formatSRTTime(t *testing.T) {
	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	tests := []struct {
		name     string
		duration time.Duration
		expected string
	}{
		{
			name:     "Zero duration",
			duration: 0,
			expected: "00:00:00,000",
		},
		{
			name:     "1 second",
			duration: time.Second,
			expected: "00:00:01,000",
		},
		{
			name:     "1 minute 30 seconds 500ms",
			duration: time.Minute + 30*time.Second + 500*time.Millisecond,
			expected: "00:01:30,500",
		},
		{
			name:     "1 hour 5 minutes 15 seconds 250ms",
			duration: time.Hour + 5*time.Minute + 15*time.Second + 250*time.Millisecond,
			expected: "01:05:15,250",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.formatSRTTime(tt.duration)
			if result != tt.expected {
				t.Errorf("formatSRTTime() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSubtitleService_estimateAudioDuration(t *testing.T) {
	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	tests := []struct {
		name     string
		text     string
		expected time.Duration
	}{
		{
			name:     "Short text",
			text:     "Hello",
			expected: time.Duration(float64(5) / 3.0 * float64(time.Second)),
		},
		{
			name:     "Text with pause markers",
			text:     "Hello [停顿1秒] world",
			expected: time.Duration(float64(10) / 3.0 * float64(time.Second)), // "Hello world" = 10 chars
		},
		{
			name:     "Chinese text",
			text:     "你好世界",
			expected: time.Duration(float64(4) / 3.0 * float64(time.Second)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.estimateAudioDuration(tt.text)
			// Allow some tolerance for floating point comparison
			if result < tt.expected-time.Millisecond*10 || result > tt.expected+time.Millisecond*10 {
				t.Errorf("estimateAudioDuration() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSubtitleService_GenerateSubtitleFile(t *testing.T) {
	// Create temporary directory for test
	tempDir, err := os.MkdirTemp("", "subtitle_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cfg := &config.Config{}
	store := NewMockStoreService()
	service := NewSubtitleService(cfg, store)

	// Create test project and slides
	project := &models.PPTProject{
		ID:       "test-project",
		Filename: "test.pptx",
	}
	store.AddProject(project)

	slides := []*models.PPTSlide{
		{
			ID:             "slide1",
			ProjectID:      "test-project",
			SlideNumber:    1,
			NarrationText:  "这是第一张幻灯片的内容。[停顿1秒]请仔细观看。",
			NarrationAudio: "", // No audio file for testing
		},
		{
			ID:             "slide2",
			ProjectID:      "test-project",
			SlideNumber:    2,
			NarrationText:  "这是第二张幻灯片。包含更多详细信息！",
			NarrationAudio: "",
		},
	}
	store.AddSlides("test-project", slides)

	// Generate subtitle file
	outputPath := filepath.Join(tempDir, "test_subtitles.srt")
	err = service.GenerateSubtitleFile("test-project", outputPath)
	if err != nil {
		t.Fatalf("GenerateSubtitleFile() error = %v", err)
	}

	// Verify file was created
	if !service.fileExists(outputPath) {
		t.Errorf("Subtitle file was not created at %s", outputPath)
		return
	}

	// Read and verify file content
	content, err := os.ReadFile(outputPath)
	if err != nil {
		t.Fatalf("Failed to read subtitle file: %v", err)
	}

	contentStr := string(content)
	
	// Check for SRT format elements
	if !strings.Contains(contentStr, "1\n") {
		t.Errorf("Subtitle file should contain segment index '1'")
	}
	
	if !strings.Contains(contentStr, "-->") {
		t.Errorf("Subtitle file should contain time separator '-->'")
	}
	
	if !strings.Contains(contentStr, "这是第一张幻灯片的内容") {
		t.Errorf("Subtitle file should contain slide text")
	}
	
	// Check time format
	if !strings.Contains(contentStr, "00:00:00,000") {
		t.Errorf("Subtitle file should contain proper time format")
	}
}
