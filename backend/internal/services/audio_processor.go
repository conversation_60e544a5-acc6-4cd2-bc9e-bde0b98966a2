package services

import (
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
)

// AudioProcessor handles audio processing tasks
type AudioProcessor struct {
	ffmpegPath       string
	tempDir          string
	pauseSpeedFactor float64 // Factor to adjust pause durations
}

// RetryConfig defines retry behavior for audio generation
type RetryConfig struct {
	MaxRetries      int           // Maximum number of retries
	BaseDelay       time.Duration // Base delay between retries
	MaxDelay        time.Duration // Maximum delay between retries
	BackoffFactor   float64       // Exponential backoff factor
	JitterEnabled   bool          // Whether to add random jitter
}

// DefaultRetryConfig returns default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    5,
		BaseDelay:     2 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
	}
}

// NewAudioProcessor creates a new audio processor
func NewAudioProcessor(ffmpegPath, tempDir string) *AudioProcessor {
	return &AudioProcessor{
		ffmpegPath:       ffmpegPath,
		tempDir:          tempDir,
		pauseSpeedFactor: 0.6, // Default to 60% of original pause time
	}
}

// NewAudioProcessorWithConfig creates a new audio processor with configuration
func NewAudioProcessorWithConfig(ffmpegPath, tempDir string, pauseSpeedFactor float64) *AudioProcessor {
	return &AudioProcessor{
		ffmpegPath:       ffmpegPath,
		tempDir:          tempDir,
		pauseSpeedFactor: pauseSpeedFactor,
	}
}

// TextSegment represents a text segment with optional pause
type TextSegment struct {
	Text          string
	PauseDuration float64 // in seconds
}

// ParseTextWithPauses parses text and extracts pause markers
func (ap *AudioProcessor) ParseTextWithPauses(text string) []TextSegment {
	// Regular expression to match pause markers like [停顿1秒], [停顿2秒], [停顿1.5秒]
	pauseRegex := regexp.MustCompile(`\[停顿(\d+(?:\.\d+)?)秒\]`)

	var segments []TextSegment
	lastIndex := 0

	// Find all pause markers
	matches := pauseRegex.FindAllStringSubmatchIndex(text, -1)

	for _, match := range matches {
		// Extract text before the pause marker
		beforeText := strings.TrimSpace(text[lastIndex:match[0]])
		if beforeText != "" && ap.isValidTextSegment(beforeText) {
			segments = append(segments, TextSegment{
				Text:          beforeText,
				PauseDuration: 0,
			})
		}
		
		// Extract pause duration
		durationStr := text[match[2]:match[3]]
		duration, err := strconv.ParseFloat(durationStr, 64)
		if err != nil {
			duration = 0.5 // default to 0.5 second if parsing fails
		}

		// Apply configurable pause speed factor
		duration = duration * ap.pauseSpeedFactor
		
		// Add pause segment
		segments = append(segments, TextSegment{
			Text:          "",
			PauseDuration: duration,
		})
		
		lastIndex = match[1]
	}
	
	// Add remaining text after the last pause marker
	if lastIndex < len(text) {
		remainingText := strings.TrimSpace(text[lastIndex:])
		if remainingText != "" && ap.isValidTextSegment(remainingText) {
			segments = append(segments, TextSegment{
				Text:          remainingText,
				PauseDuration: 0,
			})
		}
	}

	// If no pause markers found, return the entire text as one segment
	if len(segments) == 0 {
		cleanText := strings.TrimSpace(text)
		if cleanText != "" && ap.isValidTextSegment(cleanText) {
			segments = append(segments, TextSegment{
				Text:          cleanText,
				PauseDuration: 0,
			})
		}
	}
	
	return segments
}

// isValidTextSegment checks if a text segment is valid for TTS generation
func (ap *AudioProcessor) isValidTextSegment(text string) bool {
	// Trim whitespace
	text = strings.TrimSpace(text)

	// Empty text is not valid
	if text == "" {
		return false
	}

	// Count meaningful characters (letters, digits, Chinese characters)
	meaningfulChars := 0
	for _, r := range text {
		if unicode.IsLetter(r) || unicode.IsDigit(r) {
			meaningfulChars++
		}
	}

	// Must have at least 1 meaningful character to be worth generating audio
	// This filters out segments that are only punctuation or symbols
	if meaningfulChars == 0 {
		return false
	}

	// For segments that are only punctuation or very short with no meaningful content, skip them
	// This handles cases like "。", "，", "！" etc.
	// But allow single meaningful characters like "在", "好" etc.
	if meaningfulChars == 0 {
		return false
	}

	// Skip very short segments that are mostly punctuation
	if meaningfulChars == 1 && len([]rune(text)) > 2 {
		// Single meaningful char with lots of punctuation, probably not worth it
		return false
	}

	return true
}

// GenerateSilence creates a natural pause with subtle ambient sound
func (ap *AudioProcessor) GenerateSilence(duration float64, outputPath string) error {
	if duration <= 0 {
		return fmt.Errorf("duration must be positive")
	}

	return ap.GenerateNaturalPause(duration, outputPath)
}

// GenerateNaturalPause creates a natural-sounding pause with subtle breathing and room tone
func (ap *AudioProcessor) GenerateNaturalPause(duration float64, outputPath string) error {
	if duration <= 0 {
		return fmt.Errorf("duration must be positive")
	}

	// Create different types of pauses based on duration (adjusted for faster playback)
	if duration <= 0.3 {
		return ap.generateShortPause(duration, outputPath)
	} else if duration <= 1.2 {
		return ap.generateMediumPause(duration, outputPath)
	} else {
		return ap.generateLongPause(duration, outputPath)
	}
}

// generateShortPause creates a short, subtle pause
func (ap *AudioProcessor) generateShortPause(duration float64, outputPath string) error {
	// Very subtle room tone for short pauses
	cmd := exec.Command(ap.ffmpegPath,
		"-f", "lavfi",
		"-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", duration)+":sample_rate=32000:amplitude=0.005:color=pink",
		"-af", "highpass=f=200,lowpass=f=2000,volume=0.02,afade=t=in:ss=0:d=0.1,afade=t=out:st=0:d=0.1",
		"-c:a", "mp3",
		"-b:a", "64k",
		"-y",
		outputPath,
	)

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to generate short pause: %w", err)
	}

	return nil
}

// generateMediumPause creates a medium pause with subtle breathing
func (ap *AudioProcessor) generateMediumPause(duration float64, outputPath string) error {
	// Add very subtle breathing sound for medium pauses
	tempFiles := []string{}
	defer func() {
		for _, file := range tempFiles {
			os.Remove(file)
		}
	}()

	// Generate base room tone
	roomTonePath := filepath.Join(ap.tempDir, fmt.Sprintf("room_tone_%d.mp3", time.Now().UnixNano()))
	tempFiles = append(tempFiles, roomTonePath)

	cmd1 := exec.Command(ap.ffmpegPath,
		"-f", "lavfi",
		"-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", duration)+":sample_rate=32000:amplitude=0.008:color=pink",
		"-af", "highpass=f=150,lowpass=f=3000,volume=0.03",
		"-c:a", "mp3",
		"-b:a", "64k",
		"-y",
		roomTonePath,
	)

	if err := cmd1.Run(); err != nil {
		return fmt.Errorf("failed to generate room tone: %w", err)
	}

	// Generate subtle breathing if pause is long enough
	if duration > 1.0 {
		breathPath := filepath.Join(ap.tempDir, fmt.Sprintf("breath_%d.mp3", time.Now().UnixNano()))
		tempFiles = append(tempFiles, breathPath)

		// Position breathing sound in the middle of the pause
		breathDuration := 0.3

		cmd2 := exec.Command(ap.ffmpegPath,
			"-f", "lavfi",
			"-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", breathDuration)+":sample_rate=32000:amplitude=0.01:color=brown",
			"-af", fmt.Sprintf("highpass=f=80,lowpass=f=800,volume=0.08,afade=t=in:ss=0:d=0.1,afade=t=out:st=0:d=0.1"),
			"-c:a", "mp3",
			"-b:a", "64k",
			"-y",
			breathPath,
		)

		if err := cmd2.Run(); err != nil {
			fmt.Printf("Warning: failed to generate breathing sound: %v\n", err)
		} else {
			// Mix room tone with breathing
			breathStart := duration * 0.4
			cmd3 := exec.Command(ap.ffmpegPath,
				"-i", roomTonePath,
				"-i", breathPath,
				"-filter_complex", fmt.Sprintf("[0:a][1:a]amix=inputs=2:duration=first:dropout_transition=0,adelay=%d|%d",
					int(breathStart*1000), int(breathStart*1000)),
				"-c:a", "mp3",
				"-b:a", "64k",
				"-y",
				outputPath,
			)

			if err := cmd3.Run(); err != nil {
				return fmt.Errorf("failed to mix pause audio: %w", err)
			}
			return nil
		}
	}

	// If breathing generation failed or not needed, just use room tone
	return ap.copyFile(roomTonePath, outputPath)
}

// generateLongPause creates a longer pause with multiple breathing sounds
func (ap *AudioProcessor) generateLongPause(duration float64, outputPath string) error {
	// For long pauses, add multiple subtle breathing sounds
	tempFiles := []string{}
	defer func() {
		for _, file := range tempFiles {
			os.Remove(file)
		}
	}()

	// Generate base room tone
	roomTonePath := filepath.Join(ap.tempDir, fmt.Sprintf("long_room_tone_%d.mp3", time.Now().UnixNano()))
	tempFiles = append(tempFiles, roomTonePath)

	cmd := exec.Command(ap.ffmpegPath,
		"-f", "lavfi",
		"-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", duration)+":sample_rate=32000:amplitude=0.01:color=pink",
		"-af", "highpass=f=120,lowpass=f=4000,volume=0.04,afade=t=in:ss=0:d=0.2,afade=t=out:st=0:d=0.2",
		"-c:a", "mp3",
		"-b:a", "64k",
		"-y",
		roomTonePath,
	)

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to generate long room tone: %w", err)
	}

	// Add 1-2 breathing sounds for very long pauses
	numBreaths := 1
	if duration > 4.0 {
		numBreaths = 2
	}

	filterInputs := "[0:a]"
	mixInputs := "inputs=1"

	for i := 0; i < numBreaths; i++ {
		breathPath := filepath.Join(ap.tempDir, fmt.Sprintf("long_breath_%d_%d.mp3", time.Now().UnixNano(), i))
		tempFiles = append(tempFiles, breathPath)

		// Position breaths at different points
		breathDuration := 0.4

		breathCmd := exec.Command(ap.ffmpegPath,
			"-f", "lavfi",
			"-i", "anoisesrc=duration="+fmt.Sprintf("%.2f", breathDuration)+":sample_rate=32000:amplitude=0.012:color=brown",
			"-af", "highpass=f=60,lowpass=f=1200,volume=0.1,afade=t=in:ss=0:d=0.15,afade=t=out:st=0:d=0.15",
			"-c:a", "mp3",
			"-b:a", "64k",
			"-y",
			breathPath,
		)

		if breathCmd.Run() == nil {
			filterInputs += fmt.Sprintf("[%d:a]", i+1)
			mixInputs = fmt.Sprintf("inputs=%d", i+2)
		}
	}

	// Build final command with all inputs
	finalArgs := []string{"-i", roomTonePath}
	for _, file := range tempFiles[1:] { // Skip room tone as it's already added
		if ap.fileExists(file) {
			finalArgs = append(finalArgs, "-i", file)
		}
	}

	finalArgs = append(finalArgs,
		"-filter_complex", filterInputs+"amix="+mixInputs+":duration=first:dropout_transition=0",
		"-c:a", "mp3",
		"-b:a", "64k",
		"-y",
		outputPath,
	)

	finalCmd := exec.Command(ap.ffmpegPath, finalArgs...)

	if err := finalCmd.Run(); err != nil {
		// Fallback to just room tone if mixing fails
		return ap.copyFile(roomTonePath, outputPath)
	}

	return nil
}

// CombineAudioFiles combines multiple audio files into one with natural transitions
func (ap *AudioProcessor) CombineAudioFiles(inputFiles []string, outputPath string) error {
	if len(inputFiles) == 0 {
		return fmt.Errorf("no input files provided")
	}

	// Validate all input files first
	fmt.Printf("🔍 Validating %d input audio files...\n", len(inputFiles))
	for i, inputFile := range inputFiles {
		if err := ap.validateAudioFile(inputFile); err != nil {
			fmt.Printf("⚠️  Input file %d validation failed: %v\n", i+1, err)
			// Continue with other files, don't fail completely
		}
	}

	var err error
	if len(inputFiles) == 1 {
		// If only one file, apply natural processing and copy
		err = ap.processAndCopyFile(inputFiles[0], outputPath)
	} else {
		// Use advanced audio mixing with crossfades and natural effects
		err = ap.combineWithNaturalTransitions(inputFiles, outputPath)
	}

	if err != nil {
		return err
	}

	// Validate the output file
	if validateErr := ap.validateAudioFile(outputPath); validateErr != nil {
		return fmt.Errorf("output audio validation failed: %w", validateErr)
	}

	fmt.Printf("✅ Audio combination completed successfully: %s\n", outputPath)
	return nil
}

// processAndCopyFile processes a single audio file with natural effects
func (ap *AudioProcessor) processAndCopyFile(inputPath, outputPath string) error {
	// Try with filters first
	cmd := exec.Command(ap.ffmpegPath,
		"-i", inputPath,
		"-af", ap.buildSimpleNaturalFilter(),
		"-c:a", "mp3",
		"-b:a", "128k",
		"-y",
		outputPath,
	)

	// Capture output for debugging
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Warning: processing with filters failed (%v), trying direct copy\n", err)
		fmt.Printf("FFmpeg output: %s\n", string(output))
		// Fallback: direct copy without any processing
		return ap.copyFileWithoutProcessing(inputPath, outputPath)
	}

	return nil
}

// copyFileWithoutProcessing copies audio file without any processing
func (ap *AudioProcessor) copyFileWithoutProcessing(inputPath, outputPath string) error {
	cmd := exec.Command(ap.ffmpegPath,
		"-i", inputPath,
		"-c", "copy", // Direct copy without re-encoding
		"-y",
		outputPath,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to copy audio file: %w, output: %s", err, string(output))
	}

	return nil
}

// combineWithNaturalTransitions combines multiple audio files with crossfades and natural effects
func (ap *AudioProcessor) combineWithNaturalTransitions(inputFiles []string, outputPath string) error {
	// Use a simpler, more reliable approach for combining audio files
	return ap.combineAudioFilesSimple(inputFiles, outputPath)
}

// combineAudioFilesSimple combines audio files using a simple concat method
func (ap *AudioProcessor) combineAudioFilesSimple(inputFiles []string, outputPath string) error {
	// Try the concat demuxer approach first
	if err := ap.combineWithConcatDemuxer(inputFiles, outputPath); err != nil {
		fmt.Printf("Warning: concat demuxer failed (%v), trying fallback method\n", err)
		// Fallback to basic concat without filters
		return ap.combineWithBasicConcat(inputFiles, outputPath)
	}
	return nil
}

// combineWithConcatDemuxer uses FFmpeg concat demuxer with audio processing
func (ap *AudioProcessor) combineWithConcatDemuxer(inputFiles []string, outputPath string) error {
	// Create a temporary file list for FFmpeg concat
	listFile := filepath.Join(ap.tempDir, fmt.Sprintf("audio_list_%d.txt", time.Now().UnixNano()))
	defer os.Remove(listFile)

	// Write the file list
	file, err := os.Create(listFile)
	if err != nil {
		return fmt.Errorf("failed to create audio list file: %w", err)
	}
	defer file.Close()

	for _, audioFile := range inputFiles {
		// Escape file path for FFmpeg
		escapedPath := strings.ReplaceAll(audioFile, "'", "'\\''")
		_, err := file.WriteString(fmt.Sprintf("file '%s'\n", escapedPath))
		if err != nil {
			return fmt.Errorf("failed to write to audio list: %w", err)
		}
	}
	file.Close()

	// Try with filters first
	cmd := exec.Command(ap.ffmpegPath,
		"-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-af", ap.buildSimpleNaturalFilter(),
		"-c:a", "mp3",
		"-b:a", "128k",
		"-y",
		outputPath,
	)

	// Capture both stdout and stderr for debugging
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Warning: concat with filters failed (%v), trying without filters\n", err)
		fmt.Printf("FFmpeg output: %s\n", string(output))
		// Fallback: try without any filters
		return ap.combineWithBasicConcat(inputFiles, outputPath)
	}

	return nil
}

// combineWithBasicConcat uses basic concat without any filters as fallback
func (ap *AudioProcessor) combineWithBasicConcat(inputFiles []string, outputPath string) error {
	// Create a temporary file list for FFmpeg concat
	listFile := filepath.Join(ap.tempDir, fmt.Sprintf("basic_audio_list_%d.txt", time.Now().UnixNano()))
	defer os.Remove(listFile)

	// Write the file list
	file, err := os.Create(listFile)
	if err != nil {
		return fmt.Errorf("failed to create basic audio list file: %w", err)
	}
	defer file.Close()

	for _, audioFile := range inputFiles {
		// Use absolute path and escape properly
		absPath, err := filepath.Abs(audioFile)
		if err != nil {
			absPath = audioFile
		}
		// Simple escaping for Windows/Unix compatibility
		escapedPath := strings.ReplaceAll(absPath, "\\", "/")
		_, err = file.WriteString(fmt.Sprintf("file '%s'\n", escapedPath))
		if err != nil {
			return fmt.Errorf("failed to write to basic audio list: %w", err)
		}
	}
	file.Close()

	// Use very basic FFmpeg concat without any filters
	cmd := exec.Command(ap.ffmpegPath,
		"-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-c", "copy", // Just copy without re-encoding
		"-y",
		outputPath,
	)

	// Capture both stdout and stderr for debugging
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("basic ffmpeg concat failed: %w, output: %s", err, string(output))
	}

	return nil
}

// buildSimpleNaturalFilter creates a simpler audio filter for natural voice processing
func (ap *AudioProcessor) buildSimpleNaturalFilter() string {
	// Minimal filter chain to preserve audio content
	filters := []string{
		// Only basic volume boost - no normalization that might cause issues
		"volume=1.5",
	}

	return strings.Join(filters, ",")
}

// buildNaturalVoiceFilter creates an audio filter for natural voice processing
func (ap *AudioProcessor) buildNaturalVoiceFilter() string {
	// Build a comprehensive filter chain for natural voice
	filters := []string{
		// 1. Gentle fade in/out to avoid clicks
		"afade=t=in:ss=0:d=0.1",
		"afade=t=out:st=0:d=0.1",

		// 2. Dynamic range compression (like natural speech)
		"acompressor=threshold=0.1:ratio=3:attack=5:release=50",

		// 3. Subtle EQ to enhance voice naturalness
		"equalizer=f=200:width_type=h:width=50:g=2", // Boost low-mid for warmth
		"equalizer=f=3000:width_type=h:width=1000:g=1", // Slight presence boost

		// 4. Add very subtle room tone/ambience
		"highpass=f=80", // Remove very low frequencies
		"lowpass=f=8000", // Remove harsh highs

		// 5. Gentle normalization
		"loudnorm=I=-16:TP=-1.5:LRA=11",
	}

	return strings.Join(filters, ",")
}

// buildNaturalMixingFilter creates a complex filter for mixing multiple audio files naturally
func (ap *AudioProcessor) buildNaturalMixingFilter(inputFiles []string) string {
	if len(inputFiles) <= 1 {
		return "[0:a]" + ap.buildNaturalVoiceFilter() + "[final]"
	}

	var filterParts []string
	var currentLabel string = "[0:a]"

	// Process each input with natural voice effects
	for i := 0; i < len(inputFiles); i++ {
		processedLabel := fmt.Sprintf("[processed%d]", i)
		filterParts = append(filterParts,
			fmt.Sprintf("[%d:a]%s%s", i, ap.buildNaturalVoiceFilter(), processedLabel))
	}

	// Create crossfade transitions between segments
	currentLabel = "[processed0]"
	for i := 1; i < len(inputFiles); i++ {
		nextLabel := fmt.Sprintf("[processed%d]", i)
		mixedLabel := fmt.Sprintf("[mixed%d]", i)

		// Determine crossfade duration based on file types
		crossfadeDuration := ap.determineCrossfadeDuration(inputFiles[i-1], inputFiles[i])

		// Add crossfade between current and next
		filterParts = append(filterParts,
			fmt.Sprintf("%s%s acrossfade=d=%s:c1=tri:c2=tri%s",
				currentLabel, nextLabel, crossfadeDuration, mixedLabel))

		currentLabel = mixedLabel
	}

	// Add final processing and breathing effects
	filterParts = append(filterParts,
		fmt.Sprintf("%s%s[final]", currentLabel, ap.buildFinalNaturalProcessing()))

	return strings.Join(filterParts, ";")
}

// determineCrossfadeDuration determines appropriate crossfade duration based on audio types
func (ap *AudioProcessor) determineCrossfadeDuration(file1, file2 string) string {
	// Check if either file is a pause/silence
	if strings.Contains(file1, "pause_") || strings.Contains(file2, "pause_") {
		return "0.15" // Shorter crossfade for pauses (reduced from 0.3)
	}

	// Check if transitioning between speech segments
	if strings.Contains(file1, "segment_") && strings.Contains(file2, "segment_") {
		return "0.08" // Very short crossfade between speech (reduced from 0.15)
	}

	return "0.12" // Default crossfade duration (reduced from 0.2)
}

// buildFinalNaturalProcessing creates final processing for natural speech
func (ap *AudioProcessor) buildFinalNaturalProcessing() string {
	filters := []string{
		// Add subtle reverb for natural room sound
		"aecho=0.8:0.88:20:0.4",

		// Final gentle compression
		"acompressor=threshold=0.05:ratio=2:attack=10:release=80",

		// Final normalization
		"loudnorm=I=-18:TP=-2:LRA=7",

		// Very gentle final fade
		"afade=t=in:ss=0:d=0.05",
		"afade=t=out:st=0:d=0.05",
	}

	return "," + strings.Join(filters, ",")
}

// generateBreathingSound creates subtle breathing sounds for natural pauses
func (ap *AudioProcessor) generateBreathingSound() string {
	breathingPath := filepath.Join(ap.tempDir, fmt.Sprintf("breathing_%d.mp3", time.Now().UnixNano()))

	// Generate very subtle breathing/room tone
	cmd := exec.Command(ap.ffmpegPath,
		"-f", "lavfi",
		"-i", "anoisesrc=duration=0.5:sample_rate=32000:amplitude=0.01:color=pink",
		"-af", "highpass=f=100,lowpass=f=1000,volume=0.05",
		"-c:a", "mp3",
		"-b:a", "64k",
		"-y",
		breathingPath,
	)

	if err := cmd.Run(); err != nil {
		fmt.Printf("Warning: failed to generate breathing sound: %v\n", err)
		return ""
	}

	return breathingPath
}

// copyFile copies a file from src to dst
func (ap *AudioProcessor) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer sourceFile.Close()
	
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer destFile.Close()
	
	_, err = sourceFile.WriteTo(destFile)
	if err != nil {
		return fmt.Errorf("failed to copy file: %w", err)
	}
	
	return nil
}

// SaveAudioData saves audio data to a file
func (ap *AudioProcessor) SaveAudioData(audioData []byte, outputPath string) error {
	if len(audioData) == 0 {
		return fmt.Errorf("no audio data provided")
	}
	
	// Ensure directory exists
	dir := filepath.Dir(outputPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}
	
	// Write audio data to file
	if err := os.WriteFile(outputPath, audioData, 0644); err != nil {
		return fmt.Errorf("failed to write audio file: %w", err)
	}
	
	return nil
}

// ProcessTextToAudioSegments processes text with pauses and returns audio file paths
func (ap *AudioProcessor) ProcessTextToAudioSegments(text string, outputDir string, generateAudioFunc func(string) ([]byte, error)) ([]string, error) {
	return ap.ProcessTextToAudioSegmentsWithRetry(text, outputDir, generateAudioFunc, DefaultRetryConfig())
}

// ProcessTextToAudioSegmentsWithRetry processes text with pauses, retry logic, and resume capability
func (ap *AudioProcessor) ProcessTextToAudioSegmentsWithRetry(text string, outputDir string, generateAudioFunc func(string) ([]byte, error), retryConfig *RetryConfig) ([]string, error) {
	// Parse text into segments
	segments := ap.ParseTextWithPauses(text)

	var audioFiles []string
	segmentIndex := 0

	// Create progress file for resume capability
	progressFile := filepath.Join(outputDir, "progress.txt")
	startIndex := ap.loadProgress(progressFile)

	fmt.Printf("Processing %d segments, starting from segment %d\n", len(segments), startIndex)

	for i, segment := range segments {
		// Skip already processed segments
		if i < startIndex {
			// Still need to add the file paths for already processed segments
			if segment.Text != "" {
				audioPath := filepath.Join(outputDir, fmt.Sprintf("segment_%03d.mp3", segmentIndex))
				if ap.fileExists(audioPath) {
					audioFiles = append(audioFiles, audioPath)
				}
				segmentIndex++
			}
			if segment.PauseDuration > 0 {
				silencePath := filepath.Join(outputDir, fmt.Sprintf("pause_%03d.mp3", segmentIndex))
				if ap.fileExists(silencePath) {
					audioFiles = append(audioFiles, silencePath)
				}
				segmentIndex++
			}
			continue
		}

		if segment.Text != "" {
			audioPath := filepath.Join(outputDir, fmt.Sprintf("segment_%03d.mp3", segmentIndex))

			// Check if file already exists (resume capability)
			if ap.fileExists(audioPath) {
				fmt.Printf("Segment %d already exists, skipping\n", segmentIndex)
				audioFiles = append(audioFiles, audioPath)
				segmentIndex++
				ap.saveProgress(progressFile, i+1)
				continue
			}

			// Generate audio for text segment with retry
			var audioData []byte
			err := ap.retryWithBackoff(retryConfig, func() error {
				var genErr error
				audioData, genErr = generateAudioFunc(segment.Text)
				return genErr
			})

			if err != nil {
				return nil, fmt.Errorf("failed to generate audio for segment %d after retries: %w", i, err)
			}

			// Save audio data
			if err := ap.SaveAudioData(audioData, audioPath); err != nil {
				return nil, fmt.Errorf("failed to save audio segment %d: %w", i, err)
			}

			audioFiles = append(audioFiles, audioPath)
			segmentIndex++

			fmt.Printf("Generated audio segment %d/%d\n", i+1, len(segments))
		}

		if segment.PauseDuration > 0 {
			silencePath := filepath.Join(outputDir, fmt.Sprintf("pause_%03d.mp3", segmentIndex))

			// Check if silence file already exists
			if ap.fileExists(silencePath) {
				fmt.Printf("Pause %d already exists, skipping\n", segmentIndex)
				audioFiles = append(audioFiles, silencePath)
				segmentIndex++
				ap.saveProgress(progressFile, i+1)
				continue
			}

			// Generate silence for pause with retry
			err := ap.retryWithBackoff(retryConfig, func() error {
				return ap.GenerateSilence(segment.PauseDuration, silencePath)
			})

			if err != nil {
				return nil, fmt.Errorf("failed to generate silence for segment %d after retries: %w", i, err)
			}

			audioFiles = append(audioFiles, silencePath)
			segmentIndex++

			fmt.Printf("Generated pause segment %d/%d (%.1fs)\n", i+1, len(segments), segment.PauseDuration)
		}

		// Save progress after each successful segment
		ap.saveProgress(progressFile, i+1)
	}

	// Clean up progress file on successful completion
	os.Remove(progressFile)

	return audioFiles, nil
}

// fileExists checks if a file exists and has content
func (ap *AudioProcessor) fileExists(path string) bool {
	info, err := os.Stat(path)
	return err == nil && info.Size() > 0
}

// validateAudioFile checks if an audio file has actual content
func (ap *AudioProcessor) validateAudioFile(filepath string) error {
	if !ap.fileExists(filepath) {
		return fmt.Errorf("audio file does not exist or is empty: %s", filepath)
	}

	// Check file size
	stat, err := os.Stat(filepath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	if stat.Size() < 100 { // Less than 100 bytes is likely empty
		return fmt.Errorf("audio file is too small (likely empty): %d bytes", stat.Size())
	}

	// Use ffprobe to check if file has audio content
	cmd := exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=duration", "-of", "csv=p=0", filepath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// If ffprobe fails, just check file size (ffprobe might not be available)
		fmt.Printf("Warning: ffprobe failed for %s, relying on file size check\n", filepath)
		return nil
	}

	duration := strings.TrimSpace(string(output))
	if duration == "" || duration == "0.000000" || duration == "N/A" {
		return fmt.Errorf("audio file has no duration or is invalid")
	}

	fmt.Printf("✅ Audio file validated: %s (size: %d bytes, duration: %s seconds)\n",
		filepath, stat.Size(), duration)
	return nil
}

// saveProgress saves the current progress to a file
func (ap *AudioProcessor) saveProgress(progressFile string, segmentIndex int) {
	os.WriteFile(progressFile, []byte(fmt.Sprintf("%d", segmentIndex)), 0644)
}

// loadProgress loads the progress from a file
func (ap *AudioProcessor) loadProgress(progressFile string) int {
	data, err := os.ReadFile(progressFile)
	if err != nil {
		return 0 // Start from beginning if no progress file
	}

	index, err := strconv.Atoi(strings.TrimSpace(string(data)))
	if err != nil {
		return 0
	}

	return index
}

// isRetryableError checks if an error is retryable (rate limit, network issues, etc.)
func (ap *AudioProcessor) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// Check for rate limit errors
	if strings.Contains(errStr, "rate limit") ||
	   strings.Contains(errStr, "too many requests") ||
	   strings.Contains(errStr, "1002") { // MiniMax rate limit error code
		return true
	}

	// Check for network errors
	if strings.Contains(errStr, "connection") ||
	   strings.Contains(errStr, "timeout") ||
	   strings.Contains(errStr, "network") ||
	   strings.Contains(errStr, "temporary") {
		return true
	}

	// Check for server errors (5xx)
	if strings.Contains(errStr, "500") ||
	   strings.Contains(errStr, "502") ||
	   strings.Contains(errStr, "503") ||
	   strings.Contains(errStr, "504") {
		return true
	}

	return false
}

// calculateRetryDelay calculates the delay for the next retry attempt
func (ap *AudioProcessor) calculateRetryDelay(config *RetryConfig, attempt int) time.Duration {
	if attempt <= 0 {
		return config.BaseDelay
	}

	// Exponential backoff
	delay := time.Duration(float64(config.BaseDelay) *
		(config.BackoffFactor * float64(attempt)))

	// Cap at maximum delay
	if delay > config.MaxDelay {
		delay = config.MaxDelay
	}

	// Add jitter to avoid thundering herd
	if config.JitterEnabled {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
		delay += jitter
	}

	return delay
}

// retryWithBackoff executes a function with retry logic
func (ap *AudioProcessor) retryWithBackoff(config *RetryConfig, operation func() error) error {
	var lastErr error

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		if attempt > 0 {
			delay := ap.calculateRetryDelay(config, attempt)
			fmt.Printf("Retrying in %v (attempt %d/%d)...\n", delay, attempt, config.MaxRetries)
			time.Sleep(delay)
		}

		err := operation()
		if err == nil {
			return nil // Success
		}

		lastErr = err

		// Check if error is retryable
		if !ap.isRetryableError(err) {
			return fmt.Errorf("non-retryable error: %w", err)
		}

		fmt.Printf("Retryable error (attempt %d/%d): %v\n", attempt+1, config.MaxRetries+1, err)
	}

	return fmt.Errorf("max retries exceeded: %w", lastErr)
}
