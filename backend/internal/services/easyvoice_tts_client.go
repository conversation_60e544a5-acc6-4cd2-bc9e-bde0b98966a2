package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"ppt-narrator/internal/config"
	"strings"
	"time"
	"unicode"
)

// EasyVoiceTTSClient handles EasyVoice TTS API requests
type EasyVoiceTTSClient struct {
	config     *config.Config
	httpClient *http.Client
	baseURL    string
}

// EasyVoiceTTSRequest represents the request structure for EasyVoice TTS API
type EasyVoiceTTSRequest struct {
	Text   string `json:"text"`
	Voice  string `json:"voice"`
	Rate   string `json:"rate"`
	Pitch  string `json:"pitch"`
	Volume string `json:"volume"`
}

// EasyVoiceTTSResponse represents the response structure from EasyVoice TTS API
type EasyVoiceTTSResponse struct {
	Success bool                `json:"success"`
	Data    EasyVoiceTTSData    `json:"data"`
	Code    int                 `json:"code"`
	Message string              `json:"message,omitempty"`
}

// EasyVoiceTTSData represents the audio data in the response
type EasyVoiceTTSData struct {
	Audio string `json:"audio"`
	SRT   string `json:"srt"`
	File  string `json:"file"`
}

// NewEasyVoiceTTSClient creates a new EasyVoice TTS client
func NewEasyVoiceTTSClient(cfg *config.Config) *EasyVoiceTTSClient {
	return &EasyVoiceTTSClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		baseURL: cfg.EasyVoiceAPIURL,
	}
}

// ValidateConfig validates the EasyVoice TTS configuration
func (c *EasyVoiceTTSClient) ValidateConfig() error {
	if c.config.EasyVoiceAPIURL == "" {
		return fmt.Errorf("EASYVOICE_API_URL is required")
	}
	if c.config.EasyVoiceUsername == "" {
		return fmt.Errorf("EASYVOICE_USERNAME is required")
	}
	if c.config.EasyVoicePassword == "" {
		return fmt.Errorf("EASYVOICE_PASSWORD is required")
	}
	return nil
}

// GenerateAudio generates audio from text using EasyVoice TTS API
func (c *EasyVoiceTTSClient) GenerateAudio(ctx context.Context, text string) ([]byte, error) {
	if err := c.ValidateConfig(); err != nil {
		return nil, err
	}

	// EasyVoice requires at least 5 characters, pad short text with spaces and period
	processedText := c.padTextIfNeeded(text)

	// Prepare request
	request := EasyVoiceTTSRequest{
		Text:   processedText,
		Voice:  c.config.EasyVoiceVoice,
		Rate:   c.config.EasyVoiceRate,
		Pitch:  c.config.EasyVoicePitch,
		Volume: c.config.EasyVoiceVolume,
	}

	// Convert to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(c.config.EasyVoiceUsername, c.config.EasyVoicePassword)

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	// Parse response
	var ttsResponse EasyVoiceTTSResponse
	if err := json.Unmarshal(responseBody, &ttsResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check if request was successful
	if !ttsResponse.Success || ttsResponse.Code != 200 {
		return nil, fmt.Errorf("TTS generation failed: %s (code: %d)", ttsResponse.Message, ttsResponse.Code)
	}

	// Download the audio file
	audioURL, err := c.buildAudioURL(ttsResponse.Data.Audio)
	if err != nil {
		return nil, fmt.Errorf("failed to build audio URL: %w", err)
	}

	audioData, err := c.downloadAudioFile(ctx, audioURL)
	if err != nil {
		return nil, fmt.Errorf("failed to download audio file: %w", err)
	}

	return audioData, nil
}

// buildAudioURL constructs the full URL for downloading the audio file
func (c *EasyVoiceTTSClient) buildAudioURL(audioPath string) (string, error) {
	// Parse the base URL to get the host
	baseURL, err := url.Parse(c.baseURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse base URL: %w", err)
	}

	// Construct the download URL
	// The audio path is relative, so we need to construct the full URL
	downloadURL := fmt.Sprintf("%s://%s%s", baseURL.Scheme, baseURL.Host, audioPath)
	
	return downloadURL, nil
}

// downloadAudioFile downloads the audio file from the given URL
func (c *EasyVoiceTTSClient) downloadAudioFile(ctx context.Context, audioURL string) ([]byte, error) {
	// Create download request
	req, err := http.NewRequestWithContext(ctx, "GET", audioURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create download request: %w", err)
	}

	// Send download request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send download request: %w", err)
	}
	defer resp.Body.Close()

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("audio download failed with status %d", resp.StatusCode)
	}

	// Read audio data
	audioData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read audio data: %w", err)
	}

	return audioData, nil
}

// padTextIfNeeded pads text to meet EasyVoice minimum length requirement (5 characters)
func (c *EasyVoiceTTSClient) padTextIfNeeded(text string) string {
	// Trim whitespace first
	text = strings.TrimSpace(text)

	// If text is empty, return a default text
	if text == "" {
		return "无内容。"
	}

	// Count meaningful characters (letters, digits, Chinese characters)
	meaningfulChars := 0
	for _, r := range text {
		if unicode.IsLetter(r) || unicode.IsDigit(r) {
			meaningfulChars++
		}
	}

	// If text has no meaningful characters (only punctuation/symbols), skip it
	if meaningfulChars == 0 {
		return "无内容。"
	}

	// Count total characters (including Chinese characters)
	textRunes := []rune(text)
	textLength := len(textRunes)

	// EasyVoice requires at least 5 characters
	minLength := 5

	if textLength >= minLength {
		return text
	}

	// For very short meaningful text, pad with spaces and add period
	paddingNeeded := minLength - textLength

	// Add spaces and a period
	paddedText := text
	for i := 0; i < paddingNeeded-1; i++ {
		paddedText += " "
	}
	paddedText += "。"

	return paddedText
}

// GetSupportedVoices returns a list of supported voices for EasyVoice
func (c *EasyVoiceTTSClient) GetSupportedVoices() []string {
	// Common Azure TTS voices that are likely supported by EasyVoice
	return []string{
		"zh-CN-YunxiNeural",     // 云希 (男声)
		"zh-CN-YunyangNeural",   // 云扬 (男声)
		"zh-CN-XiaoxiaoNeural",  // 晓晓 (女声)
		"zh-CN-XiaohanNeural",   // 晓涵 (女声)
		"zh-CN-XiaomengNeural",  // 晓梦 (女声)
		"zh-CN-XiaomoNeural",    // 晓墨 (女声)
		"zh-CN-XiaoqiuNeural",   // 晓秋 (女声)
		"zh-CN-XiaoruiNeural",   // 晓睿 (女声)
		"zh-CN-XiaoshuangNeural", // 晓双 (女声)
		"zh-CN-XiaoxuanNeural",  // 晓萱 (女声)
		"zh-CN-XiaoyanNeural",   // 晓颜 (女声)
		"zh-CN-XiaoyouNeural",   // 晓悠 (女声)
		"zh-CN-XiaozhenNeural",  // 晓甄 (女声)
		"zh-CN-YunjianNeural",   // 云健 (男声)
		"zh-CN-YunfengNeural",   // 云枫 (男声)
		"zh-CN-YunhaoNeural",    // 云皓 (男声)
		"zh-CN-YunjieNeural",    // 云杰 (男声)
		"zh-CN-YunxiaNeural",    // 云夏 (男声)
		"zh-CN-YunyeNeural",     // 云野 (男声)
		"zh-CN-YunzeNeural",     // 云泽 (男声)
	}
}
