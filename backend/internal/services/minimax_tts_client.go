package services

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"ppt-narrator/internal/config"
	"time"
)

// MinimaxTTSClient handles MiniMax TTS API requests
type MinimaxTTSClient struct {
	config     *config.Config
	httpClient *http.Client
	baseURL    string
}

// MinimaxTTSRequest represents the request structure for MiniMax TTS API
type MinimaxTTSRequest struct {
	Model        string                    `json:"model"`
	Text         string                    `json:"text"`
	Stream       bool                      `json:"stream"`
	VoiceSetting MinimaxTTSVoiceSetting    `json:"voice_setting"`
	AudioSetting MinimaxTTSAudioSetting    `json:"audio_setting"`
	OutputFormat string                    `json:"output_format,omitempty"`
}

// MinimaxTTSVoiceSetting represents voice configuration
type MinimaxTTSVoiceSetting struct {
	VoiceID string  `json:"voice_id"`
	Speed   float64 `json:"speed"`
	Vol     float64 `json:"vol"`
	Pitch   int     `json:"pitch"`
	Emotion string  `json:"emotion,omitempty"`
}

// MinimaxTTSAudioSetting represents audio configuration
type MinimaxTTSAudioSetting struct {
	SampleRate int    `json:"sample_rate"`
	Bitrate    int    `json:"bitrate"`
	Format     string `json:"format"`
	Channel    int    `json:"channel"`
}

// MinimaxTTSResponse represents the response from MiniMax TTS API
type MinimaxTTSResponse struct {
	Data      MinimaxTTSData `json:"data"`
	ExtraInfo struct {
		AudioLength    int     `json:"audio_length"`
		AudioSampleRate int    `json:"audio_sample_rate"`
		AudioSize      int     `json:"audio_size"`
		AudioBitrate   int     `json:"audio_bitrate"`
		WordCount      int     `json:"word_count"`
		AudioFormat    string  `json:"audio_format"`
		UsageCharacters int    `json:"usage_characters"`
	} `json:"extra_info"`
	TraceID  string `json:"trace_id"`
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
}

// MinimaxTTSData represents the audio data in the response
type MinimaxTTSData struct {
	Audio  string `json:"audio"`
	Status int    `json:"status"`
}

// NewMinimaxTTSClient creates a new MiniMax TTS client
func NewMinimaxTTSClient(cfg *config.Config) *MinimaxTTSClient {
	return &MinimaxTTSClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		baseURL: "https://api.minimaxi.com/v1/t2a_v2",
	}
}

// ValidateConfig validates the MiniMax TTS configuration
func (c *MinimaxTTSClient) ValidateConfig() error {
	if c.config.MinimaxTTSAPIKey == "" {
		return fmt.Errorf("MINIMAX_TTS_API_KEY is required")
	}
	if c.config.MinimaxTTSGroupID == "" {
		return fmt.Errorf("MINIMAX_TTS_GROUP_ID is required")
	}
	return nil
}

// GenerateAudio generates audio from text using MiniMax TTS API
func (c *MinimaxTTSClient) GenerateAudio(ctx context.Context, text string) ([]byte, error) {
	if err := c.ValidateConfig(); err != nil {
		return nil, err
	}

	// Prepare request
	request := MinimaxTTSRequest{
		Model:  c.config.MinimaxTTSModel,
		Text:   text,
		Stream: false,
		VoiceSetting: MinimaxTTSVoiceSetting{
			VoiceID: c.config.MinimaxTTSVoiceID,
			Speed:   c.config.TTSSpeed,
			Vol:     1.0,
			Pitch:   0,
			Emotion: c.config.MinimaxTTSEmotion,
		},
		AudioSetting: MinimaxTTSAudioSetting{
			SampleRate: 32000,
			Bitrate:    128000,
			Format:     "mp3",
			Channel:    1,
		},
		OutputFormat: "hex",
	}

	// Convert to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s?GroupId=%s", c.baseURL, c.config.MinimaxTTSGroupID)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.MinimaxTTSAPIKey)

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(responseBody))
	}

	// Parse response
	var ttsResponse MinimaxTTSResponse
	if err := json.Unmarshal(responseBody, &ttsResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check API status
	if ttsResponse.BaseResp.StatusCode != 0 {
		// Create more detailed error message for different error codes
		errorMsg := fmt.Sprintf("API error %d: %s", ttsResponse.BaseResp.StatusCode, ttsResponse.BaseResp.StatusMsg)

		// Add specific handling for rate limit errors
		if ttsResponse.BaseResp.StatusCode == 1002 {
			errorMsg = fmt.Sprintf("rate limit: %s (code: %d)", ttsResponse.BaseResp.StatusMsg, ttsResponse.BaseResp.StatusCode)
		}

		return nil, fmt.Errorf(errorMsg)
	}

	// Decode hex audio data
	audioBytes, err := hex.DecodeString(ttsResponse.Data.Audio)
	if err != nil {
		return nil, fmt.Errorf("failed to decode hex audio: %w", err)
	}

	return audioBytes, nil
}

// GenerateAudioWithPauses generates audio with pause segments
func (c *MinimaxTTSClient) GenerateAudioWithPauses(ctx context.Context, textSegments []string, pauseDurations []float64) ([][]byte, error) {
	var audioSegments [][]byte

	// Generate audio for each text segment
	for i, text := range textSegments {
		if text == "" {
			continue
		}

		audioData, err := c.GenerateAudio(ctx, text)
		if err != nil {
			return nil, fmt.Errorf("failed to generate audio for segment %d: %w", i, err)
		}

		audioSegments = append(audioSegments, audioData)

		// Add pause if not the last segment
		if i < len(pauseDurations) && pauseDurations[i] > 0 {
			pauseAudio := c.generateSilence(pauseDurations[i])
			audioSegments = append(audioSegments, pauseAudio)
		}
	}

	return audioSegments, nil
}

// generateSilence generates silent audio for the specified duration
func (c *MinimaxTTSClient) generateSilence(duration float64) []byte {
	// This is a placeholder - in a real implementation, you would generate
	// actual silent MP3 data or use FFmpeg to create silence
	// For now, we'll return an empty byte slice and handle it in the combining logic
	return []byte{}
}
