package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
)

// SubtitleService handles subtitle generation and management
type SubtitleService struct {
	config *config.Config
	store  *DatabaseStoreService
}

// NewSubtitleService creates a new subtitle service
func NewSubtitleService(config *config.Config, store *DatabaseStoreService) *SubtitleService {
	return &SubtitleService{
		config: config,
		store:  store,
	}
}

// SubtitleSegment represents a single subtitle segment
type SubtitleSegment struct {
	Index     int
	StartTime time.Duration
	EndTime   time.Duration
	Text      string
}

// GenerateSubtitleFile generates an SRT subtitle file for a project
func (s *SubtitleService) GenerateSubtitleFile(projectID string, outputPath string) error {
	// Get slides
	slides, err := s.store.GetSlidesByProject(projectID)
	if err != nil {
		return fmt.Errorf("failed to get slides: %w", err)
	}

	if len(slides) == 0 {
		return fmt.Errorf("no slides found for project %s", projectID)
	}

	// Generate subtitle segments
	segments, err := s.generateSubtitleSegments(slides)
	if err != nil {
		return fmt.Errorf("failed to generate subtitle segments: %w", err)
	}

	// Write SRT file
	return s.writeSRTFile(segments, outputPath)
}

// generateSubtitleSegments creates subtitle segments from slides
func (s *SubtitleService) generateSubtitleSegments(slides []*models.PPTSlide) ([]SubtitleSegment, error) {
	var segments []SubtitleSegment
	var currentTime time.Duration
	segmentIndex := 1

	for _, slide := range slides {
		if slide.NarrationText == "" {
			continue
		}

		// Get audio duration for this slide
		audioDuration, err := s.getAudioDuration(slide.NarrationAudio)
		if err != nil {
			// Fallback to estimated duration based on text length
			audioDuration = s.estimateAudioDuration(slide.NarrationText)
		}

		// Extract pause duration from text
		pauseDuration := s.extractPauseDuration(slide.NarrationText)

		// Adjust audio duration by subtracting pause time for subtitle timing
		effectiveAudioDuration := audioDuration - pauseDuration
		if effectiveAudioDuration < time.Second {
			effectiveAudioDuration = time.Second // Minimum duration
		}

		// Parse text and create subtitle segments
		slideSegments := s.parseTextToSubtitleSegments(slide.NarrationText, currentTime, effectiveAudioDuration, &segmentIndex)
		segments = append(segments, slideSegments...)

		currentTime += audioDuration
	}

	return segments, nil
}

// parseTextToSubtitleSegments parses narration text and creates subtitle segments
func (s *SubtitleService) parseTextToSubtitleSegments(text string, startTime, totalDuration time.Duration, segmentIndex *int) []SubtitleSegment {
	var segments []SubtitleSegment

	// Remove pause markers and split text into sentences
	cleanText := s.removePauseMarkers(text)
	sentences := s.splitIntoSentences(cleanText)

	if len(sentences) == 0 {
		return segments
	}

	// Calculate duration based on sentence length (more accurate timing)
	sentenceDurations := s.calculateSentenceDurations(sentences, totalDuration)
	currentTime := startTime

	for i, sentence := range sentences {
		if strings.TrimSpace(sentence) == "" {
			continue
		}

		sentenceDuration := sentenceDurations[i]

		// Split long sentences into multiple subtitle lines
		subtitleLines := s.splitSentenceForSubtitle(sentence)

		if len(subtitleLines) == 0 {
			continue
		}

		// Distribute sentence duration among subtitle lines
		durationPerLine := sentenceDuration / time.Duration(len(subtitleLines))

		for _, line := range subtitleLines {
			segment := SubtitleSegment{
				Index:     *segmentIndex,
				StartTime: currentTime,
				EndTime:   currentTime + durationPerLine,
				Text:      strings.TrimSpace(line),
			}
			segments = append(segments, segment)
			*segmentIndex++
			currentTime += durationPerLine
		}
	}

	return segments
}

// calculateSentenceDurations calculates duration for each sentence based on length
func (s *SubtitleService) calculateSentenceDurations(sentences []string, totalDuration time.Duration) []time.Duration {
	if len(sentences) == 0 {
		return []time.Duration{}
	}

	// Calculate total character count for all sentences
	totalChars := 0
	sentenceCharCounts := make([]int, len(sentences))

	for i, sentence := range sentences {
		charCount := len([]rune(strings.TrimSpace(sentence)))
		sentenceCharCounts[i] = charCount
		totalChars += charCount
	}

	// Distribute total duration based on character count proportion
	durations := make([]time.Duration, len(sentences))
	remainingDuration := totalDuration

	for i, charCount := range sentenceCharCounts {
		if totalChars == 0 {
			// Fallback to equal distribution
			durations[i] = totalDuration / time.Duration(len(sentences))
		} else {
			// Calculate proportional duration
			proportion := float64(charCount) / float64(totalChars)
			sentenceDuration := time.Duration(float64(totalDuration) * proportion)

			// Ensure minimum duration of 1 second per sentence
			minDuration := 1 * time.Second
			if sentenceDuration < minDuration {
				sentenceDuration = minDuration
			}

			durations[i] = sentenceDuration
			remainingDuration -= sentenceDuration
		}
	}

	// Adjust if we have remaining time due to minimum duration constraints
	if remainingDuration > 0 && len(durations) > 0 {
		// Distribute remaining time proportionally
		for i := range durations {
			if totalChars > 0 {
				proportion := float64(sentenceCharCounts[i]) / float64(totalChars)
				durations[i] += time.Duration(float64(remainingDuration) * proportion)
			}
		}
	}

	return durations
}

// removePauseMarkers removes pause markers like [停顿1秒] from text and returns total pause duration
func (s *SubtitleService) removePauseMarkers(text string) string {
	pauseRegex := regexp.MustCompile(`\[停顿\d+(?:\.\d+)?秒\]`)
	return pauseRegex.ReplaceAllString(text, "")
}

// extractPauseDuration extracts total pause duration from text
func (s *SubtitleService) extractPauseDuration(text string) time.Duration {
	pauseRegex := regexp.MustCompile(`\[停顿(\d+(?:\.\d+)?)秒\]`)
	matches := pauseRegex.FindAllStringSubmatch(text, -1)

	totalPause := 0.0
	for _, match := range matches {
		if len(match) > 1 {
			if pauseSeconds, err := strconv.ParseFloat(match[1], 64); err == nil {
				totalPause += pauseSeconds
			}
		}
	}

	return time.Duration(totalPause * float64(time.Second))
}

// splitIntoSentences splits text into sentences based on punctuation
func (s *SubtitleService) splitIntoSentences(text string) []string {
	// Split by common Chinese and English sentence endings
	sentenceRegex := regexp.MustCompile(`[。！？.!?]+`)
	sentences := sentenceRegex.Split(text, -1)
	
	var result []string
	for _, sentence := range sentences {
		trimmed := strings.TrimSpace(sentence)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	
	return result
}

// splitSentenceForSubtitle splits long sentences into subtitle-appropriate lines
func (s *SubtitleService) splitSentenceForSubtitle(sentence string) []string {
	// Optimized for Chinese text and video display
	const maxCharsPerLine = 15        // Reduced for better readability
	const maxLinesPerSubtitle = 2     // Maximum 2 lines per subtitle
	const maxChineseCharsPerLine = 12 // Even shorter for Chinese characters

	// Check if text is primarily Chinese
	chineseCharCount := s.countChineseChars(sentence)
	totalCharCount := len([]rune(sentence))
	isPrimarilyChinese := float64(chineseCharCount)/float64(totalCharCount) > 0.5

	maxChars := maxCharsPerLine
	if isPrimarilyChinese {
		maxChars = maxChineseCharsPerLine
	}

	// For Chinese text, split by characters rather than words
	if isPrimarilyChinese {
		return s.splitChineseTextForSubtitle(sentence, maxChars, maxLinesPerSubtitle)
	}

	// For English/mixed text, split by words
	words := strings.Fields(sentence)
	if len(words) == 0 {
		return []string{}
	}

	var lines []string
	var currentLine strings.Builder

	for _, word := range words {
		// Check if adding this word would exceed the line limit
		if currentLine.Len() > 0 && len([]rune(currentLine.String()))+len([]rune(word))+1 > maxChars {
			lines = append(lines, strings.TrimSpace(currentLine.String()))
			currentLine.Reset()
		}

		if currentLine.Len() > 0 {
			currentLine.WriteString(" ")
		}
		currentLine.WriteString(word)
	}

	// Add the last line
	if currentLine.Len() > 0 {
		lines = append(lines, strings.TrimSpace(currentLine.String()))
	}

	// If we have more than maxLinesPerSubtitle, split into multiple subtitles
	if len(lines) > maxLinesPerSubtitle {
		var result []string
		for i := 0; i < len(lines); i += maxLinesPerSubtitle {
			end := i + maxLinesPerSubtitle
			if end > len(lines) {
				end = len(lines)
			}
			combined := strings.Join(lines[i:end], "\n")
			result = append(result, combined)
		}
		return result
	}

	// Join lines with newline for multi-line subtitles
	if len(lines) > 1 {
		return []string{strings.Join(lines, "\n")}
	}

	return lines
}

// countChineseChars counts Chinese characters in text
func (s *SubtitleService) countChineseChars(text string) int {
	count := 0
	for _, r := range text {
		// Check if character is in Chinese Unicode ranges
		if (r >= 0x4E00 && r <= 0x9FFF) || // CJK Unified Ideographs
			(r >= 0x3400 && r <= 0x4DBF) || // CJK Extension A
			(r >= 0x20000 && r <= 0x2A6DF) || // CJK Extension B
			(r >= 0x2A700 && r <= 0x2B73F) || // CJK Extension C
			(r >= 0x2B740 && r <= 0x2B81F) || // CJK Extension D
			(r >= 0x2B820 && r <= 0x2CEAF) { // CJK Extension E
			count++
		}
	}
	return count
}

// splitChineseTextForSubtitle splits Chinese text optimally for subtitles
func (s *SubtitleService) splitChineseTextForSubtitle(text string, maxCharsPerLine, maxLinesPerSubtitle int) []string {
	runes := []rune(text)
	if len(runes) == 0 {
		return []string{}
	}

	var lines []string
	var currentLine []rune

	for _, r := range runes {
		// Check if adding this character would exceed the line limit
		if len(currentLine) >= maxCharsPerLine {
			lines = append(lines, strings.TrimSpace(string(currentLine)))
			currentLine = []rune{}
		}
		currentLine = append(currentLine, r)
	}

	// Add the last line
	if len(currentLine) > 0 {
		lines = append(lines, strings.TrimSpace(string(currentLine)))
	}

	// If we have more than maxLinesPerSubtitle, split into multiple subtitles
	if len(lines) > maxLinesPerSubtitle {
		var result []string
		for i := 0; i < len(lines); i += maxLinesPerSubtitle {
			end := i + maxLinesPerSubtitle
			if end > len(lines) {
				end = len(lines)
			}
			combined := strings.Join(lines[i:end], "\n")
			result = append(result, combined)
		}
		return result
	}

	// Join lines with newline for multi-line subtitles
	if len(lines) > 1 {
		return []string{strings.Join(lines, "\n")}
	}

	return lines
}

// getAudioDuration gets the duration of an audio file using FFprobe
func (s *SubtitleService) getAudioDuration(audioPath string) (time.Duration, error) {
	if audioPath == "" || !s.fileExists(audioPath) {
		return 0, fmt.Errorf("audio file not found: %s", audioPath)
	}

	// Use ffprobe to get accurate audio duration
	return s.getAudioDurationWithFFprobe(audioPath)
}

// estimateAudioDuration estimates audio duration based on text length
func (s *SubtitleService) estimateAudioDuration(text string) time.Duration {
	// Rough estimation: 3 characters per second for Chinese text
	cleanText := s.removePauseMarkers(text)
	charCount := len([]rune(cleanText))
	seconds := float64(charCount) / 3.0
	return time.Duration(seconds * float64(time.Second))
}

// getAudioDurationWithFFprobe gets accurate audio duration using FFprobe
func (s *SubtitleService) getAudioDurationWithFFprobe(audioPath string) (time.Duration, error) {
	// Use ffprobe to get accurate duration
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		audioPath)

	output, err := cmd.Output()
	if err != nil {
		// Fallback to file-based estimation if ffprobe fails
		return s.estimateAudioDurationFromFile(audioPath)
	}

	// Parse duration from ffprobe output
	durationStr := strings.TrimSpace(string(output))
	durationFloat, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return s.estimateAudioDurationFromFile(audioPath)
	}

	return time.Duration(durationFloat * float64(time.Second)), nil
}

// estimateAudioDurationFromFile estimates duration from audio file as fallback
func (s *SubtitleService) estimateAudioDurationFromFile(audioPath string) (time.Duration, error) {
	fileInfo, err := os.Stat(audioPath)
	if err != nil {
		return 0, err
	}

	// Better estimation based on typical audio bitrates
	// Assume 128kbps MP3 encoding: ~16KB per second
	estimatedSeconds := fileInfo.Size() / 16384
	if estimatedSeconds < 1 {
		estimatedSeconds = 1 // Minimum 1 second
	}

	return time.Duration(estimatedSeconds) * time.Second, nil
}

// fileExists checks if a file exists
func (s *SubtitleService) fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// writeSRTFile writes subtitle segments to an SRT file
func (s *SubtitleService) writeSRTFile(segments []SubtitleSegment, outputPath string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(outputPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create SRT file: %w", err)
	}
	defer file.Close()

	for _, segment := range segments {
		// Write SRT format:
		// Index
		// StartTime --> EndTime
		// Text
		// Empty line
		_, err := fmt.Fprintf(file, "%d\n%s --> %s\n%s\n\n",
			segment.Index,
			s.formatSRTTime(segment.StartTime),
			s.formatSRTTime(segment.EndTime),
			segment.Text)
		if err != nil {
			return fmt.Errorf("failed to write subtitle segment: %w", err)
		}
	}

	return nil
}

// formatSRTTime formats time duration to SRT time format (HH:MM:SS,mmm)
func (s *SubtitleService) formatSRTTime(duration time.Duration) string {
	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60
	seconds := int(duration.Seconds()) % 60
	milliseconds := int(duration.Nanoseconds()/1000000) % 1000

	return fmt.Sprintf("%02d:%02d:%02d,%03d", hours, minutes, seconds, milliseconds)
}
