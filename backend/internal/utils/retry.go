package utils

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// RetryConfig defines retry behavior for any operation
type RetryConfig struct {
	MaxRetries      int           // Maximum number of retries
	BaseDelay       time.Duration // Base delay between retries
	MaxDelay        time.Duration // Maximum delay between retries
	BackoffFactor   float64       // Exponential backoff factor
	JitterEnabled   bool          // Whether to add random jitter
	RetryableErrors []string      // List of error patterns that are retryable
}

// RetryResult contains the result of a retry operation
type RetryResult struct {
	Success      bool
	Attempts     int
	TotalTime    time.Duration
	LastError    error
	FirstError   error
}

// RetryManager handles retry operations with configurable behavior
type RetryManager struct {
	config *RetryConfig
}

// NewRetryManager creates a new retry manager with the given configuration
func NewRetryManager(config *RetryConfig) *RetryManager {
	if config == nil {
		config = DefaultRetryConfig()
	}
	return &RetryManager{config: config}
}

// DefaultRetryConfig returns a default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		BaseDelay:     2 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []string{
			"rate limit", "too many requests", "timeout", "connection",
			"network", "temporary", "503", "502", "504", "1002",
		},
	}
}

// PPTRetryConfig returns retry configuration optimized for PPT processing
func PPTRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		BaseDelay:     3 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 1.5,
		JitterEnabled: true,
		RetryableErrors: []string{
			"libreoffice", "conversion failed", "timeout", "temporary",
			"file locked", "permission denied", "resource busy",
		},
	}
}

// VideoRetryConfig returns retry configuration optimized for video generation
func VideoRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    5,
		BaseDelay:     10 * time.Second,
		MaxDelay:      300 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []string{
			"ffmpeg", "encoding failed", "timeout", "temporary",
			"resource busy", "disk space", "memory",
		},
	}
}

// AIRetryConfig returns retry configuration optimized for AI services
func AIRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    5,
		BaseDelay:     2 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []string{
			"rate limit", "too many requests", "timeout", "connection",
			"network", "temporary", "503", "502", "504", "429",
			"openai", "minimax", "1002",
		},
	}
}

// IsRetryableError checks if an error is retryable based on configuration
func (rm *RetryManager) IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	for _, pattern := range rm.config.RetryableErrors {
		if strings.Contains(errStr, strings.ToLower(pattern)) {
			return true
		}
	}
	return false
}

// CalculateDelay calculates the delay for the next retry attempt
func (rm *RetryManager) CalculateDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return rm.config.BaseDelay
	}

	// Exponential backoff
	delay := time.Duration(float64(rm.config.BaseDelay) *
		(rm.config.BackoffFactor * float64(attempt)))

	// Cap at maximum delay
	if delay > rm.config.MaxDelay {
		delay = rm.config.MaxDelay
	}

	// Add jitter to avoid thundering herd
	if rm.config.JitterEnabled {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
		delay += jitter
	}

	return delay
}

// Execute executes an operation with retry logic
func (rm *RetryManager) Execute(operation func() error) *RetryResult {
	return rm.ExecuteWithContext(context.Background(), operation)
}

// ExecuteWithContext executes an operation with retry logic and context support
func (rm *RetryManager) ExecuteWithContext(ctx context.Context, operation func() error) *RetryResult {
	startTime := time.Now()
	result := &RetryResult{}

	for attempt := 0; attempt <= rm.config.MaxRetries; attempt++ {
		// Check context cancellation
		if ctx.Err() != nil {
			result.LastError = ctx.Err()
			break
		}

		// Add delay for retry attempts
		if attempt > 0 {
			delay := rm.CalculateDelay(attempt)
			fmt.Printf("🔄 Retrying in %v (attempt %d/%d)...\n", delay, attempt, rm.config.MaxRetries)
			
			select {
			case <-ctx.Done():
				result.LastError = ctx.Err()
				break
			case <-time.After(delay):
			}
		}

		// Execute the operation
		err := operation()
		result.Attempts = attempt + 1

		if err == nil {
			result.Success = true
			result.TotalTime = time.Since(startTime)
			return result
		}

		// Store errors
		if result.FirstError == nil {
			result.FirstError = err
		}
		result.LastError = err

		// Check if error is retryable
		if !rm.IsRetryableError(err) {
			fmt.Printf("❌ Non-retryable error: %v\n", err)
			break
		}

		fmt.Printf("⚠️  Retryable error (attempt %d/%d): %v\n", attempt+1, rm.config.MaxRetries+1, err)
	}

	result.TotalTime = time.Since(startTime)
	return result
}

// ExecuteWithResult executes an operation that returns a result with retry logic
func ExecuteWithResult[T any](rm *RetryManager, operation func() (T, error)) (T, *RetryResult) {
	return ExecuteWithResultAndContext(context.Background(), rm, operation)
}

// ExecuteWithResultAndContext executes an operation that returns a result with retry logic and context
func ExecuteWithResultAndContext[T any](ctx context.Context, rm *RetryManager, operation func() (T, error)) (T, *RetryResult) {
	var result T
	var operationResult *RetryResult

	wrappedOperation := func() error {
		var err error
		result, err = operation()
		return err
	}

	operationResult = rm.ExecuteWithContext(ctx, wrappedOperation)
	return result, operationResult
}

// LogRetryResult logs the result of a retry operation
func LogRetryResult(operation string, result *RetryResult) {
	if result.Success {
		fmt.Printf("✅ %s succeeded after %d attempts in %v\n", 
			operation, result.Attempts, result.TotalTime)
	} else {
		fmt.Printf("❌ %s failed after %d attempts in %v. Last error: %v\n", 
			operation, result.Attempts, result.TotalTime, result.LastError)
	}
}
