package docs

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// OpenAPISpec represents the OpenAPI specification
type OpenAPISpec struct {
	OpenAPI string                 `json:"openapi"`
	Info    Info                   `json:"info"`
	Servers []Server               `json:"servers"`
	Paths   map[string]interface{} `json:"paths"`
}

// Info represents API information
type Info struct {
	Title       string  `json:"title"`
	Description string  `json:"description"`
	Version     string  `json:"version"`
	Contact     Contact `json:"contact"`
}

// Contact represents contact information
type Contact struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

// Server represents a server
type Server struct {
	URL         string `json:"url"`
	Description string `json:"description"`
}

// SetupRapiDocRoutes sets up RapiDoc documentation routes
func SetupRapiDocRoutes(router *gin.Engine) {
	// OpenAPI JSON endpoint
	router.GET("/openapi.json", func(c *gin.Context) {
		spec := generateOpenAPISpec(c.Request)
		c.JSON(http.StatusOK, spec)
	})

	// RapiDoc HTML page
	router.GET("/docs", func(c *gin.Context) {
		html := getRapiDocHTML()
		c.Header("Content-Type", "text/html")
		c.String(http.StatusOK, html)
	})

	// Alternative documentation endpoints
	router.GET("/api-docs", func(c *gin.Context) {
		html := getAlternativeDocsHTML()
		c.Header("Content-Type", "text/html")
		c.String(http.StatusOK, html)
	})
}

// generateOpenAPISpec generates the OpenAPI specification
func generateOpenAPISpec(r *http.Request) OpenAPISpec {
	baseURL := "http://" + r.Host

	return OpenAPISpec{
		OpenAPI: "3.0.0",
		Info: Info{
			Title:       "PPT Narrator API",
			Description: "Convert PowerPoint presentations to narrated videos through a comprehensive pipeline",
			Version:     "1.0.0",
			Contact: Contact{
				Name:  "API Support",
				Email: "<EMAIL>",
			},
		},
		Servers: []Server{
			{
				URL:         baseURL,
				Description: "Development server",
			},
		},
		Paths: generatePaths(),
	}
}

// generatePaths generates API paths
func generatePaths() map[string]interface{} {
	return map[string]interface{}{
		"/health": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Health check",
				"description": "Get the health status of the API",
				"tags":        []string{"System"},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"status":  map[string]string{"type": "string"},
										"service": map[string]string{"type": "string"},
										"version": map[string]string{"type": "string"},
									},
								},
							},
						},
					},
				},
			},
		},
		"/api/v1/pipeline/upload-and-process": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Upload and process PowerPoint file",
				"description": "Upload a PowerPoint file and start the complete processing pipeline",
				"tags":        []string{"Pipeline"},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"multipart/form-data": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"file": map[string]interface{}{
										"type":        "string",
										"format":      "binary",
										"description": "PowerPoint file (.ppt, .pptx)",
									},
									"project_name": map[string]interface{}{
										"type":        "string",
										"description": "Project name",
									},
									"user_requirements": map[string]interface{}{
										"type":        "string",
										"description": "User requirements for narration style",
									},
								},
								"required": []string{"file"},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Pipeline started successfully",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success":    map[string]string{"type": "boolean"},
										"message":    map[string]string{"type": "string"},
										"project_id": map[string]string{"type": "string"},
									},
								},
							},
						},
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
				},
			},
		},
		"/api/v1/pipeline/{projectId}/progress": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get pipeline progress",
				"description": "Get the current progress of the processing pipeline",
				"tags":        []string{"Pipeline"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"progress": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":      map[string]string{"type": "string"},
												"current_stage":   map[string]string{"type": "string"},
												"progress":        map[string]string{"type": "number"},
												"message":         map[string]string{"type": "string"},
												"start_time":      map[string]string{"type": "string", "format": "date-time"},
												"last_update":     map[string]string{"type": "string", "format": "date-time"},
												"estimated_time":  map[string]string{"type": "string"},
												"stage_details":   map[string]string{"type": "object"},
												"can_retry":       map[string]string{"type": "boolean"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/pipeline/{projectId}/retry": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Retry pipeline",
				"description": "Retry the processing pipeline from a specific stage",
				"tags":        []string{"Pipeline"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"application/json": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"stage": map[string]interface{}{
										"type":        "string",
										"description": "Stage to retry from",
										"enum":        []string{"screenshot", "narration", "audio", "video"},
										"example":     "narration",
									},
									"user_requirements": map[string]interface{}{
										"type":        "string",
										"description": "Updated user requirements",
										"example":     "Please generate more detailed narration",
									},
								},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Pipeline retry started successfully",
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/pipeline/{projectId}/cancel": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Cancel pipeline",
				"description": "Cancel the currently running processing pipeline",
				"tags":        []string{"Pipeline"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Pipeline cancelled successfully",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/pipeline/{projectId}/status": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get pipeline status",
				"description": "Get the current status of the processing pipeline",
				"tags":        []string{"Pipeline"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/pipeline/active": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "List active pipelines",
				"description": "Get a list of all currently active processing pipelines",
				"tags":        []string{"Pipeline"},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"active_pipelines": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"project_id":     map[string]string{"type": "string"},
															"current_stage":  map[string]string{"type": "string"},
															"progress":       map[string]string{"type": "number"},
															"start_time":     map[string]string{"type": "string", "format": "date-time"},
														},
													},
												},
												"total": map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"/api/v1/projects": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "List all projects",
				"description": "Get a list of all projects",
				"tags":        []string{"Projects"},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"projects": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"id":                 map[string]string{"type": "string"},
															"name":               map[string]string{"type": "string"},
															"description":        map[string]string{"type": "string"},
															"status":             map[string]string{"type": "string"},
															"created_at":         map[string]string{"type": "string", "format": "date-time"},
															"updated_at":         map[string]string{"type": "string", "format": "date-time"},
															"slide_count":        map[string]string{"type": "integer"},
															"original_file_name": map[string]string{"type": "string"},
															"file_size":          map[string]string{"type": "integer"},
														},
													},
												},
												"total": map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"/api/v1/projects/upload": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Upload PowerPoint file",
				"description": "Upload a PowerPoint file to create a new project",
				"tags":        []string{"Projects"},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"multipart/form-data": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"file": map[string]interface{}{
										"type":        "string",
										"format":      "binary",
										"description": "PowerPoint file (.ppt, .pptx)",
									},
									"name": map[string]interface{}{
										"type":        "string",
										"description": "Project name",
									},
									"description": map[string]interface{}{
										"type":        "string",
										"description": "Project description",
									},
								},
								"required": []string{"file", "name"},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Project created successfully",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success":    map[string]string{"type": "boolean"},
										"message":    map[string]string{"type": "string"},
										"project_id": map[string]string{"type": "string"},
									},
								},
							},
						},
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
				},
			},
		},
		"/api/v1/projects/{projectId}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get project details",
				"description": "Get detailed information about a specific project",
				"tags":        []string{"Projects"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"id":                 map[string]string{"type": "string"},
												"name":               map[string]string{"type": "string"},
												"description":        map[string]string{"type": "string"},
												"status":             map[string]string{"type": "string"},
												"created_at":         map[string]string{"type": "string", "format": "date-time"},
												"updated_at":         map[string]string{"type": "string", "format": "date-time"},
												"slide_count":        map[string]string{"type": "integer"},
												"original_file_name": map[string]string{"type": "string"},
												"file_size":          map[string]string{"type": "integer"},
												"file_path":          map[string]string{"type": "string"},
												"screenshots_path":   map[string]string{"type": "string"},
												"narration_script":   map[string]string{"type": "string"},
												"video_path":         map[string]string{"type": "string"},
												"audio_path":         map[string]string{"type": "string"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
			"delete": map[string]interface{}{
				"summary":     "Delete project",
				"description": "Delete a project and all its associated files",
				"tags":        []string{"Projects"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Project deleted successfully",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/projects/{projectId}/screenshots": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get project screenshots",
				"description": "Get all screenshot files for a project",
				"tags":        []string{"Projects"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id": map[string]string{"type": "string"},
												"screenshots": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"slide_number": map[string]string{"type": "integer"},
															"filename":     map[string]string{"type": "string"},
															"path":         map[string]string{"type": "string"},
															"url":          map[string]string{"type": "string"},
														},
													},
												},
												"total": map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/narration/{projectId}/generate": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Generate narration",
				"description": "Generate AI narration for all slides in a project",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
					{
						"name":        "force",
						"in":          "query",
						"required":    false,
						"description": "Force restart narration generation",
						"schema":      map[string]string{"type": "boolean"},
					},
				},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"application/json": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"user_requirements": map[string]interface{}{
										"type":        "string",
										"description": "User requirements for narration style and content",
										"example":     "Please generate professional narration suitable for business presentation",
									},
								},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Narration generation started successfully",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"message": map[string]string{"type": "string"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id": map[string]string{"type": "string"},
												"status":     map[string]string{"type": "string"},
											},
										},
									},
								},
							},
						},
					},
					"400": map[string]interface{}{
						"description": "Bad Request - Invalid project state or parameters",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/narration/{projectId}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get project narration",
				"description": "Get the generated narration for all slides in a project",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":     map[string]string{"type": "string"},
												"full_narration": map[string]string{"type": "string"},
												"slides": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"id":               map[string]string{"type": "string"},
															"slide_number":     map[string]string{"type": "integer"},
															"title":            map[string]string{"type": "string"},
															"content":          map[string]string{"type": "string"},
															"narration_text":   map[string]string{"type": "string"},
															"narration_audio":  map[string]string{"type": "string"},
															"duration":         map[string]string{"type": "number"},
															"screenshot_path":  map[string]string{"type": "string"},
														},
													},
												},
												"total_slides": map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/narration/{projectId}/progress": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get narration progress",
				"description": "Get the current progress of narration generation",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":        map[string]string{"type": "string"},
												"status":            map[string]string{"type": "string"},
												"progress":          map[string]string{"type": "number"},
												"current_slide":     map[string]string{"type": "integer"},
												"total_slides":      map[string]string{"type": "integer"},
												"completed_slides":  map[string]string{"type": "integer"},
												"failed_slides":     map[string]string{"type": "integer"},
												"can_resume":        map[string]string{"type": "boolean"},
												"estimated_time_remaining": map[string]string{"type": "string"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/memory/{projectId}": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Add AI memory",
				"description": "Add memory entry for AI context and continuity",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"application/json": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"memory_key": map[string]interface{}{
										"type":        "string",
										"description": "Memory key identifier",
										"example":     "presentation_theme",
									},
									"memory_value": map[string]interface{}{
										"type":        "string",
										"description": "Memory value content",
										"example":     "This is a business presentation about quarterly results",
									},
									"memory_type": map[string]interface{}{
										"type":        "string",
										"description": "Type of memory",
										"enum":        []string{"context", "preference", "fact"},
										"example":     "context",
									},
								},
								"required": []string{"memory_key", "memory_value"},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Memory added successfully",
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
			"get": map[string]interface{}{
				"summary":     "Get AI memories",
				"description": "Get all memory entries for a project",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"memories": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"id":           map[string]string{"type": "string"},
															"memory_key":   map[string]string{"type": "string"},
															"memory_value": map[string]string{"type": "string"},
															"memory_type":  map[string]string{"type": "string"},
															"created_at":   map[string]string{"type": "string", "format": "date-time"},
															"updated_at":   map[string]string{"type": "string", "format": "date-time"},
														},
													},
												},
												"total": map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
			"delete": map[string]interface{}{
				"summary":     "Clear AI memories",
				"description": "Clear all memory entries for a project",
				"tags":        []string{"Narration"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Memories cleared successfully",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/video/{projectId}/generate": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Generate video",
				"description": "Generate video by combining slides and audio",
				"tags":        []string{"Video"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
					},
				},
			},
		},
		"/api/v1/audio/{projectId}/generate": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Generate audio",
				"description": "Generate audio files from narration text using TTS",
				"tags":        []string{"Audio"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"application/json": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"voice_type": map[string]interface{}{
										"type":        "string",
										"description": "Voice type for TTS",
										"enum":        []string{"male", "female"},
										"example":     "female",
									},
									"speed": map[string]interface{}{
										"type":        "string",
										"description": "Speech speed",
										"enum":        []string{"slow", "normal", "fast"},
										"example":     "normal",
									},
									"language": map[string]interface{}{
										"type":        "string",
										"description": "Language code",
										"example":     "zh-CN",
									},
								},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Audio generation started successfully",
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/audio/{projectId}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get project audio",
				"description": "Get audio files information for a project",
				"tags":        []string{"Audio"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":   map[string]string{"type": "string"},
												"audio_files":  map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"slide_number": map[string]string{"type": "integer"},
															"audio_path":   map[string]string{"type": "string"},
															"duration":     map[string]string{"type": "number"},
														},
													},
												},
												"total_duration": map[string]string{"type": "number"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/audio/{projectId}/progress": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get audio generation progress",
				"description": "Get the current progress of audio generation",
				"tags":        []string{"Audio"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":       map[string]string{"type": "string"},
												"status":           map[string]string{"type": "string"},
												"progress":         map[string]string{"type": "number"},
												"current_slide":    map[string]string{"type": "integer"},
												"total_slides":     map[string]string{"type": "integer"},
												"completed_slides": map[string]string{"type": "integer"},
												"failed_slides":    map[string]string{"type": "integer"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/video/{projectId}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get video information",
				"description": "Get video file information for a project",
				"tags":        []string{"Video"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":   map[string]string{"type": "string"},
												"video_path":   map[string]string{"type": "string"},
												"video_url":    map[string]string{"type": "string"},
												"duration":     map[string]string{"type": "number"},
												"file_size":    map[string]string{"type": "integer"},
												"created_at":   map[string]string{"type": "string", "format": "date-time"},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found or video not generated",
					},
				},
			},
		},
		"/api/v1/system/ffmpeg/validate": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Validate FFmpeg installation",
				"description": "Check if FFmpeg is properly installed and accessible",
				"tags":        []string{"System"},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"ffmpeg_available": map[string]string{"type": "boolean"},
												"ffmpeg_version":   map[string]string{"type": "string"},
												"ffmpeg_path":      map[string]string{"type": "string"},
												"ffprobe_available": map[string]string{"type": "boolean"},
												"ffprobe_version":  map[string]string{"type": "string"},
												"ffprobe_path":     map[string]string{"type": "string"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"/api/v1/system/ai/info": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Get AI provider information",
				"description": "Get information about the configured AI provider",
				"tags":        []string{"System"},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"data": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"ai_provider":     map[string]string{"type": "string"},
												"openai_model":    map[string]string{"type": "string"},
												"openai_base_url": map[string]string{"type": "string"},
												"tts_provider":    map[string]string{"type": "string"},
												"minimax_model":   map[string]string{"type": "string"},
												"system_prompt":   map[string]string{"type": "string"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"/api/v1/audio-debug/{projectId}/diagnose": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Diagnose audio issues",
				"description": "Diagnose audio-related issues for a project",
				"tags":        []string{"Debug"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"diagnosis": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id":   map[string]string{"type": "string"},
												"project_name": map[string]string{"type": "string"},
												"status":       map[string]string{"type": "string"},
												"slides_count": map[string]string{"type": "integer"},
												"audio_files": map[string]interface{}{
													"type": "array",
													"items": map[string]interface{}{
														"type": "object",
														"properties": map[string]interface{}{
															"filename": map[string]string{"type": "string"},
															"exists":   map[string]string{"type": "boolean"},
															"size":     map[string]string{"type": "integer"},
															"duration": map[string]string{"type": "string"},
															"format":   map[string]string{"type": "string"},
															"issues":   map[string]interface{}{
																"type": "array",
																"items": map[string]string{"type": "string"},
															},
														},
													},
												},
												"issues": map[string]interface{}{
													"type": "array",
													"items": map[string]string{"type": "string"},
												},
												"suggestions": map[string]interface{}{
													"type": "array",
													"items": map[string]string{"type": "string"},
												},
											},
										},
									},
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/audio-debug/{projectId}/fix": map[string]interface{}{
			"post": map[string]interface{}{
				"summary":     "Fix audio issues",
				"description": "Attempt to fix common audio issues for a project",
				"tags":        []string{"Debug"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"requestBody": map[string]interface{}{
					"content": map[string]interface{}{
						"application/json": map[string]interface{}{
							"schema": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"fix_type": map[string]interface{}{
										"type":        "string",
										"description": "Type of fix to apply",
										"enum":        []string{"validate", "combine"},
										"example":     "combine",
									},
								},
								"required": []string{"fix_type"},
							},
						},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Fix operation completed",
						"content": map[string]interface{}{
							"application/json": map[string]interface{}{
								"schema": map[string]interface{}{
									"type": "object",
									"properties": map[string]interface{}{
										"success": map[string]string{"type": "boolean"},
										"result": map[string]interface{}{
											"type": "object",
											"properties": map[string]interface{}{
												"project_id": map[string]string{"type": "string"},
												"fix_type":   map[string]string{"type": "string"},
												"actions": map[string]interface{}{
													"type": "array",
													"items": map[string]string{"type": "string"},
												},
												"success": map[string]string{"type": "boolean"},
											},
										},
									},
								},
							},
						},
					},
					"400": map[string]interface{}{
						"description": "Bad Request",
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/download/{projectId}/all": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Download all project files",
				"description": "Download a ZIP file containing all project files (video, audio, screenshots, narration)",
				"tags":        []string{"Downloads"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"application/zip": map[string]interface{}{
								"schema": map[string]interface{}{
									"type":   "string",
									"format": "binary",
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Project not found",
					},
				},
			},
		},
		"/api/v1/download/{projectId}/video": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Download video",
				"description": "Download the generated video file",
				"tags":        []string{"Downloads"},
				"parameters": []map[string]interface{}{
					{
						"name":        "projectId",
						"in":          "path",
						"required":    true,
						"description": "Project identifier",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Success",
						"content": map[string]interface{}{
							"video/mp4": map[string]interface{}{
								"schema": map[string]interface{}{
									"type":   "string",
									"format": "binary",
								},
							},
						},
					},
				},
			},
		},
		"/uploads/{filename}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Serve uploaded files",
				"description": "Serve static uploaded files (PPT files, etc.)",
				"tags":        []string{"Static Files"},
				"parameters": []map[string]interface{}{
					{
						"name":        "filename",
						"in":          "path",
						"required":    true,
						"description": "File name to serve",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "File served successfully",
						"content": map[string]interface{}{
							"application/octet-stream": map[string]interface{}{
								"schema": map[string]interface{}{
									"type":   "string",
									"format": "binary",
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "File not found",
					},
				},
			},
		},
		"/screenshots/{filename}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Serve screenshot files",
				"description": "Serve static screenshot image files",
				"tags":        []string{"Static Files"},
				"parameters": []map[string]interface{}{
					{
						"name":        "filename",
						"in":          "path",
						"required":    true,
						"description": "Screenshot file name to serve",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Screenshot served successfully",
						"content": map[string]interface{}{
							"image/png": map[string]interface{}{
								"schema": map[string]interface{}{
									"type":   "string",
									"format": "binary",
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Screenshot not found",
					},
				},
			},
		},
		"/videos/{filename}": map[string]interface{}{
			"get": map[string]interface{}{
				"summary":     "Serve video files",
				"description": "Serve static video files",
				"tags":        []string{"Static Files"},
				"parameters": []map[string]interface{}{
					{
						"name":        "filename",
						"in":          "path",
						"required":    true,
						"description": "Video file name to serve",
						"schema":      map[string]string{"type": "string"},
					},
				},
				"responses": map[string]interface{}{
					"200": map[string]interface{}{
						"description": "Video served successfully",
						"content": map[string]interface{}{
							"video/mp4": map[string]interface{}{
								"schema": map[string]interface{}{
									"type":   "string",
									"format": "binary",
								},
							},
						},
					},
					"404": map[string]interface{}{
						"description": "Video not found",
					},
				},
			},
		},
	}
}

// getRapiDocHTML returns the RapiDoc HTML page
func getRapiDocHTML() string {
	return `<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>PPT Narrator API Documentation</title>
  <script type="module" src="https://unpkg.com/rapidoc/dist/rapidoc-min.js"></script>
</head>
<body>
  <rapi-doc 
    spec-url="/openapi.json"
    theme="light"
    bg-color="#fafbfc"
    text-color="#333"
    header-color="#005b96"
    primary-color="#005b96"
    nav-bg-color="#f6f8fa"
    nav-text-color="#24292e"
    nav-hover-bg-color="#e1e4e8"
    nav-hover-text-color="#24292e"
    nav-accent-color="#005b96"
    render-style="read"
    schema-style="table"
    default-schema-tab="schema"
    show-header="true"
    show-info="true"
    allow-try="true"
    allow-server-selection="false"
    allow-authentication="false"
    allow-spec-url-load="false"
    allow-spec-file-load="false"
    show-curl-before-try="true"
    layout="row"
    sort-tags="true"
    goto-path=""
    fill-request-fields-with-example="true"
    persist-auth="false"
    use-path-in-nav-bar="false"
    nav-item-spacing="default"
    font-size="default"
    regular-font="Open Sans"
    mono-font="Monaco"
    load-fonts="true"
    style="height: 100vh; width: 100%;"
  >
    <div slot="nav-logo" style="display: flex; align-items: center; justify-content: center; padding: 16px;">
      <div style="font-size: 18px; font-weight: bold; color: #005b96;">
        🎬 PPT Narrator API
      </div>
    </div>
  </rapi-doc>
</body>
</html>`
}

// getAlternativeDocsHTML returns an alternative documentation page
func getAlternativeDocsHTML() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT Narrator API - Documentation Hub</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 60px 0; text-align: center; margin-bottom: 40px; border-radius: 12px; }
        .header h1 { font-size: 3rem; margin-bottom: 15px; }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .docs-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 40px; }
        .doc-card { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.3s, box-shadow 0.3s; }
        .doc-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .doc-card h3 { color: #667eea; margin-bottom: 15px; font-size: 1.5rem; }
        .doc-card p { color: #666; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; font-weight: 500; transition: background 0.3s; }
        .btn:hover { background: #5a6fd8; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #5a6268; }
        .endpoints { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .endpoint { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .method { display: inline-block; padding: 4px 8px; border-radius: 4px; font-weight: bold; font-size: 0.8rem; margin-right: 10px; }
        .method.GET { background: #d4edda; color: #155724; }
        .method.POST { background: #d1ecf1; color: #0c5460; }
        .method.DELETE { background: #f8d7da; color: #721c24; }
        .path { font-family: 'Monaco', monospace; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 PPT Narrator API</h1>
            <p>Convert PowerPoint presentations to narrated videos</p>
        </div>
        
        <div class="docs-grid">
            <div class="doc-card">
                <h3>📖 Interactive Documentation</h3>
                <p>Modern, interactive API documentation with RapiDoc. Test endpoints directly in your browser.</p>
                <a href="/docs" class="btn">Open RapiDoc</a>
            </div>
            
            <div class="doc-card">
                <h3>📄 OpenAPI Specification</h3>
                <p>Download the OpenAPI 3.0 specification in JSON format for use with other tools.</p>
                <a href="/openapi.json" class="btn btn-secondary" target="_blank">Download JSON</a>
            </div>
            
            <div class="doc-card">
                <h3>🚀 Quick Start</h3>
                <p>Get started quickly with our API. Upload a PowerPoint file and generate a narrated video in minutes.</p>
                <a href="#endpoints" class="btn">View Endpoints</a>
            </div>
        </div>
        
        <div class="endpoints" id="endpoints">
            <h2>🔗 Key API Endpoints</h2>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/health</span>
                <p>Health check endpoint to verify API status</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/pipeline/upload-and-process</span>
                <p>Upload PowerPoint file and start complete processing pipeline</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/pipeline/{projectId}/progress</span>
                <p>Get processing pipeline progress</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/pipeline/{projectId}/retry</span>
                <p>Retry pipeline from specific stage</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/projects</span>
                <p>List all projects</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/projects/upload</span>
                <p>Upload PowerPoint file to create project</p>
            </div>
            <div class="endpoint">
                <span class="method DELETE">DELETE</span>
                <span class="path">/api/v1/projects/{projectId}</span>
                <p>Delete project and all associated files</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/narration/{projectId}/generate</span>
                <p>Generate AI narration for project slides</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/narration/{projectId}</span>
                <p>Get generated narration for all slides</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/narration/{projectId}/progress</span>
                <p>Get narration generation progress</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/memory/{projectId}</span>
                <p>Add AI memory for context and continuity</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/memory/{projectId}</span>
                <p>Get all AI memories for a project</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/audio/{projectId}/generate</span>
                <p>Generate audio files from narration using TTS</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/audio/{projectId}</span>
                <p>Get audio files information for a project</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/audio/{projectId}/progress</span>
                <p>Get audio generation progress</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/video/{projectId}/generate</span>
                <p>Generate video by combining slides and audio</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/download/{projectId}/video</span>
                <p>Download the generated video file</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/download/{projectId}/all</span>
                <p>Download all project files as ZIP</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/system/ffmpeg/validate</span>
                <p>Validate FFmpeg installation</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/system/ai/info</span>
                <p>Get AI provider information</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/api/v1/audio-debug/{projectId}/diagnose</span>
                <p>Diagnose audio issues for a project</p>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/api/v1/audio-debug/{projectId}/fix</span>
                <p>Fix common audio issues</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/uploads/{filename}</span>
                <p>Serve uploaded PPT files</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/screenshots/{filename}</span>
                <p>Serve screenshot image files</p>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/videos/{filename}</span>
                <p>Serve generated video files</p>
            </div>
        </div>
    </div>
</body>
</html>`
}
