package handlers

import (
	"archive/zip"
	"fmt"
	"io"
	"net/http"
	"os"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
	"strings"

	"github.com/gin-gonic/gin"
)

// DownloadHandler handles file download requests
type DownloadHandler struct {
	cfg   *config.Config
	store *services.DatabaseStoreService
}

// NewDownloadHandler creates a new download handler
func NewDownloadHandler(cfg *config.Config, store *services.DatabaseStoreService) *DownloadHandler {
	return &DownloadHandler{
		cfg:   cfg,
		store: store,
	}
}

// DownloadVideo downloads the final video file
func (dh *DownloadHandler) DownloadVideo(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Check if video is ready
	if project.VideoPath == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Video not available for this project",
		})
		return
	}

	// Check if file exists
	if !dh.fileExists(project.VideoPath) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Video file not found on disk",
		})
		return
	}

	// Set headers for file download
	filename := fmt.Sprintf("%s_video.mp4", dh.sanitizeFilename(project.Name))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "video/mp4")

	// Stream the file
	c.File(project.VideoPath)
}

// DownloadAudio downloads the combined audio file
func (dh *DownloadHandler) DownloadAudio(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Check if audio is ready
	if project.AudioPath == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Audio not available for this project",
		})
		return
	}

	// Check if file exists
	if !dh.fileExists(project.AudioPath) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Audio file not found on disk",
		})
		return
	}

	// Set headers for file download
	filename := fmt.Sprintf("%s_audio.mp3", dh.sanitizeFilename(project.Name))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "audio/mpeg")

	// Stream the file
	c.File(project.AudioPath)
}

// DownloadScreenshots downloads all screenshots as a ZIP file
func (dh *DownloadHandler) DownloadScreenshots(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project and slides
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	slides, err := dh.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get slides",
		})
		return
	}

	// Check if screenshots are available
	screenshotFiles := []string{}
	for _, slide := range slides {
		if slide.ScreenshotPath != "" && dh.fileExists(slide.ScreenshotPath) {
			screenshotFiles = append(screenshotFiles, slide.ScreenshotPath)
		}
	}

	if len(screenshotFiles) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "No screenshots available for this project",
		})
		return
	}

	// Create ZIP file in memory
	filename := fmt.Sprintf("%s_screenshots.zip", dh.sanitizeFilename(project.Name))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/zip")

	// Create ZIP writer
	zipWriter := zip.NewWriter(c.Writer)
	defer zipWriter.Close()

	// Add each screenshot to ZIP
	for i, screenshotPath := range screenshotFiles {
		if err := dh.addFileToZip(zipWriter, screenshotPath, fmt.Sprintf("slide_%03d.png", i+1)); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   fmt.Sprintf("Failed to add screenshot to ZIP: %v", err),
			})
			return
		}
	}
}

// DownloadNarrationScript downloads the narration script as a text file
func (dh *DownloadHandler) DownloadNarrationScript(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Check if narration script is available
	if project.NarrationScript == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Narration script not available for this project",
		})
		return
	}

	// Set headers for file download
	filename := fmt.Sprintf("%s_narration.txt", dh.sanitizeFilename(project.Name))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "text/plain; charset=utf-8")

	// Send the narration script content
	c.String(http.StatusOK, project.NarrationScript)
}

// DownloadAll downloads all project assets as a ZIP file
func (dh *DownloadHandler) DownloadAll(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project and slides
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	slides, err := dh.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get slides",
		})
		return
	}

	// Create ZIP file
	filename := fmt.Sprintf("%s_complete.zip", dh.sanitizeFilename(project.Name))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/zip")

	// Create ZIP writer
	zipWriter := zip.NewWriter(c.Writer)
	defer zipWriter.Close()

	// Add video if available
	if project.VideoPath != "" && dh.fileExists(project.VideoPath) {
		if err := dh.addFileToZip(zipWriter, project.VideoPath, "video.mp4"); err != nil {
			fmt.Printf("Warning: Failed to add video to ZIP: %v\n", err)
		}
	}

	// Add audio if available
	if project.AudioPath != "" && dh.fileExists(project.AudioPath) {
		if err := dh.addFileToZip(zipWriter, project.AudioPath, "audio.mp3"); err != nil {
			fmt.Printf("Warning: Failed to add audio to ZIP: %v\n", err)
		}
	}

	// Add narration script if available
	if project.NarrationScript != "" {
		if err := dh.addTextToZip(zipWriter, project.NarrationScript, "narration.txt"); err != nil {
			fmt.Printf("Warning: Failed to add narration script to ZIP: %v\n", err)
		}
	}

	// Add screenshots
	screenshotCount := 0
	for _, slide := range slides {
		if slide.ScreenshotPath != "" && dh.fileExists(slide.ScreenshotPath) {
			screenshotCount++
			zipPath := fmt.Sprintf("screenshots/slide_%03d.png", slide.SlideNumber)
			if err := dh.addFileToZip(zipWriter, slide.ScreenshotPath, zipPath); err != nil {
				fmt.Printf("Warning: Failed to add screenshot %d to ZIP: %v\n", slide.SlideNumber, err)
			}
		}
	}

	// Add original PPT file if available
	if project.FilePath != "" && dh.fileExists(project.FilePath) {
		originalFilename := fmt.Sprintf("original_%s", project.OriginalFileName)
		if err := dh.addFileToZip(zipWriter, project.FilePath, originalFilename); err != nil {
			fmt.Printf("Warning: Failed to add original PPT to ZIP: %v\n", err)
		}
	}
}

// GetDownloadInfo returns information about available downloads for a project
func (dh *DownloadHandler) GetDownloadInfo(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project and slides
	project, err := dh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	slides, err := dh.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get slides",
		})
		return
	}

	// Check available downloads
	downloadInfo := gin.H{
		"project_id":   projectID,
		"project_name": project.Name,
		"status":       project.Status,
		"downloads":    gin.H{},
	}

	downloads := gin.H{}

	// Check video availability
	if project.VideoPath != "" && dh.fileExists(project.VideoPath) {
		fileInfo, _ := os.Stat(project.VideoPath)
		downloads["video"] = gin.H{
			"available": true,
			"size":      fileInfo.Size(),
			"url":       fmt.Sprintf("/api/v1/download/%s/video", projectID),
		}
	} else {
		downloads["video"] = gin.H{
			"available": false,
			"reason":    "Video not generated or file missing",
		}
	}

	// Check audio availability
	if project.AudioPath != "" && dh.fileExists(project.AudioPath) {
		fileInfo, _ := os.Stat(project.AudioPath)
		downloads["audio"] = gin.H{
			"available": true,
			"size":      fileInfo.Size(),
			"url":       fmt.Sprintf("/api/v1/download/%s/audio", projectID),
		}
	} else {
		downloads["audio"] = gin.H{
			"available": false,
			"reason":    "Audio not generated or file missing",
		}
	}

	// Check screenshots availability
	screenshotCount := 0
	totalScreenshotSize := int64(0)
	for _, slide := range slides {
		if slide.ScreenshotPath != "" && dh.fileExists(slide.ScreenshotPath) {
			screenshotCount++
			if fileInfo, err := os.Stat(slide.ScreenshotPath); err == nil {
				totalScreenshotSize += fileInfo.Size()
			}
		}
	}

	if screenshotCount > 0 {
		downloads["screenshots"] = gin.H{
			"available": true,
			"count":     screenshotCount,
			"size":      totalScreenshotSize,
			"url":       fmt.Sprintf("/api/v1/download/%s/screenshots", projectID),
		}
	} else {
		downloads["screenshots"] = gin.H{
			"available": false,
			"reason":    "Screenshots not generated",
		}
	}

	// Check narration script availability
	if project.NarrationScript != "" {
		downloads["narration"] = gin.H{
			"available": true,
			"size":      len(project.NarrationScript),
			"url":       fmt.Sprintf("/api/v1/download/%s/narration", projectID),
		}
	} else {
		downloads["narration"] = gin.H{
			"available": false,
			"reason":    "Narration script not generated",
		}
	}

	// Complete package availability
	hasAnyAsset := (project.VideoPath != "" && dh.fileExists(project.VideoPath)) ||
		(project.AudioPath != "" && dh.fileExists(project.AudioPath)) ||
		screenshotCount > 0 ||
		project.NarrationScript != ""

	if hasAnyAsset {
		downloads["complete"] = gin.H{
			"available": true,
			"url":       fmt.Sprintf("/api/v1/download/%s/all", projectID),
		}
	} else {
		downloads["complete"] = gin.H{
			"available": false,
			"reason":    "No assets available for download",
		}
	}

	downloadInfo["downloads"] = downloads

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    downloadInfo,
	})
}

// Helper methods

// fileExists checks if a file exists
func (dh *DownloadHandler) fileExists(filepath string) bool {
	if filepath == "" {
		return false
	}
	_, err := os.Stat(filepath)
	return !os.IsNotExist(err)
}

// sanitizeFilename removes invalid characters from filename
func (dh *DownloadHandler) sanitizeFilename(filename string) string {
	// Replace invalid characters with underscores
	invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", " "}
	result := filename
	for _, char := range invalidChars {
		result = strings.ReplaceAll(result, char, "_")
	}
	return result
}

// addFileToZip adds a file to the ZIP archive
func (dh *DownloadHandler) addFileToZip(zipWriter *zip.Writer, filePath, zipPath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return err
	}

	// Create ZIP file header
	header, err := zip.FileInfoHeader(fileInfo)
	if err != nil {
		return err
	}
	header.Name = zipPath
	header.Method = zip.Deflate

	// Create writer for this file
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	// Copy file content
	_, err = io.Copy(writer, file)
	return err
}

// addTextToZip adds text content to the ZIP archive
func (dh *DownloadHandler) addTextToZip(zipWriter *zip.Writer, content, zipPath string) error {
	// Create ZIP file header
	header := &zip.FileHeader{
		Name:   zipPath,
		Method: zip.Deflate,
	}

	// Create writer for this file
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	// Write content
	_, err = writer.Write([]byte(content))
	return err
}
