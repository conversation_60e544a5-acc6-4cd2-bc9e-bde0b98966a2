package handlers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UploadHandler handles PPT file uploads
type UploadHandler struct {
	config       *config.Config
	store        *services.DatabaseStoreService
	pptProcessor *services.PPTProcessorService
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(cfg *config.Config, store *services.DatabaseStoreService, pptProcessor *services.PPTProcessorService) *UploadHandler {
	return &UploadHandler{
		config:       cfg,
		store:        store,
		pptProcessor: pptProcessor,
	}
}

// UploadPPT handles PPT file upload
func (h *UploadHandler) UploadPPT(c *gin.Context) {
	// Get form data
	name := c.PostForm("name")
	description := c.PostForm("description")

	if name == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project name is required",
		})
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No file uploaded or invalid file",
		})
		return
	}
	defer file.Close()

	// Validate file type
	filename := header.Filename
	if !h.isValidPPTFile(filename) {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid file type. Only .ppt and .pptx files are supported",
		})
		return
	}

	// Create project
	project, err := h.store.CreateProject(name, description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to create project: %v", err),
		})
		return
	}

	// Create upload directory
	uploadDir := filepath.Join(h.config.UploadDir, project.ID)
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to create upload directory: %v", err),
		})
		return
	}

	// Save uploaded file
	filePath := filepath.Join(uploadDir, filename)
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to create file: %v", err),
		})
		return
	}
	defer dst.Close()

	// Copy file content
	fileSize, err := io.Copy(dst, file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to save file: %v", err),
		})
		return
	}

	// Update project with file information
	project.OriginalFileName = filename
	project.FilePath = filePath
	project.FileSize = fileSize
	if err := h.store.UpdateProject(project); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to update project: %v", err),
		})
		return
	}

	// Start processing in background
	go func() {
		if err := h.pptProcessor.ProcessPPTFile(project.ID, filePath); err != nil {
			// Log error but don't fail the upload response
			fmt.Printf("Error processing PPT file: %v\n", err)
		}
	}()

	// Return success response
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "File uploaded successfully. Processing started.",
		Data: models.UploadResponse{
			ProjectID:        project.ID,
			OriginalFileName: filename,
			FileSize:         fileSize,
			Message:          "Processing screenshots...",
		},
	})
}

// GetUploadProgress returns the upload and processing progress
func (h *UploadHandler) GetUploadProgress(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	// Check if project exists
	_, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Get processing progress
	progress, err := h.pptProcessor.GetProcessingProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get progress: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    progress,
	})
}

// ListProjects returns all projects
func (h *UploadHandler) ListProjects(c *gin.Context) {
	projects, err := h.store.ListProjects()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to list projects: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    projects,
	})
}

// GetProject returns a specific project
func (h *UploadHandler) GetProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    project,
	})
}

// DeleteProject deletes a project and all associated files
func (h *UploadHandler) DeleteProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	// Get project first to get file paths
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Delete project from store
	if err := h.store.DeleteProject(projectID); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to delete project: %v", err),
		})
		return
	}

	// Clean up files in background
	go h.cleanupProjectFiles(project)

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Project deleted successfully",
	})
}

// GetProjectScreenshots returns screenshots for a project
func (h *UploadHandler) GetProjectScreenshots(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	screenshots, err := h.pptProcessor.GetProjectScreenshots(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get screenshots: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    screenshots,
	})
}

// GetSlideScreenshot serves a specific slide screenshot
func (h *UploadHandler) GetSlideScreenshot(c *gin.Context) {
	projectID := c.Param("projectId")
	slideNumberStr := c.Param("slideNumber")

	if projectID == "" || slideNumberStr == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID and slide number are required",
		})
		return
	}

	slideNumber, err := strconv.Atoi(slideNumberStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid slide number",
		})
		return
	}

	screenshotPath, err := h.pptProcessor.GetSlideScreenshot(projectID, slideNumber)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Screenshot not found",
		})
		return
	}

	// Serve the file
	c.File(screenshotPath)
}

// isValidPPTFile checks if the file has a valid PPT extension
func (h *UploadHandler) isValidPPTFile(filename string) bool {
	ext := filepath.Ext(filename)
	return ext == ".ppt" || ext == ".pptx"
}

// cleanupProjectFiles removes all files associated with a project
func (h *UploadHandler) cleanupProjectFiles(project *models.PPTProject) {
	// Remove upload directory
	if project.FilePath != "" {
		uploadDir := filepath.Dir(project.FilePath)
		os.RemoveAll(uploadDir)
	}

	// Remove screenshot directory
	if project.ScreenshotsPath != "" {
		os.RemoveAll(project.ScreenshotsPath)
	}

	// Remove video directory
	if project.VideoPath != "" {
		videoDir := filepath.Dir(project.VideoPath)
		os.RemoveAll(videoDir)
	}

	// Remove audio files
	if project.AudioPath != "" {
		audioDir := filepath.Dir(project.AudioPath)
		os.RemoveAll(audioDir)
	}
}
