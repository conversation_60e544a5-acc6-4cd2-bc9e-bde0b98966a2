package handlers

import (
	"fmt"
	"net/http"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"

	"github.com/gin-gonic/gin"
)

// VideoHandler handles video generation operations
type VideoHandler struct {
	videoService *services.VideoService
	ttsService   *services.TTSService
	store        *services.DatabaseStoreService
	config       *config.Config
}

// NewVideoHandler creates a new video handler
func NewVideoHandler(videoService *services.VideoService, ttsService *services.TTSService, store *services.DatabaseStoreService, config *config.Config) *VideoHandler {
	return &VideoHandler{
		videoService: videoService,
		ttsService:   ttsService,
		store:        store,
		config:       config,
	}
}

// GenerateAudio generates audio files for a project
func (h *VideoHandler) GenerateAudio(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	// Validate project exists and has narration
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Check if project is in a valid state for audio generation
	validStates := []string{"narration_ready", "audio_failed", "video_failed"}
	isValidState := false
	for _, state := range validStates {
		if project.Status == state {
			isValidState = true
			break
		}
	}

	if !isValidState {
		// Check if we have narration content even if status is not ideal
		slides, err := h.store.GetSlidesByProject(projectID)
		if err != nil || len(slides) == 0 {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "Narration not ready. Please generate narration first.",
			})
			return
		}

		hasNarration := false
		for _, slide := range slides {
			if slide.NarrationText != "" {
				hasNarration = true
				break
			}
		}

		if !hasNarration {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "Narration not ready. Please generate narration first.",
			})
			return
		}
	}

	// Start audio generation in background
	go func() {
		if err := h.ttsService.GenerateAudio(projectID); err != nil {
			fmt.Printf("Error generating audio: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Audio generation started",
		Data: map[string]interface{}{
			"project_id": projectID,
			"status":     "generating",
		},
	})
}

// GetAudioProgress returns the progress of audio generation
func (h *VideoHandler) GetAudioProgress(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	progress, err := h.ttsService.GetAudioProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get progress: %v", err),
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"project_id": projectID,
			"progress":   progress,
			"status":     project.Status,
			"message":    h.getAudioProgressMessage(project.Status, 0),
		},
	})
}

// GenerateVideo generates the final video for a project
func (h *VideoHandler) GenerateVideo(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request models.VideoGenerationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		// Use default values if no request body
		request = models.VideoGenerationRequest{
			ProjectID:    projectID,
			OutputFormat: "mp4",
			Quality:      "high",
			FPS:          1,
			Resolution:   "1920x1080",
		}
	} else {
		request.ProjectID = projectID
	}

	// Validate project exists and has audio
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Check if project is in a valid state for video generation
	validStates := []string{"audio_ready", "video_failed", "failed", "generating_video"}
	isValidState := false
	for _, state := range validStates {
		if project.Status == state {
			isValidState = true
			break
		}
	}

	// Check for force parameter
	forceRestart := c.Query("force") == "true"

	// Provide detailed error information
	if !isValidState && !forceRestart {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Project status '%s' is not ready for video generation. Expected: %v. Current project info: ID=%s, Status=%s. Use ?force=true to force restart.",
				project.Status, validStates, project.ID, project.Status),
		})
		return
	}

	// If forcing restart, reset the project status
	if forceRestart && !isValidState {
		fmt.Printf("Force restarting video generation for project %s (current status: %s)\n", project.ID, project.Status)
		project.Status = "audio_ready"
		project.ErrorMessage = ""
		if err := h.store.UpdateProject(project); err != nil {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   fmt.Sprintf("Failed to reset project status: %v", err),
			})
			return
		}
	}

	if project.AudioPath == "" {
		// Check if slides have audio files
		slides, err := h.store.GetSlidesByProject(projectID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   fmt.Sprintf("Failed to check slides: %v", err),
			})
			return
		}

		audioCount := 0
		for _, slide := range slides {
			if slide.NarrationAudio != "" {
				audioCount++
			}
		}

		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Audio path is empty. Project has %d slides with audio out of %d total slides. Please ensure audio generation is complete.",
				audioCount, len(slides)),
		})
		return
	}

	// Start video generation in background
	go func() {
		if err := h.videoService.GenerateVideo(projectID, &request); err != nil {
			fmt.Printf("Error generating video: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Video generation started",
		Data: map[string]interface{}{
			"project_id": projectID,
			"status":     "generating",
			"request":    request,
		},
	})
}

// RetryVideoGeneration retries video generation for failed projects
func (h *VideoHandler) RetryVideoGeneration(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request models.VideoGenerationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		// Use default values if request is empty
		request = models.VideoGenerationRequest{
			FPS:          1,    // Default FPS
			Resolution:   "1280x720", // Default resolution
			OutputFormat: "mp4", // Default format
		}
	}

	// Get project
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Check if project can be retried
	retryableStates := []string{"video_failed", "audio_ready", "generating_video"}
	canRetry := false
	for _, state := range retryableStates {
		if project.Status == state {
			canRetry = true
			break
		}
	}

	// Check for force parameter
	forceRetry := c.Query("force") == "true"

	if !canRetry && !forceRetry {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Cannot retry video generation. Current status: %s. Retryable states: %v. Use ?force=true to force retry.",
				project.Status, retryableStates),
		})
		return
	}

	// If forcing retry, allow any status
	if forceRetry && !canRetry {
		fmt.Printf("Force retrying video generation for project %s (current status: %s)\n", project.ID, project.Status)
	}

	// Check if audio is available
	slides, err := h.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to check slides: %v", err),
		})
		return
	}

	audioCount := 0
	for _, slide := range slides {
		if slide.NarrationAudio != "" {
			audioCount++
		}
	}

	if audioCount == 0 {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No audio files found. Please generate audio first.",
		})
		return
	}

	// Reset project status for retry
	project.Status = "audio_ready"
	project.ErrorMessage = ""
	if err := h.store.UpdateProject(project); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to reset project status: %v", err),
		})
		return
	}

	// Start video generation in background
	go func() {
		if err := h.videoService.GenerateVideo(projectID, &request); err != nil {
			fmt.Printf("Error retrying video generation: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("Video generation retry started. Found %d slides with audio.", audioCount),
		Data: map[string]interface{}{
			"project_id":    projectID,
			"status":        "generating_video",
			"slides_with_audio": audioCount,
			"total_slides":  len(slides),
		},
	})
}

// DiagnoseProject provides detailed diagnostic information for video generation issues
func (h *VideoHandler) DiagnoseProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Get slides
	slides, err := h.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get slides: %v", err),
		})
		return
	}

	// Analyze project state
	diagnosis := map[string]interface{}{
		"project_id":     project.ID,
		"project_name":   project.Name,
		"current_status": project.Status,
		"error_message":  project.ErrorMessage,
		"audio_path":     project.AudioPath,
		"video_path":     project.VideoPath,
		"total_slides":   len(slides),
	}

	// Check slides
	slidesWithScreenshots := 0
	slidesWithNarration := 0
	slidesWithAudio := 0
	slideDetails := make([]map[string]interface{}, 0)

	for _, slide := range slides {
		hasScreenshot := slide.ScreenshotPath != ""
		hasNarration := slide.NarrationText != ""
		hasAudio := slide.NarrationAudio != ""

		if hasScreenshot {
			slidesWithScreenshots++
		}
		if hasNarration {
			slidesWithNarration++
		}
		if hasAudio {
			slidesWithAudio++
		}

		slideDetails = append(slideDetails, map[string]interface{}{
			"slide_number":    slide.SlideNumber,
			"title":           slide.Title,
			"has_screenshot":  hasScreenshot,
			"has_narration":   hasNarration,
			"has_audio":       hasAudio,
			"screenshot_path": slide.ScreenshotPath,
			"audio_path":      slide.NarrationAudio,
		})
	}

	diagnosis["slides_with_screenshots"] = slidesWithScreenshots
	diagnosis["slides_with_narration"] = slidesWithNarration
	diagnosis["slides_with_audio"] = slidesWithAudio
	diagnosis["slide_details"] = slideDetails

	// Determine readiness - allow more states for recovery
	validVideoStates := []string{"audio_ready", "video_failed", "failed", "generating_video"}
	canGenerateVideo := false
	for _, state := range validVideoStates {
		if project.Status == state {
			canGenerateVideo = true
			break
		}
	}
	hasRequiredAudio := slidesWithAudio > 0

	diagnosis["can_generate_video"] = canGenerateVideo && hasRequiredAudio
	diagnosis["readiness_check"] = map[string]interface{}{
		"status_ok":    canGenerateVideo,
		"has_audio":    hasRequiredAudio,
		"audio_count":  slidesWithAudio,
		"required_states": validVideoStates,
	}

	// Provide recommendations
	recommendations := []string{}
	if !canGenerateVideo {
		recommendations = append(recommendations, fmt.Sprintf("Project status is '%s', expected 'audio_ready' or 'video_failed'", project.Status))
	}
	if !hasRequiredAudio {
		recommendations = append(recommendations, "No audio files found. Generate audio first.")
	}
	if slidesWithScreenshots == 0 {
		recommendations = append(recommendations, "No screenshots found. Generate screenshots first.")
	}
	if slidesWithNarration == 0 {
		recommendations = append(recommendations, "No narration text found. Generate narration first.")
	}

	diagnosis["recommendations"] = recommendations

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    diagnosis,
	})
}

// GetVideoProgress returns the progress of video generation
func (h *VideoHandler) GetVideoProgress(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	progress, err := h.videoService.GetVideoProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get progress: %v", err),
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"project_id": projectID,
			"progress":   progress,
			"status":     project.Status,
			"message":    h.getVideoProgressMessage(project.Status, progress),
		},
	})
}

// GetVideo serves the generated video file
func (h *VideoHandler) GetVideo(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	if project.VideoPath == "" {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Video not generated yet",
		})
		return
	}

	// Set appropriate headers for video download
	c.Header("Content-Type", "video/mp4")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s.mp4\"", project.Name))

	// Serve the video file
	c.File(project.VideoPath)
}

// GetVideoInfo returns information about the generated video
func (h *VideoHandler) GetVideoInfo(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	videoInfo, err := h.videoService.GetVideoInfo(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Video info not available: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    videoInfo,
	})
}



// ResumeAudio resumes audio generation from the last successful point
func (h *VideoHandler) ResumeAudio(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	// Check if project can be resumed
	progress, err := h.ttsService.GetAudioProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to check resume status: %v", err),
		})
		return
	}

	if !progress.CanResume {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project cannot be resumed. Either no slides have audio or all slides already have audio.",
		})
		return
	}

	// Start audio generation (it will automatically resume from the correct point)
	go func() {
		err := h.ttsService.GenerateAudio(projectID)
		if err != nil {
			fmt.Printf("Background audio generation failed: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("Audio generation resumed from slide %d", progress.CurrentSlide),
		Data: map[string]interface{}{
			"current_slide":    progress.CurrentSlide,
			"completed_slides": progress.CompletedSlides,
			"total_slides":     progress.TotalSlides,
		},
	})
}

// GetSlideAudio serves audio for a specific slide
func (h *VideoHandler) GetSlideAudio(c *gin.Context) {
	projectID := c.Param("projectId")
	slideNumber := c.Param("slideNumber")

	if projectID == "" || slideNumber == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID and slide number are required",
		})
		return
	}

	// Convert slide number to int
	var slideNum int
	if _, err := fmt.Sscanf(slideNumber, "%d", &slideNum); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid slide number",
		})
		return
	}

	audioPath, err := h.ttsService.GetSlideAudio(projectID, slideNum)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Audio not found",
		})
		return
	}

	// Set appropriate headers for audio
	c.Header("Content-Type", "audio/mpeg")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"slide_%d.mp3\"", slideNum))

	// Serve the audio file
	c.File(audioPath)
}

// GetProjectAudio serves the combined audio for the entire project
func (h *VideoHandler) GetProjectAudio(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	if project.AudioPath == "" {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Audio not generated yet",
		})
		return
	}

	// Set appropriate headers for audio download
	c.Header("Content-Type", "audio/mpeg")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s_audio.mp3\"", project.Name))

	// Serve the audio file
	c.File(project.AudioPath)
}

// ValidateFFmpeg checks if FFmpeg is available
func (h *VideoHandler) ValidateFFmpeg(c *gin.Context) {
	err := h.videoService.ValidateFFmpeg()
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("FFmpeg validation failed: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "FFmpeg is available and working",
	})
}

// getAudioProgressMessage returns a user-friendly progress message for audio generation
func (h *VideoHandler) getAudioProgressMessage(status string, progress int) string {
	switch status {
	case "generating_audio":
		return fmt.Sprintf("Generating audio... %d%% complete", progress)
	case "audio_ready":
		return "Audio generation completed"
	case "failed":
		return "Audio generation failed"
	default:
		return "Waiting to start audio generation"
	}
}

// getVideoProgressMessage returns a user-friendly progress message for video generation
func (h *VideoHandler) getVideoProgressMessage(status string, progress int) string {
	switch status {
	case "generating_video":
		return fmt.Sprintf("Generating video... %d%% complete", progress)
	case "completed":
		return "Video generation completed"
	case "failed":
		return "Video generation failed"
	default:
		return "Waiting to start video generation"
	}
}

// ForceRestartVideoGeneration forcefully restarts video generation regardless of current status
func (h *VideoHandler) ForceRestartVideoGeneration(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request models.VideoGenerationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		// Use default values if request is empty
		request = models.VideoGenerationRequest{
			FPS:          1,    // Default FPS
			Resolution:   "1280x720", // Default resolution
			OutputFormat: "mp4", // Default format
		}
	}

	// Get project
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Check if audio is available
	slides, err := h.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to check slides: %v", err),
		})
		return
	}

	audioCount := 0
	for _, slide := range slides {
		if slide.NarrationAudio != "" {
			audioCount++
		}
	}

	if audioCount == 0 {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No audio files found. Please generate audio first.",
		})
		return
	}

	// Force reset project status regardless of current state
	originalStatus := project.Status
	project.Status = "audio_ready"
	project.ErrorMessage = ""
	if err := h.store.UpdateProject(project); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to reset project status: %v", err),
		})
		return
	}

	fmt.Printf("Force restarting video generation for project %s (original status: %s -> audio_ready)\n", project.ID, originalStatus)

	// Start video generation in background
	go func() {
		if err := h.videoService.GenerateVideo(projectID, &request); err != nil {
			fmt.Printf("Error in force restart video generation: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("Video generation force restarted. Found %d slides with audio. Original status: %s", audioCount, originalStatus),
		Data: map[string]interface{}{
			"project_id":        projectID,
			"status":            "generating_video",
			"original_status":   originalStatus,
			"slides_with_audio": audioCount,
			"total_slides":      len(slides),
			"force_restart":     true,
		},
	})
}

// GetSubtitleStyleTemplates returns available subtitle style templates
func (h *VideoHandler) GetSubtitleStyleTemplates(c *gin.Context) {
	templates := make([]map[string]interface{}, 0, len(h.config.SubtitleStyleTemplates))

	for key, template := range h.config.SubtitleStyleTemplates {
		templateInfo := map[string]interface{}{
			"id":               key,
			"name":             template.Name,
			"description":      template.Description,
			"font_size":        template.FontSize,
			"font_color":       template.FontColor,
			"font_family":      template.FontFamily,
			"outline":          template.Outline,
			"shadow":           template.Shadow,
			"background_color": template.BackgroundColor,
			"position":         template.Position,
		}
		templates = append(templates, templateInfo)
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Subtitle style templates retrieved successfully",
		Data: map[string]interface{}{
			"templates": templates,
			"count":     len(templates),
		},
	})
}
