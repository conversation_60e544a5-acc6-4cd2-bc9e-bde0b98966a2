package handlers

import (
	"fmt"
	"net/http"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"

	"github.com/gin-gonic/gin"
)

// TTSHandler handles TTS-related operations
type TTSHandler struct {
	config     *config.Config
	ttsService *services.TTSService
	store      *services.DatabaseStoreService
}

// NewTTSHandler creates a new TTS handler
func NewTTSHandler(cfg *config.Config, ttsService *services.TTSService, store *services.DatabaseStoreService) *TTSHandler {
	return &TTSHandler{
		config:     cfg,
		ttsService: ttsService,
		store:      store,
	}
}

// TTSProviderInfo represents information about a TTS provider
type TTSProviderInfo struct {
	Name        string   `json:"name"`
	DisplayName string   `json:"display_name"`
	Description string   `json:"description"`
	Voices      []string `json:"voices"`
	Available   bool     `json:"available"`
	Current     bool     `json:"current"`
}

// GetTTSProviders returns information about available TTS providers
func (h *TTSHandler) GetTTSProviders(c *gin.Context) {
	providers := []TTSProviderInfo{
		{
			Name:        "openai",
			DisplayName: "OpenAI TTS",
			Description: "OpenAI的高质量语音合成服务",
			Voices:      []string{"alloy", "echo", "fable", "onyx", "nova", "shimmer"},
			Available:   h.config.OpenAIAPIKey != "",
			Current:     h.config.TTSProvider == "openai",
		},
		{
			Name:        "minimax",
			DisplayName: "MiniMax TTS",
			Description: "MiniMax的中文语音合成服务",
			Voices:      []string{"male-qn-qingse", "female-tianmei", "male-qn-jingying", "female-qn-qingse"},
			Available:   h.config.MinimaxTTSAPIKey != "",
			Current:     h.config.TTSProvider == "minimax",
		},
		{
			Name:        "easyvoice",
			DisplayName: "EasyVoice TTS",
			Description: "EasyVoice的多语言语音合成服务",
			Voices:      h.getEasyVoiceVoices(),
			Available:   h.config.EasyVoiceUsername != "" && h.config.EasyVoicePassword != "",
			Current:     h.config.TTSProvider == "easyvoice",
		},
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"providers":        providers,
			"current_provider": h.config.TTSProvider,
		},
	})
}

// SetTTSProviderRequest represents a request to change TTS provider
type SetTTSProviderRequest struct {
	Provider string `json:"provider" binding:"required"`
	Voice    string `json:"voice,omitempty"`
}

// SetTTSProvider changes the current TTS provider
func (h *TTSHandler) SetTTSProvider(c *gin.Context) {
	var request SetTTSProviderRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	// Validate provider
	validProviders := []string{"openai", "minimax", "easyvoice"}
	isValid := false
	for _, provider := range validProviders {
		if request.Provider == provider {
			isValid = true
			break
		}
	}

	if !isValid {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid TTS provider. Supported providers: openai, minimax, easyvoice",
		})
		return
	}

	// Check if provider is available
	switch request.Provider {
	case "openai":
		if h.config.OpenAIAPIKey == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "OpenAI API key is not configured",
			})
			return
		}
	case "minimax":
		if h.config.MinimaxTTSAPIKey == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "MiniMax TTS API key is not configured",
			})
			return
		}
	case "easyvoice":
		if h.config.EasyVoiceUsername == "" || h.config.EasyVoicePassword == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "EasyVoice credentials are not configured",
			})
			return
		}
	}

	// Update configuration (Note: This is runtime change, not persistent)
	h.config.TTSProvider = request.Provider
	if request.Voice != "" {
		switch request.Provider {
		case "openai":
			h.config.TTSVoice = request.Voice
		case "minimax":
			h.config.MinimaxTTSVoiceID = request.Voice
		case "easyvoice":
			h.config.EasyVoiceVoice = request.Voice
		}
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("TTS provider changed to %s", request.Provider),
		Data: map[string]interface{}{
			"provider": request.Provider,
			"voice":    request.Voice,
		},
	})
}

// TestTTSProvider tests a TTS provider with sample text
func (h *TTSHandler) TestTTSProvider(c *gin.Context) {
	provider := c.Param("provider")
	if provider == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Provider parameter is required",
		})
		return
	}

	// Test text
	testText := "这是一个语音合成测试。"

	// Temporarily change provider for testing
	originalProvider := h.config.TTSProvider
	h.config.TTSProvider = provider

	// Restore original provider after test
	defer func() {
		h.config.TTSProvider = originalProvider
	}()

	// Test audio generation
	switch provider {
	case "openai":
		if h.config.OpenAIAPIKey == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "OpenAI API key is not configured",
			})
			return
		}
	case "minimax":
		if h.config.MinimaxTTSAPIKey == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "MiniMax TTS API key is not configured",
			})
			return
		}
	case "easyvoice":
		if h.config.EasyVoiceUsername == "" || h.config.EasyVoicePassword == "" {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "EasyVoice credentials are not configured",
			})
			return
		}
	default:
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Unsupported TTS provider",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("TTS provider %s is available and configured correctly", provider),
		Data: map[string]interface{}{
			"provider":   provider,
			"test_text":  testText,
			"status":     "ready",
		},
	})
}

// getEasyVoiceVoices returns available EasyVoice voices
func (h *TTSHandler) getEasyVoiceVoices() []string {
	return []string{
		"zh-CN-YunxiNeural",
		"zh-CN-YunyangNeural",
		"zh-CN-XiaoxiaoNeural",
		"zh-CN-XiaohanNeural",
		"zh-CN-XiaomengNeural",
		"zh-CN-XiaomoNeural",
		"zh-CN-XiaoqiuNeural",
		"zh-CN-XiaoruiNeural",
		"zh-CN-XiaoshuangNeural",
		"zh-CN-XiaoxuanNeural",
		"zh-CN-XiaoyanNeural",
		"zh-CN-XiaoyouNeural",
		"zh-CN-XiaozhenNeural",
		"zh-CN-YunjianNeural",
		"zh-CN-YunfengNeural",
		"zh-CN-YunhaoNeural",
		"zh-CN-YunjieNeural",
		"zh-CN-YunxiaNeural",
		"zh-CN-YunyeNeural",
		"zh-CN-YunzeNeural",
	}
}
