package handlers

import (
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
	"strings"

	"github.com/gin-gonic/gin"
)

// AudioDebugHandler handles audio debugging requests
type AudioDebugHandler struct {
	cfg   *config.Config
	store *services.DatabaseStoreService
}

// NewAudioDebugHandler creates a new audio debug handler
func NewAudioDebugHandler(cfg *config.Config, store *services.DatabaseStoreService) *AudioDebugHandler {
	return &AudioDebugHandler{
		cfg:   cfg,
		store: store,
	}
}

// DiagnoseAudioIssues diagnoses audio issues for a project
func (adh *AudioDebugHandler) DiagnoseAudioIssues(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := adh.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Get slides
	slides, err := adh.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get slides",
		})
		return
	}

	diagnosis := gin.H{
		"project_id":   projectID,
		"project_name": project.Name,
		"status":       project.Status,
		"slides_count": len(slides),
		"audio_files":  []gin.H{},
		"issues":       []string{},
		"suggestions":  []string{},
	}

	// Check audio directory
	audioDir := filepath.Join(adh.cfg.UploadDir, projectID, "audio")
	if _, err := os.Stat(audioDir); os.IsNotExist(err) {
		diagnosis["issues"] = append(diagnosis["issues"].([]string), "Audio directory does not exist")
		diagnosis["suggestions"] = append(diagnosis["suggestions"].([]string), "Run audio generation for this project")
	} else {
		// Analyze audio files
		audioFiles := []gin.H{}
		issues := diagnosis["issues"].([]string)
		suggestions := diagnosis["suggestions"].([]string)

		// Check individual slide audio files
		for _, slide := range slides {
			slideAudioPath := filepath.Join(audioDir, fmt.Sprintf("slide_%03d.mp3", slide.SlideNumber))
			audioInfo := adh.analyzeAudioFile(slideAudioPath, fmt.Sprintf("slide_%03d.mp3", slide.SlideNumber))
			audioFiles = append(audioFiles, audioInfo)

			if !audioInfo["exists"].(bool) {
				issues = append(issues, fmt.Sprintf("Missing audio for slide %d", slide.SlideNumber))
			} else if audioInfo["size"].(int64) < 1000 {
				issues = append(issues, fmt.Sprintf("Audio file for slide %d is too small", slide.SlideNumber))
			}
		}

		// Check combined audio file
		combinedAudioPath := filepath.Join(audioDir, "combined.mp3")
		combinedInfo := adh.analyzeAudioFile(combinedAudioPath, "combined.mp3")
		audioFiles = append(audioFiles, combinedInfo)

		if !combinedInfo["exists"].(bool) {
			issues = append(issues, "Combined audio file does not exist")
			suggestions = append(suggestions, "Re-run audio generation to create combined file")
		} else if combinedInfo["size"].(int64) < 1000 {
			issues = append(issues, "Combined audio file is too small")
			suggestions = append(suggestions, "Check individual audio files and re-generate if needed")
		}

		diagnosis["audio_files"] = audioFiles
		diagnosis["issues"] = issues
		diagnosis["suggestions"] = suggestions
	}

	// Check FFmpeg availability
	if err := adh.checkFFmpeg(); err != nil {
		diagnosis["issues"] = append(diagnosis["issues"].([]string), "FFmpeg not available: "+err.Error())
		diagnosis["suggestions"] = append(diagnosis["suggestions"].([]string), "Install FFmpeg and ensure it's in PATH")
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"diagnosis": diagnosis,
	})
}

// analyzeAudioFile analyzes a single audio file
func (adh *AudioDebugHandler) analyzeAudioFile(filePath, fileName string) gin.H {
	info := gin.H{
		"name":     fileName,
		"path":     filePath,
		"exists":   false,
		"size":     int64(0),
		"duration": "0",
		"format":   "unknown",
		"issues":   []string{},
	}

	// Check if file exists
	stat, err := os.Stat(filePath)
	if err != nil {
		info["issues"] = append(info["issues"].([]string), "File does not exist")
		return info
	}

	info["exists"] = true
	info["size"] = stat.Size()

	// Check file size
	if stat.Size() < 100 {
		info["issues"] = append(info["issues"].([]string), "File is too small (likely empty)")
	}

	// Try to get audio info using ffprobe
	if duration, format, err := adh.getAudioInfo(filePath); err == nil {
		info["duration"] = duration
		info["format"] = format
		
		if duration == "0.000000" || duration == "" {
			info["issues"] = append(info["issues"].([]string), "Audio has no duration")
		}
	} else {
		info["issues"] = append(info["issues"].([]string), "Cannot read audio metadata: "+err.Error())
	}

	return info
}

// getAudioInfo gets audio information using ffprobe
func (adh *AudioDebugHandler) getAudioInfo(filePath string) (duration, format string, err error) {
	// Try to get duration
	cmd := exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=duration", "-of", "csv=p=0", filePath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", "", fmt.Errorf("ffprobe failed: %w", err)
	}
	duration = strings.TrimSpace(string(output))

	// Try to get format
	cmd = exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=format_name", "-of", "csv=p=0", filePath)
	output, err = cmd.CombinedOutput()
	if err != nil {
		format = "unknown"
	} else {
		format = strings.TrimSpace(string(output))
	}

	return duration, format, nil
}

// checkFFmpeg checks if FFmpeg is available
func (adh *AudioDebugHandler) checkFFmpeg() error {
	cmd := exec.Command("ffmpeg", "-version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ffmpeg not found or not working")
	}
	return nil
}

// FixAudioIssues attempts to fix common audio issues
func (adh *AudioDebugHandler) FixAudioIssues(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	var request struct {
		FixType string `json:"fix_type"` // "regenerate", "combine", "validate"
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
		})
		return
	}

	result := gin.H{
		"project_id": projectID,
		"fix_type":   request.FixType,
		"actions":    []string{},
		"success":    false,
	}

	switch request.FixType {
	case "validate":
		// Validate all audio files
		actions, success := adh.validateProjectAudio(projectID)
		result["actions"] = actions
		result["success"] = success

	case "combine":
		// Re-combine existing audio files
		actions, success := adh.recombineAudio(projectID)
		result["actions"] = actions
		result["success"] = success

	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported fix type. Use 'validate' or 'combine'",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"result":  result,
	})
}

// validateProjectAudio validates all audio files in a project
func (adh *AudioDebugHandler) validateProjectAudio(projectID string) ([]string, bool) {
	actions := []string{}
	audioDir := filepath.Join(adh.cfg.UploadDir, projectID, "audio")

	// Check if audio directory exists
	if _, err := os.Stat(audioDir); os.IsNotExist(err) {
		actions = append(actions, "Audio directory does not exist")
		return actions, false
	}

	// Find all audio files
	files, err := filepath.Glob(filepath.Join(audioDir, "*.mp3"))
	if err != nil {
		actions = append(actions, "Failed to list audio files: "+err.Error())
		return actions, false
	}

	allValid := true
	for _, file := range files {
		if duration, _, err := adh.getAudioInfo(file); err != nil {
			actions = append(actions, fmt.Sprintf("Invalid audio file: %s - %v", filepath.Base(file), err))
			allValid = false
		} else if duration == "0.000000" || duration == "" {
			actions = append(actions, fmt.Sprintf("Empty audio file: %s", filepath.Base(file)))
			allValid = false
		} else {
			actions = append(actions, fmt.Sprintf("Valid audio file: %s (duration: %s)", filepath.Base(file), duration))
		}
	}

	return actions, allValid
}

// recombineAudio re-combines existing audio files
func (adh *AudioDebugHandler) recombineAudio(projectID string) ([]string, bool) {
	actions := []string{}
	audioDir := filepath.Join(adh.cfg.UploadDir, projectID, "audio")

	// Find slide audio files
	slideFiles := []string{}
	for i := 1; i <= 100; i++ { // Check up to 100 slides
		slideFile := filepath.Join(audioDir, fmt.Sprintf("slide_%03d.mp3", i))
		if _, err := os.Stat(slideFile); err == nil {
			slideFiles = append(slideFiles, slideFile)
		}
	}

	if len(slideFiles) == 0 {
		actions = append(actions, "No slide audio files found")
		return actions, false
	}

	actions = append(actions, fmt.Sprintf("Found %d slide audio files", len(slideFiles)))

	// Create simple combination using FFmpeg
	combinedPath := filepath.Join(audioDir, "combined_fixed.mp3")
	if err := adh.combineAudioFilesSimple(slideFiles, combinedPath); err != nil {
		actions = append(actions, "Failed to combine audio files: "+err.Error())
		return actions, false
	}

	actions = append(actions, "Successfully created combined_fixed.mp3")
	return actions, true
}

// combineAudioFilesSimple combines audio files using basic FFmpeg concat
func (adh *AudioDebugHandler) combineAudioFilesSimple(inputFiles []string, outputPath string) error {
	// Create temporary file list
	listFile := filepath.Join(adh.cfg.TempDir, fmt.Sprintf("fix_audio_list_%d.txt", os.Getpid()))
	defer os.Remove(listFile)

	file, err := os.Create(listFile)
	if err != nil {
		return fmt.Errorf("failed to create file list: %w", err)
	}
	defer file.Close()

	for _, audioFile := range inputFiles {
		_, err := file.WriteString(fmt.Sprintf("file '%s'\n", audioFile))
		if err != nil {
			return fmt.Errorf("failed to write file list: %w", err)
		}
	}
	file.Close()

	// Use FFmpeg to combine
	cmd := exec.Command("ffmpeg",
		"-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-c", "copy",
		"-y",
		outputPath,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg failed: %w, output: %s", err, string(output))
	}

	return nil
}
