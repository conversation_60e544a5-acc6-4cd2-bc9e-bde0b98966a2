package handlers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/services"
	"strings"

	"github.com/gin-gonic/gin"
)

// PipelineHandler handles full pipeline processing requests
type PipelineHandler struct {
	cfg             *config.Config
	store           *services.DatabaseStoreService
	pipelineService *services.PipelineService
}

// NewPipelineHandler creates a new pipeline handler
func NewPipelineHandler(
	cfg *config.Config,
	store *services.DatabaseStoreService,
	pipelineService *services.PipelineService,
) *PipelineHandler {
	return &PipelineHandler{
		cfg:             cfg,
		store:           store,
		pipelineService: pipelineService,
	}
}

// UploadAndProcessRequest represents the request for upload and process
type UploadAndProcessRequest struct {
	UserRequirements       string `form:"user_requirements" json:"user_requirements"`
	ProjectName            string `form:"project_name" json:"project_name"`
	EnableSubtitles        bool   `form:"enable_subtitles" json:"enable_subtitles"`
	SubtitleStyleTemplate  string `form:"subtitle_style_template" json:"subtitle_style_template"`
}

// UploadAndProcess handles PPTX upload and starts the full processing pipeline
func (ph *PipelineHandler) UploadAndProcess(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(100 << 20); err != nil { // 100MB max
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to parse multipart form: " + err.Error(),
		})
		return
	}

	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "No file uploaded or invalid file: " + err.Error(),
		})
		return
	}
	defer file.Close()

	// Get form parameters
	userRequirements := c.PostForm("user_requirements")
	projectName := c.PostForm("project_name")
	enableSubtitles := c.PostForm("enable_subtitles") == "true"
	subtitleStyleTemplate := c.PostForm("subtitle_style_template")

	// Validate file type
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".pptx") {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Only PPTX files are supported",
		})
		return
	}

	// Set default project name if not provided
	if projectName == "" {
		projectName = strings.TrimSuffix(header.Filename, filepath.Ext(header.Filename))
	}

	// Create project record first to get the database-generated ID
	createdProject, err := ph.store.CreateProject(projectName, "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create project record: " + err.Error(),
		})
		return
	}

	// Use the database-generated project ID
	projectID := createdProject.ID

	// Create project directory
	projectDir := filepath.Join(ph.cfg.UploadDir, projectID)
	if err := os.MkdirAll(projectDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create project directory: " + err.Error(),
		})
		return
	}

	// Save uploaded file
	filename := fmt.Sprintf("%s_%s", projectID, header.Filename)
	filePath := filepath.Join(projectDir, filename)

	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create file: " + err.Error(),
		})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to save file: " + err.Error(),
		})
		return
	}

	// Update the created project with additional fields
	createdProject.FilePath = filePath
	createdProject.OriginalFileName = header.Filename
	createdProject.FileSize = header.Size
	createdProject.Status = "processing"
	if err := ph.store.UpdateProject(createdProject); err != nil {
		// Clean up file if database operation fails
		os.RemoveAll(projectDir)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update project record: " + err.Error(),
		})
		return
	}

	// Create pipeline configuration
	config := &services.PipelineConfig{
		UserRequirements:       userRequirements,
		EnableSubtitles:        enableSubtitles,
		SubtitleStyleTemplate:  subtitleStyleTemplate,
	}

	// Start the full processing pipeline using the created project ID
	if err := ph.pipelineService.StartFullPipeline(createdProject.ID, config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start processing pipeline: " + err.Error(),
		})
		return
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "File uploaded successfully and processing started",
		"project_id": createdProject.ID,
		"project":    createdProject,
	})
}

// GetPipelineProgress returns the current progress of the processing pipeline
func (ph *PipelineHandler) GetPipelineProgress(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get pipeline progress
	progress, err := ph.pipelineService.GetProgress(projectID)
	if err != nil {
		// If no pipeline progress found, check project status
		project, projectErr := ph.store.GetProject(projectID)
		if projectErr != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "Project not found",
			})
			return
		}

		// Return project status as progress
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"progress": gin.H{
				"project_id":     projectID,
				"current_stage":  ph.getStageFromStatus(project.Status),
				"progress":       ph.getProgressFromStatus(project.Status),
				"message":        fmt.Sprintf("Project status: %s", project.Status),
				"start_time":     project.CreatedAt,
				"last_update":    project.UpdatedAt,
				"stage_details":  gin.H{},
				"can_retry":      project.Status == "failed" || project.Status == "error",
				"retry_from":     "screenshot",
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"progress": progress,
	})
}

// RetryPipeline retries the processing pipeline from a specific stage
func (ph *PipelineHandler) RetryPipeline(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Parse request body
	var req struct {
		Stage            string `json:"stage"`
		UserRequirements string `json:"user_requirements"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate stage
	stage := services.PipelineStage(req.Stage)
	validStages := []services.PipelineStage{
		services.StageScreenshot,
		services.StageNarration,
		services.StageAudio,
		services.StageVideo,
	}

	isValidStage := false
	for _, validStage := range validStages {
		if stage == validStage {
			isValidStage = true
			break
		}
	}

	if !isValidStage {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid stage. Valid stages: screenshot, narration, audio, video",
		})
		return
	}

	// Check if project exists
	project, err := ph.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Check current pipeline status
	progress, err := ph.pipelineService.GetProgress(projectID)
	if err != nil {
		// No existing progress, this is fine for completed projects
		progress = nil
	}

	// Allow retry for completed projects or failed projects
	canRetry := false
	if progress == nil {
		// No active pipeline, allow retry for any project
		canRetry = true
	} else if progress.CurrentStage == "failed" {
		// Failed pipeline, allow retry
		canRetry = true
	} else if progress.CurrentStage == "completed" {
		// Completed pipeline, allow retry to regenerate content
		canRetry = true
	} else if progress.CanRetry {
		// Pipeline explicitly marked as retryable
		canRetry = true
	}

	if !canRetry && progress != nil && progress.CurrentStage != "completed" {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Cannot retry: pipeline is currently %s", progress.CurrentStage),
		})
		return
	}

	// Retry pipeline from specified stage
	if err := ph.pipelineService.RetryFromStage(projectID, stage, req.UserRequirements); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retry pipeline: " + err.Error(),
		})
		return
	}

	// Determine the action message based on project status
	actionMessage := "Pipeline retry started"
	if project.Status == "completed" {
		actionMessage = "Pipeline regeneration started"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("%s from %s stage", actionMessage, req.Stage),
		"project_status": project.Status,
		"retry_stage": req.Stage,
	})
}

// CancelPipeline cancels a running processing pipeline
func (ph *PipelineHandler) CancelPipeline(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Check if project exists
	_, err := ph.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Cancel pipeline
	if err := ph.pipelineService.CancelPipeline(projectID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Pipeline cancelled successfully",
	})
}

// GetRetryOptions returns available retry options for a project
func (ph *PipelineHandler) GetRetryOptions(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := ph.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Get current pipeline progress
	progress, err := ph.pipelineService.GetProgress(projectID)
	if err != nil {
		progress = nil // No active pipeline
	}

	// Determine available retry options
	retryOptions := gin.H{
		"project_id":     projectID,
		"project_name":   project.Name,
		"project_status": project.Status,
		"can_retry":      false,
		"available_stages": []gin.H{},
		"current_stage":  "",
		"is_running":     false,
	}

	if progress != nil {
		retryOptions["current_stage"] = progress.CurrentStage
		retryOptions["is_running"] = progress.CurrentStage != "completed" && progress.CurrentStage != "failed"
	}

	// Define all available stages with descriptions
	allStages := []gin.H{
		{
			"stage":       "screenshot",
			"name":        "截图生成",
			"description": "重新生成PPT幻灯片截图",
			"progress":    0,
		},
		{
			"stage":       "narration",
			"name":        "讲稿生成",
			"description": "重新生成AI讲稿内容",
			"progress":    20,
		},
		{
			"stage":       "audio",
			"name":        "音频合成",
			"description": "重新生成语音音频文件",
			"progress":    40,
		},
		{
			"stage":       "video",
			"name":        "视频制作",
			"description": "重新生成最终演示视频",
			"progress":    70,
		},
	}

	// Determine if retry is allowed
	canRetry := false
	if progress == nil {
		// No active pipeline
		canRetry = true
	} else if progress.CurrentStage == "failed" {
		// Failed pipeline
		canRetry = true
	} else if progress.CurrentStage == "completed" {
		// Completed pipeline - allow regeneration
		canRetry = true
	} else if progress.CanRetry {
		// Explicitly retryable
		canRetry = true
	}

	retryOptions["can_retry"] = canRetry

	if canRetry {
		retryOptions["available_stages"] = allStages
		if project.Status == "completed" {
			retryOptions["retry_type"] = "regenerate"
			retryOptions["retry_message"] = "项目已完成，可以重新生成任意阶段的内容"
		} else {
			retryOptions["retry_type"] = "retry"
			retryOptions["retry_message"] = "项目失败或可重试，可以从任意阶段重新开始"
		}
	} else {
		retryOptions["retry_message"] = fmt.Sprintf("项目正在处理中（%s阶段），无法重试", progress.CurrentStage)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    retryOptions,
	})
}

// GetDetailedError returns detailed error information for debugging
func (ph *PipelineHandler) GetDetailedError(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get pipeline progress with detailed error
	progress, err := ph.pipelineService.GetProgress(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Pipeline progress not found",
		})
		return
	}

	// Return detailed error information
	if progress.DetailedError != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":        true,
			"detailed_error": progress.DetailedError,
			"project_id":     projectID,
			"current_stage":  progress.CurrentStage,
			"progress":       progress.Progress,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "No detailed error information available",
			"project_id": projectID,
			"current_stage": progress.CurrentStage,
		})
	}
}

// GetPipelineStatus returns the overall status of the pipeline
func (ph *PipelineHandler) GetPipelineStatus(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Project ID is required",
		})
		return
	}

	// Get project
	project, err := ph.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Project not found",
		})
		return
	}

	// Get pipeline progress if available
	progress, _ := ph.pipelineService.GetProgress(projectID)

	// Check if pipeline is running
	isRunning := ph.pipelineService.IsRunning(projectID)

	// Get slides count
	slides, _ := ph.store.GetSlidesByProject(projectID)
	slideCount := len(slides)

	// Count completed stages
	screenshotCount := 0
	narrationCount := 0
	audioCount := 0
	for _, slide := range slides {
		if slide.ScreenshotPath != "" {
			screenshotCount++
		}
		if slide.NarrationText != "" {
			narrationCount++
		}
		if slide.NarrationAudio != "" {
			audioCount++
		}
	}

	hasVideo := project.VideoPath != ""

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"status": gin.H{
			"project_id":       projectID,
			"project_name":     project.Name,
			"project_status":   project.Status,
			"is_running":       isRunning,
			"slide_count":      slideCount,
			"screenshot_count": screenshotCount,
			"narration_count":  narrationCount,
			"audio_count":      audioCount,
			"has_video":        hasVideo,
			"created_at":       project.CreatedAt,
			"updated_at":       project.UpdatedAt,
			"progress":         progress,
		},
	})
}

// ListActivePipelines returns all currently active pipelines
func (ph *PipelineHandler) ListActivePipelines(c *gin.Context) {
	// Get all projects
	projects, err := ph.store.ListProjects()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get projects: " + err.Error(),
		})
		return
	}

	// Filter active pipelines
	var activePipelines []gin.H
	for _, project := range projects {
		if ph.pipelineService.IsRunning(project.ID) {
			progress, _ := ph.pipelineService.GetProgress(project.ID)
			activePipelines = append(activePipelines, gin.H{
				"project_id":   project.ID,
				"project_name": project.Name,
				"status":       project.Status,
				"progress":     progress,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"pipelines": activePipelines,
		"count":     len(activePipelines),
	})
}

// getStageFromStatus converts project status to pipeline stage
func (ph *PipelineHandler) getStageFromStatus(status string) string {
	switch status {
	case "processing", "screenshots_ready":
		return "screenshot"
	case "narration_ready":
		return "narration"
	case "audio_ready":
		return "audio"
	case "generating_video":
		return "video"
	case "completed":
		return "completed"
	case "failed", "error":
		return "failed"
	default:
		return "unknown"
	}
}

// getProgressFromStatus converts project status to progress percentage
func (ph *PipelineHandler) getProgressFromStatus(status string) float64 {
	switch status {
	case "processing":
		return 10
	case "screenshots_ready":
		return 20
	case "narration_ready":
		return 40
	case "audio_ready":
		return 70
	case "generating_video":
		return 90
	case "completed":
		return 100
	case "failed", "error":
		return 0
	default:
		return 0
	}
}
