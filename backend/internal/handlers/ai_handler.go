package handlers

import (
	"fmt"
	"net/http"
	"ppt-narrator/internal/models"
	"ppt-narrator/internal/services"

	"github.com/gin-gonic/gin"
)

// AIHandler handles AI-related operations
type AIHandler struct {
	aiService *services.AIService
	store     *services.DatabaseStoreService
}

// NewAIHandler creates a new AI handler
func NewAIHandler(aiService *services.AIService, store *services.DatabaseStoreService) *AIHandler {
	return &AIHandler{
		aiService: aiService,
		store:     store,
	}
}

// GenerateNarration generates narration for a project
func (h *AIHandler) GenerateNarration(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request models.NarrationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	// Validate project exists and has screenshots
	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	// Check if project is in a valid state for narration generation
	validStates := []string{"screenshots_ready", "completed", "narration_failed", "audio_failed", "video_failed"}
	isValidState := false
	for _, state := range validStates {
		if project.Status == state {
			isValidState = true
			break
		}
	}

	// Check for force parameter
	forceRestart := c.Query("force") == "true"

	if !isValidState && !forceRestart {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Project status '%s' is not ready for narration generation. Expected: %v. Use ?force=true to force restart.", project.Status, validStates),
		})
		return
	}

	// If forcing restart, check if we have screenshots
	if forceRestart && !isValidState {
		slides, err := h.store.GetSlidesByProject(projectID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   fmt.Sprintf("Failed to check slides: %v", err),
			})
			return
		}

		screenshotCount := 0
		for _, slide := range slides {
			if slide.ScreenshotPath != "" {
				screenshotCount++
			}
		}

		if screenshotCount == 0 {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Error:   "No screenshots found. Please generate screenshots first.",
			})
			return
		}

		fmt.Printf("Force restarting narration generation for project %s (current status: %s)\n", project.ID, project.Status)
	}

	// Start narration generation in background
	go func() {
		if err := h.aiService.GenerateNarration(projectID, request.UserRequirements); err != nil {
			fmt.Printf("Error generating narration: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Narration generation started",
		Data: map[string]interface{}{
			"project_id": projectID,
			"status":     "generating",
		},
	})
}

// GetNarrationProgress returns the progress of narration generation
func (h *AIHandler) GetNarrationProgress(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	progress, err := h.aiService.GetNarrationProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get progress: %v", err),
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"project_id": projectID,
			"progress":   progress,
			"status":     project.Status,
			"message":    h.getProgressMessage(project.Status, progress),
		},
	})
}

// GetNarration returns the generated narration for a project
func (h *AIHandler) GetNarration(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	project, err := h.store.GetProject(projectID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Project not found",
		})
		return
	}

	if project.NarrationScript == "" {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Narration not generated yet",
		})
		return
	}

	// Get slides with narration
	slides, err := h.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get slides: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"project_id":     projectID,
			"full_narration": project.NarrationScript,
			"slides":         slides,
			"total_slides":   len(slides),
		},
	})
}

// GetSlideNarration returns narration for a specific slide
func (h *AIHandler) GetSlideNarration(c *gin.Context) {
	projectID := c.Param("projectId")
	slideNumber := c.Param("slideNumber")

	if projectID == "" || slideNumber == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID and slide number are required",
		})
		return
	}

	slides, err := h.store.GetSlidesByProject(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get slides: %v", err),
		})
		return
	}

	for _, slide := range slides {
		if fmt.Sprintf("%d", slide.SlideNumber) == slideNumber {
			c.JSON(http.StatusOK, models.APIResponse{
				Success: true,
				Data:    slide,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, models.APIResponse{
		Success: false,
		Error:   "Slide not found",
	})
}

// AddMemory adds a memory entry for a project
func (h *AIHandler) AddMemory(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request struct {
		Key   string `json:"key" binding:"required"`
		Value string `json:"value" binding:"required"`
		Type  string `json:"type"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	if request.Type == "" {
		request.Type = "context"
	}

	memory, err := h.store.AddMemory(projectID, request.Key, request.Value, request.Type)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to add memory: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Memory added successfully",
		Data:    memory,
	})
}

// GetMemories returns all memories for a project
func (h *AIHandler) GetMemories(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	memories, err := h.aiService.GetMemories(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get memories: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    memories,
	})
}



// ResumeNarration resumes narration generation from the last successful point
func (h *AIHandler) ResumeNarration(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	var request models.NarrationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	// Check if project can be resumed
	progress, err := h.aiService.GetDetailedNarrationProgress(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to check resume status: %v", err),
		})
		return
	}

	if !progress.CanResume {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project cannot be resumed. Either no slides are completed or all slides are already completed.",
		})
		return
	}

	// Start narration generation (it will automatically resume from the correct point)
	go func() {
		err := h.aiService.GenerateNarration(projectID, request.UserRequirements)
		if err != nil {
			fmt.Printf("Background narration generation failed: %v\n", err)
		}
	}()

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: fmt.Sprintf("Narration generation resumed from slide %d", progress.CurrentSlide),
		Data: map[string]interface{}{
			"current_slide":    progress.CurrentSlide,
			"completed_slides": progress.CompletedSlides,
			"total_slides":     progress.TotalSlides,
		},
	})
}

// UpdateMemory updates an existing memory entry
func (h *AIHandler) UpdateMemory(c *gin.Context) {
	projectID := c.Param("projectId")
	memoryKey := c.Param("key")

	if projectID == "" || memoryKey == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID and memory key are required",
		})
		return
	}

	var request struct {
		Value string `json:"value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	err := h.aiService.UpdateMemory(projectID, memoryKey, request.Value)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to update memory: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Memory updated successfully",
	})
}

// DeleteMemory deletes a memory entry
func (h *AIHandler) DeleteMemory(c *gin.Context) {
	projectID := c.Param("projectId")
	memoryKey := c.Param("key")

	if projectID == "" || memoryKey == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID and memory key are required",
		})
		return
	}

	// Find and delete the memory
	memory, err := h.store.GetMemory(projectID, memoryKey)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   "Memory not found",
		})
		return
	}

	err = h.store.DeleteMemory(memory.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to delete memory: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Memory deleted successfully",
	})
}

// ClearMemories clears all memories for a project
func (h *AIHandler) ClearMemories(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Project ID is required",
		})
		return
	}

	err := h.aiService.ClearMemories(projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to clear memories: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "All memories cleared successfully",
	})
}

// GetAIProviderInfo returns information about the current AI provider
func (h *AIHandler) GetAIProviderInfo(c *gin.Context) {
	info := h.aiService.GetAIProviderInfo()

	// Validate provider configuration
	if err := h.aiService.ValidateAIProvider(); err != nil {
		info["validation_error"] = err.Error()
		info["status"] = "error"
	} else {
		info["status"] = "ok"
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    info,
	})
}

// getProgressMessage returns a user-friendly progress message
func (h *AIHandler) getProgressMessage(status string, progress int) string {
	switch status {
	case "generating_narration":
		return fmt.Sprintf("Generating narration... %d%% complete", progress)
	case "narration_ready":
		return "Narration generation completed"
	case "failed":
		return "Narration generation failed"
	default:
		return "Waiting to start narration generation"
	}
}
