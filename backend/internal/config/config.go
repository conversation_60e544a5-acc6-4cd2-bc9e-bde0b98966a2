package config

import (
	"os"
	"strconv"
)

// SubtitleStyleTemplate defines a predefined subtitle style
type SubtitleStyleTemplate struct {
	Name            string `json:"name"`
	FontSize        int    `json:"font_size"`
	FontColor       string `json:"font_color"`
	FontFamily      string `json:"font_family"`
	Outline         int    `json:"outline"`
	Shadow          bool   `json:"shadow"`
	BackgroundColor string `json:"background_color"`
	Position        string `json:"position"`
	Description     string `json:"description"`
}

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port string

	// Database configuration
	DatabaseURL  string
	DatabasePath string // Keep for backward compatibility

	// Directory paths
	UploadDir     string
	ScreenshotDir string
	VideoDir      string
	TempDir       string

	// AI configuration
	AIProvider    string // "openai", "minimax"
	OpenAIAPIKey  string
	OpenAIModel   string
	OpenAIBaseURL string

	// MiniMax configuration
	MinimaxAPIKey  string
	MinimaxGroupID string
	MinimaxModel   string
	MinimaxBaseURL string

	SystemPrompt      string
	NarratorRole      string
	NarratorStyle     string
	TargetAudience    string
	SpeakingTone      string
	SpeechNaturalness string
	MaxTokens         int
	Temperature       float64

	// FFmpeg configuration
	FFmpegPath string

	// TTS configuration (Text-to-Speech)
	TTSProvider string // "openai", "minimax", "easyvoice"
	TTSVoice    string
	TTSSpeed    float64

	// Audio pause configuration
	PauseSpeedFactor float64 // Factor to adjust pause durations (0.5 = half time, 2.0 = double time)

	// MiniMax TTS configuration
	MinimaxTTSAPIKey  string
	MinimaxTTSGroupID string
	MinimaxTTSModel   string
	MinimaxTTSVoiceID string
	MinimaxTTSEmotion string

	// EasyVoice TTS configuration
	EasyVoiceAPIURL  string
	EasyVoiceUsername string
	EasyVoicePassword string
	EasyVoiceVoice   string
	EasyVoiceRate    string
	EasyVoicePitch   string
	EasyVoiceVolume  string

	// Retry configuration for TTS
	TTSMaxRetries    int
	TTSBaseDelay     int // seconds
	TTSMaxDelay      int // seconds
	TTSBackoffFactor float64

	// Retry configuration for PPT Processing
	PPTMaxRetries    int
	PPTBaseDelay     int // seconds
	PPTMaxDelay      int // seconds
	PPTBackoffFactor float64

	// Retry configuration for Video Generation
	VideoMaxRetries    int
	VideoBaseDelay     int // seconds
	VideoMaxDelay      int // seconds
	VideoBackoffFactor float64

	// Subtitle configuration
	SubtitleEnabled        bool    // Global default for subtitle generation
	SubtitleFontSize       int     // Default font size in pixels
	SubtitleFontColor      string  // Default font color in hex format
	SubtitleBackgroundColor string // Default background color in hex format
	SubtitlePosition       string  // Default position: bottom, top, center
	SubtitleFontFamily     string  // Default font family
	SubtitleOutline        int     // Default outline width in pixels
	SubtitleShadow         bool    // Default shadow setting

	// Predefined subtitle style templates
	SubtitleStyleTemplates map[string]SubtitleStyleTemplate

	// Retry configuration for AI Services
	AIMaxRetries    int
	AIBaseDelay     int // seconds
	AIMaxDelay      int // seconds
	AIBackoffFactor float64

	// Global retry configuration
	GlobalRetryEnabled bool
	GlobalMaxRetries   int

	// LibreOffice configuration for PPT conversion
	LibreOfficePath string
}

// Load loads configuration from environment variables with defaults
func Load() *Config {
	config := &Config{
		// Server defaults
		Port: getEnv("PORT", "8080"),

		// Database defaults
		DatabaseURL:  getEnv("DATABASE_URL", ""),
		DatabasePath: getEnv("DATABASE_PATH", "./ppt-narrator.db"),

		// Directory defaults
		UploadDir:     getEnv("UPLOAD_DIR", "./uploads"),
		ScreenshotDir: getEnv("SCREENSHOT_DIR", "./screenshots"),
		VideoDir:      getEnv("VIDEO_DIR", "./videos"),
		TempDir:       getEnv("TEMP_DIR", "./temp"),

		// AI defaults
		AIProvider:    getEnv("AI_PROVIDER", "openai"),
		OpenAIAPIKey:  getEnv("OPENAI_API_KEY", ""),
		OpenAIModel:   getEnv("OPENAI_MODEL", "gpt-4"),
		OpenAIBaseURL: getEnv("OPENAI_BASE_URL", "https://api.openai.com/v1"),

		// MiniMax defaults
		MinimaxAPIKey:  getEnv("MINIMAX_API_KEY", ""),
		MinimaxGroupID: getEnv("MINIMAX_GROUP_ID", ""),
		MinimaxModel:   getEnv("MINIMAX_MODEL", "abab6.5s-chat"),
		MinimaxBaseURL: getEnv("MINIMAX_BASE_URL", "https://api.minimaxi.com/v1/text/chatcompletion_v2"),

		SystemPrompt:      getEnv("SYSTEM_PROMPT", getDefaultSystemPrompt()),
		NarratorRole:      getEnv("NARRATOR_ROLE", "专业讲师"),
		NarratorStyle:     getEnv("NARRATOR_STYLE", "亲切自然"),
		TargetAudience:    getEnv("TARGET_AUDIENCE", "大学生"),
		SpeakingTone:      getEnv("SPEAKING_TONE", "轻松友好"),
		SpeechNaturalness: getEnv("SPEECH_NATURALNESS", "高度口语化"),
		MaxTokens:         getEnvInt("MAX_TOKENS", 8000),
		Temperature:       getEnvFloat("TEMPERATURE", 0.7),

		// FFmpeg defaults
		FFmpegPath: getEnv("FFMPEG_PATH", "ffmpeg"),

		// TTS defaults
		TTSProvider: getEnv("TTS_PROVIDER", "minimax"), // "openai", "minimax", "easyvoice"
		TTSVoice:    getEnv("TTS_VOICE", "alloy"),
		TTSSpeed:    getEnvFloat("TTS_SPEED", 1.0),

		// Audio pause defaults
		PauseSpeedFactor: getEnvFloat("PAUSE_SPEED_FACTOR", 0.6), // Default to 60% of original pause time

		// MiniMax TTS defaults
		MinimaxTTSAPIKey:  getEnv("MINIMAX_TTS_API_KEY", ""),
		MinimaxTTSGroupID: getEnv("MINIMAX_TTS_GROUP_ID", ""),
		MinimaxTTSModel:   getEnv("MINIMAX_TTS_MODEL", "speech-02-hd"),
		MinimaxTTSVoiceID: getEnv("MINIMAX_TTS_VOICE_ID", "male-qn-qingse"),
		MinimaxTTSEmotion: getEnv("MINIMAX_TTS_EMOTION", "happy"),

		// EasyVoice TTS defaults
		EasyVoiceAPIURL:  getEnv("EASYVOICE_API_URL", "https://easyvoice.wetolink.com/api/v1/tts/generate"),
		EasyVoiceUsername: getEnv("EASYVOICE_USERNAME", ""),
		EasyVoicePassword: getEnv("EASYVOICE_PASSWORD", ""),
		EasyVoiceVoice:   getEnv("EASYVOICE_VOICE", "zh-CN-YunxiNeural"),
		EasyVoiceRate:    getEnv("EASYVOICE_RATE", "0%"),
		EasyVoicePitch:   getEnv("EASYVOICE_PITCH", "0Hz"),
		EasyVoiceVolume:  getEnv("EASYVOICE_VOLUME", "0%"),

		// TTS Retry defaults
		TTSMaxRetries:    getEnvInt("TTS_MAX_RETRIES", 8),
		TTSBaseDelay:     getEnvInt("TTS_BASE_DELAY", 5),
		TTSMaxDelay:      getEnvInt("TTS_MAX_DELAY", 120),
		TTSBackoffFactor: getEnvFloat("TTS_BACKOFF_FACTOR", 2.0),

		// PPT Processing Retry defaults
		PPTMaxRetries:    getEnvInt("PPT_MAX_RETRIES", 3),
		PPTBaseDelay:     getEnvInt("PPT_BASE_DELAY", 3),
		PPTMaxDelay:      getEnvInt("PPT_MAX_DELAY", 60),
		PPTBackoffFactor: getEnvFloat("PPT_BACKOFF_FACTOR", 1.5),

		// Video Generation Retry defaults
		VideoMaxRetries:    getEnvInt("VIDEO_MAX_RETRIES", 5),
		VideoBaseDelay:     getEnvInt("VIDEO_BASE_DELAY", 10),
		VideoMaxDelay:      getEnvInt("VIDEO_MAX_DELAY", 300),
		VideoBackoffFactor: getEnvFloat("VIDEO_BACKOFF_FACTOR", 2.0),

		// Subtitle defaults
		SubtitleEnabled:         getEnvBool("SUBTITLE_ENABLED", false),
		SubtitleFontSize:        getEnvInt("SUBTITLE_FONT_SIZE", 24),
		SubtitleFontColor:       getEnv("SUBTITLE_FONT_COLOR", "#FFFFFF"),
		SubtitleBackgroundColor: getEnv("SUBTITLE_BACKGROUND_COLOR", ""),
		SubtitlePosition:        getEnv("SUBTITLE_POSITION", "bottom"),
		SubtitleFontFamily:      getEnv("SUBTITLE_FONT_FAMILY", "Arial"),
		SubtitleOutline:         getEnvInt("SUBTITLE_OUTLINE", 2),
		SubtitleShadow:          getEnvBool("SUBTITLE_SHADOW", true),

		// Initialize predefined subtitle style templates
		SubtitleStyleTemplates: initSubtitleStyleTemplates(),

		// AI Services Retry defaults
		AIMaxRetries:    getEnvInt("AI_MAX_RETRIES", 5),
		AIBaseDelay:     getEnvInt("AI_BASE_DELAY", 2),
		AIMaxDelay:      getEnvInt("AI_MAX_DELAY", 60),
		AIBackoffFactor: getEnvFloat("AI_BACKOFF_FACTOR", 2.0),

		// Global Retry defaults
		GlobalRetryEnabled: getEnvBool("GLOBAL_RETRY_ENABLED", true),
		GlobalMaxRetries:   getEnvInt("GLOBAL_MAX_RETRIES", 3),

		// LibreOffice defaults
		LibreOfficePath: getEnv("LIBREOFFICE_PATH", "libreoffice"),
	}

	return config
}

// initSubtitleStyleTemplates initializes predefined subtitle style templates
func initSubtitleStyleTemplates() map[string]SubtitleStyleTemplate {
	templates := make(map[string]SubtitleStyleTemplate)

	// Classic Yellow Style - 经典黄色字幕
	templates["classic_yellow"] = SubtitleStyleTemplate{
		Name:            "classic_yellow",
		FontSize:        24,
		FontColor:       "#FFFF00",
		FontFamily:      "Arial",
		Outline:         2,
		Shadow:          true,
		BackgroundColor: "",
		Position:        "bottom",
		Description:     "经典黄色字幕，适合大多数视频",
	}

	// Professional White - 专业白色字幕
	templates["professional_white"] = SubtitleStyleTemplate{
		Name:            "professional_white",
		FontSize:        22,
		FontColor:       "#FFFFFF",
		FontFamily:      "Arial",
		Outline:         3,
		Shadow:          true,
		BackgroundColor: "#80000000",
		Position:        "bottom",
		Description:     "专业白色字幕，带半透明背景",
	}

	// Elegant Blue - 优雅蓝色字幕
	templates["elegant_blue"] = SubtitleStyleTemplate{
		Name:            "elegant_blue",
		FontSize:        26,
		FontColor:       "#00BFFF",
		FontFamily:      "Arial",
		Outline:         2,
		Shadow:          true,
		BackgroundColor: "",
		Position:        "bottom",
		Description:     "优雅蓝色字幕，适合商务演示",
	}

	// Bold Red - 醒目红色字幕
	templates["bold_red"] = SubtitleStyleTemplate{
		Name:            "bold_red",
		FontSize:        28,
		FontColor:       "#FF4444",
		FontFamily:      "Arial",
		Outline:         3,
		Shadow:          true,
		BackgroundColor: "",
		Position:        "bottom",
		Description:     "醒目红色字幕，适合重要内容",
	}

	// Soft Green - 柔和绿色字幕
	templates["soft_green"] = SubtitleStyleTemplate{
		Name:            "soft_green",
		FontSize:        24,
		FontColor:       "#00FF88",
		FontFamily:      "Arial",
		Outline:         2,
		Shadow:          true,
		BackgroundColor: "",
		Position:        "bottom",
		Description:     "柔和绿色字幕，护眼舒适",
	}

	// Large Text - 大字体字幕
	templates["large_text"] = SubtitleStyleTemplate{
		Name:            "large_text",
		FontSize:        32,
		FontColor:       "#FFFFFF",
		FontFamily:      "Arial",
		Outline:         4,
		Shadow:          true,
		BackgroundColor: "#80000000",
		Position:        "bottom",
		Description:     "大字体字幕，适合远距离观看",
	}

	// Minimal Style - 简约风格字幕
	templates["minimal"] = SubtitleStyleTemplate{
		Name:            "minimal",
		FontSize:        20,
		FontColor:       "#FFFFFF",
		FontFamily:      "Arial",
		Outline:         1,
		Shadow:          false,
		BackgroundColor: "#C0000000",
		Position:        "bottom",
		Description:     "简约风格字幕，干净清爽",
	}

	return templates
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt gets an environment variable as integer with a default value
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvFloat gets an environment variable as float64 with a default value
func getEnvFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

// getEnvBool gets an environment variable as boolean with a default value
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// getDefaultSystemPrompt returns the default system prompt for AI
func getDefaultSystemPrompt() string {
	return `你是一位富有经验的讲师，正在为听众生动地讲解PPT内容。你的表达方式自然流畅，充满个人特色，避免任何机械化的教学模式。

## 🎯 核心理念
1. **真实对话感**: 像与朋友聊天一样自然，避免刻板的教学腔调
2. **表达多样性**: 每句话都有独特的表达方式，绝不重复套路
3. **情感丰富性**: 根据内容调整语调，有惊喜、疑问、感慨等情感变化
4. **逻辑自然性**: 思路跳跃要合理，转折要顺畅，不强行连接
5. **充分表达**: 不限制讲稿长度，根据内容深度自由发挥，该详细时就详细

## 🗣️ 自然表达风格
**语言特点**:
- 使用丰富的口语化表达，但避免过度使用填充词
- 句式长短搭配，有节奏感
- 适当使用反问、感叹、假设等多种句式
- 根据内容难易程度调整语速和详细程度

**情感表达**:
- 遇到有趣内容时表现出好奇和兴奋
- 面对复杂概念时展现理解和耐心
- 讲到关键点时自然流露出重视
- 适时加入个人感悟和思考

## 🔄 表达变化策略
**避免固定模式**:
- 不使用序号式讲解（第一、第二、第三...）
- 不用固定的开场白和结束语
- 不重复相同的转折词和连接词
- 不采用相同的句式结构

**创新表达方式**:
- **引入话题**: "想象一下..."、"有没有想过..."、"这让我想到..."、"换个角度思考..."
- **解释概念**: "简单来说..."、"用大白话讲..."、"通俗点说..."、"换个说法..."
- **举例说明**: "比如说..."、"拿...来说..."、"就好比..."、"类似于..."
- **强调重点**: "值得注意的是..."、"特别有意思的是..."、"令人惊讶的是..."、"不得不提的是..."
- **转换话题**: "话说回来..."、"另一方面..."、"与此同时..."、"顺便提一下..."

## ⏸️ 停顿的艺术
在以下时机自然停顿 [停顿X秒]：
- **思考引导**: 抛出问题后给听众思考时间 [停顿2-3秒]
- **重点强调**: 关键信息前后的自然停顿 [停顿1-2秒]
- **情感转换**: 语调或话题转变时的缓冲 [停顿1秒]
- **内容消化**: 复杂概念讲完后的理解时间 [停顿2秒]
- **节奏调节**: 避免语速过快的自然间歇 [停顿0.5-1秒]

## 🚫 绝对避免的表达
**机械化用词**:
- 教学套话: "重要"、"关键"、"注意"、"掌握"、"学习"、"了解"
- 固定开场: "大家好"、"同学们"、"各位"、"今天我们"、"现在让我们"
- 序列词汇: "首先"、"其次"、"然后"、"接下来"、"最后"、"总结"
- 机械过渡: "我们来看"、"我们来分析"、"下面我们"、"这里我们"

**重复表达**:
- 同一段讲稿中任何词汇不超过2次使用
- 避免相似的句式结构
- 不重复相同的语气词和连接词
- 每个概念用不同的方式阐述

## ✨ 优秀表达示例
- "这个现象挺有意思的 [停顿1秒]，你有没有发现..."
- "等等，这里有个细节值得琢磨一下 [停顿1.5秒]"
- "说到这儿，我想起一个很形象的比喻..."
- "你可能会疑惑 [停顿1秒]，为什么会出现这种情况呢？"
- "有意思的来了 [停顿1秒]，事情并没有这么简单..."

## 🎨 个性化讲解要求
**内容处理**:
- 根据内容性质调整讲解深度和方式，不要自我限制长度
- 复杂概念用生活化的例子解释，可以多举几个例子
- 抽象理论结合具体应用场景，深入浅出地阐述
- 适时加入个人见解和思考角度，分享更多洞察
- 重要内容可以从多个角度分析，给听众全面的理解

**语言风格**:
- 保持一致的个人语言风格，但表达方式要多样
- 根据内容情感色彩调整语调
- 适当使用修辞手法增强表达效果
- 让每段讲解都有独特的开始和结束方式

记住：让每句话都充满生命力，让听众感受到真诚的分享而非机械的灌输！`
}
