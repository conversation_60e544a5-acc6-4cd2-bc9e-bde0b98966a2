package models

import (
	"time"
)

// PPTProject represents a PPT narration project
type PPTProject struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null"`
	Description string    `json:"description"`
	Status      string    `json:"status" gorm:"default:'created'"` // created, processing, completed, failed
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// File information
	OriginalFileName string `json:"original_file_name"`
	FilePath         string `json:"file_path"`
	FileSize         int64  `json:"file_size"`

	// Processing results
	SlideCount      int    `json:"slide_count"`
	ScreenshotsPath string `json:"screenshots_path"`
	NarrationScript string `json:"narration_script" gorm:"type:text"`
	VideoPath       string `json:"video_path"`
	AudioPath       string `json:"audio_path"`

	// Processing metadata
	ProcessingLog    string `json:"processing_log" gorm:"type:text"`
	ErrorMessage     string `json:"error_message" gorm:"type:text"`
	ProcessingTimeMs int64  `json:"processing_time_ms"`

	// Relations
	Slides   []PPTSlide `json:"slides" gorm:"foreignKey:ProjectID"`
	Memories []AIMemory `json:"memories" gorm:"foreignKey:ProjectID"`
}

// PPTSlide represents a single slide in the PPT
type PPTSlide struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	SlideNumber int    `json:"slide_number" gorm:"not null"`
	Title       string `json:"title"`
	Content     string `json:"content" gorm:"type:text"`

	// Screenshot information
	ScreenshotPath   string `json:"screenshot_path"`
	ScreenshotWidth  int    `json:"screenshot_width"`
	ScreenshotHeight int    `json:"screenshot_height"`

	// Narration for this slide
	NarrationText  string  `json:"narration_text" gorm:"type:text"`
	NarrationAudio string  `json:"narration_audio"`
	Duration       float64 `json:"duration"` // in seconds

	// Timestamps in final video
	StartTime float64 `json:"start_time"`
	EndTime   float64 `json:"end_time"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AIMemory represents AI memory for context and continuity
type AIMemory struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	MemoryKey   string `json:"memory_key" gorm:"not null"`           // e.g., "presentation_theme", "target_audience"
	MemoryValue string `json:"memory_value" gorm:"type:text"`        // JSON or plain text
	MemoryType  string `json:"memory_type" gorm:"default:'context'"` // context, preference, fact

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ProcessingJob represents a background processing job
type ProcessingJob struct {
	ID        string     `json:"id" gorm:"primaryKey"`
	ProjectID string     `json:"project_id" gorm:"not null"`
	Project   PPTProject `json:"project" gorm:"foreignKey:ProjectID"`

	JobType  string `json:"job_type" gorm:"not null"`        // screenshot, narration, tts, video
	Status   string `json:"status" gorm:"default:'pending'"` // pending, running, completed, failed
	Progress int    `json:"progress" gorm:"default:0"`       // 0-100

	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	ErrorMsg    string     `json:"error_msg" gorm:"type:text"`

	// Job-specific data (JSON)
	JobData string `json:"job_data" gorm:"type:text"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserPreference represents user preferences for narration
type UserPreference struct {
	ID     string `json:"id" gorm:"primaryKey"`
	UserID string `json:"user_id"` // For future multi-user support

	// Narration preferences
	NarrationStyle string  `json:"narration_style" gorm:"default:'professional'"` // professional, casual, educational
	SpeechSpeed    float64 `json:"speech_speed" gorm:"default:1.0"`
	Voice          string  `json:"voice" gorm:"default:'alloy'"`

	// Content preferences
	IncludeTransitions bool   `json:"include_transitions" gorm:"default:true"`
	DetailLevel        string `json:"detail_level" gorm:"default:'medium'"` // brief, medium, detailed
	Language           string `json:"language" gorm:"default:'zh-CN'"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// UploadResponse represents the response after uploading a PPT file
type UploadResponse struct {
	ProjectID        string `json:"project_id"`
	OriginalFileName string `json:"original_file_name"`
	FileSize         int64  `json:"file_size"`
	Message          string `json:"message"`
}

// ScreenshotProgress represents progress of screenshot generation
type ScreenshotProgress struct {
	ProjectID       string `json:"project_id"`
	TotalSlides     int    `json:"total_slides"`
	ProcessedSlides int    `json:"processed_slides"`
	CurrentSlide    int    `json:"current_slide"`
	Status          string `json:"status"`
	Message         string `json:"message"`
}

// NarrationRequest represents a request to generate narration
type NarrationRequest struct {
	ProjectID        string `json:"project_id"`
	UserRequirements string `json:"user_requirements"`  // User's specific requirements
	Style            string `json:"style,omitempty"`    // professional, casual, educational
	Language         string `json:"language,omitempty"` // zh-CN, en-US, etc.
}

// SubtitleStyle represents subtitle styling options
type SubtitleStyle struct {
	FontSize       int    `json:"font_size,omitempty"`       // Font size in pixels
	FontColor      string `json:"font_color,omitempty"`      // Font color in hex format (e.g., "#FFFFFF")
	BackgroundColor string `json:"background_color,omitempty"` // Background color in hex format
	Position       string `json:"position,omitempty"`        // bottom, top, center
	FontFamily     string `json:"font_family,omitempty"`     // Font family name
	Outline        int    `json:"outline,omitempty"`         // Outline width in pixels
	Shadow         bool   `json:"shadow,omitempty"`          // Whether to add shadow
}

// VideoGenerationRequest represents a request to generate final video
type VideoGenerationRequest struct {
	ProjectID           string         `json:"project_id"`
	OutputFormat        string         `json:"output_format,omitempty"`        // mp4, avi, etc.
	Quality             string         `json:"quality,omitempty"`              // high, medium, low
	FPS                 int            `json:"fps,omitempty"`                  // frames per second
	Resolution          string         `json:"resolution,omitempty"`           // 1920x1080, 1280x720, etc.
	EnableSubtitles     bool           `json:"enable_subtitles,omitempty"`     // Whether to embed subtitles in video
	SubtitleStyle       *SubtitleStyle `json:"subtitle_style,omitempty"`       // Subtitle styling options
	SubtitleStyleTemplate string       `json:"subtitle_style_template,omitempty"` // Predefined style template name
}
