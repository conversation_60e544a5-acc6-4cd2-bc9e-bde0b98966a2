package models

import "time"

// Common response structures for Swagger documentation

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Status  string      `json:"status" example:"success"`
	Message string      `json:"message" example:"Operation completed successfully"`
	Data    interface{} `json:"data,omitempty"`
} // @name SuccessResponse

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Status  string `json:"status" example:"error"`
	Message string `json:"message" example:"An error occurred"`
	Error   string `json:"error,omitempty" example:"Detailed error message"`
} // @name ErrorResponse

// HealthResponse represents health check response
type HealthResponse struct {
	Status  string `json:"status" example:"healthy"`
	Service string `json:"service" example:"ppt-narrator"`
	Version string `json:"version" example:"1.0.0"`
} // @name HealthResponse

// ProjectResponse represents a project in API responses
type ProjectResponse struct {
	ID            string    `json:"id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Name          string    `json:"name" example:"My Presentation"`
	Status        string    `json:"status" example:"completed"`
	CreatedAt     time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt     time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	SlideCount    int       `json:"slide_count" example:"10"`
	HasNarration  bool      `json:"has_narration" example:"true"`
	HasAudio      bool      `json:"has_audio" example:"true"`
	HasVideo      bool      `json:"has_video" example:"false"`
	Progress      int       `json:"progress" example:"75"`
	ErrorMessage  string    `json:"error_message,omitempty"`
} // @name ProjectResponse

// Note: UploadResponse is defined in models.go

// ProgressResponse represents progress information
type ProgressResponse struct {
	ProjectID     string `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	CurrentStep   string `json:"current_step" example:"generating_narration"`
	Progress      int    `json:"progress" example:"45"`
	TotalSteps    int    `json:"total_steps" example:"4"`
	CompletedSteps int   `json:"completed_steps" example:"2"`
	Status        string `json:"status" example:"processing"`
	Message       string `json:"message" example:"Generating narration for slide 5 of 10"`
	EstimatedTime string `json:"estimated_time,omitempty" example:"5 minutes"`
} // @name ProgressResponse

// NarrationResponse represents narration data
type NarrationResponse struct {
	ProjectID   string           `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	SlideCount  int              `json:"slide_count" example:"10"`
	Status      string           `json:"status" example:"completed"`
	Slides      []SlideNarration `json:"slides"`
	TotalLength string           `json:"total_length" example:"5m 30s"`
} // @name NarrationResponse

// SlideNarration represents narration for a single slide
type SlideNarration struct {
	SlideNumber int    `json:"slide_number" example:"1"`
	Title       string `json:"title" example:"Introduction"`
	Content     string `json:"content" example:"Welcome to our presentation..."`
	Narration   string `json:"narration" example:"Welcome everyone to today's presentation..."`
	Duration    string `json:"duration" example:"30s"`
} // @name SlideNarration

// AudioResponse represents audio generation response
type AudioResponse struct {
	ProjectID    string `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Status       string `json:"status" example:"completed"`
	AudioURL     string `json:"audio_url,omitempty" example:"/api/v1/download/123e4567-e89b-12d3-a456-426614174000/audio"`
	Duration     string `json:"duration" example:"5m 30s"`
	FileSize     int64  `json:"file_size" example:"2048000"`
	Format       string `json:"format" example:"mp3"`
	SampleRate   int    `json:"sample_rate" example:"44100"`
} // @name AudioResponse

// VideoResponse represents video generation response
type VideoResponse struct {
	ProjectID    string `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Status       string `json:"status" example:"completed"`
	VideoURL     string `json:"video_url,omitempty" example:"/api/v1/download/123e4567-e89b-12d3-a456-426614174000/video"`
	Duration     string `json:"duration" example:"5m 30s"`
	FileSize     int64  `json:"file_size" example:"10240000"`
	Format       string `json:"format" example:"mp4"`
	Resolution   string `json:"resolution" example:"1920x1080"`
	FrameRate    int    `json:"frame_rate" example:"30"`
} // @name VideoResponse

// PipelineRequest represents pipeline processing request
type PipelineRequest struct {
	VoiceType     string `json:"voice_type" example:"female" enums:"male,female"`
	Speed         string `json:"speed" example:"normal" enums:"slow,normal,fast"`
	Language      string `json:"language" example:"zh-CN" enums:"zh-CN,en-US"`
	GenerateVideo bool   `json:"generate_video" example:"true"`
	VideoQuality  string `json:"video_quality" example:"high" enums:"low,medium,high"`
} // @name PipelineRequest

// PipelineResponse represents pipeline processing response
type PipelineResponse struct {
	ProjectID     string `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Status        string `json:"status" example:"processing"`
	CurrentStep   string `json:"current_step" example:"generating_audio"`
	Progress      int    `json:"progress" example:"60"`
	Message       string `json:"message" example:"Processing audio for slide 6 of 10"`
	EstimatedTime string `json:"estimated_time,omitempty" example:"3 minutes"`
} // @name PipelineResponse

// MemoryRequest represents memory management request
type MemoryRequest struct {
	Key   string `json:"key" example:"presentation_style" binding:"required"`
	Value string `json:"value" example:"professional and engaging" binding:"required"`
} // @name MemoryRequest

// MemoryResponse represents memory data
type MemoryResponse struct {
	ProjectID string            `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Memories  map[string]string `json:"memories"`
} // @name MemoryResponse

// SystemInfoResponse represents system information
type SystemInfoResponse struct {
	FFmpegVersion string `json:"ffmpeg_version" example:"4.4.0"`
	FFmpegPath    string `json:"ffmpeg_path" example:"/usr/bin/ffmpeg"`
	AIProvider    string `json:"ai_provider" example:"openai"`
	TTSProvider   string `json:"tts_provider" example:"minimax"`
	Status        string `json:"status" example:"ready"`
} // @name SystemInfoResponse

// DownloadInfoResponse represents download information
type DownloadInfoResponse struct {
	ProjectID     string                    `json:"project_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	ProjectName   string                    `json:"project_name" example:"My Presentation"`
	AvailableFiles []AvailableFile          `json:"available_files"`
	TotalSize     int64                     `json:"total_size" example:"15360000"`
} // @name DownloadInfoResponse

// AvailableFile represents an available file for download
type AvailableFile struct {
	Type        string `json:"type" example:"video" enums:"video,audio,screenshots,narration"`
	FileName    string `json:"file_name" example:"presentation.mp4"`
	FileSize    int64  `json:"file_size" example:"10240000"`
	DownloadURL string `json:"download_url" example:"/api/v1/download/123e4567-e89b-12d3-a456-426614174000/video"`
	Available   bool   `json:"available" example:"true"`
} // @name AvailableFile

// Note: NarrationRequest is defined in models.go

// RetryRequest represents a pipeline retry request
type RetryRequest struct {
	Stage            string `json:"stage" example:"narration" enums:"screenshot,narration,audio,video"`
	UserRequirements string `json:"user_requirements,omitempty" example:"Updated requirements"`
} // @name RetryRequest

// AudioDebugDiagnosis represents audio debug diagnosis results
type AudioDebugDiagnosis struct {
	ProjectStatus   string                 `json:"project_status" example:"audio_ready"`
	AudioFiles      []AudioFileInfo        `json:"audio_files"`
	FFmpegStatus    string                 `json:"ffmpeg_status" example:"available"`
	Recommendations []string               `json:"recommendations"`
	Issues          []string               `json:"issues"`
} // @name AudioDebugDiagnosis

// AudioFileInfo represents information about an audio file
type AudioFileInfo struct {
	Name     string `json:"name" example:"slide_1.mp3"`
	Path     string `json:"path" example:"/uploads/project/audio/slide_1.mp3"`
	Exists   bool   `json:"exists" example:"true"`
	Size     int64  `json:"size" example:"512000"`
	Duration string `json:"duration,omitempty" example:"30.5s"`
	Format   string `json:"format,omitempty" example:"mp3"`
	Valid    bool   `json:"valid" example:"true"`
} // @name AudioFileInfo

// AudioFixResult represents the result of audio fix operation
type AudioFixResult struct {
	ActionsTaken []string `json:"actions_taken"`
	Success      bool     `json:"success" example:"true"`
	Message      string   `json:"message" example:"Audio issues fixed successfully"`
} // @name AudioFixResult
