package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"ppt-narrator/internal/config"
	"ppt-narrator/internal/docs"
	"ppt-narrator/internal/handlers"
	"ppt-narrator/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Print startup information
	log.Printf("Starting PPT Narrator Server...")
	log.Printf("Configuration:")
	log.Printf("  Port: %s", cfg.Port)
	log.Printf("  Upload Dir: %s", cfg.UploadDir)
	log.Printf("  Screenshot Dir: %s", cfg.ScreenshotDir)
	log.Printf("  Video Dir: %s", cfg.VideoDir)
	log.Printf("  OpenAI Model: %s", cfg.OpenAIModel)
	log.Printf("  TTS Provider: %s", cfg.TTSProvider)

	// Create directories
	createDirectories(cfg)

	// Initialize database
	var databaseURL string
	if cfg.DatabaseURL != "" {
		databaseURL = cfg.DatabaseURL
		log.Printf("  Database: PostgreSQL")
	} else {
		databaseURL = cfg.DatabasePath
		log.Printf("  Database: SQLite (%s)", cfg.DatabasePath)
	}

	dbService, err := services.NewDatabaseService(databaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer dbService.Close()

	// Initialize services
	store := services.NewDatabaseStoreService(dbService.GetDB())
	pptProcessor := services.NewPPTProcessorService(cfg, store)
	aiService := services.NewAIService(cfg, store)
	ttsService := services.NewTTSService(cfg, store)
	videoService := services.NewVideoService(cfg, store)
	pipelineService := services.NewPipelineService(cfg, store, pptProcessor, aiService, ttsService, videoService)

	// Initialize handlers
	uploadHandler := handlers.NewUploadHandler(cfg, store, pptProcessor)
	aiHandler := handlers.NewAIHandler(aiService, store)
	videoHandler := handlers.NewVideoHandler(videoService, ttsService, store, cfg)
	ttsHandler := handlers.NewTTSHandler(cfg, ttsService, store)
	pipelineHandler := handlers.NewPipelineHandler(cfg, store, pipelineService)
	downloadHandler := handlers.NewDownloadHandler(cfg, store)
	audioDebugHandler := handlers.NewAudioDebugHandler(cfg, store)

	// Setup router
	router := setupRouter(uploadHandler, aiHandler, videoHandler, ttsHandler, pipelineHandler, downloadHandler, audioDebugHandler)

	// Start server
	log.Printf("Server starting on port %s", cfg.Port)
	log.Printf("API endpoints available at: http://localhost:%s", cfg.Port)
	log.Printf("Health check: http://localhost:%s/health", cfg.Port)

	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// createDirectories creates necessary directories
func createDirectories(cfg *config.Config) {
	dirs := []string{
		cfg.UploadDir,
		cfg.ScreenshotDir,
		cfg.VideoDir,
		cfg.TempDir,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Printf("Warning: Failed to create directory %s: %v", dir, err)
		} else {
			log.Printf("✓ Directory created/verified: %s", dir)
		}
	}
}

// setupRouter configures the HTTP router
func setupRouter(uploadHandler *handlers.UploadHandler, aiHandler *handlers.AIHandler, videoHandler *handlers.VideoHandler, ttsHandler *handlers.TTSHandler, pipelineHandler *handlers.PipelineHandler, downloadHandler *handlers.DownloadHandler, audioDebugHandler *handlers.AudioDebugHandler) *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.Default()

	// Setup RapiDoc documentation
	docs.SetupRapiDocRoutes(router)

	// Configure CORS
	router.Use(cors.New(cors.Config{
		AllowOrigins: []string{
			"http://localhost:3000",
			"http://localhost:5173",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:5173",
			"http://127.0.0.1:8080",
			"http://127.0.0.1:38080",
		},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "Content-Disposition"},
		ExposeHeaders:    []string{"Content-Length", "Content-Disposition"},
		AllowCredentials: true,
	}))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "ppt-narrator",
			"version": "1.0.0",
		})
	})

	// API routes
	api := router.Group("/api/v1")
	{
		// Pipeline routes
		pipeline := api.Group("/pipeline")
		{
			pipeline.POST("/upload-and-process", pipelineHandler.UploadAndProcess)
			pipeline.GET("/:projectId/progress", pipelineHandler.GetPipelineProgress)
			pipeline.POST("/:projectId/retry", pipelineHandler.RetryPipeline)
			pipeline.POST("/:projectId/cancel", pipelineHandler.CancelPipeline)
			pipeline.GET("/:projectId/status", pipelineHandler.GetPipelineStatus)
			pipeline.GET("/active", pipelineHandler.ListActivePipelines)
		}

		// Project management routes
		projects := api.Group("/projects")
		{
			projects.POST("/upload", uploadHandler.UploadPPT)
			projects.GET("/", uploadHandler.ListProjects)
			projects.GET("/:projectId", uploadHandler.GetProject)
			projects.DELETE("/:projectId", uploadHandler.DeleteProject)
			projects.GET("/:projectId/screenshots", uploadHandler.GetProjectScreenshots)
		}

		// Narration routes
		narration := api.Group("/narration")
		{
			narration.POST("/:projectId/generate", aiHandler.GenerateNarration)
			narration.GET("/:projectId", aiHandler.GetNarration)
			narration.GET("/:projectId/progress", aiHandler.GetNarrationProgress)
		}

		// Memory routes
		memory := api.Group("/memory")
		{
			memory.POST("/:projectId", aiHandler.AddMemory)
			memory.GET("/:projectId", aiHandler.GetMemories)
			memory.DELETE("/:projectId", aiHandler.ClearMemories)
		}

		// Audio routes
		audio := api.Group("/audio")
		{
			audio.POST("/:projectId/generate", videoHandler.GenerateAudio)
			audio.GET("/:projectId", videoHandler.GetProjectAudio)
			audio.GET("/:projectId/progress", videoHandler.GetAudioProgress)
		}

		// TTS routes
		tts := api.Group("/tts")
		{
			tts.GET("/providers", ttsHandler.GetTTSProviders)
			tts.POST("/provider", ttsHandler.SetTTSProvider)
			tts.GET("/test/:provider", ttsHandler.TestTTSProvider)
		}

		// Video routes
		video := api.Group("/video")
		{
			video.POST("/:projectId/generate", videoHandler.GenerateVideo)
			video.GET("/:projectId", videoHandler.GetVideo)
			video.GET("/:projectId/progress", videoHandler.GetVideoProgress)
		}

		// Subtitle routes
		subtitle := api.Group("/subtitle")
		{
			subtitle.GET("/templates", videoHandler.GetSubtitleStyleTemplates)
		}

		// Download routes
		download := api.Group("/download")
		{
			download.GET("/:projectId/info", downloadHandler.GetDownloadInfo)
			download.GET("/:projectId/video", downloadHandler.DownloadVideo)
			download.GET("/:projectId/audio", downloadHandler.DownloadAudio)
			download.GET("/:projectId/screenshots", downloadHandler.DownloadScreenshots)
			download.GET("/:projectId/narration", downloadHandler.DownloadNarrationScript)
			download.GET("/:projectId/all", downloadHandler.DownloadAll)
		}

		// System routes
		system := api.Group("/system")
		{
			system.GET("/ffmpeg/validate", videoHandler.ValidateFFmpeg)
			system.GET("/ai/info", aiHandler.GetAIProviderInfo)
		}

		// Audio debug routes
		audioDebug := api.Group("/audio-debug")
		{
			audioDebug.GET("/:projectId/diagnose", audioDebugHandler.DiagnoseAudioIssues)
			audioDebug.POST("/:projectId/fix", audioDebugHandler.FixAudioIssues)
		}
	}

	// Serve static files (for development)
	router.Static("/uploads", "./uploads")
	router.Static("/screenshots", "./screenshots")
	router.Static("/videos", "./videos")

	// Add middleware for request logging
	router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("02/Jan/2006:15:04:05 -0700"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))

	// Recovery middleware
	router.Use(gin.Recovery())

	return router
}
