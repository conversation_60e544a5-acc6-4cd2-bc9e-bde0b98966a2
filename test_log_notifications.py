#!/usr/bin/env python3
"""
Test script to verify MCP log notifications are working properly.
This script establishes a proper MCP session and tests the upload_and_monitor tool.
"""

import requests
import json
import time

# Configuration
MCP_URL = "http://localhost:48081/mcp"
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxODgxMDYyODMxMSIsImp0aSI6ImE0MzBlMDA1LWFlZWEtNDUyNy04YmI1LWM5OTVmMWQ4NDA3YSIsImlhdCI6MTc1NjczMjgyMCwiZXhwIjoxNzU2ODE5MjIwfQ.X6uJvvBGH6t6uBCE_X-Mfy7IkTlcMXdsAIPVx7fkEUw"

def make_mcp_request(method, params=None, request_id=1):
    """Make an MCP request"""
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {AUTH_TOKEN}',
        'Offical_endpoint_url': 'https://dstaff.dbappsecurity.com.cn',
        'Mcp-Session-Id': 'mcp-session-test-log-notifications'
    }
    
    data = {
        'jsonrpc': '2.0',
        'id': request_id,
        'method': method
    }
    
    if params:
        data['params'] = params
    
    print(f"🔄 Making MCP request: {method}")
    response = requests.post(MCP_URL, headers=headers, json=data, timeout=30)
    print(f"📊 Status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"✅ Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON response: {response.text}")
            return None
    else:
        print(f"❌ Error response: {response.text}")
        return None

def test_mcp_log_notifications():
    """Test MCP log notifications functionality"""
    print("🧪 Testing MCP Log Notifications")
    print("=" * 50)
    
    # Step 1: Initialize MCP session
    print("\n1️⃣ Initializing MCP session...")
    init_result = make_mcp_request('initialize', {
        'protocolVersion': '2025-03-26',
        'capabilities': {},
        'clientInfo': {
            'name': 'test-client',
            'version': '1.0.0'
        }
    })
    
    if not init_result:
        print("❌ Failed to initialize MCP session")
        return False
    
    # Step 2: Send initialized notification
    print("\n2️⃣ Sending initialized notification...")
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {AUTH_TOKEN}',
        'Offical_endpoint_url': 'https://dstaff.dbappsecurity.com.cn',
        'Mcp-Session-Id': 'mcp-session-test-log-notifications'
    }
    
    notification_data = {
        'jsonrpc': '2.0',
        'method': 'notifications/initialized'
    }
    
    response = requests.post(MCP_URL, headers=headers, json=notification_data, timeout=10)
    print(f"📊 Initialized notification status: {response.status_code}")
    
    # Step 3: List available tools
    print("\n3️⃣ Listing available tools...")
    tools_result = make_mcp_request('tools/list', request_id=2)
    
    if not tools_result:
        print("❌ Failed to list tools")
        return False
    
    # Step 4: Test upload_and_monitor tool
    print("\n4️⃣ Testing upload_and_monitor tool...")
    tool_params = {
        'name': 'upload_and_monitor',
        'arguments': {
            'name': 'test-presentation.pptx',
            'file_url': 'https://example.com/test.pptx',
            'user_requirements': '生成一个测试视频，验证日志通知功能',
            'enable_subtitles': True,
            'subtitle_style_template': 'classic_yellow',
            'context': {
                'task_id': 'test-log-notifications-123'
            }
        }
    }
    
    tool_result = make_mcp_request('tools/call', tool_params, request_id=3)
    
    if tool_result:
        print("✅ Tool call completed successfully!")
        print("🔍 Check the MCP server logs for log notification messages")
        return True
    else:
        print("❌ Tool call failed")
        return False

if __name__ == "__main__":
    success = test_mcp_log_notifications()
    if success:
        print("\n🎉 Test completed! Check the MCP server logs for log notifications.")
    else:
        print("\n❌ Test failed!")
