version: '3.8'

services:
  ppt-narrator:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # 数据库配置
      - DATABASE_URL=****************************************/pptnarrator?sslmode=disable
      # 目录配置
      - UPLOAD_DIR=/app/uploads
      - SCREENSHOT_DIR=/app/screenshots
      - VIDEO_DIR=/app/videos
      - TEMP_DIR=/app/temp
      # AI 配置 - 使用 OpenAI 作为备用
      - AI_PROVIDER=${AI_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      # TTS 配置 - 默认使用 EasyVoice
      - TTS_PROVIDER=easyvoice
      - TTS_VOICE=${TTS_VOICE:-alloy}
      - TTS_SPEED=${TTS_SPEED:-1.0}
      # EasyVoice TTS 配置
      - EASYVOICE_API_URL=${EASYVOICE_API_URL:-https://easyvoice.wetolink.com/api/v1/tts/generate}
      - EASYVOICE_USERNAME=${EASYVOICE_USERNAME}
      - EASYVOICE_PASSWORD=${EASYVOICE_PASSWORD}
      - EASYVOICE_VOICE=${EASYVOICE_VOICE:-zh-CN-YunxiNeural}
      - EASYVOICE_RATE=${EASYVOICE_RATE:-0%}
      - EASYVOICE_PITCH=${EASYVOICE_PITCH:-0Hz}
      - EASYVOICE_VOLUME=${EASYVOICE_VOLUME:-0%}
      # OpenAI TTS 配置（备用）
      - OPENAI_TTS_API_KEY=${OPENAI_API_KEY}
      # MiniMax TTS 配置（备用）
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY:-}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID:-}
      - MINIMAX_TTS_MODEL=${MINIMAX_TTS_MODEL:-speech-02-hd}
      - MINIMAX_TTS_VOICE_ID=${MINIMAX_TTS_VOICE_ID:-male-qn-qingse}
      - MINIMAX_TTS_EMOTION=${MINIMAX_TTS_EMOTION:-happy}
      # 系统配置
      - LIBREOFFICE_PATH=libreoffice
      - FFMPEG_PATH=ffmpeg
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-}
      - NARRATOR_ROLE=${NARRATOR_ROLE:-资深教授}
      - NARRATOR_STYLE=${NARRATOR_STYLE:-亲切自然}
      - TARGET_AUDIENCE=${TARGET_AUDIENCE:-大学生}
      - SPEAKING_TONE=${SPEAKING_TONE:-轻松友好}
      - SPEECH_NATURALNESS=${SPEECH_NATURALNESS:-高度口语化}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - TEMPERATURE=${TEMPERATURE:-0.7}
    volumes:
      - ppt_uploads_easyvoice:/app/uploads
      - ppt_screenshots_easyvoice:/app/screenshots
      - ppt_videos_easyvoice:/app/videos
      - ppt_temp_easyvoice:/app/temp
      - ppt_audio_easyvoice:/app/audio
      - ppt_work_easyvoice:/app/work
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=pptnarrator
      - POSTGRES_USER=pptuser
      - POSTGRES_PASSWORD=pptpass
    volumes:
      - ppt_postgres_data_easyvoice:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pptuser -d pptnarrator"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  ppt_uploads_easyvoice:
    driver: local
  ppt_screenshots_easyvoice:
    driver: local
  ppt_videos_easyvoice:
    driver: local
  ppt_temp_easyvoice:
    driver: local
  ppt_audio_easyvoice:
    driver: local
  ppt_work_easyvoice:
    driver: local
  ppt_postgres_data_easyvoice:
    driver: local

networks:
  default:
    name: ppt-narrator-easyvoice
