#!/usr/bin/env python3
"""
EasyVoice TTS 使用示例

这个脚本演示如何使用新增的 EasyVoice TTS 功能：
1. 获取可用的 TTS 提供商
2. 切换到 EasyVoice 提供商
3. 选择不同的语音模型
4. 生成音频文件

使用前请确保：
1. PPT Narrator 服务正在运行
2. 在 .env 文件中配置了 EasyVoice 凭据
"""

import requests
import json
import time
import sys
from typing import Dict, List, Optional

class PPTNarratorTTSClient:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        
    def get_tts_providers(self) -> Dict:
        """获取可用的 TTS 提供商"""
        response = requests.get(f"{self.api_base}/tts/providers")
        return response.json()
    
    def set_tts_provider(self, provider: str, voice: Optional[str] = None) -> Dict:
        """设置 TTS 提供商"""
        data = {"provider": provider}
        if voice:
            data["voice"] = voice
            
        response = requests.post(
            f"{self.api_base}/tts/provider",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        return response.json()
    
    def test_tts_provider(self, provider: str) -> Dict:
        """测试 TTS 提供商"""
        response = requests.get(f"{self.api_base}/tts/test/{provider}")
        return response.json()
    
    def generate_audio(self, project_id: str) -> Dict:
        """生成音频"""
        response = requests.post(f"{self.api_base}/audio/{project_id}/generate")
        return response.json()
    
    def get_audio_progress(self, project_id: str) -> Dict:
        """获取音频生成进度"""
        response = requests.get(f"{self.api_base}/audio/{project_id}/progress")
        return response.json()

def print_colored(text: str, color: str = "white"):
    """打印彩色文本"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")

def main():
    print_colored("=== EasyVoice TTS 使用示例 ===", "blue")
    
    # 创建客户端
    client = PPTNarratorTTSClient()
    
    try:
        # 1. 获取可用的 TTS 提供商
        print_colored("\n1. 获取可用的 TTS 提供商", "yellow")
        providers_response = client.get_tts_providers()
        
        if providers_response.get("success"):
            providers = providers_response["data"]["providers"]
            current_provider = providers_response["data"]["current_provider"]
            
            print(f"当前提供商: {current_provider}")
            print("\n可用提供商:")
            
            for provider in providers:
                status = "✓ 可用" if provider["available"] else "✗ 不可用"
                current = " (当前)" if provider["current"] else ""
                print(f"  - {provider['display_name']} ({provider['name']}): {status}{current}")
                print(f"    {provider['description']}")
                if provider["voices"]:
                    print(f"    支持语音: {', '.join(provider['voices'][:5])}{'...' if len(provider['voices']) > 5 else ''}")
                print()
        else:
            print_colored(f"获取提供商失败: {providers_response.get('error')}", "red")
            return
        
        # 2. 测试 EasyVoice 提供商
        print_colored("2. 测试 EasyVoice 提供商", "yellow")
        test_response = client.test_tts_provider("easyvoice")
        
        if test_response.get("success"):
            print_colored("✓ EasyVoice 提供商测试成功", "green")
            print(f"状态: {test_response['data']['status']}")
        else:
            print_colored(f"✗ EasyVoice 提供商测试失败: {test_response.get('error')}", "red")
            print_colored("请检查 EasyVoice 凭据配置", "yellow")
        
        # 3. 切换到 EasyVoice 提供商
        print_colored("\n3. 切换到 EasyVoice 提供商", "yellow")
        
        # 可用的 EasyVoice 语音模型
        easyvoice_voices = [
            ("zh-CN-YunxiNeural", "云希 (男声，自然亲切)"),
            ("zh-CN-XiaoxiaoNeural", "晓晓 (女声，甜美可爱)"),
            ("zh-CN-YunyangNeural", "云扬 (男声，成熟稳重)"),
            ("zh-CN-XiaohanNeural", "晓涵 (女声，知性优雅)"),
        ]
        
        print("可选的 EasyVoice 语音模型:")
        for i, (voice_id, description) in enumerate(easyvoice_voices, 1):
            print(f"  {i}. {voice_id} - {description}")
        
        # 选择语音模型
        try:
            choice = input(f"\n请选择语音模型 (1-{len(easyvoice_voices)}, 默认1): ").strip()
            if not choice:
                choice = "1"
            choice_idx = int(choice) - 1
            
            if 0 <= choice_idx < len(easyvoice_voices):
                selected_voice = easyvoice_voices[choice_idx][0]
                print(f"选择了: {easyvoice_voices[choice_idx][1]}")
            else:
                selected_voice = easyvoice_voices[0][0]
                print(f"无效选择，使用默认: {easyvoice_voices[0][1]}")
        except (ValueError, KeyboardInterrupt):
            selected_voice = easyvoice_voices[0][0]
            print(f"使用默认: {easyvoice_voices[0][1]}")
        
        # 切换提供商
        switch_response = client.set_tts_provider("easyvoice", selected_voice)
        
        if switch_response.get("success"):
            print_colored(f"✓ 成功切换到 EasyVoice 提供商，语音: {selected_voice}", "green")
        else:
            print_colored(f"✗ 切换提供商失败: {switch_response.get('error')}", "red")
            return
        
        # 4. 演示其他语音模型切换
        print_colored("\n4. 演示语音模型切换", "yellow")
        
        for voice_id, description in easyvoice_voices[1:3]:  # 演示另外两个语音
            print(f"\n切换到: {description}")
            switch_response = client.set_tts_provider("easyvoice", voice_id)
            
            if switch_response.get("success"):
                print_colored(f"✓ 成功切换到 {voice_id}", "green")
            else:
                print_colored(f"✗ 切换失败: {switch_response.get('error')}", "red")
            
            time.sleep(1)  # 短暂延迟
        
        # 5. 如果提供了项目ID，演示音频生成
        project_id = input("\n请输入项目ID进行音频生成测试 (留空跳过): ").strip()
        
        if project_id:
            print_colored(f"\n5. 使用项目 {project_id} 生成音频", "yellow")
            
            # 切换回推荐的语音
            client.set_tts_provider("easyvoice", "zh-CN-YunxiNeural")
            print("使用推荐语音: zh-CN-YunxiNeural (云希)")
            
            # 开始生成音频
            generate_response = client.generate_audio(project_id)
            
            if generate_response.get("success"):
                print_colored("✓ 音频生成已开始", "green")
                
                # 监控进度
                print("监控生成进度...")
                for i in range(30):  # 最多等待30次
                    time.sleep(2)
                    progress_response = client.get_audio_progress(project_id)
                    
                    if progress_response.get("success"):
                        progress_data = progress_response["data"]
                        status = progress_data.get("status", "unknown")
                        
                        if status == "completed":
                            print_colored("✓ 音频生成完成！", "green")
                            break
                        elif status == "failed":
                            print_colored("✗ 音频生成失败", "red")
                            if "error" in progress_data:
                                print(f"错误: {progress_data['error']}")
                            break
                        else:
                            completed = progress_data.get("completed_slides", 0)
                            total = progress_data.get("total_slides", 0)
                            print(f"进度: {completed}/{total} 幻灯片")
                    else:
                        print_colored(f"获取进度失败: {progress_response.get('error')}", "red")
                        break
                else:
                    print_colored("音频生成超时，请稍后检查", "yellow")
            else:
                print_colored(f"✗ 音频生成失败: {generate_response.get('error')}", "red")
        
        print_colored("\n=== 示例完成 ===", "blue")
        print_colored("EasyVoice TTS 功能演示完成！", "green")
        
    except requests.exceptions.ConnectionError:
        print_colored("错误: 无法连接到 PPT Narrator 服务", "red")
        print_colored("请确保服务正在运行在 http://localhost:8080", "yellow")
    except KeyboardInterrupt:
        print_colored("\n用户中断操作", "yellow")
    except Exception as e:
        print_colored(f"发生错误: {str(e)}", "red")

if __name__ == "__main__":
    main()
